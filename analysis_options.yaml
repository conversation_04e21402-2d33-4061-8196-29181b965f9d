# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

# set severity for lint rules
# error: cause analysis fail
analyzer:
  exclude:
  # adding file which ignore analysis
  # example:
  # - lib/client.dart
  # - lib/server/*.g.dart
  # - test/_data/**
  errors:
    avoid_print: error


  language:
    strict-inference: true
    strict-raw-types: true


linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at
  # https://dart-lang.github.io/linter/lints/index.html.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    # To Enable or Disable a rule
    #  * avoid_print: false  # Uncomment to disable the `avoid_print` rule
    #  * prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule
    # To setup a lint rule severity is Error [must be fixed before your application can be compiled and run.]
    # so these rules MUST BE configured in [analyzer.errors].

    # === ADDITIONAL RULES ===
    # References: https://dart.dev/tools/linter-rules

    # Error Rules: Aim to prevent bugs or issues that could lead to incorrect program behavior.
    avoid_catching_errors: true
    avoid_void_async: true
    cancel_subscriptions: true
    close_sinks: true
    avoid_catches_without_on_clauses: true
    avoid_implementing_value_types: true

    # Performance Rules: Focus on improving the efficiency and speed of the code.
    prefer_final_locals: true
    prefer_final_in_for_each: true

    # Style Rules: Encourage code formatting and organization for readability and maintainability.
    prefer_single_quotes: true
    type_annotate_public_apis: true
    join_return_with_assignment: true
    avoid_redundant_argument_values: true
    avoid_unused_constructor_parameters: true
    unnecessary_parenthesis: true
    use_setters_to_change_properties: true
    always_specify_types: true
    prefer_relative_imports: true
    avoid_bool_literals_in_conditional_expressions: true
    depend_on_referenced_packages: true
    always_put_control_body_on_new_line: true
    always_put_required_named_parameters_first: true


# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options
