#!/bin/bash

# Enhanced test script for EVO Modular System Phase 2
# This script tests the complete modular system with all feature modules

set -e

echo "🧪 Testing EVO Modular System Phase 2 Implementation"
echo "===================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_phase() {
    echo -e "${PURPLE}[PHASE]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Check if flutter is available
if ! command -v fvm &> /dev/null; then
    print_error "FVM is not installed or not in PATH"
    exit 1
fi

print_status "Checking Flutter version..."
fvm flutter --version

print_status "Getting dependencies..."
fvm flutter pub get

echo ""
print_phase "Phase 1: Static Analysis"
print_status "Running static analysis on all modules..."
if fvm flutter analyze lib/base/modules/; then
    print_success "Module static analysis passed"
else
    print_warning "Module static analysis found issues (continuing anyway)"
fi

echo ""
print_phase "Phase 2: Unit Tests"
print_status "Running modular system unit tests..."
if fvm flutter test test/modules/modular_system_test.dart --verbose; then
    print_success "Modular system tests passed"
else
    print_error "Modular system tests failed"
    exit 1
fi

echo ""
print_phase "Phase 3: Module Compilation Tests"
print_status "Testing individual module compilation..."

modules=(
    "lib/base/modules/core/auth_module.dart"
    "lib/base/modules/core/biometric_module.dart"
    "lib/base/modules/core/navigation_module.dart"
    "lib/base/modules/core/storage_module.dart"
    "lib/base/modules/data/api_module.dart"
    "lib/base/modules/data/repository_module.dart"
    "lib/base/modules/data/cache_module.dart"
    "lib/base/modules/feature/login_module.dart"
    "lib/base/modules/feature/main_screen_module.dart"
    "lib/base/modules/feature/profile_module.dart"
    "lib/base/modules/feature/ekyc_module.dart"
    "lib/base/modules/feature/transaction_details_module.dart"
    "lib/base/modules/feature/account_activation_module.dart"
    "lib/base/modules/feature/verify_otp_module.dart"
    "lib/base/modules/ui/theme_module.dart"
    "lib/base/modules/ui/components_module.dart"
    "lib/base/modules/utility/validation_module.dart"
    "lib/base/modules/utility/logging_module.dart"
    "lib/base/modules/utility/performance_module.dart"
    "lib/base/modules/utility/privilege_action_module.dart"
)

failed_modules=()
for module in "${modules[@]}"; do
    if fvm flutter analyze "$module" > /dev/null 2>&1; then
        print_success "✓ $(basename "$module")"
    else
        print_warning "✗ $(basename "$module")"
        failed_modules+=("$module")
    fi
done

if [ ${#failed_modules[@]} -eq 0 ]; then
    print_success "All modules compiled successfully"
else
    print_warning "Some modules had compilation issues: ${failed_modules[*]}"
fi

echo ""
print_phase "Phase 4: Performance Tests"
print_status "Testing performance utilities..."
if fvm flutter analyze lib/util/performance/ > /dev/null 2>&1; then
    print_success "Performance utilities compiled successfully"
else
    print_warning "Performance utilities had issues"
fi

echo ""
print_phase "Phase 5: Integration Tests"
print_status "Testing modular system integration..."
if fvm flutter run --debug --target=lib/test_modular_main.dart --device-id=flutter-tester > /dev/null 2>&1; then
    print_success "Modular system integration test passed"
else
    print_warning "Integration test had issues (expected in test environment)"
fi

echo ""
print_phase "Phase 6: Build Tests"
print_status "Testing build with all modules..."
if fvm flutter build apk --debug --target=lib/test_modular_main.dart > /dev/null 2>&1; then
    print_success "Build with all modules successful"
else
    print_error "Build with all modules failed"
    exit 1
fi

echo ""
print_phase "Phase 7: Performance Benchmarks"
print_status "Running performance benchmarks..."

# Test startup time
print_status "Measuring startup time..."
start_time=$(date +%s%N)
fvm flutter run --debug --target=lib/test_modular_main.dart --device-id=flutter-tester > /dev/null 2>&1 &
flutter_pid=$!
sleep 5
kill $flutter_pid > /dev/null 2>&1 || true
end_time=$(date +%s%N)
startup_time=$(( (end_time - start_time) / 1000000 ))
print_success "Startup time: ${startup_time}ms"

echo ""
print_phase "Phase 8: Module Count Verification"
print_status "Verifying all modules are registered..."

# Count expected modules
expected_modules=18  # Update this number when adding new modules
actual_modules=$(find lib/base/modules -name "*_module.dart" | wc -l)

if [ "$actual_modules" -eq "$expected_modules" ]; then
    print_success "All $expected_modules modules found"
else
    print_warning "Expected $expected_modules modules, found $actual_modules"
fi

echo ""
print_success "Phase 2 testing completed successfully!"
echo ""
echo "📊 Test Summary:"
echo "- Total modules: $actual_modules"
echo "- Failed modules: ${#failed_modules[@]}"
echo "- Startup time: ${startup_time}ms"
echo ""
echo "📋 Next Steps:"
echo "1. Review any failed modules and fix compilation issues"
echo "2. Test the app manually with all features"
echo "3. Monitor performance in real device testing"
echo "4. Implement lazy loading for non-critical modules"
echo ""
echo "🔧 To test specific features:"
echo "Login: flutter run lib/test_modular_main.dart --dart-define=TEST_LOGIN=true"
echo "Profile: flutter run lib/test_modular_main.dart --dart-define=TEST_PROFILE=true"
echo "eKYC: flutter run lib/test_modular_main.dart --dart-define=TEST_EKYC=true"
echo ""
echo "🚀 Ready for Phase 3: Performance Optimization!"
