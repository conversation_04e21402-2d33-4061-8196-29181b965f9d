#!/bin/bash

# Test script for EVO Modular System
# This script tests both the legacy and modular initialization systems

set -e

echo "🧪 Testing EVO Modular System Implementation"
echo "============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

# Check if flutter is available
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi

print_status "Checking Flutter version..."
flutter --version

print_status "Getting dependencies..."
flutter pub get

echo ""
print_status "Running static analysis..."
if flutter analyze; then
    print_success "Static analysis passed"
else
    print_warning "Static analysis found issues (continuing anyway)"
fi

echo ""
print_status "Running modular system unit tests..."
if flutter test test/modules/modular_system_test.dart --verbose; then
    print_success "Modular system tests passed"
else
    print_error "Modular system tests failed"
    exit 1
fi

echo ""
print_status "Testing legacy initialization (dry run)..."
if flutter test test/prepare_for_app_initiation_test.dart --verbose; then
    print_success "Legacy initialization tests passed"
else
    print_warning "Legacy initialization tests had issues"
fi

echo ""
print_status "Building with legacy system..."
if flutter build apk --debug --target=lib/flavors/main_stag.dart; then
    print_success "Legacy system build successful"
else
    print_error "Legacy system build failed"
    exit 1
fi

echo ""
print_status "Building with modular system..."
if flutter build apk --debug --target=lib/flavors/main_stag_modular.dart --dart-define=USE_MODULAR=true; then
    print_success "Modular system build successful"
else
    print_error "Modular system build failed"
    exit 1
fi

echo ""
print_status "Testing modular system with feature flag..."
if flutter run --debug --target=lib/flavors/main_stag_modular.dart --dart-define=USE_MODULAR=true --device-id=flutter-tester; then
    print_success "Modular system runs successfully"
else
    print_warning "Could not test run (no device available)"
fi

echo ""
print_success "All tests completed successfully!"
echo ""
echo "📋 Next Steps:"
echo "1. Test the app manually with: flutter run lib/flavors/main_stag_modular.dart --dart-define=USE_MODULAR=true"
echo "2. Compare performance between legacy and modular systems"
echo "3. Gradually migrate more features to modules"
echo "4. Monitor for any runtime issues"
echo ""
echo "🔧 To switch between systems:"
echo "Legacy:  flutter run lib/flavors/main_stag.dart"
echo "Modular: flutter run lib/flavors/main_stag_modular.dart --dart-define=USE_MODULAR=true"
