{"data": {"card_types": [{"back_qr": {"exist": false}, "code": "ph.national_id", "front_qr": {"exist": false}, "has_back_side": false, "name": "Philippines Cards", "orientation": "horizontal"}], "country": "ph", "settings": {"enable_compare_faces": true, "enable_convert_pdf": false, "enable_detect_id_card_tampering": false, "enable_detect_id_cards": false, "enable_digital_journey": false, "enable_encryption": false, "enable_face_authentication": true, "enable_face_registration": true, "enable_face_retrieval": true, "enable_flash_liveness": true, "enable_index_faces": true, "enable_qr_scanner": false, "enable_read_id_card_info": false, "enable_verify_face_liveness": true, "enable_verify_id_card_sanity": false, "enable_verify_identity": false, "enable_verify_nfc": false, "enable_verify_portrait_sanity": true, "enable_vkyc": false, "flashing_modes": {"flash": {"colors": " rr rr  ", "colors_android": "rr r r r", "colors_ios": " r r  r ", "colors_web": "rr r  r ", "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "frames_per_color_web": 4}, "flash_16": {"colors": "r r ", "colors_android": "r r ", "colors_ios": "r rr", "colors_web": " r r", "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "frames_per_color_web": 4}, "flash_32": {"colors": "r r r r ", "colors_android": "rr r r r", "colors_ios": " rrr rrr", "colors_web": "  rr rr ", "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "frames_per_color_web": 4}, "flash_8": {"colors": " r", "colors_android": "r ", "colors_ios": "r ", "colors_web": "r ", "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "frames_per_color_web": 4}, "flash_advanced": {"colors": "rrr rr r", "colors_android": "rr rr r ", "colors_ios": " r r r  ", "colors_web": "r rr rrr", "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "frames_per_color_web": 4}, "flash_edge": {"colors": " r", "colors_android": "r ", "colors_ios": "r ", "colors_web": " r", "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "frames_per_color_web": 4}}, "gui": {"face_auth_mode": {"authen": [{"code": "flash_16", "disable": false, "label": "Device registration"}, {"code": "flash_32", "disable": false, "label": "Pre-login"}, {"code": "flash_16", "disable": false, "label": "Manage account"}, {"code": "flash_16", "disable": false, "label": "Transfer - FTA"}, {"code": "flash_16", "disable": false, "label": "Transfer - FT3"}, {"code": "flash_16", "disable": false, "label": "Transfer - IBFT"}, {"code": "passive", "disable": false, "label": "Payment - QR"}, {"code": "passive", "disable": false, "label": "Payment - Bill"}, {"code": "flash_16", "disable": false, "label": "Payment - eWallet"}, {"code": "flash_16", "disable": false, "label": "Investment"}, {"code": "flash_16", "disable": false, "label": "Card - Debit"}, {"code": "flash_16", "disable": false, "label": "Card - Credit"}, {"code": "nfc", "disable": true, "label": "NFC"}, {"code": "light", "disable": true, "label": "Light"}, {"code": "active", "disable": true, "label": "Active"}, {"code": "passive_v2", "disable": true, "label": "Passive_v2"}, {"code": "passive", "disable": true, "label": "Passive"}, {"code": "flash_8", "disable": true, "label": "Flash 8"}, {"code": "flash_16", "disable": true, "label": "Flash 16"}, {"code": "flash_32", "disable": true, "label": "Flash 32"}], "register": [{"code": "passive", "disable": false, "label": "Registration - Low risk"}, {"code": "flash_16", "disable": false, "label": "Registration - Medium risk"}, {"code": "flash_32", "disable": false, "label": "Registration - High risk"}, {"code": "nfc", "disable": true, "label": "NFC"}, {"code": "light", "disable": true, "label": "Light"}, {"code": "passive", "disable": true, "label": "Passive"}, {"code": "passive_v2", "disable": true, "label": "Passive_v2"}, {"code": "flash_8", "disable": true, "label": "Flash 8"}, {"code": "flash_32", "disable": true, "label": "Flash 32"}, {"code": "flash_16", "disable": true, "label": "Flash 16"}, {"code": "active", "disable": true, "label": "Active"}]}, "features": [{"code": "face_authentication", "disable": false, "label": "Face authentication"}, {"code": "full", "disable": true, "label": "Full eKYC journey"}, {"code": "index_faces", "disable": true, "label": "index faces"}, {"code": "read_card_info_one_side", "disable": true, "label": "Read card info one side"}, {"code": "read_card_info_two_side", "disable": true, "label": "Read card info two side"}, {"code": "capture_card_scan_nfc", "disable": true, "label": "Capture card scan NFC"}, {"code": "liveness", "disable": true, "label": "Liveness"}, {"code": "scan_nfc", "disable": true, "label": "Scan nfc"}, {"code": "scan_qr_code", "disable": true, "label": "Scan qr code"}, {"code": "face_matching", "disable": true, "label": "Face matching"}, {"code": "ekyc_platform", "disable": true, "label": "Ekyc platform"}, {"code": "ekyc_platform_webview", "disable": true, "label": "Ekyc platform webview"}], "languages_old": [{"code": "en", "disable": false, "label": "English"}, {"code": "vi", "disable": false, "label": "Tiếng <PERSON>"}, {"code": "id", "disable": false, "label": "Bahasa Indonesia"}]}, "liveness_modes": ["flash_32", "flash_16", "flash_8"], "scan_qr": "none", "sdk_settings": {"active_liveness_settings": {"close_eye_detector": {"android_threshold": 0.342, "android_timeout": 5, "enable": true, "ios_threshold": 0.342, "ios_timeout": 5, "web_threshold": 0.342, "web_timeout": 5}, "face_tracking_setting": {"android_terminate_threshold": 0.002847, "android_warning_threshold": 0.001474, "enable": true, "ios_terminate_threshold": 0.003393, "ios_warning_threshold": 0.002176, "limit_for": "all_flow", "max_interval_ms": 2000, "max_warning_time": 5, "web_terminate_threshold": 0.0030152991993743408, "web_warning_threshold": 0.0017317430600108828}, "flow_interval_time_ms": 3000, "limit_time_liveness_check": {"enable": true, "limit_time_second": 45}, "save_encoded_frames": {"enable": true, "frames_batch_len": 20, "frames_interval_ms": 180, "quality_android": 90, "quality_ios": 80}, "terminate_if_no_face": {"enable": true, "max_invalid_frame": 5, "max_time_ms": 1000}}, "environment": "ph", "flash_liveness_settings": {"blur_close_settings": {"blurry_threshold": 0.2, "blurry_threshold_android": 0.2, "blurry_threshold_ios": 0.1999, "enable": true, "wait_for_best_image_time_ms": 200}, "blur_far_settings": {"blurry_threshold": 0.2, "blurry_threshold_android": 0.25, "blurry_threshold_ios": 0.1999, "enable": true, "wait_for_best_image_time_ms": 200}, "capture_frame_settings": {"enable": true, "enable_ios": true, "frame_quality": 0.99, "frame_quality_android": 99, "frame_quality_ios": 0.99, "frame_scale": 1, "frames_batch_length": 3, "frames_batch_length_android": 3, "frames_batch_length_ios": 3, "frames_interval_time": 10, "frames_interval_time_android": 31000, "frames_interval_time_ios": 31000}, "close_eyes_settings": {"enable": true, "enable_ios": true, "threshold_ios": 0.342, "timeout": 5, "timeout_ios": 5}, "exif_data_settings": {"enable": true, "enable_ios": true}, "face_settings": {"chin_height": 200, "chin_height_android": 200, "chin_height_ios": 100, "close_face_ratio": 0.7, "close_face_ratio_android": 0.4, "close_face_ratio_ios": 0.35, "max_far_face_ratio": 0.85, "max_far_face_ratio_android": 0.5, "max_far_face_ratio_ios": 0.67, "min_far_face_ratio": 0.3, "min_far_face_ratio_android": 0.23, "min_far_face_ratio_ios": 0.17, "mobile": {"close_face_ratio": 0.7, "max_far_face_ratio": 0.85, "min_far_face_ratio": 0.3}}, "flash_settings": {"brightness_duration_ms_android": 1500, "brightness_duration_ms_ios": 1500, "colors_length": 8, "colors_length_android": 8, "colors_length_ios": 8, "delay_between_colors": 350, "delay_between_colors_android": 400, "delay_between_colors_ios": 450, "flash_color": "rgb(255, 128, 0)", "flash_color_android": "#FF8000", "flash_color_ios": {"blue": 0, "green": 128, "red": 255}, "flash_frame_interval": 0, "flash_frame_interval_android": 10, "flash_frame_interval_ios": 10, "flash_intensity": 1, "flash_intensity_android": 1, "flash_intensity_ios": 1, "frames_per_color": 4, "frames_per_color_android": 4, "frames_per_color_ios": 4, "min_brightness": 1, "min_brightness_android": 1, "min_brightness_ios": 1, "use_face_detector_when_flashing": false}, "image_settings": {"crop_center_close": false, "crop_center_close_ios": false, "crop_center_far": false, "crop_center_far_ios": false, "image_quality": 1, "image_quality_android": 100, "image_quality_ios": 1, "valid_face_ratio": 0.6, "valid_face_ratio_android": 0.45, "valid_face_ratio_ios": 0.2}, "liveness_settings": {"enable_far_step": true, "enable_far_step_ios": true, "timeout": 30}, "mask_settings": {"background_color": "#FFFFFF", "large_scale": 0.85, "large_scale_android": 0.75, "large_scale_ios": 0.85, "mobile": {"chin_to_mask_bottom_padding": 200, "large_scale": 0.85, "oval_padding": {"bottom": 20, "left": 30, "right": 30, "top": 0}, "oval_vertical_offset": 0, "small_scale": 0.6}, "oval_horizontal_offset": 0, "oval_horizontal_offset_android": 0, "oval_horizontal_offset_ios": 0, "oval_horizontal_padding": 40, "oval_horizontal_padding_android": 40, "oval_horizontal_padding_ios": 20, "oval_padding": {"bottom": 20, "left": 30, "right": 30, "top": 0}, "oval_vertical_offset": 0, "oval_vertical_offset_android": 0, "oval_vertical_offset_ios": 0, "oval_vertical_padding": 50, "oval_vertical_padding_android": 30, "oval_vertical_padding_ios": 15, "small_scale": 0.6, "small_scale_android": 0.6, "small_scale_ios": 0.65}, "passive_liveness_v2_settings": {"blur_settings": {"blurry_threshold": 0.2, "enable": true}}, "timeout_settings": {"countdown": 10, "countdown_ios": 10, "enable": true, "enable_ios": true, "enable_max_retry_count": false, "enable_max_retry_count_ios": false, "max_retry_count": 5, "max_retry_count_ios": 5, "total": 45, "total_ios": 45}}, "id_detection_settings": {"auto_capture": {"enable": false, "show_capture_button": false, "wait_for_best_image_time_ms": 2000}, "blur_check": {"enable": true, "large_blurry_threshold": 8000, "small_blurry_threshold": 13, "threshold": 0.29}, "disable_capture_button_if_alert": true, "exif_data_settings": {"enable": true}, "flow_interval_time_ms": 3000, "glare_check": {"enable": true, "threshold": 0.001}, "id_detection": {"enable": true}, "scan_qr_settings": {"enable": false, "limit_time_second": 30}}, "license_settings": {"license_key": "Y29tLnRydXN0aW5nc29jaWFsLnRydXN0dmlzaW9uLmVreWM7O29mZmxpbmU7O2V5SmhjSEJmYVdRaU9pSmpiMjB1ZEhKMWMzUnBibWR6YjJOcFlXd3VkSEoxYzNSMmFYTnBiMjR1Wld0NVl5SXNJbU5zYVdWdWRGOXBaQ0k2SWpoa05XVTVNREptTFdNMk1ERXRORGswWlMwNU16QXdMVEkwTnpBNVpqUXlNekZsWlNJc0ltTnlaV0YwWldSZllYUWlPaUl5TURJMExURXhMVEF4VkRBek9qRTVPakV3TGpnMU1EQTBPRm9pTENKbGVIQnBjbVZrWDJGMElqb2lNakEwTUMweE1TMHlNVlF4Tnpvd01Eb3dNRm9pTENKcFpDSTZJbkV2ZDFaTFVrVmhabkF6ZGtJclNXcG5hSGt4VWxObGIzYzNMMmxUVjJ4UFZGWnJabmQxVVV4T1pETmlka2d2YlZVNVZuQk1XbE5FTUhGVVVGbEhVRmtpTENKeVpXTmxhWFpsWkY5aGRDSTZJakl3TWpRdE1URXRNREZVTURNNk5EWTZNRFl1TXpjd09UQTNORFF5V2lKOTs7TUVVQ0lRREpxVTlsYXZUZXVzZzdYMEluNmFiVEdoSVh5YTJaWDAxVlNqRXFxVUpQd1FJZ0ZsMUZ1V2UzeVVBVlo3c0h6Ymp3VVZrY1U5ZVA4ZFpObzB0Q01oUEcvdk09", "license_key_android": "Y29tLnRydXN0aW5nc29jaWFsLnR2c2RrZXhhbXBsZS5mYWNlQXV0aC5zdGFnaW5nOztvZmZsaW5lOztleUpoY0hCZmFXUWlPaUpqYjIwdWRISjFjM1JwYm1kemIyTnBZV3d1ZEhaelpHdGxlR0Z0Y0d4bExtWmhZMlZCZFhSb0xuTjBZV2RwYm1jaUxDSmpiR2xsYm5SZmFXUWlPaUpqWWpZMFpUVmpNUzA0TXpjMkxUUTFZbVF0T0dOaU9TMHlNMlkyWmpJMVltSmlOalFpTENKamNtVmhkR1ZrWDJGMElqb2lNakF5TkMweE1DMHlNVlF3T1RveU1Eb3lOUzQxTXpZeU9ETmFJaXdpWlhod2FYSmxaRjloZENJNklqSXdNamd0TVRBdE1UbFVNVGM2TURBNk1EQmFJaXdpYVdRaU9pSnlNVGRLY1ZsWFZXRm9TbTB4TkZOa1ZWWlFhbkJDUWsxeGMyUkthV3hHVDBWVWNrOXhkbVpFWTI5cEwyMUZjV3BqYXpGbmMwUlliMjh5UkVnMFFURkJJaXdpY21WalpXbDJaV1JmWVhRaU9pSXlNREkwTFRFd0xUSTRWREF4T2pVNU9qTTBMamd4TmpZM05ETTVORm9pZlE9PTs7TUVVQ0lRRENIclNhZFdRZlJ6MXJwVGdvNHp0YU9jaStXNFVKM09JYVNkQTJxTGUwRUFJZ0RzY2Z6Q2EyOU04SUdpYkNKTHJNLzBlVC8rbldXaDV6V2dIRU85czlNNjQ9"}, "license_settings_android": {"com.trustingsocial.tvsdkexample.faceAuth.staging": "Y29tLnRydXN0aW5nc29jaWFsLnR2c2RrZXhhbXBsZS5mYWNlQXV0aC5zdGFnaW5nOztvZmZsaW5lOztleUpoY0hCZmFXUWlPaUpqYjIwdWRISjFjM1JwYm1kemIyTnBZV3d1ZEhaelpHdGxlR0Z0Y0d4bExtWmhZMlZCZFhSb0xuTjBZV2RwYm1jaUxDSmpiR2xsYm5SZmFXUWlPaUpqWWpZMFpUVmpNUzA0TXpjMkxUUTFZbVF0T0dOaU9TMHlNMlkyWmpJMVltSmlOalFpTENKamNtVmhkR1ZrWDJGMElqb2lNakF5TkMweE1DMHlNVlF3T1RveU1Eb3lOUzQxTXpZeU9ETmFJaXdpWlhod2FYSmxaRjloZENJNklqSXdNamd0TVRBdE1UbFVNVGM2TURBNk1EQmFJaXdpYVdRaU9pSnlNVGRLY1ZsWFZXRm9TbTB4TkZOa1ZWWlFhbkJDUWsxeGMyUkthV3hHVDBWVWNrOXhkbVpFWTI5cEwyMUZjV3BqYXpGbmMwUlliMjh5UkVnMFFURkJJaXdpY21WalpXbDJaV1JmWVhRaU9pSXlNREkwTFRFd0xUSTRWREF4T2pVNU9qTTBMamd4TmpZM05ETTVORm9pZlE9PTs7TUVVQ0lRRENIclNhZFdRZlJ6MXJwVGdvNHp0YU9jaStXNFVKM09JYVNkQTJxTGUwRUFJZ0RzY2Z6Q2EyOU04SUdpYkNKTHJNLzBlVC8rbldXaDV6V2dIRU85czlNNjQ9", "license_key_android": "Y29tLnRydXN0aW5nc29jaWFsLnR2c2RrZXhhbXBsZS5mYWNlQXV0aC5zdGFnaW5nOztvZmZsaW5lOztleUpoY0hCZmFXUWlPaUpqYjIwdWRISjFjM1JwYm1kemIyTnBZV3d1ZEhaelpHdGxlR0Z0Y0d4bExtWmhZMlZCZFhSb0xuTjBZV2RwYm1jaUxDSmpiR2xsYm5SZmFXUWlPaUpqWWpZMFpUVmpNUzA0TXpjMkxUUTFZbVF0T0dOaU9TMHlNMlkyWmpJMVltSmlOalFpTENKamNtVmhkR1ZrWDJGMElqb2lNakF5TkMweE1DMHlNVlF3T1RveU1Eb3lOUzQxTXpZeU9ETmFJaXdpWlhod2FYSmxaRjloZENJNklqSXdNamd0TVRBdE1UbFVNVGM2TURBNk1EQmFJaXdpYVdRaU9pSnlNVGRLY1ZsWFZXRm9TbTB4TkZOa1ZWWlFhbkJDUWsxeGMyUkthV3hHVDBWVWNrOXhkbVpFWTI5cEwyMUZjV3BqYXpGbmMwUlliMjh5UkVnMFFURkJJaXdpY21WalpXbDJaV1JmWVhRaU9pSXlNREkwTFRFd0xUSTRWREF4T2pVNU9qTTBMamd4TmpZM05ETTVORm9pZlE9PTs7TUVVQ0lRRENIclNhZFdRZlJ6MXJwVGdvNHp0YU9jaStXNFVKM09JYVNkQTJxTGUwRUFJZ0RzY2Z6Q2EyOU04SUdpYkNKTHJNLzBlVC8rbldXaDV6V2dIRU85czlNNjQ9"}, "license_settings_ios": {"com.trustingsocial.trustvision.ekyc": "Y29tLnRydXN0aW5nc29jaWFsLnRydXN0dmlzaW9uLmVreWM7O29mZmxpbmU7O2V5SmhjSEJmYVdRaU9pSmpiMjB1ZEhKMWMzUnBibWR6YjJOcFlXd3VkSEoxYzNSMmFYTnBiMjR1Wld0NVl5SXNJbU5zYVdWdWRGOXBaQ0k2SWpoa05XVTVNREptTFdNMk1ERXRORGswWlMwNU16QXdMVEkwTnpBNVpqUXlNekZsWlNJc0ltTnlaV0YwWldSZllYUWlPaUl5TURJMExURXhMVEF4VkRBek9qRTVPakV3TGpnMU1EQTBPRm9pTENKbGVIQnBjbVZrWDJGMElqb2lNakEwTUMweE1TMHlNVlF4Tnpvd01Eb3dNRm9pTENKcFpDSTZJbkV2ZDFaTFVrVmhabkF6ZGtJclNXcG5hSGt4VWxObGIzYzNMMmxUVjJ4UFZGWnJabmQxVVV4T1pETmlka2d2YlZVNVZuQk1XbE5FTUhGVVVGbEhVRmtpTENKeVpXTmxhWFpsWkY5aGRDSTZJakl3TWpRdE1URXRNREZVTURNNk5EWTZNRFl1TXpjd09UQTNORFF5V2lKOTs7TUVVQ0lRREpxVTlsYXZUZXVzZzdYMEluNmFiVEdoSVh5YTJaWDAxVlNqRXFxVUpQd1FJZ0ZsMUZ1V2UzeVVBVlo3c0h6Ymp3VVZrY1U5ZVA4ZFpObzB0Q01oUEcvdk09", "license_key": "Y29tLnRydXN0aW5nc29jaWFsLnRydXN0dmlzaW9uLmVreWM7O29mZmxpbmU7O2V5SmhjSEJmYVdRaU9pSmpiMjB1ZEhKMWMzUnBibWR6YjJOcFlXd3VkSEoxYzNSMmFYTnBiMjR1Wld0NVl5SXNJbU5zYVdWdWRGOXBaQ0k2SWpoa05XVTVNREptTFdNMk1ERXRORGswWlMwNU16QXdMVEkwTnpBNVpqUXlNekZsWlNJc0ltTnlaV0YwWldSZllYUWlPaUl5TURJMExURXhMVEF4VkRBek9qRTVPakV3TGpnMU1EQTBPRm9pTENKbGVIQnBjbVZrWDJGMElqb2lNakEwTUMweE1TMHlNVlF4Tnpvd01Eb3dNRm9pTENKcFpDSTZJbkV2ZDFaTFVrVmhabkF6ZGtJclNXcG5hSGt4VWxObGIzYzNMMmxUVjJ4UFZGWnJabmQxVVV4T1pETmlka2d2YlZVNVZuQk1XbE5FTUhGVVVGbEhVRmtpTENKeVpXTmxhWFpsWkY5aGRDSTZJakl3TWpRdE1URXRNREZVTURNNk5EWTZNRFl1TXpjd09UQTNORFF5V2lKOTs7TUVVQ0lRREpxVTlsYXZUZXVzZzdYMEluNmFiVEdoSVh5YTJaWDAxVlNqRXFxVUpQd1FJZ0ZsMUZ1V2UzeVVBVlo3c0h6Ymp3VVZrY1U5ZVA4ZFpObzB0Q01oUEcvdk09"}, "liveness_settings": {"exif_data_settings": {"enable": true}, "take_one_picture_time_ms": 1000}, "sdk_adapter": {"services": {"card_ocr": true, "card_sanity": true, "card_tampering": true, "compare_faces": true, "face_liveness": true, "face_sanity": true, "index_face": true, "search_face": true}}, "server_log_settings": {"enable": true}}, "selfie_camera_options": ["front"], "selfie_enable_detect_multiple_face": true, "support_transaction": false, "utilities": {"length_video_sec": 5, "num_of_photo_taken": 3, "photo_res": "640x640", "timing_take_photo_sec": "1,2.5,4"}, "web_app_crop_face": "none", "web_ui": {"show_score": true}}}, "message": "get client settings successfully", "time": "2024-11-27T09:14:30+07:00", "verdict": "success"}