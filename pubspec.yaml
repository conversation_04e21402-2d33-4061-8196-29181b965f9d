name: evoapp
description: Evo app

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0

environment:
  sdk: ">=3.5.4 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # Flutter common package of Trusting Social Mobile team
  flutter_common_package:
    git:
      url: https://github.com/tsocial/flutter-common-package.git
      ref: release/v5.x.x

  flutter_native_splash: 2.4.3
  # due to conflict WillPopScope | PopScope with go_router latest version. we must use go_router: v12.1.3
  # for compatible.
  # issue: https://github.com/flutter/flutter/issues/140869
  go_router: 14.6.2
  jwt_decoder: 2.0.1
  local_auth:
    git:
      url: https://github.com/tsocial/ts-flutter-local-auth
      path: local_auth
      ref: 2.2.0-TS_v2

  app_settings: 5.1.1
  ts_bio_detect_changed:
    git:
      url: https://github.com/tsocial/flutter-bio-detect-change.git
      ref: v1.0.1
  flutter_jailbreak_detection: 1.10.0
  flutter_exit_app: 1.1.4
  collection: 1.18.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: 5.0.0
  bloc_test: 9.1.7
  mocktail: 1.0.4
  fake_async: 1.3.1

flutter_native_splash:
  image: assets/images/native_splash_image.png
  background_image: assets/images/native_splash_background_image.png
  android: true
  ios: true
  android_12:
    image: assets/images/native_splash_image.png
    color: "#FFFFFF"

# The following section is specific to Flutter.
flutter:

  uses-material-design: true

  assets:
    - assets/images/
    #DYNAMIC_ASSETS_START
    # To avoid security risks, this folder will not be included in the Release build.
    # Do not remove the comment DYNAMIC_ASSETS_START & DYNAMIC_ASSETS_END as it is used to identify
    # the folder in the script /.github/remove_mock_file.sh
    - assets/mock/
    #DYNAMIC_ASSETS_END

  fonts:
    - family: Gabarito
      fonts:
        - asset: assets/fonts/Gabarito-Regular.ttf
          weight: 400
        - asset: assets/fonts/Gabarito-Medium.ttf
          weight: 500
        - asset: assets/fonts/Gabarito-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Gabarito-Bold.ttf
          weight: 700
