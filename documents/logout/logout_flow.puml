@startuml

autonumber "<b>[0]"

title Logout Flow

actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

=== Scenario #1: Profile Page ==
note over user
user open Profile Page
end note

user -> app: click SignOut on AppBar
app --> user: show SignOutDialog

opt user press Cancel on Dialog
    app --> user: hide SignOutDialog
end opt

user -> app: confirm to SignOut
app -> app: hide SignOutDialog

app -> be: **POST user/signout**
be --> app: **Response**

alt #implementation status_code != 200
    app --> user: show **Toast** with error message
else #technology status_code == 200
    app -> app: clear all user data
    note over app
        * Clear notification data
        * Clear biometric authentication data
        * Clear user data in AppState
    end note

    app --> user: navigate to **LoginOnOldDeviceScreen**
        note over user
           Enter **Username** to start login flow again
        end note

end alt

=== Scenario #2: Inactive for 120s ==

note over user
    There is no user interaction for 120s
end note
    app -> user: show **InActiveDialog**
    user -> app: click **Login In Again** on Dialog

    app -> app: clear all user data
        note over app
            * Clear user data in AppState
        end note
    app --> user: navigate to **LoginOnOldDeviceScreen**
        note over user
            Enter **MPIN** to login again
        end note

@enduml