@startuml

autonumber "<b>[0]"
actor "**User**" as user
participant "**EvoApp**" as app
participant "**EvoGateWay**" as be

title OTP Verification

note over user
Open this feature from:
* **sign-in**:
    * input_phone_number_page
* **reset pin**:
    * activate_biometric / confirm_pin_screen
    * login_old_device_screen
    * re_auth_input_pin_screen
    * change_pin / current_pin_verification_screen
end note

opt user press back on app bar
    app --> user: back to previous screen
end opt

opt user press `Need Help?` on app bar
    app --> user: show support dialog
end opt

==Verify OTP==
note over user
User wait for receiving SMS
end note

app --> user: start countdown to OTP validity seconds

alt countdown not timeout
    user -> app: submit OTP
    alt verifyOtpType == signIn
        app -> be: **POST user/signin**
    else verifyOtpType == resetPin
        app -> be: **PATCH user/pin/forgot**
    end alt

    note over app, be
        header: { "X-SESSION" : string }
        body: {
            "otp": string,
        }
    end note
    app --> user: Response

    alt #LightGreen status_code == 200
        app --> user: exit flow and perform success callback
    else #Coral verdict == limit_exceeded || verdict == invalid_token || verdict == session_expired
        app --> user: prompt user to exit flow
    else #Coral other verdicts
        app --> user: display inline error
    end alt

else countdown timeout
    app --> user: enable resend OTP
    user -> app: Press resend OTP
    app --> user: go to **Resend OTP**
end opt

==Resend OTP==
alt verifyOtpType == signIn
    app -> be: **POST user/signin**
    note right
    body:{
        phone_number: string
        type: otp
    }
    end note
else verifyOtpType == resetPin
    app -> be: **PATCH user/pin/forgot**
    note right
    body:{
        phone_number: string
        type: none
    }
    end note
end alt
be --> app: Response
alt #LightGreen status_code == 200
    app --> user: go to **Verify OTP** steps
else #Coral verdict == limit_exceeded
    app --> user: prompt user to exit flow
else #Coral other verdicts
    app --> user: handle common error
end alt

@enduml