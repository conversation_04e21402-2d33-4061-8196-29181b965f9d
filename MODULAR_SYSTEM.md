# EVO Modular System Implementation

## Overview

This document describes the modular system implementation for the EVO application, built on top of the flutter-common-package modular infrastructure. The system provides better separation of concerns, easier testing, and more maintainable code structure.

## Architecture

### System Components

1. **Common Package Modules**: Pre-built modules from flutter-common-package
   - Core Module: Essential dependencies and utilities
   - Network Module: HTTP clients and networking
   - UI Module: Common UI components and themes
   - Analytics Module: Event tracking and analytics
   - Device Info Module: Device information and identification
   - eKYC Module: Electronic Know Your Customer services

2. **Host Application Modules**: Custom modules specific to EVO app
   - **Core Modules**:
     - App State Module: Application state and locale management
     - Network Config Module: HTTP client setup and interceptors
     - Auth Biometric Module: Authentication and biometric services
     - Storage Repository Module: Data storage and repository services
   - **UI Modules**:
     - Navigation Theme Module: Navigation and UI theme services
   - **Utility Modules**:
     - Privilege Validation Module: Security and validation services
     - Utility Wrapper Module: Dialog functions, wrappers, and utilities
   - **Feature Modules**:
     - Security Session Module: Session management and security detection
     - Pin Reset Module: PIN management and reset functionality

### Module Structure

```
lib/base/modules/
├── core/                    # Core infrastructure modules
│   ├── app_state_module.dart
│   ├── network_config_module.dart
│   └── auth_biometric_module.dart
├── data/                    # Data layer modules
│   └── storage_repository_module.dart
├── feature/                 # Feature-specific modules
│   ├── security_session_module.dart
│   └── pin_reset_module.dart
├── ui/                      # UI and theme modules
│   └── navigation_theme_module.dart
└── utility/                 # Utility modules
    ├── privilege_validation_module.dart
    └── utility_wrapper_module.dart
```

## Creating New Modules

### 1. Module Implementation

Create a new module by extending `FeatureModule` from flutter-common-package:

```dart
class MyCustomModule extends FeatureModule {
  MyCustomModule(super.getIt);

  @override
  String get name => 'my_custom_module';

  @override
  Set<Type> get dependencies => {CoreModule, NetworkModule};

  @override
  Future<void> register() async {
    try {
      // Register your dependencies
      registerLazySingleton<MyService>(MyServiceImpl());
      
      registerFactory<MyFactory>(
        MyFactoryImpl(
          dependency: get<MyDependency>(),
        ),
      );

    } catch (e, stackTrace) {
      logError('Failed to register my custom module dependencies', e, stackTrace);
      rethrow;
    }
  }
}
```

### 2. Module Registration

Register your module in the modular initialization:

```dart
// In prepare_for_app_initiation.dart
registerCustomModule(
  MyCustomModule(getIt), 
  source: 'evo_app',
);

await initializeSpecificModules([
  'my_custom_module',
  // ... other modules
]);
```

### 3. Module Testing

Create tests using the standardized testing framework:

```dart
void main() {
  ModuleTestFramework.testHostAppModule<MyCustomModule>(
    () => MyCustomModule(GetIt.instance),
    expectedRegistrations: [
      MyService,
      MyFactory,
    ],
    requiredDependencies: [CoreModule, NetworkModule],
  );
}
```

## Best Practices

### Module Design

1. **Single Responsibility**: Each module should handle a specific domain or feature
2. **Clear Dependencies**: Explicitly declare module dependencies
3. **Error Handling**: Always wrap registration in try-catch blocks
4. **Naming Convention**: Use `<domain>_<feature>_module` naming pattern

### Dependency Management

1. **Use Helper Methods**: Leverage `registerLazySingleton()`, `registerFactory()`, etc.
2. **Dependency Injection**: Use `get<T>()` to retrieve dependencies
3. **Avoid Circular Dependencies**: Design modules to avoid circular references
4. **Optional Dependencies**: Use `isRegistered<T>()` for optional dependencies

### Testing

1. **Comprehensive Coverage**: Test all registered types and dependencies
2. **Integration Testing**: Test module integration with common package modules
3. **Performance Testing**: Ensure modules initialize within acceptable time limits
4. **Isolation**: Each test should reset GetIt for proper isolation

## Migration Strategy

### Phase 1: Infrastructure ✅
- [x] Create module directory structure
- [x] Implement base modules using flutter-common-package infrastructure

### Phase 2: Core Modules ✅
- [x] Auth Biometric Module
- [x] Storage Repository Module
- [x] Navigation Theme Module
- [x] Privilege Validation Module

### Phase 3: Additional Modules ✅
- [x] Utility Wrapper Module
- [x] Security Session Module
- [x] Pin Reset Module

### Phase 4: Complete Migration ✅
- [x] App State Module
- [x] Network Config Module
- [x] Remove all dependencies on legacy prepare_for_app_initiation.dart

### Phase 4: Initialization System ✅
- [x] Modular initialization implementation
- [x] Feature toggle support
- [x] Conditional initialization in main entry points

### Phase 5: Testing Framework ✅
- [x] Module testing framework
- [x] Individual module tests
- [x] Integration tests

### Phase 6: Documentation ✅
- [x] Comprehensive documentation
- [x] Best practices guide
- [x] Migration strategy

## Usage

### Development Mode

Use the legacy system (default):
```bash
fvm flutter run --target=lib/flavors/main_stag.dart
```

Use the modular system:
```bash
fvm flutter run --target=lib/flavors/main_stag.dart --dart-define=USE_MODULAR=true
```

### Testing

Run module tests:
```bash
fvm flutter test test/modules/
```

Run specific module test:
```bash
fvm flutter test test/modules/auth_biometric_module_test.dart
```

### Building

Build with legacy system:
```bash
fvm flutter build apk --target=lib/flavors/main_stag.dart
```

Build with modular system:
```bash
fvm flutter build apk --target=lib/flavors/main_stag.dart --dart-define=USE_MODULAR=true
```

## Benefits

1. **Modularity**: Clear separation of concerns with well-defined module boundaries
2. **Testability**: Each module can be tested independently with proper isolation
3. **Maintainability**: Easier to maintain and update individual features
4. **Scalability**: New features can be added as separate modules
5. **Performance**: Selective module loading and optimized dependency injection
6. **Code Quality**: Enforced SOLID principles and DRY patterns
7. **Team Collaboration**: Different teams can work on different modules independently

## Troubleshooting

### Common Issues

1. **Module Not Found**: Ensure module is registered before initialization
2. **Dependency Not Available**: Check that required modules are initialized first
3. **Circular Dependencies**: Review module dependency graph
4. **Registration Errors**: Check error logs for specific registration failures

### Debugging

1. **Module Status**: Use `isModuleRegistered()` and `isModuleInitialized()`
2. **Dependency Check**: Use `getIt.isRegistered<T>()` to verify registrations
3. **Module List**: Use `getRegisteredModules()` and `getInitializedModules()`

## Future Enhancements

1. **Additional Modules**: Continue migrating remaining services to modules
2. **Lazy Loading**: Implement lazy module loading for better performance
3. **Module Marketplace**: Support for third-party modules
4. **Hot Reload**: Improve hot reload support for modular development
5. **Dependency Visualization**: Tools to visualize module dependencies

## Contributing

When adding new modules:

1. Follow the established naming conventions
2. Include comprehensive tests
3. Update this documentation
4. Ensure proper error handling
5. Add performance benchmarks
