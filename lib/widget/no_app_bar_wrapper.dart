// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../resources/resources.dart';
import '../util/screen_util.dart';

/// A wrapper for screens that do not require an app bar but share the same status bar style.
/// The [SystemUiOverlayStyle] is based on properties of [EvoAppBar].
class NoAppBarWrapper extends StatelessWidget {
  final Widget child;

  final bool resizeToAvoidBottomInset;

  const NoAppBarWrapper({
    required this.child,
    this.resizeToAvoidBottomInset = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // SnackBar needs to be wrapped in a Scaffold to work.
    return Scaffold(
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      backgroundColor: evoColors.grayBackground,
      body: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle(
          statusBarColor: evoColors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        child: SafeArea(
          child: Padding(
            padding: EdgeInsets.only(top: 16.w),
            child: child,
          ),
        ),
      ),
    );
  }
}
