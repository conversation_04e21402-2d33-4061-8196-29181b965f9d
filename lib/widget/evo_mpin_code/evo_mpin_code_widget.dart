import 'package:flutter/material.dart';

import '../../resources/resources.dart';
import '../evo_pin_text_field.dart';
import 'evo_mpin_code_config.dart';

class EvoMPINCodeWidget extends StatelessWidget {
  final TextEditingController textEditingController;

  final String title;

  final String? errorMessage;

  final bool autoFocus, autoUnFocus;
  final FocusNode? focusNode;

  final void Function(String)? onChange;
  final void Function(String)? onSubmit;
  final VoidCallback? onResetPin;

  const EvoMPINCodeWidget({
    required this.textEditingController,
    required this.title,
    this.errorMessage,
    this.autoFocus = true,
    this.autoUnFocus = false,
    this.focusNode,
    this.onSubmit,
    this.onChange,
    this.onResetPin,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        _buildTitle(),
        _buildMPINCode(),
        _buildResetMPIN(),
      ],
    );
  }

  Widget _buildTitle() {
    return Text(
      title,
      style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
    );
  }

  Widget _buildMPINCode() {
    return EvoPinTextField(
      focusNode: focusNode,
      textEditingController: textEditingController,
      autoFocus: autoFocus,
      autoUnFocus: autoUnFocus,
      pinLength: EvoMPINCodeConfig.defaultMPINCodeLength,
      onChange: onChange,
      onSubmit: onSubmit,
      isObscureText: true,
      errorMessage: errorMessage,
    );
  }

  Widget _buildResetMPIN() {
    return Center(
      child: GestureDetector(
        onTap: onResetPin,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              EvoStrings.forgotMPINQuestion,
              style: evoTextStyles.regular(TextSize.base),
            ),
            EvoDimension.space4,
            Text(
              EvoStrings.reset,
              style: evoTextStyles.regular(TextSize.base, color: evoColors.secondaryBase),
            ),
          ],
        ),
      ),
    );
  }
}
