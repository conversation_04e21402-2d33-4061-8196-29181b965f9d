// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../resources/resources.dart';
import '../util/screen_util.dart';
import 'appbar/evo_appbar.dart';

/// A wrapper for screens that do require an app bar.
/// apply default padding for screen, default color, default appbar
class AppBarWrapper extends StatelessWidget {
  final Widget child;
  final EvoAppBar? customAppbar;
  final Color? backgroundColor;
  final EdgeInsets? contentPadding;

  const AppBarWrapper({
    required this.child,
    this.customAppbar,
    this.backgroundColor,
    this.contentPadding,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // SnackBar needs to be wrapped in a Scaffold to work.
    return Scaffold(
      backgroundColor: backgroundColor ?? evoColors.grayBackground,
      appBar: customAppbar ?? EvoAppBar(),
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        child: Padding(
          padding: contentPadding ??
              EdgeInsets.only(left: 16.w, right: 16.w, bottom: EvoDimension.screenBottomPadding),
          child: child,
        ),
      ),
    );
  }
}
