import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import '../resources/resources.dart';
import '../util/screen_util.dart';

/// Displays a question followed by a tappable call-to-action text.
class QuestionCtaText extends StatelessWidget {
  final String question;
  final String cta;
  final VoidCallback? onTap;
  final Color? ctaColor;

  const QuestionCtaText({
    required this.cta,
    required this.question,
    required this.onTap,
    this.ctaColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final bool isEnabled = onTap != null;

    return RichText(
      text: TextSpan(
        style:
            evoTextStyles.regular(TextSize.base, color: evoColors.textActive),
        children: <InlineSpan>[
          TextSpan(text: question),
          WidgetSpan(child: SizedBox(width: 8.w)),
          TextSpan(
            text: cta,
            recognizer: TapGestureRecognizer()..onTap = onTap,
            style: TextStyle(color: _generateCtaColor(isEnabled)),
          ),
        ],
      ),
    );
  }

  Color _generateCtaColor(bool isEnabled) {
    if (!isEnabled) {
      return evoColors.greyScale70;
    }
    return ctaColor ?? evoColors.primary;
  }
}
