// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/widget/count_down_timer/common_count_down_timer.dart';

/// A widget that provides a one-time countdown timer with a customizable UI builder.
///
/// This widget creates a countdown timer that runs once from a specified duration
/// down to zero. It uses a builder pattern to allow complete customization of how
/// the countdown is displayed while handling the timer logic internally.
///
/// Key features:
/// - Counts down from a specified duration to zero
/// - Provides real-time updates through a builder function
/// - Executes a callback when the countdown finishes
/// - Automatically manages timer lifecycle (start/stop)
/// - Handles proper cleanup to prevent memory leaks
///
/// Common use cases:
/// - Inactivity warning dialogs with auto-logout
/// - OTP expiration timers
/// - Session timeout warnings
/// - Temporary promotional banners
///
/// Example usage:
/// ```dart
/// OnetimeCountdownBuilder(
///   durationInSec: 60,
///   onFinish: () => print('Timer finished!'),
///   builder: (seconds) => Text('$seconds seconds remaining'),
/// )
/// ```
class OnetimeCountdownBuilder extends StatefulWidget {
  /// The initial countdown duration in seconds.
  /// Must be a positive integer representing the starting countdown value.
  final int durationInSec;

  /// Optional callback function executed when the countdown reaches zero.
  /// This is typically used to trigger actions like auto-logout, navigation,
  /// or showing additional UI elements.
  final VoidCallback? onFinish;

  /// Builder function that creates the widget tree for displaying the countdown.
  ///
  /// This function is called every second with the current remaining seconds.
  /// It allows complete customization of how the countdown is presented to the user.
  ///
  /// Parameters:
  /// - `seconds`: Current remaining seconds in the countdown (decreases each second)
  ///
  /// Returns: Widget to display for the current countdown value
  final Widget Function(int seconds) builder;

  const OnetimeCountdownBuilder({
    required this.durationInSec,
    required this.builder,
    super.key,
    this.onFinish,
  });

  @override
  State<OnetimeCountdownBuilder> createState() => OnetimeCountdownBuilderState();
}

/// State class that manages the countdown timer lifecycle and updates.
///
/// This class handles the internal timer management, state updates, and
/// proper cleanup of resources. It maintains the current countdown value
/// and ensures the UI is updated every second.
@visibleForTesting
class OnetimeCountdownBuilderState extends State<OnetimeCountdownBuilder> {
  /// The internal timer instance that handles the countdown logic.
  /// Uses [CommonCountdownTimer] for consistent timer behavior across the app.
  late CommonCountdownTimer timer;

  /// Current remaining duration in seconds.
  /// This value decreases every second and triggers UI rebuilds when updated.
  /// Initialized from widget.durationInSec and maintained throughout the countdown.
  late int durationInSec;

  @override
  void initState() {
    super.initState();
    // Initialize the countdown duration from the widget parameter
    durationInSec = widget.durationInSec;
    // Start the countdown timer immediately when the widget is created
    startTimer();
  }

  @override
  void dispose() {
    // Stop the timer to prevent memory leaks and unnecessary callbacks
    // This is crucial for proper resource cleanup
    timer.stop();
    super.dispose();
  }

  /// Builds the widget using the current countdown value.
  ///
  /// This method calls the builder function provided by the widget with the
  /// current remaining seconds. The builder function is responsible for
  /// creating the actual UI representation of the countdown.
  @override
  Widget build(BuildContext context) {
    return widget.builder(durationInSec);
  }

  /// Initializes and starts the countdown timer.
  ///
  /// Creates a new [CommonCountdownTimer] instance with the specified duration
  /// and callback functions. The timer is configured to:
  /// - Count down from the initial duration to zero
  /// - Call [onTick] every second to update the UI
  /// - Call the optional [onFinish] callback when reaching zero
  ///
  /// The timer starts immediately after creation.
  @visibleForTesting
  void startTimer() {
    timer = CommonCountdownTimer(
      countDownSeconds: durationInSec,
      onTick: onTick, // Called every second with remaining time
      onFinished: () => widget.onFinish?.call(), // Called when countdown reaches zero
    );
    timer.start();
  }

  /// Handles timer tick events and updates the UI.
  ///
  /// This method is called every second by the timer with the current
  /// remaining seconds. It updates the internal state and triggers a
  /// widget rebuild to reflect the new countdown value.
  ///
  /// Parameters:
  /// - `seconds`: The current remaining seconds in the countdown
  @visibleForTesting
  void onTick(int seconds) {
    setState(() {
      durationInSec = seconds;
    });
  }
}
