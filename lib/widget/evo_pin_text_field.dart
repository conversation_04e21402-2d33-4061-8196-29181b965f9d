import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_code_field_shape.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_code_fields.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_theme.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';

import '../resources/resources.dart';
import '../util/screen_util.dart';
import 'default_obscure_widget.dart';

class EvoPinTextField extends StatelessWidget {
  static double defaultPinTextFieldSize = 40.w;
  static double defaultPinCursorHeight = 20.w;

  final void Function(String)? onChange;
  final void Function(String)? onSubmit;
  final String? errorMessage;
  final int pinLength;
  final FocusNode? focusNode;
  final TextEditingController textEditingController;

  /// true: auto focus when open screen (show keyboard)
  /// false: don't auto focus when open screen
  final bool autoFocus;

  /// true: auto hide keyboard when tap/complete input
  /// false: don't hide keyboard
  final bool autoUnFocus;

  final bool isObscureText;

  const EvoPinTextField({
    required this.pinLength,
    required this.textEditingController,
    this.onChange,
    this.onSubmit,
    this.focusNode,
    this.autoFocus = true,
    this.autoUnFocus = false,
    this.isObscureText = false,
    this.errorMessage,
    super.key,
  });

  @visibleForTesting
  bool get isError => errorMessage?.isNotEmpty == true;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Padding(
          padding: EdgeInsets.symmetric(vertical: 16.w),
          child: CommonPinCode(
            separatorBuilder: (_, int index) {
              return SizedBox.shrink();
            },
            spaceBetweenItems: 0,
            focusNode: focusNode,
            textController: textEditingController,
            autoFocus: autoFocus,
            autoUnFocus: autoUnFocus,
            pinTheme: _mPinTheme,
            animationDuration: Duration.zero,
            obscuringWidget: isObscureText ? const DefaultObscureWidget() : null,
            mainAxisAlignment: MainAxisAlignment.center,
            pinLength: pinLength,
            textStyle: evoTextStyles.regular(TextSize.base),
            showCursor: true,
            cursorColor: evoColors.textActive,
            cursorHeight: defaultPinCursorHeight,
            onChange: onChange,
            onSubmit: onSubmit,
            borderBuilder: getBorder,
            borderRadiusBuilder: getBorderRadius,
          ),
        ),
        _buildErrorMessage(),
      ],
    );
  }

  CommonPinTheme get _mPinTheme => CommonPinTheme(
        fieldHeight: defaultPinTextFieldSize,
        fieldWidth: defaultPinTextFieldSize,
        shape: CommonPinCodeFieldShape.box,
        borderWidth: 1.w,
        activeBorderWidth: 1.w,
        inactiveBorderWidth: 1.w,
        selectedBorderWidth: 2.w,
        selectedColor: evoColors.secondaryBase,
        inactiveColor: _getBorderColor(),
        activeColor: _getBorderColor(),
        selectedFillColor: _getFillColor(),
        inactiveFillColor: _getFillColor(),
        activeFillColor: _getFillColor(),
      );

  Color _getFillColor() {
    return evoColors.defaultTransparent;
  }

  Color _getBorderColor() {
    return isError ? evoColors.errorBase : evoColors.grayBorders;
  }

  Widget _buildErrorMessage() {
    final String? error = errorMessage;
    if (error == null || error.isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.only(bottom: 16.w),
      child: Text(
        error,
        textAlign: TextAlign.center,
        style: evoTextStyles.regular(
          TextSize.s,
          color: evoColors.errorBase,
        ),
      ),
    );
  }

  @visibleForTesting
  Border getBorder(
    BuildContext context,
    FieldStatus state,
    BorderSide borderSide,
    int index,
  ) {
    final BorderSide updatedBorderSide = borderSide;

    if (index == 0 || state == FieldStatus.selected) {
      return Border.fromBorderSide(updatedBorderSide);
    }

    return Border(
      right: updatedBorderSide,
      top: updatedBorderSide,
      bottom: updatedBorderSide,
    );
  }

  @visibleForTesting
  BorderRadius getBorderRadius(BuildContext context, FieldStatus status, int index) {
    final Radius radius = Radius.circular(8.w);
    if (index == 0) {
      return BorderRadius.only(
        topLeft: radius,
        bottomLeft: radius,
      );
    }
    if (index == pinLength - 1) {
      return BorderRadius.only(
        topRight: radius,
        bottomRight: radius,
      );
    }

    return BorderRadius.zero;
  }
}
