import 'package:flutter/material.dart';

import '../resources/resources.dart';
import '../util/screen_util.dart';
import 'elevated_container.dart';

class ActionButtonWidget extends StatelessWidget {
  final String icon;
  final String title;
  final VoidCallback? onPress;
  final Color? iconColor;
  final Color? trailingColor;
  final Color? titleColor;
  final Widget? trailing;

  const ActionButtonWidget({
    required this.title,
    required this.icon,
    this.iconColor,
    this.trailingColor,
    this.titleColor,
    this.onPress,
    this.trailing,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final SizedBox spacing12 = SizedBox(width: 12.w);
    return ElevatedContainer(
      onTap: onPress,
      child: Padding(
        padding: EdgeInsets.all(24.w),
        child: Row(
          children: <Widget>[
            _getLeadingIcon(),
            spacing12,
            Expanded(
              child: Text(
                title,
                style: evoTextStyles.semibold(
                  TextSize.base,
                  color: titleColor ?? evoColors.grayText,
                ),
              ),
            ),
            spacing12,
            _getTrailingWidget(),
          ],
        ),
      ),
    );
  }

  Widget _getLeadingIcon() {
    return SizedBox.square(
      dimension: 24.w,
      child: evoImageProvider.asset(
        icon,
        color: iconColor,
        fit: BoxFit.scaleDown,
      ),
    );
  }

  Widget _getTrailingWidget() {
    final double dimension = 20.w;

    if (trailing == null) {
      return evoImageProvider.asset(
        EvoImages.icArrowRight,
        color: trailingColor ?? evoColors.grayBase,
        width: dimension,
        height: dimension,
        fit: BoxFit.contain,
      );
    }

    return trailing!;
  }
}
