import 'package:flutter/material.dart';

import '../resources/resources.dart';
import '../util/screen_util.dart';

class ElevatedContainer extends StatelessWidget {
  final Widget child;
  final Color? surfaceColor;
  final VoidCallback? onTap;
  final double? width;
  final double? height;

  const ElevatedContainer({
    required this.child,
    this.surfaceColor,
    this.onTap,
    this.width,
    this.height,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(boxShadow: <BoxShadow>[
        BoxShadow(
          color: evoColors.shadowColor,
          spreadRadius: -8.w,
          blurRadius: 24.w,
          offset: Offset(0, 15.w),
        ),
      ]),
      child: Material(
          color: surfaceColor ?? evoColors.defaultWhite,
          child: InkWell(onTap: onTap, child: child)),
    );
  }
}
