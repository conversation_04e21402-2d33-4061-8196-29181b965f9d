import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/ui_model/dialog_type.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../../feature/feature_toggle.dart';
import '../../base/modules/core/app_state.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/screen_util.dart';

class EvoDialogConfirm extends StatelessWidget {
  static double dialogEdgePadding = 24.w;

  static EdgeInsets defaultHeaderPadding = EdgeInsets.only(top: dialogEdgePadding);

  static EdgeInsets defaultContentPadding = EdgeInsets.symmetric(
    horizontal: dialogEdgePadding,
  );

  static EdgeInsets defaultCTAPadding = EdgeInsets.only(
    left: dialogEdgePadding,
    right: dialogEdgePadding,
    bottom: dialogEdgePadding,
    top: 16.w,
  );

  static double defaultContentSpacing = 8.w;

  static double defaultCTASpacing = 8.w;

  static const ButtonListOrientation defaultCTAOrientation = ButtonListOrientation.verticalUp;

  static double defaultDialogHorizontalPadding = 16.w;

  final Widget? imageHeader;
  final String? title;
  final String? content;
  final String? textNegative;
  final String textPositive;
  final Widget? footer;
  final VoidCallback? onClickPositive;
  final VoidCallback? onClickNegative;
  final ButtonStyle? positiveButtonStyle;
  final ButtonStyle? negativeButtonStyle;
  final TextStyle? titleTextStyle;
  final TextStyle? contentTextStyle;
  final String dialogId;
  final bool isShowButtonClose;
  final TextAlign? titleTextAlign;
  final TextAlign? contentTextAlign;
  final ButtonListOrientation? buttonListOrientation;
  final EdgeInsets? headerPadding;
  final EdgeInsets? contentPadding;
  final EdgeInsets? ctaPadding;
  final double? contentSpacing;
  final double? ctaSpacing;
  final double? dialogHorizontalPadding;
  final Map<String, dynamic>? loggingEventOnShowMetaData;
  final Map<String, dynamic>? loggingEventMetaData;

  const EvoDialogConfirm({
    required this.textPositive,
    required this.dialogId,
    super.key,
    this.title,
    this.content,
    this.textNegative,
    this.onClickNegative,
    this.footer,
    this.onClickPositive,
    this.imageHeader,
    this.positiveButtonStyle,
    this.negativeButtonStyle,
    this.titleTextStyle,
    this.contentTextStyle,
    this.isShowButtonClose = false,
    this.titleTextAlign,
    this.contentTextAlign,
    this.buttonListOrientation,
    this.headerPadding,
    this.contentPadding,
    this.ctaPadding,
    this.contentSpacing,
    this.ctaSpacing,
    this.dialogHorizontalPadding,
    this.loggingEventOnShowMetaData,
    this.loggingEventMetaData,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.symmetric(
        vertical: 20.w,
        horizontal: dialogHorizontalPadding ?? defaultDialogHorizontalPadding,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(EvoDimension.borderRadius),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: evoColors.grayBorders, width: 1.w),
            borderRadius: BorderRadius.circular(
              EvoDimension.borderRadius,
            ),
            color: evoColors.grayBackground,
          ),
          child: CommonDialogBottomSheet(
            /// Header
            header: imageHeader,
            headerPadding: headerPadding ?? defaultHeaderPadding,

            /// Content
            // Title
            title: title,
            titleTextStyle: titleTextStyle ?? evoTextStyles.semibold(TextSize.h5),
            titleTextAlign: titleTextAlign ?? TextAlign.center,
            // Content
            content: content,
            contentTextStyle: contentTextStyle ??
                evoTextStyles.regular(
                  TextSize.base,
                  color: evoColors.grayBase,
                ),
            contentTextAlign: contentTextAlign ?? TextAlign.center,
            // Spacing
            contentPadding: contentPadding ?? defaultContentPadding,
            contentSpacing: contentSpacing ?? defaultContentSpacing,

            /// CTA
            // Positive button
            textPositive: textPositive,
            onClickPositive: onClickPositive,
            positiveButtonStyle: positiveButtonStyle ?? evoButtonStyles.primary(ButtonSize.large),
            // Negative button
            textNegative: textNegative,
            onClickNegative: onClickNegative,
            negativeButtonStyle: negativeButtonStyle ?? evoButtonStyles.secondary(ButtonSize.large),
            // Spacing
            ctaPadding: ctaPadding ?? defaultCTAPadding,
            ctaSpacing: ctaSpacing ?? defaultCTASpacing,
            buttonListOrientation: buttonListOrientation ?? defaultCTAOrientation,

            /// Footer
            footer: footer,

            /// Others
            dialogId: dialogId,
            dialogType: DialogType.dialog,
            isShowButtonClose: isShowButtonClose,
            eventTrackingScreenId: getIt<AppState>().currentScreenId.name,
            loggingEventMetaData: loggingEventMetaData,
            isEnableLoggingEvent: getIt<FeatureToggle>().enableEventTrackingFeature,
            loggingEventOnShowMetaData: loggingEventOnShowMetaData,
          ),
        ),
      ),
    );
  }
}
