import 'package:flutter_common_package/flavors/flavor_config.dart';

import 'evo_flavor_value_config.dart';
import 'factory/evo_flavor.dart';
import 'factory/evo_flavor_factory.dart';
import 'flavors_type.dart';

/// this class to simplify flavor-related operations
///
/// This class provides convenient methods to access flavor-specific values
/// without having to manually handle FlavorType conversion and factory instantiation.
class FlavorManager {
  static final EvoFlavorFactory _factory = EvoFlavorFactory();

  /// Gets the current flavor type from FlavorConfig
  static FlavorType get currentFlavorType {
    return FlavorType.fromString(FlavorConfig.instance.flavor);
  }

  /// Gets the current flavor instance
  static EvoFlavor get currentFlavor {
    return _factory.getFlavor(currentFlavorType);
  }

  /// Gets the DOP link for the current flavor
  static String getDOPLink() {
    switch (currentFlavorType) {
      case FlavorType.stag:
        return EvoFlavorValueConfig.dopLinkStag;
      case FlavorType.uat:
        return EvoFlavorValueConfig.dopLinkUat;
      case FlavorType.prod:
        // TODO: update later when goto production
        throw UnimplementedError();
    }
  }

  /// Gets the flavor values for the current flavor
  static CommonFlavorValues getFlavorValues() {
    return currentFlavor.getFlavorValue();
  }
}
