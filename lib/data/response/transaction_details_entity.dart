import 'package:flutter_common_package/data/response/base_entity.dart';

class TransactionDetailsEntity extends BaseEntity {
  final String? id;
  final double? amount;
  final String? paidTo;
  final String? paidBy;
  final DateTime? dateTime;
  final DateTime? postingDate;
  final bool? byVirtualCard;

  TransactionDetailsEntity({
    this.id,
    this.paidTo,
    this.amount,
    this.dateTime,
    this.paidBy,
    this.postingDate,
    this.byVirtualCard,
  });
}
