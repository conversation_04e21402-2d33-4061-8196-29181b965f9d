// lib/data/response/login_new_device_entity_test.dart
import 'package:flutter_common_package/data/http_client/common_http_client.dart';

import 'authorization_entity.dart';

class LoginNewDeviceEntity extends AuthorizationEntity {
  final String? challengeType;
  final String? sessionToken;

  LoginNewDeviceEntity({
    this.challengeType,
    this.sessionToken,
    super.biometricToken,
    super.accessToken,
    super.authNotiToken,
    super.deviceToken,
    super.refreshToken,
    super.userId,
  });

  LoginNewDeviceEntity.unserializable()
      : challengeType = null,
        sessionToken = null,
        super.unserializable(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  LoginNewDeviceEntity.fromBaseResponse(super.baseResponse)
      : challengeType = baseResponse.data?['challenge_type'] as String?,
        sessionToken = baseResponse.data?['session_token'] as String?,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'challenge_type': challengeType,
      'session_token': sessionToken,
    });
    return json;
  }
}
