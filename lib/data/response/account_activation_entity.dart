import 'package:flutter_common_package/data/http_client/common_http_client.dart';

import 'authorization_entity.dart';

class AccountActivationEntity extends AuthorizationEntity {
  /// Activate Card OTP m2p verdict error
  static const String verdictInvalidEntityId = 'invalid_entity_id';
  static const String verdictInvalidKitNo = 'invalid_kit_no';
  static const String verdictKitNotMappedToEntity = 'kit_not_mapped_to_entity';

  // Activation Status
  static const String verdictStatusRejected = 'rejected_applicant';
  static const String verdictStatusProcessing = 'in_progress_applicant';
  static const String verdictStatusNone = 'none_applicant';
  static const String verdictStatusExisting = 'already_sign_up';
  static const String verdictStatusCancelled = 'canceled_applicant';

  // verify email
  static const String verdictSuccessEmailExists = 'success_and_email_exists';
  static const String verdictDuplicate = 'duplicate';

  final String? challengeType;
  final String? sessionToken;
  final int? otpResendSecs;
  final int? otpValiditySecs;
  final String? email;

  AccountActivationEntity({
    this.challengeType,
    this.otpResendSecs,
    this.otpValiditySecs,
    this.sessionToken,
    this.email,
    super.biometricToken,
    super.accessToken,
    super.deviceToken,
    super.authNotiToken,
    super.refreshToken,
    super.userId,
  });

  AccountActivationEntity.unserializable()
      : challengeType = null,
        otpResendSecs = null,
        otpValiditySecs = null,
        sessionToken = null,
        email = null,
        super.unserializable(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  AccountActivationEntity.fromBaseResponse(super.baseResponse)
      : challengeType = baseResponse.data?['challenge_type'] as String?,
        sessionToken = baseResponse.data?['session_token'] as String?,
        otpResendSecs = baseResponse.data?['otp_resend_secs'] as int?,
        otpValiditySecs = baseResponse.data?['otp_validity_secs'] as int?,
        email = baseResponse.data?['email'] as String?,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'challenge_type': challengeType,
      'session_token': sessionToken,
      'otp_resend_secs': otpResendSecs,
      'otp_validity_secs': otpValiditySecs,
      'email': email,
    });
    return json;
  }
}
