// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
// coverage:ignore-file
// Testing implement in ticket https://trustingsocial1.atlassian.net/browse/ENBCC-290

import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

class LoginPreviousDeviceEntity extends BaseEntity {
  final String? accessToken;
  final String? refreshToken;
  final int? userId;

  LoginPreviousDeviceEntity({
    this.accessToken,
    this.refreshToken,
    this.userId,
  });

  LoginPreviousDeviceEntity.unserializable()
      : accessToken = null,
        refreshToken = null,
        userId = null,
        super(localExceptionCode: CommonHttpClient.INVALID_FORMAT);

  LoginPreviousDeviceEntity.fromBaseResponse(BaseResponse super.baseResponse)
      : accessToken = baseResponse.data?['access_token'] as String?,
        refreshToken = baseResponse.data?['refresh_token'] as String?,
        userId = baseResponse.data?['user_id'] as int?,
        super.fromBaseResponse();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'user_id': userId,
    });
    return json;
  }
}
