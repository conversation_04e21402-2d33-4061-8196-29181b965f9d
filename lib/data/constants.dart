// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

class HeaderKey {
  static const String sessionToken = 'X-SESSION';
  static const String deviceToken = 'x-device-token';
  static const String authorization = 'Authorization';
}

class DurationConstants {
  // refer: https://trustingsocial1.atlassian.net/wiki/spaces/PN/pages/3989766384/Expired+Session
  static const int defaultForegroundInactiveDurationInSec = 5 * 60; // 5 minutes
  static const int defaultDurationLogoutAfterInactiveInSec = 60; // 60 seconds
  static const int defaultBackgroundInactiveDurationInSec = 5 * 60; // 5 minutes
}
