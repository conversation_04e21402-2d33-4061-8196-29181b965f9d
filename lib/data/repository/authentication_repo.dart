import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import '../request/activate_account_request.dart';
import '../request/login_new_device_request.dart';
import '../request/login_previous_device_request.dart';
import '../request/reset_pin_request.dart';
import '../response/account_activation_entity.dart';
import '../response/login_new_device_entity.dart';
import '../response/login_previous_device_entity.dart';
import '../response/reset_pin_entity.dart';
import '../response/sign_in_entity.dart';

enum SignInRequestParam {
  type('type'),
  phoneNumber('phone_number'),
  otp('otp'),
  pin('pin'),
  refreshToken('refresh_token'),
  biometricToken('biometric_token');

  const SignInRequestParam(this.value);

  final String value;
}

enum TypeLogin {
  /// 1st sign in - sign in a new device
  otp('otp'),
  verifyOTP('verify_otp'),

  /// sign in on old device with mpin
  verifyPin('verify_pin'),

  /// refresh token when access token is expired
  refreshToken('refresh_token'),

  /// sign in with biometric token
  biometricToken('biometric_token');

  const TypeLogin(this.value);

  final String value;
}

enum ResetPinType {
  none('none'),
  verifyOTP('verify_otp'),
  changePin('change_pin'),
  faceAuth('face_auth'),
  unknown('unknown');

  const ResetPinType(this.value);

  final String value;

  static ResetPinType fromString(String? value) {
    switch (value) {
      case 'none':
        return ResetPinType.none;
      case 'verify_otp':
        return ResetPinType.verifyOTP;
      case 'change_pin':
        return ResetPinType.changePin;
      case 'face_auth':
        return ResetPinType.faceAuth;
      default:
        return ResetPinType.unknown;
    }
  }
}

abstract class AuthenticationRepo {
  // TODO update this function for the flow is user log in in previous device
  @Deprecated('This function will be replaced by [loginNewDevice] & [loginOldDevice]')
  Future<SignInEntity> signIn(
    TypeLogin type, {
    String? phoneNumber,
    String? otp,
    String? pin,
    String? refreshToken,
    String? biometricToken,
    String? sessionToken,
    MockConfig? mockConfig,
  });

  Future<BaseEntity?> logout({MockConfig? mockConfig});

  Future<SignInEntity> refreshToken(String? refreshToken);

  Future<ResetPinEntity> resetPin({
    required ResetPinRequest request,
    MockConfig? mockConfig,
  });

  Future<AccountActivationEntity> activateAccount({
    required ActivateAccountRequest request,
    MockConfig? mockConfig,
  });

  /// this function is used for flow login with new device
  /// references: https://trustingsocial1.atlassian.net/wiki/spaces/PN/pages/**********/Login+New+Multiple+Devices
  Future<LoginNewDeviceEntity> loginNewDevice({
    required LoginNewDeviceRequest request,
    MockConfig? mockConfig,
  });

  /// this function is used for flow login with old device
  /// references https://trustingsocial1.atlassian.net/wiki/spaces/PN/pages/**********/Sign-in+with+Tokens
  Future<LoginPreviousDeviceEntity> loginPrevDevice({
    required LoginPreviousDeviceRequest request,
    MockConfig? mockConfig,
  });
}
