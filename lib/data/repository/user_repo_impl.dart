import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/common_request_option.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../feature/biometric/base/ext_biometric_token_entity.dart';
import '../../base/modules/core/app_state.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../constants.dart';
import '../request/change_pin_request.dart';
import '../response/biometric_token_entity.dart';
import '../response/change_pin_entity.dart';
import '../response/user_entity.dart';
import 'base_repo.dart';
import 'user_repo.dart';

class UserRepoImpl extends BaseRepo implements UserRepo {
  static const String userProfile = 'user/current';
  static const String biometricToken = 'user/biometric-token';

  /// pin
  static const String changePinUrl = 'user/pin/change';

  UserRepoImpl(
    super.client,
    EvoLocalStorageHelper localStorageHelper,
    AppState appState,
  ) : super(
          localStorageHelper: localStorageHelper,
          appState: appState,
        );

  @override
  Future<UserEntity> getUserInfo({MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.get(userProfile, mockConfig: mockConfig);
    final UserEntity userEntity = commonUtilFunction.serialize(
            () => UserEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        UserEntity.unserializable();
    return userEntity;
  }

  @override
  Future<BiometricTokenEntity> getBiometricTokenByPin({String? pin, MockConfig? mockConfig}) async {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (pin != null && pin.isNotEmpty) {
      data['pin'] = pin;
    }

    final BaseResponse baseResponse =
        await client.post(biometricToken, data: data, mockConfig: mockConfig);

    final BiometricTokenEntity biometricTokenEntity = commonUtilFunction.serialize(
            () => BiometricTokenEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        BiometricTokenEntity.unserializable();

    if (biometricTokenEntity.statusCode == CommonHttpClient.SUCCESS &&
        !biometricTokenEntity.isNeedChallenge()) {
      await saveAuthenticationInfo(
        accessToken: biometricTokenEntity.accessToken,
        refreshToken: biometricTokenEntity.refreshToken,
        deviceToken: biometricTokenEntity.deviceToken,
      );
    }
    return biometricTokenEntity;
  }

  @override
  Future<ChangePinEntity> changePin({
    required ChangePinRequest request,
    MockConfig? mockConfig,
  }) async {
    Map<String, dynamic>? headers;

    if (request.sessionToken != null) {
      headers = <String, String?>{
        HeaderKey.sessionToken: request.sessionToken,
      };
    }

    final Map<String, dynamic> data = request.toJson();

    final BaseResponse response = await client.patch(
      changePinUrl,
      requestOption: CommonRequestOption(headers: headers),
      data: data,
      mockConfig: mockConfig,
    );

    return commonUtilFunction.serialize(
          () => ChangePinEntity.fromBaseResponse(response),
          originalData: response,
        ) ??
        ChangePinEntity.unserializable();
  }
}
