import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/common_request_option.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../base/modules/core/app_state.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../constants.dart';
import '../request/activate_account_request.dart';
import '../request/login_new_device_request.dart';
import '../request/login_previous_device_request.dart';
import '../request/reset_pin_request.dart';
import '../response/account_activation_entity.dart';
import '../response/authorization_entity.dart';
import '../response/login_new_device_entity.dart';
import '../response/login_previous_device_entity.dart';
import '../response/reset_pin_entity.dart';
import '../response/sign_in_entity.dart';
import 'authentication_repo.dart';
import 'base_repo.dart';

class AuthenticationRepoImpl extends BaseRepo implements AuthenticationRepo {
  final CommonHttpClient nonAuthenticationEvoHttpClient;

  AuthenticationRepoImpl({
    required CommonHttpClient evoHttpClient,
    required EvoLocalStorageHelper evoLocalStorageHelper,
    required this.nonAuthenticationEvoHttpClient,
    required AppState appState,
  }) : super(
          evoHttpClient,
          localStorageHelper: evoLocalStorageHelper,
          appState: appState,
        );

  // API urls
  @Deprecated('Using [loginNewDeviceUrl] instead.')
  static const String signInUrl = 'user/signin';

  static const String resetPinUrl = 'user/pin/forgot';
  static const String activateAccountUrl = 'user/signup';
  static const String loginNewDeviceUrl = 'user/signin';
  static const String loginOldDeviceUrl = 'user/signin/token';

  /// Logging out clears access token and refresh token from memory.
  @override
  Future<BaseEntity?> logout({MockConfig? mockConfig}) async {
    appState?.userToken = null;
    return null;
  }

  @override
  Future<SignInEntity> signIn(
    TypeLogin type, {
    String? phoneNumber,
    String? otp,
    String? pin,
    String? refreshToken,
    String? biometricToken,
    String? sessionToken,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> data = <String, dynamic>{
      SignInRequestParam.type.value: type.value,
      SignInRequestParam.phoneNumber.value: phoneNumber,
      SignInRequestParam.otp.value: otp,
      SignInRequestParam.pin.value: pin,
      SignInRequestParam.refreshToken.value: refreshToken,
      SignInRequestParam.biometricToken.value: biometricToken,
    }..removeWhere((String key, dynamic value) => value == null);

    final Map<String, dynamic> headers = await createSignInHeaders(sessionToken);

    final BaseResponse baseResponse = await client.post(
      signInUrl,
      requestOption: CommonRequestOption(headers: headers),
      data: data,
      mockConfig: mockConfig,
    );

    final SignInEntity signInEntity = commonUtilFunction.serialize(
          () => SignInEntity.fromBaseResponse(baseResponse),
          originalData: baseResponse,
        ) ??
        SignInEntity.unserializable();

    if (signInEntity.statusCode == CommonHttpClient.SUCCESS) {
      await handleSignInSucceedData(signInEntity);
    }

    return signInEntity;
  }

  Map<String, String?> _getSessionTokenHeaders(String? sessionToken) {
    return <String, String?>{HeaderKey.sessionToken: sessionToken};
  }

  Map<String, String?> _getDeviceTokenHeaders(String? deviceToken) {
    return <String, String?>{HeaderKey.deviceToken: deviceToken};
  }

  /// This function mostly like [signIn] function.
  /// The different is [refreshToken]  function will use [nonAuthenticationEvoHttpClient] to make api call, instead of [client]
  /// We accept to duplicate some code but avoid confuse between [nonAuthenticationEvoHttpClient] and [client] if using the same [signIn] function
  @override
  Future<SignInEntity> refreshToken(String? refreshToken) async {
    final Map<String, dynamic> dataMap = <String, dynamic>{
      'type': TypeLogin.refreshToken.value,
      'refresh_token': refreshToken,
    };

    final Map<String, dynamic> headers = <String, dynamic>{};

    final String? deviceToken = await localStorageHelper?.getDeviceToken();
    if (deviceToken?.isNotEmpty == true) {
      headers.addAll(_getDeviceTokenHeaders(deviceToken));
    }

    final BaseResponse baseResponse = await nonAuthenticationEvoHttpClient.post(
      signInUrl,
      requestOption: CommonRequestOption(headers: headers),
      data: dataMap,
    );

    final SignInEntity entity = commonUtilFunction.serialize(
            () => SignInEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        SignInEntity.unserializable();

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      await handleSignInSucceedData(entity);
    }

    return entity;
  }

  @override
  Future<ResetPinEntity> resetPin({
    required ResetPinRequest request,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> dataMap = request.toJson();

    final Map<String, dynamic> headers = <String, dynamic>{};

    final String? sessionToken = request.sessionToken;

    if (sessionToken?.isNotEmpty == true) {
      headers.addAll(_getSessionTokenHeaders(sessionToken));
    }

    final BaseResponse baseResponse = await client.patch(
      resetPinUrl,
      requestOption: CommonRequestOption(headers: headers),
      data: dataMap,
      mockConfig: mockConfig,
    );

    final ResetPinEntity resetPinEntity = commonUtilFunction.serialize(
            () => ResetPinEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        ResetPinEntity.unSerializable();

    return resetPinEntity;
  }

  /// Handles successful authentication by storing tokens and user information
  /// Checks if authorization tokens are present
  /// Retrieves username from [AppState] from [storeUsernameIfNeeded]
  /// Saves all authentication information
  Future<void> handleSignInSucceedData(AuthorizationEntity entity) async {
    if (entity.hasAuthorizeToken == false) {
      return;
    }

    final String? username = appState?.username;
    appState?.username = null;

    return saveAuthenticationInfo(
      accessToken: entity.accessToken,
      refreshToken: entity.refreshToken,
      biometricToken: entity.biometricToken,
      deviceToken: entity.deviceToken,
      userId: entity.userId,
      notificationAuthKey: entity.authNotiToken,
      username: username,
    );
  }

  @visibleForTesting
  Future<Map<String, dynamic>> createSignInHeaders(String? sessionToken) async {
    final Map<String, dynamic> headers = <String, dynamic>{};

    final String? deviceToken = await localStorageHelper?.getDeviceToken();
    if (deviceToken?.isNotEmpty == true) {
      headers.addAll(_getDeviceTokenHeaders(deviceToken));
    }

    if (sessionToken?.isNotEmpty == true) {
      /// Store the sessionToken in the request header for further API calls in the authentication flow.
      headers.addAll(_getSessionTokenHeaders(sessionToken));
    }
    return headers;
  }

  @override
  Future<AccountActivationEntity> activateAccount({
    required ActivateAccountRequest request,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> headers = <String, dynamic>{};

    if (request.sessionToken?.isNotEmpty == true) {
      headers.addAll(_getSessionTokenHeaders(request.sessionToken));
    }

    final BaseResponse baseResponse = await client.post(
      activateAccountUrl,
      data: request.toJson(),
      mockConfig: mockConfig,
      requestOption: CommonRequestOption(headers: headers),
    );

    final AccountActivationEntity entity = commonUtilFunction.serialize(
            () => AccountActivationEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        AccountActivationEntity.unserializable();

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      storeUsernameIfNeeded(request);
      await handleSignInSucceedData(entity);
    }

    return entity;
  }

  @override
  Future<LoginNewDeviceEntity> loginNewDevice({
    required LoginNewDeviceRequest request,
    MockConfig? mockConfig,
  }) async {
    final Map<String, dynamic> headers = <String, dynamic>{};

    if (request.sessionToken?.isNotEmpty == true) {
      headers.addAll(_getSessionTokenHeaders(request.sessionToken));
    }

    final BaseResponse baseResponse = await client.post(
      loginNewDeviceUrl,
      data: request.toJson(),
      mockConfig: mockConfig,
      requestOption: CommonRequestOption(headers: headers),
    );

    final LoginNewDeviceEntity entity = commonUtilFunction.serialize(
            () => LoginNewDeviceEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        LoginNewDeviceEntity.unserializable();

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      storeUsernameIfNeeded(request);
      await handleSignInSucceedData(entity);
    }

    return entity;
  }

  /// Storing username when successfully request
  /// Login new device request type [UserNameRequest]
  /// Account Activation request type [ActivateAccountCreateUsernameRequest]
  /// After Authenticate success, retrieve and saving username in [handleSignInSucceedData]
  @visibleForTesting
  void storeUsernameIfNeeded(dynamic request) {
    switch (request) {
      case UserNameRequest():
      case ActivateAccountCreateUsernameRequest():
        appState?.username = request.username;
        break;
      default:
        return;
    }
  }

  @override
  Future<LoginPreviousDeviceEntity> loginPrevDevice(
      {required LoginPreviousDeviceRequest request, MockConfig? mockConfig}) async {
    final BaseResponse baseResponse = await client.post(
      loginOldDeviceUrl,
      data: request.toJson(),
      mockConfig: mockConfig,
    );

    final LoginPreviousDeviceEntity entity = commonUtilFunction.serialize(
            () => LoginPreviousDeviceEntity.fromBaseResponse(baseResponse),
            originalData: baseResponse) ??
        LoginPreviousDeviceEntity.unserializable();

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      ///at current, user-id doesn't store at this flow.
      /// update later if requirement is clearly
      storeTokensInMemory(
        accessToken: entity.accessToken,
        refreshToken: entity.refreshToken,
      );
    }

    return entity;
  }
}
