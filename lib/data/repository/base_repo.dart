import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/functions.dart';

import '../../base/modules/core/app_state.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';

abstract class BaseRepo {
  @protected
  final CommonHttpClient client;

  final EvoLocalStorageHelper? localStorageHelper;
  final AppState? appState;

  BaseRepo(
    this.client, {
    this.appState,
    this.localStorageHelper,
  });

  Future<void> saveAuthenticationInfo({
    required String? accessToken,
    required String? refreshToken,
    required String? deviceToken,
    String? biometricToken,
    int? userId,
    String? notificationAuthKey,
    String? username,
  }) async {
    storeTokensInMemory(
      accessToken: accessToken,
      refreshToken: refreshToken,
    );

    await storeTokensInSecureStorage(
      deviceToken: deviceToken,
      biometricToken: biometricToken,
      username: username,
    );

    /// Setup notification & set access token to http client header
    return commonUtilFunction.handleSignInSucceedData(
      accessToken: accessToken,
      userId: userId,
      notificationAuthKey: notificationAuthKey,
    );
  }

  @visibleForTesting
  Future<void> storeTokensInSecureStorage(
      {String? deviceToken, String? biometricToken, String? username}) async {
    await localStorageHelper?.setDeviceToken(deviceToken);

    await biometricToken?.let(
      (String token) async {
        await localStorageHelper?.setBiometricToken(token);
      },
    );

    await username?.let((String username) async {
      await localStorageHelper?.setUsername(username);
    });
  }

  @visibleForTesting
  void storeTokensInMemory({
    String? accessToken,
    String? refreshToken,
  }) {
    appState?.userToken.accessToken = accessToken;
    appState?.userToken.refreshToken = refreshToken;
  }
}
