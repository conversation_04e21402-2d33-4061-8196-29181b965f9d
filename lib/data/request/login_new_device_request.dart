import 'package:flutter/foundation.dart';

/// Represents different authentication steps in the login flow for a new device
enum LoginStepType {
  /// Initial state for username input OR indicates all verification steps are completed
  none('none'),

  /// Step requiring PIN verification
  verifyPin('verify_pin'),

  /// Step requiring facial recognition authentication
  faceAuth('face_auth'),

  /// Step requiring biometric token verification (fingerprint/face/iris)
  biometricToken('biometric_token');

  final String value;

  const LoginStepType(this.value);
}

sealed class LoginNewDeviceRequest {
  abstract final LoginStepType type;
  final String? sessionToken;

  const LoginNewDeviceRequest({this.sessionToken});

  @mustCallSuper
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'type': type.value,
      'session_token': sessionToken,
    };
  }
}

class UserNameRequest extends LoginNewDeviceRequest {
  final String username;

  const UserNameRequest({required this.username}) : super();

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'user_name': username,
    });
    return json;
  }

  @override
  LoginStepType get type => LoginStepType.none;
}

class VerifyMPinRequest extends LoginNewDeviceRequest {
  final String pin;

  const VerifyMPinRequest({required this.pin, required String sessionToken})
      : super(sessionToken: sessionToken);

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();
    json.addAll(<String, dynamic>{
      'pin': pin,
    });
    return json;
  }

  @override
  LoginStepType get type => LoginStepType.verifyPin;
}

class SelfieAuthRequest extends LoginNewDeviceRequest {
  final List<String>? imageIds;
  final List<String>? videoIds;
  final String? selfieType;

  SelfieAuthRequest({
    required super.sessionToken,
    required this.imageIds,
    required this.videoIds,
    required this.selfieType,
  });

  @override
  LoginStepType get type => LoginStepType.faceAuth;

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = super.toJson();

    json.addAll(
      <String, dynamic>{
        'face_auth_request': <String, Object?>{
          'face_image_ids': imageIds,
          'video_ids': videoIds,
          'selfie_type': selfieType,
        }
      },
    );

    return json;
  }
}

class EnableBiometricRequest extends LoginNewDeviceRequest {
  final bool skip;

  EnableBiometricRequest({
    required this.skip,
    required super.sessionToken,
  });

  @override
  LoginStepType get type => LoginStepType.biometricToken;

  @override
  Map<String, dynamic> toJson() {
    return super.toJson()
      ..addAll(<String, dynamic>{
        'skip': skip,
      });
  }
}
