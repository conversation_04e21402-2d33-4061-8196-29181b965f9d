// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.
// coverage:ignore-file
// Testing implement in ticket https://trustingsocial1.atlassian.net/browse/ENBCC-290

/// Represents different authentication steps in the login flow for a previous device
enum LoginType {
  /// Step requiring PIN verification
  mPin('device_token_with_pin');

  final String value;

  const LoginType(this.value);
}

sealed class LoginPreviousDeviceRequest {
  static const String typeKey = 'type';

  Map<String, dynamic> toJson();
}

class LoginWithMPINRequest extends LoginPreviousDeviceRequest {
  static const String pinKey = 'pin';
  static const String deviceTokenKey = 'device_token';

  final String pin;
  final String deviceToken;

  LoginWithMPINRequest({required this.pin, required this.deviceToken});

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json.addAll(<String, dynamic>{
      LoginType.mPin.value: <String, dynamic>{
        pinKey: pin,
        deviceTokenKey: deviceToken,
      },
      LoginPreviousDeviceRequest.typeKey: LoginType.mPin.value,
    });
    return json;
  }
}
