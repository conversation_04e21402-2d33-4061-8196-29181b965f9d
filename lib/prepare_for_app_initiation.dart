// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/common_package/common_package.dart';

import 'base/modules/core/app_state_module.dart';
import 'base/modules/core/auth_biometric_module.dart';
import 'base/modules/core/network_config_module.dart';
import 'base/modules/data/storage_repository_module.dart';
import 'base/modules/ui/navigation_theme_module.dart';
import 'base/modules/utility/app_utilities_module.dart';
import 'base/modules/utility/privilege_validation_module.dart';
import 'base/modules/utility/utility_wrapper_module.dart';
import 'base/modules/feature/authentication_session_module.dart';
import 'base/modules/feature/pin_reset_module.dart';
import 'resources/global.dart';

/// Global GetIt instance for dependency injection
final GetIt getIt = GetIt.instance;


/// Modular initialization for the EVO application.
///
/// This method initializes the application using the modular system provided
/// by flutter-common-package. It selectively loads common package modules
/// and registers custom host application modules.
///
/// The initialization process:
/// 1. Initialize common package with selected modules
/// 2. Initialize app state
/// 3. Register custom host app modules
/// 4. Initialize custom modules
///
/// This approach provides better separation of concerns, easier testing,
/// and more maintainable code structure.
Future<void> prepareForAppInitiation() async {
  try {
    // Initialize common package with selective modules
    await initCommonPackage(
      locale: defaultLocale,
      moduleNames: <CommonPackageModuleNames>[
        CommonPackageModuleNames.core,
        CommonPackageModuleNames.network,
        CommonPackageModuleNames.ui,
        CommonPackageModuleNames.utility,
        CommonPackageModuleNames.analytics,
        CommonPackageModuleNames.deviceInfo,
        CommonPackageModuleNames.ekyc,
        CommonPackageModuleNames.dataCollection,
        CommonPackageModuleNames.notification,
      ],
    );

    // Register and initialize all custom host app modules at once using new batch method
    await registerAndInitializeCustomModules(<FeatureModule>[
      // Core modules (order matters for dependencies)
      AppStateModule(getIt),
      NetworkConfigModule(getIt),
      AuthBiometricModule(getIt),
      StorageRepositoryModule(getIt),

      // UI modules
      NavigationThemeModule(getIt),

      // Utility modules
      AppUtilitiesModule(getIt),
      PrivilegeValidationModule(getIt),
      UtilityWrapperModule(getIt),

      // Feature modules
      AuthenticationSessionModule(getIt),
      PinResetModule(getIt),
    ], source: 'evo_app');

  } catch (e) {
    'prepareForAppInitiationModular'.commonLog(
        'Error during modular initialization: $e');
    rethrow;
  }
}
