import 'dart:ui';

import 'package:flutter/foundation.dart';

//currency code
const String defaultCurrencySymbol = '₱';
const String defaultCurrencyLocale = 'fil_PH';

///User phone number format, just show last 3 char
const int maxLengthPhoneNumber = 10;
const String defaultPrefixNationalCode = '63';

/// Linked card size
const double linkedCardImagePadding = 20;

/// Default locale
const Locale defaultLocale = Locale('en');

/// String dateString = '2022-11-02T15:04:05Z'; format to Nov 02, 2022
const String defaultDateTimeFormat = 'MMM dd, yyyy';

class WebsiteUrl {
  /// Terms & conditions website
  static const String evoTermsAndConditionsUrl =
      'https://www.goevo.vn/dieu-khoan-va-dieu-kien-evo-app';
  static const String evoFaqUrl = 'https://www.goevo.vn/cau-hoi-thuong-gap-evo-app';
  static const String evoAboutUsUrl = 'https://www.goevo.vn/ve-chung-toi';

  /// TODO: hoang-nguyen2 update url
  static const String evoPolicyUrl = 'https://www.goevo.vn/ve-chung-toi';
  static const String evoContactUrl = 'https://www.goevo.vn/ve-chung-toi';

  /// The URL define here :
  /// https://trustingsocial1.atlassian.net/wiki/spaces/EMA/pages/3335356468/Card+Status+Tracking+-+View+card+activation+status
  static const String evoCardActivationTutorialUrl =
      'https://www.goevo.vn/app/huong-dan-kich-hoat-the';
}

class ContactInfo {
  static const String contactSupportEmail = '<EMAIL>';
  static const String supportNumber = '+6388182345';
}

//use to measure app start time, logged to Firebase Performance
enum AppStartPhase {
  prepareForAppInitiation,
  splashScreenInit;

  String toFPMetricName() {
    switch (this) {
      case AppStartPhase.prepareForAppInitiation:
        return 'prepare_for_app_initiation';
      case AppStartPhase.splashScreenInit:
        return 'splash_screen_init';
    }
  }
}

enum Screen {
  splashScreen(splashScreenName),
  introductionScreen(introductionScreenName),
  welcomeScreen(welcomeScreenName),
  mainScreen(mainScreenName),
  verifyUsernameScreen(verifyUsernameScreenName),
  verifyOtpScreen(verifyOtpScreenName),
  errorScreen(errorScreenName),
  createPinScreen(createPinScreenName),
  activationStatusScreen(activationStatusScreenName),

  /// this is not a real screen, just a alias to handle the redirect action to
  /// either [verifyUsernameScreenName] or [previousLogInScreenName]
  ///
  /// Action type [EvoActionModel.open_app_screen] with [EvoActionModel.Agrs.screenName] equals to [loginScreen.name]
  /// will redirect the user to:
  /// 1. [verifyUsernameScreenName] if the user has never logged in since installing the app.
  /// 2. [previousLogInScreen] if the user has already logged in on the device.
  loginScreen(loginScreenName),
  previousLogInScreen(previousLogInScreenName),

  /// Login new device
  newDeviceVerifyMPinScreen(newDeviceVerifyMPinScreenName),

  /// Keep
  profileScreen(profileScreenName),
  activateBiometricScreen(activateBiometricScreenName),
  activateBiometricBlockedScreen(activateBiometricBlockedScreenName),


  /// Pages on Home screen
  payCardPage(payCardPageName),
  usagePage(usagePageName),
  cardsPage(cardsPageName),
  homePage(homePageName),

  /// change mpin
  currentPinVerificationScreen(currentPinVerificationScreenName),
  createNewPinScreen(createNewPinScreenName),
  confirmNewPinScreen(confirmNewPinScreenName),
  newPinSuccessScreen(newPinSuccessScreenName),

  /// biometric
  biometricEnabledSuccessScreen(biometricEnabledSuccessScreenName),

  /// Pages related to Account Activation
  mobileNumberCheckScreen(mobileNumberCheckScreenName),
  faceCaptureCheckScreen(faceCaptureCheckScreenName),
  createUsernameScreen(createUsernameScreenName),
  selfieVerificationScreen(selfieVerificationScreenName),
  activateVirtualCardScreen(activateVirtualCardScreenName),
  commonErrorScreen(commonErrorScreenName),
  virtualCardActivatedScreen(virtualCardActivatedScreenName),
  activateCardSuccessScreen(activateCardSuccessScreenName),
  selfieRetryScreen(selfieRetryScreenName),
  selfieLockedScreen(selfieLockedScreenName),
  selfieSuccessScreen(selfieSuccessScreenName),
  activateBiometricSuccessScreen(activateBiometricSuccessScreenName),
  verifyEmailScreen(verifyEmailScreenName),
  inputEmailScreen(inputEmailScreenName),

  /// Forgot MPIN
  selectSecurityMethodScreen(selectSecurityMethodScreenName),

  /// page related to verify pin before executing privilege action
  /// such as show card information, unfreeze card
  verifyPinPrivilegeActionScreen(verifyPinPrivilegeActionScreenName),
  last4DigitsCheckScreen(last4DigitsCheckScreenName),
  transactionDetailsScreen(transactionDetailsScreenName);

  /// DO NOT change the value of these fields without confirm with backend
  /// Screens CAN be redirected.
  @visibleForTesting
  static const String introductionScreenName = 'introduction_screen';

  @visibleForTesting
  static const String welcomeScreenName = 'welcome_screen';

  @visibleForTesting
  static const String payCardPageName = 'pay_card_page';

  @visibleForTesting
  static const String usagePageName = 'usage_page';

  @visibleForTesting
  static const String cardsPageName = 'card_page';

  @visibleForTesting
  static const String homePageName = 'home_page';

  @visibleForTesting
  static const String profileScreenName = 'profile_screen';

  @visibleForTesting
  static const String loginScreenName = 'login_screen';

  /// Screens CANNOT be redirected.
  @visibleForTesting
  static const String mainScreenName = 'main_screen';

  @visibleForTesting
  static const String splashScreenName = 'splash_screen';

  @visibleForTesting
  static const String verifyUsernameScreenName = 'verify_username_screen';

  @visibleForTesting
  static const String previousLogInScreenName = 'previous_login_screen';

  @visibleForTesting
  static const String newDeviceVerifyMPinScreenName = 'new_device_verify_mpin_screen';

  @visibleForTesting
  static const String createEvoCardScreenName = 'create_evo_card_screen';

  @visibleForTesting
  static const String verifyOtpScreenName = 'verify_otp_screen';

  @visibleForTesting
  static const String errorScreenName = 'error_screen';

  @visibleForTesting
  static const String createPinScreenName = 'create_pin_screen';

  @visibleForTesting
  static const String activateBiometricScreenName = 'activate_biometric_screen';

  @visibleForTesting
  static const String activateBiometricBlockedScreenName = 'activate_biometric_blocked_screen';

  @visibleForTesting
  static const String profileWebViewScreenName = 'profile_webView_screen';

  @visibleForTesting
  static const String normalConfirmPaymentScreenName = 'normal_confirm_payment_screen';

  @visibleForTesting
  static const String nonUserHomeScreenName = 'non_user_home_screen';

  @visibleForTesting
  static const String currentPinVerificationScreenName = 'current_pin_verification_screen';

  @visibleForTesting
  static const String createNewPinScreenName = 'create_new_pin_screen';

  @visibleForTesting
  static const String confirmNewPinScreenName = 'confirm_new_pin_screen';

  @visibleForTesting
  static const String newPinSuccessScreenName = 'new_pin_success_screen_name';

  @visibleForTesting
  static const String biometricEnabledSuccessScreenName = 'biometric_enabled_success_screen';

  @visibleForTesting
  static const String mobileNumberCheckScreenName = 'mobile_number_check_screen';

  @visibleForTesting
  static const String faceCaptureCheckScreenName = 'face_capture_check_screen';

  @visibleForTesting
  static const String createUsernameScreenName = 'create_username_screen';

  @visibleForTesting
  static const String selfieVerificationScreenName = 'selfie_verification_screen';

  @visibleForTesting
  static const String activateVirtualCardScreenName = 'activate_virtual_card_screen';

  @visibleForTesting
  static const String commonErrorScreenName = 'common_error_screen';

  @visibleForTesting
  static const String virtualCardActivatedScreenName = 'virtual_card_activated_screen';

  @visibleForTesting
  static const String activateCardSuccessScreenName = 'activate_card_success_screen';

  @visibleForTesting
  static const String selfieRetryScreenName = 'selfie_retry_screen';

  @visibleForTesting
  static const String selfieLockedScreenName = 'selfie_locked_screen';

  @visibleForTesting
  static const String selfieSuccessScreenName = 'selfie_success_screen';

  @visibleForTesting
  static const String activateBiometricSuccessScreenName = 'activate_biometric_success_screen';

  @visibleForTesting
  static const String verifyEmailScreenName = 'verify_email_screen';

  @visibleForTesting
  static const String inputEmailScreenName = 'input_email_screen';

  @visibleForTesting
  static const String verifyPinPrivilegeActionScreenName = 'verify_pin_privilege_action_screen';

  @visibleForTesting
  static const String last4DigitsCheckScreenName = 'last_4_digits_check_screen';

  /// Widgets NOT a screen
  @visibleForTesting
  static const String announcementListRewardTabName = 'announcement_list_reward_tab';

  @visibleForTesting
  static const String transactionHistoryListForAuthorizedUserWidgetName =
      'transaction_history_list_for_authorized_user_widget';

  @visibleForTesting
  static const String transactionDetailsScreenName = 'transaction_details_screen';

  @visibleForTesting
  static const String activationStatusScreenName = 'activation_status_screen';

  @visibleForTesting
  static const String selectSecurityMethodScreenName = 'select_security_method_screen';

  static Screen byValue(String value) {
    switch (value) {
      case introductionScreenName:
        return introductionScreen;
      case splashScreenName:
        return splashScreen;
      case welcomeScreenName:
        return welcomeScreen;
      case mainScreenName:
        return mainScreen;
      case verifyUsernameScreenName:
        return verifyUsernameScreen;
      case verifyOtpScreenName:
        return verifyOtpScreen;
      case profileScreenName:
        return profileScreen;
      case errorScreenName:
        return errorScreen;
      case createPinScreenName:
        return createPinScreen;
      case loginScreenName:
        return loginScreen;
      case previousLogInScreenName:
        return previousLogInScreen;
      case newDeviceVerifyMPinScreenName:
        return newDeviceVerifyMPinScreen;
      case activateBiometricScreenName:
        return activateBiometricScreen;
      case activateBiometricBlockedScreenName:
        return activateBiometricBlockedScreen;
      case currentPinVerificationScreenName:
        return currentPinVerificationScreen;
      case createNewPinScreenName:
        return createNewPinScreen;
      case confirmNewPinScreenName:
        return confirmNewPinScreen;
      case newPinSuccessScreenName:
        return newPinSuccessScreen;
      case payCardPageName:
        return payCardPage;
      case usagePageName:
        return usagePage;
      case cardsPageName:
        return cardsPage;
      case homePageName:
        return homePage;
      case biometricEnabledSuccessScreenName:
        return biometricEnabledSuccessScreen;
      case mobileNumberCheckScreenName:
        return mobileNumberCheckScreen;
      case createUsernameScreenName:
        return createUsernameScreen;
      case faceCaptureCheckScreenName:
        return faceCaptureCheckScreen;
      case selfieVerificationScreenName:
        return selfieVerificationScreen;
      case activateVirtualCardScreenName:
        return activateVirtualCardScreen;
      case commonErrorScreenName:
        return commonErrorScreen;
      case virtualCardActivatedScreenName:
        return virtualCardActivatedScreen;
      case activateCardSuccessScreenName:
        return activateCardSuccessScreen;
      case verifyPinPrivilegeActionScreenName:
        return verifyPinPrivilegeActionScreen;
      case selfieRetryScreenName:
        return selfieRetryScreen;
      case selfieLockedScreenName:
        return selfieLockedScreen;
      case selfieSuccessScreenName:
        return selfieSuccessScreen;
      case activateBiometricSuccessScreenName:
        return activateBiometricSuccessScreen;
      case verifyEmailScreenName:
        return verifyEmailScreen;
      case inputEmailScreenName:
        return inputEmailScreen;
      case last4DigitsCheckScreenName:
        return last4DigitsCheckScreen;
      case transactionDetailsScreenName:
        return transactionDetailsScreen;
      case activationStatusScreenName:
        return activationStatusScreen;
      case selectSecurityMethodScreenName:
        return selectSecurityMethodScreen;
      default:
        return mainScreen;
    }
  }

  static Screen byRouteName(String routeName) {
    return Screen.byValue(routeName.replaceAll('/', ''));
  }

  const Screen(this.name);

  final String name;

  String get routeName => '/$name';
}
