import 'package:flutter/material.dart';

abstract final class EvoImages {
  @visibleForTesting
  static const String png = '.png';
  @visibleForTesting
  static const String svg = '.svg';

  @visibleForTesting
  static const String assetPath = 'assets/images/';

  //PNG
  static const String bgAppUpdate = '${assetPath}bg_app_update$png';
  static const String imgSplashBackgroundImage = '${assetPath}img_splash_background_image$png';

  //SVG
  static const String icDefaultAvatar = '${assetPath}ic_default_avatar$svg';
  static const String imgBrandName = '${assetPath}img_brand_name$svg';
  static const String icBottomBarHome = '${assetPath}ic_bottom_bar_home$svg';
  static const String icBottomBarCards = '${assetPath}ic_bottom_bar_cards$svg';
  static const String icBanknotes = '${assetPath}ic_banknotes$svg';
  static const String icArrowsRightLeft = '${assetPath}ic_arrows_right_left$svg';
  static const String icBottomBarProfile = '${assetPath}ic_bottom_bar_profile$svg';
  static const String icArrowRight = '${assetPath}ic_arrow_right$svg';
  static const String icMpin = '${assetPath}ic_mpin$svg';

  static const String icArrowBack = '${assetPath}ic_arrow_back$svg';
  static const String icArrowForward = '${assetPath}ic_arrow_forward$svg';

  static const String icEyeVisibilityOff = '${assetPath}ic_eye_visibility_off$svg';
  static const String icEyeVisibilityOn = '${assetPath}ic_eye_visibility_on$svg';

  static const String icClear = '${assetPath}ic_clear$svg';
  static const String icChevronDoubleUp = '${assetPath}ic_chevron_double_up$svg';
  static const String icChevronDoubleDown = '${assetPath}ic_chevron_double_down$svg';
  static const String icCalendarDays = '${assetPath}ic_calendar_days$svg';

  /// Snackbar icon
  static const String icSnackBarSuccess = '${assetPath}ic_snack_bar_success$svg';
  static const String icSnackBarError = '${assetPath}ic_snack_bar_error$svg';
  static const String icSnackBarWarning = '${assetPath}ic_snack_bar_warning$svg';
  static const String icSnackBarNeutral = '${assetPath}ic_snack_bar_neutral$svg';

  static const String icErrorWebView = '${assetPath}ic_error_web_view$svg';
  static const String icFaceId = '${assetPath}ic_face_id$svg';
  static const String icFingerId = '${assetPath}ic_finger_id$svg';
  static const String icFaceFingerId = '${assetPath}ic_face_finger_id$svg';
  static const String icSettingFaceFingerId = '${assetPath}ic_setting_face_finger$svg';
  static const String icSettingFaceId = '${assetPath}ic_setting_face$svg';
  static const String icSettingFingerId = '${assetPath}ic_setting_finger$svg';

  static const String icNotify = '${assetPath}ic_notify$svg';

  static const String icError = '${assetPath}ic_error$svg';

  static const String icSwitchAccount = '${assetPath}ic_switch_account$svg';
  static const String icBiometrics = '${assetPath}ic_biometrics$svg';

  static const String icErrorTextField = '${assetPath}ic_error_text_field$svg';

  /// Icons for banner widget
  static const String icSuccessBanner = '${assetPath}ic_success_banner$svg';
  static const String icErrorBanner = '${assetPath}ic_error_banner$svg';
  static const String icWarningBanner = '${assetPath}ic_warning_banner$svg';
  static const String icInfoBanner = '${assetPath}ic_info_banner$svg';

  static const String icMail = '${assetPath}ic_mail$svg';
  static const String icCloseGrey = '${assetPath}ic_close_grey$svg';

  /// Enable Biometric
  static const String imgEnableBiometric = '${assetPath}img_enable_biometric$svg';

  /// Alert icons
  static const String icAlertError = '${assetPath}ic_alert_error$svg';
  static const String icAlertUnsuccessful = '${assetPath}ic_alert_unsuccessful$svg';
  static const String icAlertWarning = '${assetPath}ic_alert_warning$svg';

  /// Login on new device
  static const String imgLoginOnNewDevice = '${assetPath}img_login_on_new_device$svg';

  /// Welcome screen
  static const String imgWelcome = '${assetPath}img_welcome$png';

  /// Introduction screen
  static const String imgIntroduction1 = '${assetPath}img_introduction_1$png';
  static const String imgIntroduction2 = '${assetPath}img_introduction_2$png';
  static const String imgIntroduction3 = '${assetPath}img_introduction_3$png';

  /// Change MPIN
  static const String icNewPinSuccess = '${assetPath}new_pin_success$svg';

  /// Profile
  static const String icProfileBiometric = '${assetPath}ic_profile_biometric$svg';
  static const String icCard = '${assetPath}ic_card$svg';
  static const String icQuestionMarkCircle = '${assetPath}ic_question_mark_circle$svg';
  static const String icProfileMail = '${assetPath}ic_profile_mail$svg';
  static const String icEvoSm = '${assetPath}ic_evo_sm$svg';
  static const String icDocumentText = '${assetPath}ic_document_text$svg';
  static const String icPolicy = '${assetPath}ic_policy$svg';

  /// Card page
  static const String frameVirtualCard = '${assetPath}frame_virtual_card$svg';
  static const String frameVirtualCardDetails = '${assetPath}frame_virtual_card_details$svg';
  static const String framePhysicalCard = '${assetPath}frame_physical_card$svg';
  static const String framePhysicalCardDetails = '${assetPath}frame_physical_card_details$svg';
  static const String framePhysicalCardInactive = '${assetPath}physical_card_inactive$svg';
  static const String frameVirtualCardInactive = '${assetPath}virtual_card_inactive$svg';
  static const String icInbox = '${assetPath}ic_inbox$svg';
  static const String icCopy = '${assetPath}ic_copy$svg';
  static const String icChatBubble = '${assetPath}ic_chat_bubble$svg';

  /// Credit limit
  static const String icInfoFilled = '${assetPath}ic_info_filled$svg';

  /// Activate Virtual Card
  static const String imgActivateVirtualCard = '${assetPath}img_activate_virtual_card$png';

  /// Virtual Card Activated
  static const String virtualCardActivated = '${assetPath}frame_card_activated$svg';

  /// Welcome Onboard Bottom Sheet
  static const String imgWelcomeOnboardDialog = '${assetPath}img_welcome_onboard_dialog$png';

  // Activate Card Success
  static const String imgActivateVirtualCardSuccess =
      '${assetPath}img_activate_virtual_card_success$svg';
  static const String imgActivatePhysicalCardSuccess =
      '${assetPath}img_activate_physical_card_success$svg';

  static const String imgFaceCaptureCheck = '${assetPath}img_face_capture_check$png';

  /// Activation Status Icons
  static const String imgActivationStatusExisting = '${assetPath}activation_status_existing$svg';
  static const String imgActivationStatusNone = '${assetPath}activation_status_none$svg';
  static const String imgActivationStatusReject = '${assetPath}activation_status_reject$svg';
  static const String imgActivationStatusProcessing =
      '${assetPath}activation_status_processing$svg';
  static const String imgActivationStatusCancelled = imgActivationStatusNone;
}
