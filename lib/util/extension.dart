import 'package:flutter_common_package/util/utils.dart';

import '../resources/global.dart';
import 'functions.dart';

extension Format on DateTime {
  /// The campaign which start in the next x days (x is [configDisplayCampaignInDays])
  /// Ex: A campaign was scheduled to run on 06/02, with [configDisplayCampaignInDays] = 5 days
  /// on 01/02 the app will shown campaign with status “Bắt đầu vào 06/02”

  String toStringFormatDate({String format = defaultDateTimeFormat}) {
    return toStringFormat(format);
  }

  /// returns a new `DateTime` object representing the start of the day
  DateTime startOfDay() {
    return DateTime(year, month, day);
  }
}

extension FormatPhoneNumber on String? {
  String addPrefixCountryCode() {
    final String? originalValue = this?.trim();
    if (originalValue == null || originalValue.isEmpty) {
      return '';
    }

    return defaultPrefixNationalCode + originalValue;
  }
}

extension StringEx on String {
  String getLastCharacters(int count) {
    if (length <= count) {
      return this;
    }

    final int lastIndex = length;
    return substring(lastIndex - count, lastIndex);
  }

  /// Use for sending email: https://stackoverflow.com/a/72812644/10262450
  Uri uriForSendMail({String? subject, String? body}) {
    return Uri(
      scheme: 'mailto',
      path: this,
      query: evoUtilFunction.encodeQueryParameters(
        <String, String>{'subject': subject ?? '', 'body': body ?? ''},
      ),
    );
  }
}

extension MapConversion on Map<String, dynamic>? {
  Map<String, Object> toNonNullable() {
    if (this == null) {
      return <String, Object>{};
    }
    return this!.map(
      (String key, Object? value) => MapEntry<String, Object>(key, value ?? ''),
    );
  }
}

extension NumberEx on num {
  /// simple workaround
  /// can be replaced by i18n later https://docs.flutter.dev/ui/accessibility-and-internationalization/internationalization#placeholders-plurals-and-selects
  String pluralize({
    required String pluralForm,
    required String singularForm,
  }) {
    return '$this ${this == 1 ? singularForm : pluralForm}';
  }
}
