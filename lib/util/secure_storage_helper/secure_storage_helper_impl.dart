import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';

import 'secure_storage_helper.dart';

class EvoSecureStorageHelperImpl extends CommonSecureStorageHelperImpl
    implements EvoLocalStorageHelper, CommonLocalStorageHelper {
  /// Note: When you add a new key
  /// Please call delete it to the [deleteAllSecureStorageData] method
  static const String selectedLanguageCodeKey = 'selected_language_code';
  static const String latestVersionIgnore = 'latest_version_ignore';
  static const String deviceId = 'device_id';
  static const String usernameKey = 'username';
  static const String userFullNameKey = 'user_full_name';
  static const String biometricTokenKey = 'biometric_token';
  static const String deviceTokenKey = 'device_token';

  /// The [enableBiometricAuthenticatorKey] key is used to display biometric status on the profile screen
  static const String enableBiometricAuthenticatorKey = 'enable_biometric_authenticator';
  static const String timeShowBiometricActiveKey = 'time_show_biometric_active';
  static const String isNewDeviceKey = 'new_device';

  ///
  static const String isGrantedBiometricPermissionKey = 'is_granted_biometric_permission';

  EvoSecureStorageHelperImpl({required super.secureStorage});

  @override
  Future<Locale> setLocale(String languageCode) async {
    await write(key: selectedLanguageCodeKey, value: languageCode);
    return _locale(languageCode);
  }

  @override
  Future<Locale> getLocale() async {
    final String languageCode = await read(key: selectedLanguageCodeKey) ?? 'en';
    return _locale(languageCode);
  }

  Locale _locale(String? languageCode) {
    return languageCode != null && languageCode.isNotEmpty
        ? Locale(languageCode, '')
        : const Locale('vi', '');
  }

  @override
  Future<String?> getLatestVersionIgnore() async {
    return read(key: latestVersionIgnore);
  }

  @override
  Future<void> setLatestVersionIgnore(String? latestVersion) async {
    await write(key: latestVersionIgnore, value: latestVersion);
  }

  @override
  Future<String?> getDeviceId() async {
    return await read(key: deviceId);
  }

  @override
  Future<void> setDeviceId(String? newDeviceId) async {
    await write(key: deviceId, value: newDeviceId);
  }

  @override
  Future<String?> getUsername() async {
    return await read(key: usernameKey);
  }

  @override
  Future<void> setUsername(String? username) async {
    await write(key: usernameKey, value: username);
  }

  @override
  Future<String?> getUserFullName() async {
    return await read(key: userFullNameKey);
  }

  @override
  Future<void> setUserFullName(String? userFullName) async {
    await write(key: userFullNameKey, value: userFullName);
  }

  @override
  Future<String?> getBiometricToken() {
    return read(key: biometricTokenKey);
  }

  @override
  Future<void> setBiometricToken(String? biometricToken) async {
    await write(key: biometricTokenKey, value: biometricToken);
  }

  @override
  Future<void> setBiometricAuthenticator(bool enable) async {
    await write(key: enableBiometricAuthenticatorKey, value: enable.toString());
  }

  @override
  Future<bool> isEnableBiometricAuthenticator() async {
    final String? result = await read(key: enableBiometricAuthenticatorKey);
    return result?.toBool() ?? false;
  }

  @override
  Future<String?> getTimeShowBiometric() {
    return read(key: timeShowBiometricActiveKey);
  }

  @override
  Future<void> saveTimeShowBiometric(String lastTime) async {
    await write(key: timeShowBiometricActiveKey, value: lastTime);
  }

  @override
  Future<String?> getDeviceToken() async {
    return await read(key: deviceTokenKey);
  }

  @override
  Future<void> setDeviceToken(String? deviceToken) async {
    await write(key: deviceTokenKey, value: deviceToken);
  }

  @override
  Future<void> setNewDevice(bool enable) async {
    await write(key: isNewDeviceKey, value: enable.toString());
  }

  @override
  Future<bool> isNewDevice() async {
    final String? result = await read(key: isNewDeviceKey);
    return result?.toBool() ?? true;
  }

  @override
  Future<void> clearAllUserData() async {
    await delete(key: usernameKey);
    await delete(key: userFullNameKey);
    await delete(key: biometricTokenKey);
    await delete(key: enableBiometricAuthenticatorKey);
    await delete(key: timeShowBiometricActiveKey);
    await delete(key: deviceTokenKey);
    await delete(key: isNewDeviceKey);
  }

  @override
  Future<void> deleteAllSecureStorageData() async {
    await clearAllUserData();

    await delete(key: selectedLanguageCodeKey);
    await delete(key: latestVersionIgnore);
  }

  @override
  Future<bool> didAllowBiometricPermission() async {
    final String? result = await read(key: isGrantedBiometricPermissionKey);
    return result?.toBool() == true;
  }

  @override
  Future<void> setDidAllowBiometricPermission() async {
    await write(key: isGrantedBiometricPermissionKey, value: true.toString());
  }
}
