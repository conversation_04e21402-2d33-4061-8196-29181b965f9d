import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/feature/server_logging/firebase_analytics.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/global.dart';
import 'package:go_router/go_router.dart';

import '../../feature/account_activation/activate_virtual_card/activate_virtual_card_screen.dart';
import '../../feature/account_activation/activation_status/activation_status_screen.dart';
import '../../feature/account_activation/create_username/create_username_screen.dart';
import '../../feature/account_activation/mobile_number_check/mobile_number_check_screen.dart';
import '../../feature/account_activation/verify_email/input_email_screen.dart';
import '../../feature/account_activation/verify_email/verify_email_screen.dart';
import '../../feature/account_activation/virtual_card_activated/virtual_card_activated_screen.dart';
import '../../feature/biometric/activate_biometric/activate_biometric_blocked_screen.dart';
import '../../feature/biometric/activate_biometric/activate_biometric_page.dart';
import '../../feature/ekyc/intro/face_capture_check_screen.dart';
import '../../feature/ekyc/selfie/selfie_locked_screen.dart';
import '../../feature/ekyc/selfie/selfie_retry_screen.dart';
import '../../feature/ekyc/selfie/selfie_success_screen.dart';
import '../../feature/ekyc/selfie/selfie_verification_screen.dart';
import '../../feature/biometric/activate_biometric/activate_biometric_success_screen.dart';
import '../../feature/error_screen/common_error_screen.dart';
import '../../feature/error_screen/error_screen.dart';
import '../../feature/logging/evo_navigator_observer.dart';
import '../../feature/login/new_device/verify_mpin/new_device_verify_mpin_screen.dart';
import '../../feature/login/new_device/verify_username/verify_username_screen.dart';
import '../../feature/login/old_device/previous_login_screen.dart';
import '../../feature/main_screen/card_page/activate_card_success_screen.dart';
import '../../feature/main_screen/card_page/last_4_digits_check/last_4_digits_check_screen.dart';
import '../../feature/main_screen/main_screen.dart';
import '../../feature/pin/change_pin/change_pin_arg.dart';
import '../../feature/pin/change_pin/confirm_new_pin_screen.dart';
import '../../feature/pin/change_pin/create_new_pin_screen.dart';
import '../../feature/pin/change_pin/verify_current/current_pin_verification_screen.dart';
import '../../feature/pin/new_pin_success_screen.dart';
import '../../feature/privilege_action/verify_pin_privilege_action/verify_pin_privilege_action_screen.dart';
import '../../feature/profile/profile_screen/biometric_enabled_success_screen.dart';
import '../../feature/profile/profile_screen/profile_page.dart';
import '../../feature/select_security_method/select_security_method_screen.dart';
import '../../feature/splash_screen/splash_screen.dart';
import '../../feature/transaction_details/transaction_details_screen.dart';
import '../../feature/verify_otp/verify_otp_page.dart';
import '../../feature/welcome/introduction/introduction_screen.dart';
import '../../feature/welcome/welcome_screen.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/global.dart';

final GoRouter evoRouter = GoRouter(
  initialLocation: Screen.splashScreen.routeName,
  navigatorKey: globalKeyProvider.navigatorKey,
  errorBuilder: (_, GoRouterState state) {
    /// Use-case #1: this callback is called to handle GoRouter.exception
    /// this **SplashScreen** will be displayed when GoRouter.exception is thrown
    /// refer: https://pub.dev/documentation/go_router/latest/topics/Error%20handling-topic.html
    log('evo_router: error builder: ${state.uri.path}');

    return const SplashScreen();
  },
  observers: <NavigatorObserver>[
    getIt.get<CommonNavigatorObserver>(),
    getIt.get<EvoNavigatorObserver>(),
    // Firebase Analytics Observer - to send navigation events to Firebase Analytics
    getIt.get<FirebaseAnalyticsWrapper>().analyticsObserver,
  ],
  routes: <GoRoute>[
    GoRoute(
      name: Screen.splashScreen.name,
      path: Screen.splashScreen.routeName,
      builder: (_, GoRouterState state) {
        return const SplashScreen();
      },
    ),
    GoRoute(
      name: Screen.mainScreen.name,
      path: Screen.mainScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is MainScreenArg) {
          final MainScreenArg arg = state.extra as MainScreenArg;
          return MainScreen(
            isLoggedIn: arg.isLoggedIn,
            initialPage: arg.initialPage,
            initialAction: arg.initialAction,
            isCardActivated: arg.isCardActivated,
          );
        } else {
          return ErrorPage(errMsg: '${Screen.mainScreen.name} need to pass argument');
        }
      },
    ),
    GoRoute(
      name: Screen.introductionScreen.name,
      path: Screen.introductionScreen.routeName,
      builder: (_, GoRouterState state) {
        return const IntroductionScreen();
      },
    ),
    GoRoute(
      name: Screen.welcomeScreen.name,
      path: Screen.welcomeScreen.routeName,
      builder: (_, GoRouterState state) {
        return const WelcomeScreen();
      },
    ),
    GoRoute(
      name: Screen.verifyOtpScreen.name,
      path: Screen.verifyOtpScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is VerifyOtpPageArg) {
          final VerifyOtpPageArg arg = state.extra as VerifyOtpPageArg;
          return VerifyOtpPage(
            verifyOtpType: arg.verifyOtpType,
            contactInfo: arg.contactInfo,
            otpResendSecs: arg.otpResendSecs,
            otpValiditySecs: arg.otpValiditySecs,
            onPopSuccess: arg.onPopSuccess,
            sessionToken: arg.sessionToken,
            resendSessionToken: arg.resendSessionToken,
          );
        } else {
          return ErrorPage(errMsg: '${Screen.mainScreen.name} need to pass argument');
        }
      },
    ),
    GoRoute(
      name: Screen.profileScreen.name,
      path: Screen.profileScreen.routeName,
      builder: (_, GoRouterState state) {
        return const ProfileScreen();
      },
    ),
    GoRoute(
      name: Screen.previousLogInScreen.name,
      path: Screen.previousLogInScreen.routeName,
      builder: (_, GoRouterState state) {
        return const PreviousLogInScreen();
      },
    ),
    GoRoute(
      name: Screen.newDeviceVerifyMPinScreen.name,
      path: Screen.newDeviceVerifyMPinScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is NewDeviceVerifyMPinArg) {
          final NewDeviceVerifyMPinArg arg = state.extra as NewDeviceVerifyMPinArg;
          return NewDeviceVerifyMPinScreen(
            username: arg.username,
            sessionToken: arg.sessionToken,
            onPopSuccess: arg.onPopSuccess,
          );
        } else {
          return ErrorPage(
              errMsg: '${Screen.newDeviceVerifyMPinScreen.name} need to pass argument');
        }
      },
    ),
    GoRoute(
      name: Screen.activateBiometricScreen.name,
      path: Screen.activateBiometricScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ActivateBiometricScreenArg) {
          final ActivateBiometricScreenArg arg = state.extra as ActivateBiometricScreenArg;
          return ActivateBiometricScreen(
            onSuccess: arg.onSuccess,
            sessionToken: arg.sessionToken,
            flow: arg.flow,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: CommonScreen.webViewPage.name,
      path: CommonScreen.webViewPage.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is CommonWebViewArg) {
          final CommonWebViewArg arg = state.extra as CommonWebViewArg;
          return CommonWebView(arg: arg);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.currentPinVerificationScreen.name,
      path: Screen.currentPinVerificationScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is CurrentPINVerificationArg) {
          final CurrentPINVerificationArg arg = state.extra as CurrentPINVerificationArg;
          return CurrentPINVerificationScreen(
            sessionToken: arg.sessionToken,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.createNewPinScreen.name,
      path: Screen.createNewPinScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is CreateNewPinArgs) {
          final CreateNewPinArgs arg = state.extra as CreateNewPinArgs;
          return CreateNewPinScreen(
            sessionToken: arg.sessionToken,
            flow: arg.flow,
            onSuccess: arg.onSuccess,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.confirmNewPinScreen.name,
      path: Screen.confirmNewPinScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ConfirmNewPinArgs) {
          final ConfirmNewPinArgs arg = state.extra as ConfirmNewPinArgs;
          return ConfirmNewPinScreen(
            pin: arg.pin,
            sessionToken: arg.sessionToken,
            flow: arg.flow,
            onConfirmNewPinSuccess: arg.onSuccess,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.newPinSuccessScreen.name,
      path: Screen.newPinSuccessScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is NewPinSuccessScreenArg) {
          final NewPinSuccessScreenArg arg = state.extra as NewPinSuccessScreenArg;
          return NewPinSuccessScreen(
            buttonText: arg.buttonText,
            onNext: arg.onNext,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.biometricEnabledSuccessScreen.name,
      path: Screen.biometricEnabledSuccessScreen.routeName,
      builder: (_, GoRouterState state) {
        return const BiometricEnabledSuccessScreen();
      },
    ),
    ..._accountActivationRoutes(),
    GoRoute(
      name: Screen.last4DigitsCheckScreen.name,
      path: Screen.last4DigitsCheckScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is Last4DigitsCheckScreenArg) {
          final Last4DigitsCheckScreenArg arg = state.extra as Last4DigitsCheckScreenArg;
          return Last4DigitsCheckScreen(onSuccess: arg.onSuccess);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.verifyUsernameScreen.name,
      path: Screen.verifyUsernameScreen.routeName,
      builder: (_, GoRouterState state) {
        return const VerifyUsernameScreen();
      },
    ),
    GoRoute(
      name: Screen.activationStatusScreen.name,
      path: Screen.activationStatusScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ActivationStatusScreenArg) {
          final ActivationStatusScreenArg arg = state.extra as ActivationStatusScreenArg;
          return ActivationStatusScreen(status: arg.status);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.commonErrorScreen.name,
      path: Screen.commonErrorScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is CommonErrorScreenArg) {
          final CommonErrorScreenArg arg = state.extra as CommonErrorScreenArg;
          return CommonErrorScreen(
            title: arg.title,
            description: arg.description,
            buttonText: arg.buttonText,
            onTap: arg.onTap,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.activateBiometricBlockedScreen.name,
      path: Screen.activateBiometricBlockedScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ActivateBiometricBlockedScreenArg) {
          final ActivateBiometricBlockedScreenArg arg =
              state.extra as ActivateBiometricBlockedScreenArg;
          return ActivateBiometricBlockedScreen(
            sessionToken: arg.sessionToken,
            onPopSuccess: arg.onPopSuccess,
            flow: arg.flow,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.selectSecurityMethodScreen.name,
      path: Screen.selectSecurityMethodScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is SelectSecurityMethodArg) {
          final SelectSecurityMethodArg arg = state.extra as SelectSecurityMethodArg;
          return SelectSecurityMethodScreen(
            flow: arg.flow,
            methods: arg.methods,
            sessionToken: arg.sessionToken,
            onSuccess: arg.onSuccess,
          );
        } else {
          return const ErrorPage();
        }
      },
    )
  ],
);

/// Screens related to Account Activation
List<GoRoute> _accountActivationRoutes() {
  return <GoRoute>[
    GoRoute(
      name: Screen.mobileNumberCheckScreen.name,
      path: Screen.mobileNumberCheckScreen.routeName,
      builder: (_, GoRouterState state) {
        return const MobileNumberCheckScreen();
      },
    ),
    GoRoute(
      name: Screen.faceCaptureCheckScreen.name,
      path: Screen.faceCaptureCheckScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is FaceCaptureCheckScreenArgs) {
          final FaceCaptureCheckScreenArgs arg = state.extra as FaceCaptureCheckScreenArgs;
          return FaceCaptureCheckScreen(
            sessionToken: arg.sessionToken,
            onPopSuccess: arg.onPopSuccess,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.createUsernameScreen.name,
      path: Screen.createUsernameScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is CreateUsernameScreenArgs) {
          final CreateUsernameScreenArgs arg = state.extra as CreateUsernameScreenArgs;
          return CreateUsernameScreen(
            onPopSuccess: arg.onPopSuccess,
            sessionToken: arg.sessionToken,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.selfieVerificationScreen.name,
      path: Screen.selfieVerificationScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is SelfieVerificationScreenArgs) {
          final SelfieVerificationScreenArgs arg = state.extra as SelfieVerificationScreenArgs;
          return SelfieVerificationScreen(
            sessionToken: arg.sessionToken,
            onPopSuccess: arg.onPopSuccess,
            flowType: arg.flowType,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.activateVirtualCardScreen.name,
      path: Screen.activateVirtualCardScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ActivateVirtualCardArg) {
          final ActivateVirtualCardArg arg = state.extra as ActivateVirtualCardArg;
          return ActivateVirtualCardScreen(
            sessionToken: arg.sessionToken,
            onPopSuccess: arg.onPopSuccess,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.virtualCardActivatedScreen.name,
      path: Screen.virtualCardActivatedScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is VirtualCardActivatedArg) {
          final VirtualCardActivatedArg arg = state.extra as VirtualCardActivatedArg;
          return VirtualCardActivatedScreen(username: arg.userName);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.activateCardSuccessScreen.name,
      path: Screen.activateCardSuccessScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ActivateCardSuccessArg) {
          final ActivateCardSuccessArg arg = state.extra as ActivateCardSuccessArg;
          return ActivateCardSuccessScreen(
            cardType: arg.cardType,
            onPopSuccess: arg.onPopSuccess,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.verifyPinPrivilegeActionScreen.name,
      path: Screen.verifyPinPrivilegeActionScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is VerifyPinPrivilegeActionScreenArgs) {
          final VerifyPinPrivilegeActionScreenArgs arg =
              state.extra as VerifyPinPrivilegeActionScreenArgs;
          return VerifyPinPrivilegeActionScreen(args: arg);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.selfieRetryScreen.name,
      path: Screen.selfieRetryScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is SelfieRetryScreenArg) {
          final SelfieRetryScreenArg arg = state.extra as SelfieRetryScreenArg;
          return SelfieRetryScreen(
            onRetry: arg.onRetry,
            error: arg.error,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.selfieLockedScreen.name,
      path: Screen.selfieLockedScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is SelfieLockedScreenArg) {
          final SelfieLockedScreenArg arg = state.extra as SelfieLockedScreenArg;
          return SelfieLockedScreen(title: arg.title, subtitle: arg.subtitle);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.selfieSuccessScreen.name,
      path: Screen.selfieSuccessScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is SelfieSuccessScreenArg) {
          final SelfieSuccessScreenArg arg = state.extra as SelfieSuccessScreenArg;
          return SelfieSuccessScreen(onProceed: arg.onProceed);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.activateBiometricSuccessScreen.name,
      path: Screen.activateBiometricSuccessScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is ActivateBiometricSuccessScreenArg) {
          final ActivateBiometricSuccessScreenArg arg =
              state.extra as ActivateBiometricSuccessScreenArg;
          return ActivateBiometricSuccessScreen(onProceed: arg.onProceed);
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.verifyEmailScreen.name,
      path: Screen.verifyEmailScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is VerifyEmailArg) {
          final VerifyEmailArg arg = state.extra as VerifyEmailArg;
          return VerifyEmailScreen(
            email: arg.email,
            sessionToken: arg.sessionToken,
            onPopSuccess: arg.onPopSuccess,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.inputEmailScreen.name,
      path: Screen.inputEmailScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is InputEmailArg) {
          final InputEmailArg arg = state.extra as InputEmailArg;
          return InputEmailScreen(
            email: arg.email,
            isDuplicate: arg.isDuplicate,
            duplicateErrorMessage: arg.duplicateErrorMessage,
            sessionToken: arg.sessionToken,
            onPopSuccess: arg.onPopSuccess,
          );
        } else {
          return const ErrorPage();
        }
      },
    ),
    GoRoute(
      name: Screen.transactionDetailsScreen.name,
      path: Screen.transactionDetailsScreen.routeName,
      builder: (_, GoRouterState state) {
        if (state.extra is TransactionDetailsScreenArg) {
          final TransactionDetailsScreenArg arg = state.extra as TransactionDetailsScreenArg;
          return TransactionDetailsScreen(transactionId: arg.transactionId);
        } else {
          return const ErrorPage();
        }
      },
    ),
  ];
}
