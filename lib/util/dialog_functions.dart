import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../feature/biometric/biometric_token/providers/biometric_token_provider_config.dart';
import '../feature/verify_otp/verify_otp_page.dart';
import '../feature/welcome/welcome_screen.dart';
import '../model/evo_dialog_id.dart';
import '../prepare_for_app_initiation.dart';
import '../resources/resources.dart';
import '../widget/evo_dialog/dialog_confirm.dart';
import '../widget/evo_dialog/kyko_bottom_sheet.dart';
import '../widget/evo_dialog/kyko_bottom_sheet_action.dart';
import 'evo_flutter_wrapper.dart';

part 'dialog_session_expired_ui_model.dart';

DialogFunction get evoDialogFunction => getIt.get<DialogFunction>();

enum DialogAlertType {
  error,
  warning,
  unsuccessful,
}

enum SessionDialogType {
  resetPin,
  logIn,
  activateAccount;

  static SessionDialogType? fromVerifyOtpType(VerifyOtpType? type) {
    return switch (type) {
      VerifyOtpType.signIn => SessionDialogType.logIn,
      VerifyOtpType.resetPin => SessionDialogType.resetPin,
      VerifyOtpType.activateAccount ||
      VerifyOtpType.email ||
      VerifyOtpType.activateCard =>
        SessionDialogType.activateAccount,
      _ => null,
    };
  }

  static SessionDialogType? fromEnableBiometricAuthenticationFlow(
      EnableBiometricAuthenticationFlow flow) {
    return switch (flow) {
      EnableBiometricAuthenticationFlow.accountActivation => SessionDialogType.activateAccount,
      EnableBiometricAuthenticationFlow.newDeviceLogin => SessionDialogType.logIn,
      _ => null,
    };
  }
}

class DialogFunction {
  Future<void> showDialogConfirm({
    required String textPositive,
    required EvoDialogId dialogId,
    String? content,
    String? title,
    String? textNegative,
    Widget? footer,
    VoidCallback? onClickPositive,
    VoidCallback? onClickNegative,
    Widget? imageHeader,
    DialogAlertType? alertType,
    bool isDismissible = true,
    ButtonStyle? positiveButtonStyle,
    ButtonStyle? negativeButtonStyle,
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
    Map<String, dynamic>? loggingEventMetaData,
    Map<String, dynamic>? loggingEventOnShowMetaData,
    bool isShowButtonClose = false,
    TextAlign? titleTextAlign,
    TextAlign? contentTextAlign,
    Key? key,
    ButtonListOrientation? buttonListOrientation,
    EdgeInsets? headerPadding,
    EdgeInsets? contentPadding,
    EdgeInsets? ctaPadding,
    double? contentSpacing,
    double? ctaSpacing,
    double? dialogHorizontalPadding,
    VoidCallback? onDismiss,
    bool? autoClosePopupWhenClickCTA = false,
  }) async {
    final Widget? imageHeaderWidget =
        alertType != null ? getImageHeaderByAlertType(alertType) : imageHeader;
    bool isCTAClicked = false;

    await evoFlutterWrapper.showDialog<void>(
      barrierDismissible: isDismissible,
      builder: (_) => PopScope(
        canPop: isDismissible,
        child: EvoDialogConfirm(
          key: key,
          content: content,
          textPositive: textPositive,
          title: title,
          textNegative: textNegative,
          footer: footer,
          onClickPositive: () {
            if (autoClosePopupWhenClickCTA ?? false) {
              navigatorContext?.pop();
            }
            isCTAClicked = true;
            onClickPositive?.call();
          },
          onClickNegative: () {
            if (autoClosePopupWhenClickCTA ?? false) {
              navigatorContext?.pop();
            }
            isCTAClicked = true;
            onClickNegative?.call();
          },
          imageHeader: imageHeaderWidget,
          positiveButtonStyle: positiveButtonStyle,
          negativeButtonStyle: negativeButtonStyle,
          titleTextStyle: titleTextStyle,
          contentTextStyle: contentTextStyle,
          dialogId: dialogId.id,
          loggingEventMetaData: loggingEventMetaData,
          loggingEventOnShowMetaData: loggingEventOnShowMetaData,
          isShowButtonClose: isShowButtonClose,
          titleTextAlign: titleTextAlign,
          contentTextAlign: contentTextAlign,
          buttonListOrientation: buttonListOrientation,
          headerPadding: headerPadding,
          contentPadding: contentPadding,
          ctaPadding: ctaPadding,
          contentSpacing: contentSpacing,
          ctaSpacing: ctaSpacing,
          dialogHorizontalPadding: dialogHorizontalPadding,
        ),
      ),
    );

    if (!isCTAClicked) {
      onDismiss?.call();
    }
  }

  @visibleForTesting
  Widget getImageHeaderByAlertType(DialogAlertType type) {
    final String icon = switch (type) {
      DialogAlertType.error => EvoImages.icAlertError,
      DialogAlertType.warning => EvoImages.icAlertWarning,
      DialogAlertType.unsuccessful => EvoImages.icAlertUnsuccessful,
    };
    return evoImageProvider.asset(icon);
  }

  Future<void> showDialogSessionTokenExpired({
    VoidCallback? onClickPositive,
    SessionDialogType? type = SessionDialogType.logIn,
  }) async {
    final DialogSessionExpiredUiModel? uiModel = _createSessionExpiredDialogUiModel(type);

    if (uiModel == null) {
      commonLog('session dialog type is not supported');
      return;
    }

    await showDialogConfirm(
      alertType: DialogAlertType.error,
      title: uiModel.title,
      content: uiModel.content,
      textPositive: uiModel.textPositive,
      dialogId: uiModel.dialogId,
      isDismissible: false,
      onClickPositive: onClickPositive ?? () => _handleNavigationIfSessionExpired(type),
    );
  }

  DialogSessionExpiredUiModel? _createSessionExpiredDialogUiModel(SessionDialogType? type) {
    return switch (type) {
      SessionDialogType.resetPin => DialogSessionExpiredUiModel.resetPin(),
      SessionDialogType.logIn => DialogSessionExpiredUiModel.login(),
      SessionDialogType.activateAccount => DialogSessionExpiredUiModel.activateAccount(),
      _ => null,
    };
  }

  void _handleNavigationIfSessionExpired(SessionDialogType? type) {
    final BuildContext? navigatorCtx = navigatorContext;

    switch (type) {
      case SessionDialogType.activateAccount:
        navigatorCtx?.popUntilNamed(Screen.mobileNumberCheckScreen.name);
        break;
      case SessionDialogType.logIn:
        navigatorCtx?.popUntilNamed(Screen.verifyUsernameScreen.name);
        break;
      case SessionDialogType.resetPin:
      default:
        navigatorCtx?.pop(); // dismiss popup
        navigatorCtx?.pop(); // pop screen
        break;
    }
  }

  Future<void> showDialogBottomSheet({
    required String title,
    required Widget content,
    List<KykoBottomSheetAction>? actions,
    bool hasCloseButton = false,
  }) async {
    return evoFlutterWrapper.showBottomSheet<void>(builder: (_) {
      return KykoBottomSheet(
        hasCloseButton: hasCloseButton,
        title: title,
        content: content,
        actions: actions,
      );
    });
  }

  @visibleForTesting
  EvoDialogId getDialogIdBySessionDialogType(SessionDialogType type) {
    return switch (type) {
      SessionDialogType.activateAccount => EvoDialogId.activateAccountErrorLimitExceededDialog,
      SessionDialogType.logIn => EvoDialogId.signInErrorLimitExceededDialog,
      SessionDialogType.resetPin => EvoDialogId.resetPinErrorLimitExceededDialog,
    };
  }

  Future<void> showDialogErrorLimitExceeded({
    required SessionDialogType type,
    String? content,
  }) async {
    String title;
    String description;
    String textPositive;

    switch (type) {
      case SessionDialogType.logIn:
        title = EvoStrings.loginLimitedExceededTitle;
        description = content ?? EvoStrings.loginLimitedExceededDesc;
        textPositive = EvoStrings.backToHomePage;
        break;
      default:
        title = EvoStrings.maxTriesReached;
        description = content ?? EvoStrings.tryAgainLater;
        textPositive = EvoStrings.backToHomePage;
        break;
    }

    return showDialogConfirm(
      dialogId: getDialogIdBySessionDialogType(type),
      title: title,
      textPositive: textPositive,
      content: description,
      alertType: DialogAlertType.error,
      autoClosePopupWhenClickCTA: true,
      isDismissible: false,
      onClickPositive: WelcomeScreen.goNamed,
    );
  }
}
