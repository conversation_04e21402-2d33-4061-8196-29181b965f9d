import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/common_request_option.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../data/constants.dart';
import '../../data/repository/authentication_repo.dart';
import '../../data/response/sign_in_entity.dart';
import '../../feature/authorization_session_expired/authorization_session_expired_handler.dart';
import '../../base/modules/core/app_state.dart';
import '../token_utils/jwt_helper.dart';

enum RefreshTokenType {
  none,
  error,
  needRefresh,
}

/// [UnauthorizedInterceptor] check we need to get new access token for current request or not.
/// It's work like a queue, it's mean [UnauthorizedInterceptor] will be process one by one request.
/// For each request into [UnauthorizedInterceptor], it check if need to call refresh token by [checkIsNeedRefreshToken] function
/// If this function return [TokenStatus.needRefresh], we will call [retryRefreshToken] to get new access token and update
/// new access token to current request and continue process that request.
/// If [checkIsNeedRefreshToken] return  [TokenStatus.errorMessage] this interceptor will throw error. Now will be show expired session popup
/// If [checkIsNeedRefreshToken] return  [TokenStatus.valid] we will process current request immediately
class UnauthorizedInterceptor extends QueuedInterceptor {
  final AuthorizationSessionExpiredHandler _unauthorizedHandler;
  final AuthenticationRepo _authenticationRepo;
  final AppState _appState;
  final JwtHelper _jwtHelper;
  final List<String>? ignoredRefreshTokenApiPath;
  final List<String>? ignoredVerdictEmitUnauthorized;

  UnauthorizedInterceptor(
    this._unauthorizedHandler,
    this._authenticationRepo,
    this._appState,
    this._jwtHelper, {
    this.ignoredRefreshTokenApiPath,
    this.ignoredVerdictEmitUnauthorized,
  });

  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    commonLog('UnauthorizedInterceptor -- onRequest start');

    final String? accessToken = _appState.userToken.accessToken;
    final String? refreshToken = _appState.userToken.refreshToken;

    /// In case parallel api call, we only update for current api which call refresh token flow(1),
    /// other request will not get latest token after new access token is updated in flow (1). It still keep old access token when it is emitted.
    /// Therefore we update access token here to make sure all request will get latest access token.
    checkIfNeedRemoveAccessToken(options, accessToken);

    final RefreshTokenType refreshTokenType = checkIsNeedRefreshToken(
      ignoredRefreshTokenApiPath: ignoredRefreshTokenApiPath,
      apiPath: options.path,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );

    final DioException error = _getDioException(options);

    switch (refreshTokenType) {
      case RefreshTokenType.none:
        return handler.next(options);

      case RefreshTokenType.error:
        _unauthorizedHandler.emitUnauthorized(UnauthorizedSessionState.invalidToken);
        return handler.reject(error);

      case RefreshTokenType.needRefresh:
        commonLog('Start refresh token');

        final SignInEntity? refreshTokenResult = await retryRefreshToken(refreshToken);

        if (refreshTokenResult?.statusCode == CommonHttpClient.SUCCESS) {
          commonLog('Refresh token is successfully, continue process ${options.path} api.');

          /// Update new access token to current request
          options.headers
              .addAll(commonUtilFunction.getAccessTokenHeader(refreshTokenResult?.accessToken));
          commonLog(options.headers.toString());
          return handler.next(options);
        }

        commonLog('Refresh token is fail with ${refreshTokenResult?.statusCode} code');

        if (isIgnoreRefreshTokenError(refreshTokenResult?.statusCode)) {
          return handler.next(options);
        }

        _unauthorizedHandler.emitUnauthorized(UnauthorizedSessionState.invalidToken);
        return handler.reject(error);
    }
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    final int? statusCode = err.response?.statusCode;
    switch (statusCode) {
      case CommonHttpClient.INVALID_TOKEN:
        final String? verdict = err.response?.data['verdict'] as String?;
        final bool shouldEmit = emitUnauthorizedState(verdict);
        shouldEmit ? handler.reject(err) : handler.next(err);
      default:
        handler.next(err);
        break;
    }
  }

  /// return [TRUE] if unauthorized state is emitted
  /// otherwise return [FALSE]
  @visibleForTesting
  bool emitUnauthorizedState(String? verdict) {
    /// if [verdict] is in [ignoredVerdictEmitUnauthorized] list, we will not emit unauthorized state
    final bool isNotAllowEmitUnauthorizedState =
        ignoredVerdictEmitUnauthorized?.contains(verdict) == true;

    if (isNotAllowEmitUnauthorizedState) {
      return false;
    }

    final UnauthorizedSessionState unauthorizedSessionState =
        UnauthorizedSessionState.mapFrom(verdict);
    switch (unauthorizedSessionState) {
      case UnauthorizedSessionState.forcedLogout:
        _unauthorizedHandler.emitUnauthorized(UnauthorizedSessionState.forcedLogout);
      case UnauthorizedSessionState.invalidToken:
        _unauthorizedHandler.emitUnauthorized(UnauthorizedSessionState.invalidToken);
      default:
        _unauthorizedHandler.emitUnauthorized(UnauthorizedSessionState.unknown);
    }
    return true;
  }

  RefreshTokenType checkIsNeedRefreshToken({
    required String apiPath,
    List<String>? ignoredRefreshTokenApiPath,
    String? accessToken,
    String? refreshToken,
  }) {
    if (ignoredRefreshTokenApiPath?.contains(apiPath) == true) {
      return RefreshTokenType.none;
    }

    /// We assume that [accessToken] is null will be public api.
    if (accessToken == null) {
      return RefreshTokenType.none;
    }

    /// [accessToken] is not expired.
    if (_jwtHelper.isCanUse(accessToken)) {
      return RefreshTokenType.none;
    }

    /// [refreshToken] is expired.
    if (!_jwtHelper.isCanUse(refreshToken)) {
      commonLog('Cancel refresh token because refreshToken is expired');
      return RefreshTokenType.error;
    }

    return RefreshTokenType.needRefresh;
  }

  Future<SignInEntity?> retryRefreshToken(
    String? refreshToken, {
    int intervalInSec = 1,
    int maxRetryCount = 3,
    int retryCount = 0,
  }) async {
    commonLog('Start call refresh token: $retryCount time');

    final SignInEntity response = await _authenticationRepo.refreshToken(refreshToken);

    if (response.statusCode == CommonHttpClient.SUCCESS || retryCount == maxRetryCount) {
      return response;
    } else {
      await Future<void>.delayed(Duration(seconds: intervalInSec));
      return retryRefreshToken(
        refreshToken,
        intervalInSec: intervalInSec,
        maxRetryCount: maxRetryCount,
        retryCount: retryCount + 1,
      );
    }
  }

  bool isIgnoreRefreshTokenError(int? statusCode) {
    return statusCode == CommonHttpClient.NO_INTERNET ||
        statusCode == CommonHttpClient.SOCKET_ERRORS;
  }

  DioException _getDioException(RequestOptions options) {
    return DioException(
      requestOptions: options,
      response: Response<dynamic>(
        requestOptions: options,
        statusCode: CommonHttpClient.INVALID_TOKEN,
      ),
    );
  }

  @visibleForTesting
  void checkIfNeedRemoveAccessToken(RequestOptions options, String? accessToken) {
    if (options.headers[CommonRequestOption.shouldRemoveAccessTokenHeader] == true) {
      options.headers.remove(HeaderKey.authorization);
    } else {
      options.headers.addAll(commonUtilFunction.getAccessTokenHeader(accessToken));
    }
  }
}
