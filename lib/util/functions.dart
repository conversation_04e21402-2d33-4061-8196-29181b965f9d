import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/intl.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:flutter_common_package/feature/onesignal/onesignal.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';

import '../feature/feature_toggle.dart';
import '../feature/login/new_device/verify_username/verify_username_screen.dart';
import '../feature/login/old_device/login_on_old_device_screen.dart';
import '../feature/pin/models/change_pin_status.dart';
import '../model/evo_dialog_id.dart';
import '../base/modules/core/app_state.dart';
import '../prepare_for_app_initiation.dart';
import '../resources/resources.dart';
import '../widget/animation/loading_animation_widget.dart';
import '../widget/hud_loading/hud_loading.dart';
import '../widget/hud_loading/hud_loading_simple_indicator.dart';
import 'evo_flutter_wrapper.dart';
import 'secure_storage_helper/secure_storage_helper.dart';
import 'secure_storage_helper/secure_storage_helper_impl.dart';
import 'token_utils/jwt_helper.dart';

final EvoUtilFunction evoUtilFunction = getIt.get<EvoUtilFunction>();

class EvoUtilFunction with LogErrorMixin {
  /// This method only is worked on iOS
  /// On Android, It always returns false
  Future<bool> detectReinstallAppOnIOSDevice() async {
    if (evoFlutterWrapper.isAndroid()) {
      return false;
    }

    /*
    * EvoLocalStorageHelper save data to keychain on iOS.
    * First install: save deviceId to keychain
    * Re-install: check newDeviceId == deviceId (in key chain)
    * true -> next step
    * false -> delete all data in keychain and go Tutorial page
    */
    final String? deviceIdLocal = await getIt.get<EvoLocalStorageHelper>().getDeviceId();

    /*
    * Document identifierForVendor:
    * https://developer.apple.com/documentation/uikit/uidevice/1620059-identifierforvendor
    * The value in this property remains the same while the app
    * (or another app from the same vendor) is installed on the iOS device.
    * The value changes when the user deletes all of that vendor’s apps from the device
    * and subsequently re-installs one or more of them.
    * */
    final String? newDeviceId = await getNewDeviceId();
    return newDeviceId != deviceIdLocal;
  }

  Future<void> deleteAllData() async {
    await getIt<EvoLocalStorageHelper>().deleteAllData();
  }

  Future<String?> getNewDeviceId() async {
    if (evoFlutterWrapper.isAndroid()) {
      return null;
    }

    final String? newDeviceId = (await commonUtilFunction.getIosInfo()).identifierForVendor;
    return newDeviceId;
  }

  Future<void> setNewDeviceId() async {
    final String? newDeviceId = await getNewDeviceId();
    await getIt<EvoLocalStorageHelper>().setDeviceId(newDeviceId);
  }

  String? encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((MapEntry<String, String> e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  Future<void> clearAllUserData({
    OneSignal? oneSignal,
    bool clearAllNotifications = true,
  }) async {
    await clearNotificationData(
      clearAllNotifications: clearAllNotifications,
      oneSignal: oneSignal,
    );

    // Clear user data on memory
    clearUserInfoAppState();

    // Clear all user data on local storage
    await getIt.get<EvoLocalStorageHelper>().clearAllUserData();
  }

  Future<void> clearDataOnTokenInvalid() async {
    /// Clear notification and access token
    await commonUtilFunction.clearDataOnTokenInvalid();
  }

  String evoFormatCurrency(
    double? value, {
    String? locale = defaultCurrencyLocale,
    String? currencySymbol = defaultCurrencySymbol,
  }) {
    if (value == null) {
      return '-';
    }

    final NumberFormat format = NumberFormat.currency(locale: locale, symbol: currencySymbol);
    return format.format(value);
  }

  Future<void> openAuthenticationScreen({
    bool isClearNavigationStack = false,
  }) async {
    if (await isCanLogInOnOldDevice()) {
      return _openLoginOnOldDeviceScreen(
        isClearNavigationStack: isClearNavigationStack,
      );
    } else {
      await getIt.get<EvoLocalStorageHelper>().clearAllUserData();

      _openVerifyUsernameScreen(
        isClearNavigationStack: isClearNavigationStack,
      );
    }
  }

  Future<void> _openLoginOnOldDeviceScreen({
    bool isClearNavigationStack = false,
  }) {
    if (isClearNavigationStack) {
      return LoginOnOldDeviceScreen.goNamed();
    } else {
      return LoginOnOldDeviceScreen.pushNamed();
    }
  }

  void _openVerifyUsernameScreen({
    bool isClearNavigationStack = false,
  }) {
    if (isClearNavigationStack) {
      VerifyUsernameScreen.goNamed();
    } else {
      VerifyUsernameScreen.pushNamed();
    }
  }

  Future<bool> isCanLogInOnOldDevice() async {
    final String? deviceToken = await getIt.get<EvoLocalStorageHelper>().getDeviceToken();
    return getIt.get<JwtHelper>().isCanUse(deviceToken);
  }

  Future<void> showHudLoading() {
    return HudLoading.instance.show(
      indicator: const HubLoadingSimpleIndicator(
        animation: LoadingAnimationWidget(),
      ),
    );
  }

  Future<void> hideHudLoading() {
    return HudLoading.instance.dismiss();
  }

  Future<void> hideKeyboard({bool shouldDelay = true}) async {
    FocusManager.instance.primaryFocus?.unfocus();

    /// To improve UX, we can wait a short time for the soft keyboard to be completely hidden
    /// before doing the next action (like back navigation).
    if (shouldDelay) {
      await Future<void>.delayed(const Duration(milliseconds: 300));
    }
  }

  DateTime getCurrentTime() => DateTime.now();

  String getCurrentTimeString() => getCurrentTime().toString();

  void clearUserInfoAppState() {
    final AppState appState = getIt.get<AppState>();
    appState.isUserLogIn = false;
    appState.userInfo.value = null;
    appState.userToken = null;
    appState.changePinStatusNotifier.value = ChangePinStatus.available;
  }

  @Deprecated('This function will be removed soon. Please use [ScreenUtilExtension] instead.')
  double calculateVerticalSpace({
    required BuildContext context,
    required double heightPercentage,
  }) {
    assert(heightPercentage > 0);
    return heightPercentage * context.screenHeight;
  }

  @Deprecated('This function will be removed soon. Please use [ScreenUtilExtension] instead.')
  double calculateHorizontalSpace({
    required BuildContext context,
    required double widthPercentage,
  }) {
    assert(widthPercentage > 0);
    return widthPercentage * context.screenWidth;
  }

  Future<void> showDialogBottomSheet({
    required String textPositive,
    required EvoDialogId dialogId,
    String? content,
    String? title,
    String? textNegative,
    Widget? footer,
    VoidCallback? onClickPositive,
    VoidCallback? onClickNegative,
    Widget? header,
    EdgeInsets? headerPadding,
    bool isDismissible = true,
    ButtonStyle? positiveButtonStyle,
    ButtonStyle? negativeButtonStyle,
    TextStyle? titleTextStyle,
    TextStyle? contentTextStyle,
    VoidCallback? onClickClose,
    bool isShowButtonClose = false,
    Widget? buttonClose,
    Map<String, dynamic>? loggingEventMetaData,
    Map<String, dynamic>? loggingEventOnShowMetaData,
    ButtonListOrientation? buttonListOrientation,
  }) async {
    final BuildContext? navigatorCtx = navigatorContext;
    if (navigatorCtx == null) {
      return;
    }

    return commonUtilFunction.showDialogBottomSheet(navigatorCtx,
        textPositive: textPositive,
        content: content,
        title: title,
        textNegative: textNegative,
        footer: footer,
        onClickPositive: onClickPositive,
        onClickNegative: onClickNegative,
        header: header,
        headerPadding: headerPadding,
        isDismissible: isDismissible,
        positiveButtonStyle:
            positiveButtonStyle ?? getIt.get<CommonButtonStyles>().primary(ButtonSize.xLarge),
        negativeButtonStyle:
            negativeButtonStyle ?? getIt.get<CommonButtonStyles>().tertiary(ButtonSize.xLarge),
        titleTextStyle: titleTextStyle,
        contentTextStyle: contentTextStyle,
        onClickClose: onClickClose,
        isShowButtonClose: isShowButtonClose,
        buttonClose: buttonClose,
        isEnableLoggingEvent: getIt<FeatureToggle>().enableEventTrackingFeature,
        dialogId: dialogId.id,
        buttonListOrientation: buttonListOrientation,
        loggingEventMetaData: loggingEventMetaData,
        loggingEventOnShowMetaData: loggingEventOnShowMetaData);
  }

  Future<void> clearUserDataOnLogout({
    OneSignal? oneSignal,
    bool clearAllNotifications = true,
  }) async {
    await clearNotificationData(
      clearAllNotifications: clearAllNotifications,
      oneSignal: oneSignal,
    );

    /// Clear accessToken, refreshToken, biometricToken and keep deviceToken on logout
    /// The user can re-login by inputting the MPIN base on the requirement.
    /// Refer to the requirement: https://trustingsocial1.atlassian.net/wiki/spaces/BPIRBank/pages/**********/Log-Out
    /// Refer to discuss thread: https://trustingsocial.slack.com/archives/C078YHS0DRP/p1723005674471599?thread_ts=**********.172519&cid=C078YHS0DRP
    clearUserInfoAppState();
    await clearBiometricAuthenticationData();
  }

  Future<void> clearNotificationData({
    bool clearAllNotifications = true,
    OneSignal? oneSignal,
  }) async {
    // Clear all notifications when the user logouts manually
    // Refer: https://trustingsocial1.atlassian.net/browse/EMA-1171
    await commonUtilFunction.clearDataOnTokenInvalid(
      clearAllNotifications: clearAllNotifications,
    );

    /// No longer receive notification from OneSignal
    /*
      When run [await oneSignal?.removeExternalUserId()]  or [await OneSignal.shared.removeExternalUserId()]
      on emulator this function does not return
      result from native and app is stuck here forever, that why we call this function without await
    */
    oneSignal?.removeExternalUserId() ?? OneSignal.shared.removeExternalUserId();
  }

  Future<void> clearBiometricAuthenticationData() async {
    final EvoLocalStorageHelper secureStorageHelper = getIt.get<EvoLocalStorageHelper>();
    await secureStorageHelper.setBiometricAuthenticator(false);
    await secureStorageHelper.delete(key: EvoSecureStorageHelperImpl.biometricTokenKey);
  }

  void openInAppWebView({
    required String title,
    required String url,
  }) {
    CommonWebView.pushNamed(arg: CommonWebViewArg(url: url, title: title));
  }
}
