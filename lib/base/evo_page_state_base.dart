import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../prepare_for_app_initiation.dart';
import '../feature/authorization_session_expired/authorization_session_expired_handler.dart';
import '../feature/authorization_session_expired/authorization_session_expired_popup.dart';
import '../feature/authorization_session_expired/force_logout_popup.dart';
import '../feature/biometric/biometric_token/biometric_change_mixin.dart';
import '../feature/biometric/biometric_token/biometric_token_usability_mixin.dart';
import 'modules/core/app_state.dart';
import '../util/evo_snackbar.dart';
import '../util/functions.dart';
import '../util/secure_storage_helper/secure_storage_helper.dart';

abstract class EvoPageStateBase<T extends PageBase> extends PageStateBase<T>
    with BiometricChangeMixin, BiometricTokenUsabilityMixin, LogErrorMixin {
  @visibleForTesting
  StreamSubscription<UnauthorizedSessionState>? authorizationSessionExpiredSubscription;

  final AuthorizationSessionExpiredHandler _authorizationSessionExpiredHandler =
      getIt.get<AuthorizationSessionExpiredHandler>();

  final AuthorizationSessionExpiredPopup _authorizationSessionExpiredPopup =
      getIt.get<AuthorizationSessionExpiredPopup>();

  final ForceLogoutPopup _forceLogoutPopup = getIt.get<ForceLogoutPopup>();

  final AppState appState = getIt.get<AppState>();

  @override
  void initState() {
    super.initState();
    setCurrentScreenId();
    initAuthorizationSessionExpiredSubscriptionNeed();
  }

  @override
  void dispose() {
    cancelAuthorizationSessionExpiredSubscriptionIfNeed();
    super.dispose();
  }

  /// Called when the top route has been popped off, and the current route
  /// shows up.
  @override
  void didPopNext() {
    super.didPopNext();
    setCurrentScreenId();
  }

  @visibleForTesting
  void setCurrentScreenId() {
    final EventTrackingScreenId currentScreenId = widget.eventTrackingScreenId;
    appState.eventTrackingSharedData.currentScreenId = currentScreenId;
    commonLog('Current event tracking screen id: ${currentScreenId.name}');
  }

  @override
  Future<void> onResumed() async {
    if (isTopVisible()) {
      handleBiometric();
    }
    super.onResumed();
  }

  @visibleForTesting
  Future<void> handleBiometric() async {
    await handleBiometricChangedIfNeed();
    handleBiometricTokenUnUsable();
  }

  @override
  void listenNetworkHandler(bool hasInternet) {}

  Future<void> handleEvoApiError(ErrorUIModel? errorUIModel) async {
    switch (errorUIModel?.statusCode) {
      case CommonHttpClient.INVALID_TOKEN:
        await evoUtilFunction.clearDataOnTokenInvalid();
        if (!hasListenAuthorizationSessionExpired()) {
          await showSnackBarError(
              errorUIModel?.userMessage ?? getMessageByErrorCode(errorUIModel?.statusCode));
        }
        break;
      default:
        await showSnackBarError(
            errorUIModel?.userMessage ?? getMessageByErrorCode(errorUIModel?.statusCode));
        break;
    }
  }

  Future<void> showSnackBar(
    String message, {
    SnackBarType typeSnackBar = SnackBarType.success,
    int? duration,
    String? description,
    double? marginBottomRatio,
  }) async {
    duration ??= SnackBarDuration.short.value;
    await getIt.get<EvoSnackBar>().show(
          message,
          typeSnackBar: typeSnackBar,
          durationInMilliSec: duration,
          description: description,
          marginBottomRatio: marginBottomRatio,
        );
  }

  Future<void> showSnackBarError(
    String message, {
    String? description,
    double? marginBottomRatio,
  }) async {
    await showSnackBar(
      message,
      typeSnackBar: SnackBarType.error,
      duration: SnackBarDuration.short.value,
      description: description,
      marginBottomRatio: marginBottomRatio,
    );
  }

  Future<void> showSnackBarWarning(
    String message, {
    String? description,
    double? marginBottomRatio,
  }) async {
    await showSnackBar(
      message,
      typeSnackBar: SnackBarType.warning,
      duration: SnackBarDuration.short.value,
      description: description,
      marginBottomRatio: marginBottomRatio,
    );
  }

  Future<void> showSnackBarNeutral(
    String message, {
    String? description,
    double? marginBottomRatio,
  }) async {
    await showSnackBar(
      message,
      typeSnackBar: SnackBarType.neutral,
      duration: SnackBarDuration.short.value,
      description: description,
      marginBottomRatio: marginBottomRatio,
    );
  }

  void updateUserLoginStatus(bool isLogin) {
    final AppState appState = getIt.get<AppState>();
    appState.isUserLogIn = isLogin;
  }

  Future<bool> isNewDevice() async {
    final EvoLocalStorageHelper evoLocalStorageHelper = getIt.get<EvoLocalStorageHelper>();
    return await evoLocalStorageHelper.isNewDevice();
  }

  /// If you do not want to listen Authorization SessionExpired
  /// Should Override method and return false
  /// ```dart
  ///   @override
  ///   bool hasListenAuthorizationSessionExpired() => false;
  /// ```
  bool hasListenAuthorizationSessionExpired() => true;

  @visibleForTesting
  void initAuthorizationSessionExpiredSubscriptionNeed() {
    if (hasListenAuthorizationSessionExpired()) {
      authorizationSessionExpiredSubscription = _authorizationSessionExpiredHandler
          .getStreamSubscription()
          .listen(_onHandleAuthorizationSessionExpired);
    }
  }

  void _onHandleAuthorizationSessionExpired(UnauthorizedSessionState state) {
    if (!mounted) {
      return;
    }
    switch (state) {
      case UnauthorizedSessionState.forcedLogout:
        showForceLogoutPopIfNeed();
        break;
      case UnauthorizedSessionState.invalidToken:
      case UnauthorizedSessionState.unknown:
        showAuthorizationSessionTimeoutPopupIfNeed();
        break;
    }
  }

  @visibleForTesting
  void cancelAuthorizationSessionExpiredSubscriptionIfNeed() {
    if (hasListenAuthorizationSessionExpired()) {
      authorizationSessionExpiredSubscription?.cancel();
    }
  }

  @visibleForTesting
  void showAuthorizationSessionTimeoutPopupIfNeed() {
    if (_authorizationSessionExpiredPopup.checkCanShowPopup()) {
      _authorizationSessionExpiredPopup.show();
    }
  }

  @visibleForTesting
  void showForceLogoutPopIfNeed() {
    if (_forceLogoutPopup.checkCanShowPopup()) {
      _forceLogoutPopup.show();
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    appState.appLifecycleState = state;
    super.didChangeAppLifecycleState(state);
  }
}
