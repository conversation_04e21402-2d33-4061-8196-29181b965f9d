import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../model/challenge_success_model.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/functions.dart';
import '../../../util/screen_util.dart';
import '../../../util/validator/username_validator.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../../../widget/banners/info_banner_widget.dart';
import '../../../widget/buttons.dart';
import '../../../widget/evo_text_field.dart';
import 'create_username_cubit.dart';

class CreateUsernameScreenArgs extends PageBaseArg {
  final ChallengeSuccessCallback onPopSuccess;
  final String? sessionToken;

  CreateUsernameScreenArgs({
    required this.sessionToken,
    required this.onPopSuccess,
  });
}

class CreateUsernameScreen extends PageBase {
  final ChallengeSuccessCallback onPopSuccess;
  final String? sessionToken;

  static Future<void> pushNamed({
    required ChallengeSuccessCallback onPopSuccess,
    required String? sessionToken,
  }) async {
    return navigatorContext?.pushNamed(
      Screen.createUsernameScreen.name,
      extra: CreateUsernameScreenArgs(
        onPopSuccess: onPopSuccess,
        sessionToken: sessionToken,
      ),
    );
  }

  const CreateUsernameScreen({
    required this.sessionToken,
    required this.onPopSuccess,
    super.key,
  });

  @override
  State<CreateUsernameScreen> createState() => CreateUsernameScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.createUsernameScreen.routeName);
}

@visibleForTesting
class CreateUsernameScreenState extends EvoPageStateBase<CreateUsernameScreen> {
  late CreateUsernameCubit _cubit;

  /// _phoneController & _phoneFocusNode will be disposed inside of EvoTextField widget
  final TextEditingController _usernameFieldController = TextEditingController();
  final FocusNode _usernameFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();

    _cubit = context.read<CreateUsernameCubit?>() ??
        CreateUsernameCubit(
          authRepo: getIt.get<AuthenticationRepo>(),
          usernameValidator: UsernameValidator(),
        );
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<CreateUsernameCubit>(
      create: (BuildContext context) => _cubit,
      child: BlocListener<CreateUsernameCubit, CreateUsernameState>(
        listener: (BuildContext context, CreateUsernameState state) {
          _handleUsernameState(state);
        },
        child: Scaffold(
          appBar: EvoAppBar(),
          resizeToAvoidBottomInset: false,
          body: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Text(
                    EvoStrings.activeAccountCreateUsernameTitle,
                    style: evoTextStyles.semibold(TextSize.h3, color: evoColors.grayText),
                  ),
                ),
                EvoDimension.space4,
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Text(
                    EvoStrings.activeAccountCreateUsernameDesc,
                    style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
                  ),
                ),
                _buildUsernameField(),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: const InfoBannerWidget(
                    info: <String>[
                      EvoStrings.usernameGuide1,
                      EvoStrings.usernameGuide2,
                      EvoStrings.usernameGuide3,
                      EvoStrings.usernameGuide4,
                    ],
                  ),
                ),
                const Spacer(),
                _buildSubmitButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleUsernameState(CreateUsernameState state) {
    if (state is CreateUsernameStateLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }
    evoUtilFunction.hideHudLoading();

    switch (state) {
      case CreateUsernameStateSuccess():
        widget.onPopSuccess(ChallengeSuccessModel(entity: state.entity));
      case CreateUsernameInvalidToken():
        evoDialogFunction.showDialogSessionTokenExpired(
          type: SessionDialogType.activateAccount,
        );
        return;
      case CreateUsernameFailed():
        handleEvoApiError(state.error);
        return;
      default:
        return;
    }
  }

  Widget _buildSubmitButton() {
    return Padding(
      padding: EdgeInsets.only(
        left: EvoDimension.screenHorizontalPadding,
        right: EvoDimension.screenHorizontalPadding,
        bottom: EvoDimension.screenBottomPadding,
      ),
      child: BlocBuilder<CreateUsernameCubit, CreateUsernameState>(
        buildWhen: (_, CreateUsernameState state) => state is ChangeUsernameState,
        builder: (_, CreateUsernameState state) {
          final bool isEnabled = state is ChangeUsernameState && state.username.isNotEmpty;
          return PrimaryButton(
            text: EvoStrings.ctaSubmit,
            onTap: isEnabled ? _onTapSubmit : null,
          );
        },
      ),
    );
  }

  void _onTapSubmit() {
    final String username = _usernameFieldController.text;
    // To not submit when user presses the action button on the keyboard
    if (username.isEmpty) {
      return;
    }

    FocusManager.instance.primaryFocus?.unfocus();
    _cubit.submit(
      username: username,
      sessionToken: widget.sessionToken,
    );
  }

  Widget _buildUsernameField() {
    return BlocBuilder<CreateUsernameCubit, CreateUsernameState>(
      builder: (
        BuildContext context,
        CreateUsernameState state,
      ) {
        EdgeInsets padding = EdgeInsets.all(
          EvoDimension.screenHorizontalPaddingWithTextField,
        );
        final String? error = switch (state) {
          CreateUsernameInvalidUsername(error: final ErrorUIModel error) ||
          CreateUsernameRequestError(error: final ErrorUIModel error) =>
            error.userMessage,
          _ => null,
        };

        if (error != null) {
          // The bottom element is now the error text, so bottom padding is set back to 16px
          padding = padding.copyWith(bottom: 16.w);
        }

        return Padding(
          padding: padding,
          child: EvoTextField(
            focusNode: _usernameFocusNode,
            textEditingController: _usernameFieldController,
            maxLines: 1,
            errMessage: error,
            hintText: EvoStrings.usernameLabel,
            keyboardType: TextInputType.text,
            onSubmitted: (_) => _onTapSubmit(),
            onChanged: _cubit.updateUsername,
          ),
        );
      },
    );
  }
}
