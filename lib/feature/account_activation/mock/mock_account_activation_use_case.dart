enum MockAccountActivationUseCase {
  getVerifyOtpChallengeType('get_account_activation_verify_otp_challenge_type.json'),
  getVerifySelfieChallengeType('get_account_activation_verify_selfie_challenge_type.json'),
  getCreateUsernameChallengeType('get_account_activation_create_username_challenge_type.json'),
  getCreateUsernameSuccess('get_account_activation_create_user_name_success.json'),
  getCreateUsernameBadRequest('get_account_activation_create_user_name_bad_request.json'),
  getBiometricTokenChallengeType('get_account_activation_biometric_token_challenge_type.json'),
  getActivateCardChallengeType('get_account_activation_activate_card_challenge_type.json'),
  getVerifyCardOtpChallengeType('get_account_activation_verify_card_otp_challenge_type.json'),
  getCreateUsernameTypeUserNameExists('get_account_activation_create_user_name_type_user_name_exists.json'),

  /// Limit exceeded error for verify-selfie step shown in input-mobile-number screen
  inputMobileNumberErrorLimitExceededForVerifySelfie(
      'input_mobile_number_error_limit_exceeded_for_verify_selfie.json'),

  /// A special case when status code is success but verdict is an error
  verifySelfieChallengeTypeSuccessWithError('verify_selfie_challenge_type_success_with_error.json'),

  /// Create Pin
  getCreatePinSuccess('get_account_activation_create_pin_success.json'),
  getCreatePinBadRequest('get_account_activation_create_pin_bad_request.json'),

  /// Verify Email
  getVerifyEmailChallengeType('get_account_activation_verify_email_challenge_type.json'),
  getVerifyEmailChallengeTypeEmailExists(
      'get_account_activation_verify_email_challenge_type_email_exists.json'),
  getVerifyEmailOtpChallengeType('get_account_activation_verify_email_otp_challenge_type.json'),
  getVerifyEmailErrorDuplicate('get_account_activation_verify_email_error_duplicate.json'),

  /// Enable Biometric
  getEnableBiometricSuccess('get_account_activation_enable_biometric_success.json'),
  getSkipEnableBiometricSuccess('get_account_activation_skip_enable_biometric_success.json'),

  /// Activate Card
  getActivateCardOtpChallengeType('get_account_activation_activate_card_otp_challenge_type.json'),
  getActivateCardOtpThirdPartyError(
      'get_account_activation_activate_card_otp_third_party_error.json'),

  /// None challenge type
  getNoneChallengeType('get_none_challenge_type.json');

  final String value;

  const MockAccountActivationUseCase(this.value);
}

String getMockAccountActivationFileNameByCase(MockAccountActivationUseCase mockCase) {
  return mockCase.value;
}
