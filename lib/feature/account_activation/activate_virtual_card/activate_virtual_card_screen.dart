// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../model/challenge_success_model.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/functions.dart';
import '../../../util/screen_util.dart';
import '../../../widget/buttons.dart';
import '../../../widget/no_app_bar_wrapper.dart';
import '../../welcome/welcome_screen.dart';
import 'cubit/activate_virtual_card_cubit.dart';

class ActivateVirtualCardArg extends PageBaseArg {
  final String? sessionToken;
  final ChallengeSuccessCallback onPopSuccess;

  ActivateVirtualCardArg({
    required this.sessionToken,
    required this.onPopSuccess,
  });
}

class ActivateVirtualCardScreen extends PageBase {
  final String? sessionToken;
  final ChallengeSuccessCallback onPopSuccess;

  static Future<void> pushNamed({
    required String? sessionToken,
    required ChallengeSuccessCallback onPopSuccess,
  }) async {
    return navigatorContext?.pushNamed(
      Screen.activateVirtualCardScreen.name,
      extra: ActivateVirtualCardArg(
        sessionToken: sessionToken,
        onPopSuccess: onPopSuccess,
      ),
    );
  }

  const ActivateVirtualCardScreen({
    required this.sessionToken,
    required this.onPopSuccess,
    super.key,
  });

  @override
  State<ActivateVirtualCardScreen> createState() => _ActivateVirtualCardScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.activateVirtualCardScreen.routeName);
}

class _ActivateVirtualCardScreenState extends EvoPageStateBase<ActivateVirtualCardScreen> {
  late final ActivateVirtualCardCubit _cubit = context.read<ActivateVirtualCardCubit?>() ??
      ActivateVirtualCardCubit(authRepo: getIt<AuthenticationRepo>());

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<ActivateVirtualCardCubit>(
      create: (_) => _cubit,
      child: BlocListener<ActivateVirtualCardCubit, ActivateVirtualCardState>(
        listener: (_, ActivateVirtualCardState state) => _listenActivateVirtualCardState(state),
        child: NoAppBarWrapper(
          child: PopScope(
            canPop: false,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  _buildTitle(),
                  EvoDimension.space4,
                  _buildDesc(),
                  SizedBox(height: 40.w),
                  _buildCardImage(),
                  const Spacer(),
                  _buildActivateButton(),
                  EvoDimension.space8,
                  _buildLaterButton(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _listenActivateVirtualCardState(ActivateVirtualCardState state) {
    if (state is ActivateVirtualCardLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }
    evoUtilFunction.hideHudLoading();

    if (state is ActivateVirtualCardSuccess) {
      widget.onPopSuccess(ChallengeSuccessModel(
        entity: state.entity,
        isCardActivated: state.isCardActivated,
        resendData: ResendDataModel(
          sessionToken: widget.sessionToken,
        ),
      ));
      return;
    }
    if (state is ActivateVirtualCardFailureTokenExpired) {
      evoDialogFunction.showDialogSessionTokenExpired(
        type: SessionDialogType.activateAccount,
        onClickPositive: () {
          navigatorContext?.popUntilNamed(Screen.mobileNumberCheckScreen.name);
        },
      );
      return;
    }
    if (state is ActivateVirtualCardFailureLimitExceeded) {
      evoDialogFunction.showDialogErrorLimitExceeded(
        type: SessionDialogType.activateAccount,
        content: state.error.userMessage,
      );
      return;
    }
    if (state is ActivateVirtualCardFailure) {
      handleEvoApiError(state.error);
      return;
    }
  }

  Widget _buildCardImage() {
    return imageProvider.asset(
      EvoImages.imgActivateVirtualCard,
      height: 163.w,
      fit: BoxFit.contain,
    );
  }

  Widget _buildTitle() {
    return Text(
      EvoStrings.activateVirtualCardTitle,
      style: evoTextStyles.semibold(TextSize.h3),
    );
  }

  Widget _buildDesc() {
    return Text(
      EvoStrings.activateVirtualCardDesc,
      style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
    );
  }

  Widget _buildActivateButton() {
    return PrimaryButton(
      text: EvoStrings.ctaActivateVirtualCard,
      onTap: () {
        _cubit.activate(sessionToken: widget.sessionToken, skip: false);
      },
    );
  }

  Widget _buildLaterButton() {
    return Padding(
      padding: EdgeInsets.only(bottom: EvoDimension.screenBottomPadding),
      child: TertiaryButton(
        text: EvoStrings.ctaActivateLater,
        onTap: () {
          _cubit.activate(sessionToken: widget.sessionToken, skip: true);
        },
      ),
    );
  }
}
