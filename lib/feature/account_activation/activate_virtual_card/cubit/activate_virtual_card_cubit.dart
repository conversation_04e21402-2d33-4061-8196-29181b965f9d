// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/request/activate_account_request.dart';
import '../../../../data/response/account_activation_entity.dart';
import '../../mock/mock_account_activation_use_case.dart';
part 'activate_virtual_card_state.dart';

class ActivateVirtualCardCubit extends CommonCubit<ActivateVirtualCardState> {
  final AuthenticationRepo authRepo;

  ActivateVirtualCardCubit({required this.authRepo}) : super(ActivateVirtualCardInitial());

  Future<void> activate({required String? sessionToken, required bool skip}) async {
    emit(ActivateVirtualCardLoading());

    final AccountActivationEntity entity = await authRepo.activateAccount(
      request: ActivateAccountActivateCardRequest(
        skip: skip,
        sessionToken: sessionToken,
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockAccountActivationFileNameByCase(
          MockAccountActivationUseCase.getVerifyCardOtpChallengeType,
        ),
      ),
    );

    switch (entity.statusCode) {
      case CommonHttpClient.SUCCESS:
        emit(ActivateVirtualCardSuccess(entity: entity, isCardActivated: !skip));
      case CommonHttpClient.INVALID_TOKEN:
        emit(ActivateVirtualCardFailureTokenExpired());
      case CommonHttpClient.LIMIT_EXCEEDED:
      case CommonHttpClient.LOCKED_RESOURCE:
        final ErrorUIModel error = ErrorUIModel.fromEntity(entity);
        emit(ActivateVirtualCardFailureLimitExceeded(error: error));
      default:
        final ErrorUIModel error = ErrorUIModel.fromEntity(entity);
        emit(ActivateVirtualCardFailure(error: error));
    }
  }
}
