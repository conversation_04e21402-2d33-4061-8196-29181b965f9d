// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

part of 'activate_virtual_card_cubit.dart';

sealed class ActivateVirtualCardState implements BlocState {}

class ActivateVirtualCardInitial extends ActivateVirtualCardState {}

class ActivateVirtualCardLoading extends ActivateVirtualCardState {}

class ActivateVirtualCardSuccess extends ActivateVirtualCardState {
  final BaseEntity entity;

  final bool isCardActivated;

  ActivateVirtualCardSuccess({required this.entity, required this.isCardActivated});
}

class ActivateVirtualCardFailure extends ActivateVirtualCardState {
  final ErrorUIModel error;

  ActivateVirtualCardFailure({required this.error});
}

class ActivateVirtualCardFailureTokenExpired extends ActivateVirtualCardState {}

class ActivateVirtualCardFailureLimitExceeded extends ActivateVirtualCardFailure {
  ActivateVirtualCardFailureLimitExceeded({required super.error});
}
