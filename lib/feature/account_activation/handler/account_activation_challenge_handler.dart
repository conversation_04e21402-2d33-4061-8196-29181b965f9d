import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../data/response/account_activation_entity.dart';
import '../../../model/challenge_success_model.dart';
import 'account_activation_ui_handler.dart';

class AccountActivationChallengeHandler {
  @visibleForTesting
  late AccountActivationUiHandler uiHandler;

  AccountActivationChallengeHandler({
    required void Function(ErrorUIModel? uiModel) onError,
  }) {
    uiHandler = AccountActivationUiHandler(
      onError: onError,
      onSuccess: nextChallenge,
    );
  }

  void nextChallenge(ChallengeSuccessModel model) {
    if (model.entity is! AccountActivationEntity) {
      commonLog('Entity ${model.entity.runtimeType} must be an instance of AccountActivationEntity',
          methodName: 'nextChallenge');
      uiHandler.onError(ErrorUIModel());
      return;
    }

    final AccountActivationEntity entity = model.entity as AccountActivationEntity;
    final AccountActivationType type = AccountActivationType.fromString(entity.challengeType);
    switch (type) {
      case AccountActivationType.verifyOTP:
        uiHandler.verifyOtp(
          entity: entity,
          resendData: model.resendData,
        );
      case AccountActivationType.verifySelfie:
        uiHandler.verifySelfie(
          entity: entity,
        );
      case AccountActivationType.createUsername:
        uiHandler.createUsername(
          entity: entity,
        );
        return;
      case AccountActivationType.createPin:
        uiHandler.createPin(
          entity: entity,
        );
        return;
      case AccountActivationType.verifyEmail:
        uiHandler.verifyEmail(entity: entity);
      case AccountActivationType.verifyEmailOtp:
        uiHandler.verifyEmailOtp(entity: entity, resendData: model.resendData);
      case AccountActivationType.biometricToken:
        uiHandler.enableBiometric(entity: entity);
      case AccountActivationType.activateCard:
        uiHandler.activateCard(entity: entity);
      case AccountActivationType.activateCardVerifyOtp:
        uiHandler.activateCardVerifyOtp(entity: entity, resendData: model.resendData);
      case AccountActivationType.none:
        uiHandler.activateAccountSuccess(isCardActivated: model.isCardActivated);
        return;
      case AccountActivationType.unknown:
        commonLog('unknown challenge:$type', methodName: 'nextChallenge');
        uiHandler.onError(ErrorUIModel());
        return;
    }
  }
}
