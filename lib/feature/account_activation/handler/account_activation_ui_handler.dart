import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/response/account_activation_entity.dart';
import '../../../model/challenge_success_model.dart';
import '../../../resources/resources.dart';
import '../../ekyc/intro/face_capture_check_screen.dart';
import '../../error_screen/common_error_screen.dart';
import '../../main_screen/card_page/activate_card_success_screen.dart';
import '../../main_screen/card_page/widgets/card_widget.dart';
import '../../main_screen/main_screen.dart';
import '../../pin/change_pin/create_new_pin_flow.dart';
import '../../pin/change_pin/create_new_pin_screen.dart';
import '../../verify_otp/cubit/verify_otp_cubit.dart';
import '../../verify_otp/verify_otp_page.dart';
import '../../biometric/activate_biometric/activate_biometric_page.dart';
import '../activate_virtual_card/activate_virtual_card_screen.dart';
import '../activation_status/activation_status_screen.dart';
import '../create_username/create_username_screen.dart';
import '../verify_email/input_email_screen.dart';
import '../verify_email/verify_email_screen.dart';

enum AccountActivationType {
  verifyOTP(verifyOTPValue),
  verifySelfie(verifySelfieValue),
  createUsername(createUsernameValue),
  createPin(createPinValue),
  verifyEmail(verifyEmailValue),
  verifyEmailOtp(verifyEmailOtpValue),
  // optional step
  biometricToken(biometricTokenValue),
  activateCard(activateCardValue),
  activateCardVerifyOtp(activateCardVerifyOtpValue),
  // entry / end step
  none(noneValue),
  // fallback step
  unknown(unknownValue);

  final String value;

  const AccountActivationType(this.value);

  static const String verifyOTPValue = 'verify_otp';
  static const String verifySelfieValue = 'face_auth';
  static const String createUsernameValue = 'create_user_name';
  static const String createPinValue = 'create_pin';
  static const String verifyEmailValue = 'verify_email';
  static const String verifyEmailOtpValue = 'verify_email_otp';
  static const String activateCardValue = 'activate_card';
  static const String biometricTokenValue = 'biometric_token';
  static const String activateCardVerifyOtpValue = 'activate_card_verify_otp';
  static const String noneValue = 'none';
  static const String unknownValue = 'unknown';

  static AccountActivationType fromString(String? value) {
    switch (value) {
      case verifyOTPValue:
        return AccountActivationType.verifyOTP;
      case verifySelfieValue:
        return AccountActivationType.verifySelfie;
      case createUsernameValue:
        return AccountActivationType.createUsername;
      case createPinValue:
        return AccountActivationType.createPin;
      case verifyEmailValue:
        return AccountActivationType.verifyEmail;
      case verifyEmailOtpValue:
        return AccountActivationType.verifyEmailOtp;
      case biometricTokenValue:
        return AccountActivationType.biometricToken;
      case activateCardValue:
        return AccountActivationType.activateCard;
      case activateCardVerifyOtpValue:
        return AccountActivationType.activateCardVerifyOtp;
      case noneValue:
        return AccountActivationType.none;
      default:
        return AccountActivationType.unknown;
    }
  }
}

class AccountActivationUiHandler {
  void Function(ErrorUIModel? uiModel) onError;
  ChallengeSuccessCallback onSuccess;

  AccountActivationUiHandler({
    required this.onError,
    required this.onSuccess,
  });

  void verifyOtp({
    required AccountActivationEntity entity,
    required ResendDataModel? resendData,
  }) {
    VerifyOtpPage.pushNamed(
      contactInfo: resendData?.contactInfo,
      otpResendSecs: entity.otpResendSecs,
      otpValiditySecs: entity.otpValiditySecs,
      verifyOtpType: VerifyOtpType.activateAccount,
      sessionToken: entity.sessionToken,
      onPopSuccess: _verifyOtpPopSuccess,
    );
  }

  void verifySelfie({required AccountActivationEntity entity}) {
    FaceCaptureCheckScreen.pushNamed(
      sessionToken: entity.sessionToken,
      onPopSuccess: onSuccess,
    );
  }

  void createUsername({required AccountActivationEntity entity}) {
    CreateUsernameScreen.pushNamed(
      sessionToken: entity.sessionToken,
      onPopSuccess: onSuccess,
    );
  }

  void createPin({required AccountActivationEntity entity}) {
    CreateNewPinScreen.pushNamed(
      flow: CreateNewPinFlow.createPin,
      sessionToken: entity.sessionToken,
      onSuccess: onSuccess,
    );
  }

  void verifyEmail({required AccountActivationEntity entity}) {
    if (entity.verdict == AccountActivationEntity.verdictSuccessEmailExists) {
      InputEmailScreen.pushNamed(
        isDuplicate: true,
        email: entity.email,
        duplicateErrorMessage: entity.userMessage,
        sessionToken: entity.sessionToken,
        onPopSuccess: onSuccess,
      );
    } else {
      VerifyEmailScreen.pushNamed(
        email: entity.email,
        sessionToken: entity.sessionToken,
        onPopSuccess: onSuccess,
      );
    }
  }

  void verifyEmailOtp({
    required AccountActivationEntity entity,
    required ResendDataModel? resendData,
  }) {
    VerifyOtpPage.pushNamed(
      contactInfo: resendData?.contactInfo,
      resendSessionToken: resendData?.sessionToken,
      verifyOtpType: VerifyOtpType.email,
      otpValiditySecs: entity.otpValiditySecs,
      otpResendSecs: entity.otpResendSecs,
      sessionToken: entity.sessionToken,
      onPopSuccess: _verifyOtpPopSuccess,
    );
  }

  void enableBiometric({required AccountActivationEntity entity}) {
    ActivateBiometricScreen.pushNamed(
      sessionToken: entity.sessionToken,
      onSuccess: onSuccess,
    );
  }

  void activateCard({required AccountActivationEntity entity}) {
    ActivateVirtualCardScreen.pushNamed(
      sessionToken: entity.sessionToken,
      onPopSuccess: onSuccess,
    );
  }

  void _verifyOtpPopSuccess(VerifyOtpState state) {
    if (state is VerifyEmailOtpDuplicateEmailError) {
      InputEmailScreen.pushReplacementNamed(
        isDuplicate: true,
        email: state.email,
        duplicateErrorMessage: state.error.userMessage,
        sessionToken: state.sessionToken,
        onPopSuccess: onSuccess,
      );
      return;
    }
    if (state is VerifyOtpSuccess) {
      final ActivationStatus? status = ActivationStatus.fromVerdict(state.uiModel.verdict);
      if (status != null) {
        ActivationStatusScreen.pushNamed(status: status);
        return;
      }

      final AccountActivationEntity nextEntity = AccountActivationEntity(
        challengeType: state.uiModel.challengeType,
        sessionToken: state.uiModel.sessionToken,
      );
      onSuccess(ChallengeSuccessModel(entity: nextEntity));
      return;
    }
    if (state is VerifyOtpFailed) {
      onError(state.error);
      return;
    }
  }

  void activateAccountSuccess({bool? isCardActivated}) {
    MainScreen.goNamed(isLoggedIn: true, isCardActivated: isCardActivated);
  }

  void activateCardVerifyOtp(
      {required AccountActivationEntity entity, ResendDataModel? resendData}) {
    VerifyOtpPage.pushNamed(
      contactInfo: null,
      resendSessionToken: resendData?.sessionToken,
      verifyOtpType: VerifyOtpType.activateCard,
      otpValiditySecs: entity.otpValiditySecs,
      otpResendSecs: entity.otpResendSecs,
      sessionToken: entity.sessionToken,
      onPopSuccess: _verifyActivateCardOtpOnPopSuccess,
    );
  }

  void _verifyActivateCardOtpOnPopSuccess(VerifyOtpState state) {
    if (state is VerifyOtpSuccess) {
      final AccountActivationEntity nextEntity = AccountActivationEntity(
        challengeType: state.uiModel.challengeType,
        sessionToken: state.uiModel.sessionToken,
      );

      ActivateCardSuccessScreen.pushNamed(
          cardType: CardType.virtual,
          onPopSuccess: () {
            onSuccess(ChallengeSuccessModel(
              entity: nextEntity,
              isCardActivated: true,
            ));
          });
      return;
    }

    if (state is VerifyOtpThirdPartyError) {
      CommonErrorScreen.pushReplacementNamed(
        title: EvoStrings.activateAccountActivateCardErrorTitle,
        description: EvoStrings.activateAccountActivateCardErrorDesc,
        buttonText: EvoStrings.ctaProceed,
        onTap: () {
          /// As activate card failed due to m2p error
          /// proceed to none challenged as completed the activate card flow
          onSuccess(ChallengeSuccessModel(
            entity: AccountActivationEntity(
              challengeType: AccountActivationType.noneValue,
            ),
            isCardActivated: false,
          ));
        },
      );
      return;
    }

    if (state is VerifyOtpFailed) {
      onError(state.error);
      return;
    }
  }
}
