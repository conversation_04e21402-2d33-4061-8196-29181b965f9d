// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/functions.dart';
import 'cubit/verify_email_cubit.dart';
import 'cubit/verify_email_state.dart';

mixin VerifyEmailMixin<T extends PageBase> on EvoPageStateBase<T> {
  @protected
  late final VerifyEmailCubit verifyEmailCubit = context.read<VerifyEmailCubit?>() ??
      VerifyEmailCubit(authRepo: getIt.get<AuthenticationRepo>());

  void _listenVerifyEmailState(VerifyEmailState state) {
    if (state is VerifyEmailLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }
    evoUtilFunction.hideHudLoading();

    if (state is VerifyEmailSuccess) {
      onVerifyEmailSuccess(state.entity);
      return;
    }
    if (state is VerifyEmailFailureTokenExpired) {
      evoDialogFunction.showDialogSessionTokenExpired(type: SessionDialogType.activateAccount);
      return;
    }
    if (state is VerifyEmailFailureLimitExceeded) {
      evoDialogFunction.showDialogErrorLimitExceeded(
        type: SessionDialogType.activateAccount,
        content: state.error.userMessage,
      );
      return;
    }
    if (state is VerifyEmailFailureDuplicate) {
      onDuplicateEmailError(state.error);
      return;
    }
    if (state is VerifyEmailFailure) {
      handleEvoApiError(state.error);
      return;
    }
  }

  @protected
  void onVerifyEmailSuccess(BaseEntity entity);

  @protected
  void onDuplicateEmailError(ErrorUIModel error);

  Widget provideVerifyEmailCubit({required Widget child}) {
    return BlocProvider<VerifyEmailCubit>(
      create: (_) => verifyEmailCubit,
      child: BlocListener<VerifyEmailCubit, VerifyEmailState>(
        listener: (_, VerifyEmailState state) => _listenVerifyEmailState(state),
        child: child,
      ),
    );
  }
}
