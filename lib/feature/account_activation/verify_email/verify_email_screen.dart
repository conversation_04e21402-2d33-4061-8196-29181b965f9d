// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../model/challenge_success_model.dart';
import '../../../resources/resources.dart';
import '../../../widget/buttons.dart';
import '../../../widget/evo_text_field.dart';
import '../../../widget/no_app_bar_wrapper.dart';
import 'input_email_screen.dart';
import 'verify_email_mixin.dart';

class VerifyEmailArg extends PageBaseArg {
  final String? email;

  final String? sessionToken;

  final ChallengeSuccessCallback onPopSuccess;

  VerifyEmailArg({
    required this.onPopSuccess,
    this.email,
    this.sessionToken,
  });
}

class VerifyEmailScreen extends PageBase {
  final String? email;

  final String? sessionToken;

  final ChallengeSuccessCallback onPopSuccess;

  const VerifyEmailScreen({
    required this.onPopSuccess,
    this.email,
    this.sessionToken,
    super.key,
  });

  @override
  State<VerifyEmailScreen> createState() => _VerifyEmailScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.verifyEmailScreen.routeName);

  static void pushNamed({
    required ChallengeSuccessCallback onPopSuccess,
    String? email,
    String? sessionToken,
  }) {
    navigatorContext?.pushNamed(
      Screen.verifyEmailScreen.name,
      extra: VerifyEmailArg(
        email: email,
        sessionToken: sessionToken,
        onPopSuccess: onPopSuccess,
      ),
    );
  }
}

class _VerifyEmailScreenState extends EvoPageStateBase<VerifyEmailScreen>
    with VerifyEmailMixin<VerifyEmailScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return NoAppBarWrapper(
      child: PopScope(
        canPop: false,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            _buildTitle(),
            EvoDimension.space4,
            _buildDesc(),
            EvoDimension.space16,
            _buildSubtitle(),
            _buildEmailField(),
            const Spacer(),
            _buildSendCodeBtn(),
            EvoDimension.space8,
            _buildChangeEmailBtn(),
          ],
        ),
      ),
    );
  }

  Padding _buildTitle() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
      child: Text(
        EvoStrings.verifyEmailTitle,
        style: evoTextStyles.semibold(TextSize.h3),
      ),
    );
  }

  Padding _buildDesc() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
      child: Text(
        EvoStrings.verifyEmailDesc,
        style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
      ),
    );
  }

  Padding _buildSubtitle() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
      child: Text(
        EvoStrings.verifyEmailSubtitle,
        style: evoTextStyles.medium(TextSize.h5),
      ),
    );
  }

  Padding _buildEmailField() {
    return Padding(
      padding: EdgeInsets.all(EvoDimension.screenHorizontalPaddingWithTextField),
      child: EvoTextField(isEnabled: false, hintText: widget.email),
    );
  }

  Padding _buildSendCodeBtn() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
      child: provideVerifyEmailCubit(
        child: PrimaryButton(
          text: EvoStrings.sendEmailCodeBtn,
          onTap: _sendVerificationCode,
        ),
      ),
    );
  }

  Padding _buildChangeEmailBtn() {
    return Padding(
      padding: EdgeInsets.only(
        left: EvoDimension.screenHorizontalPadding,
        right: EvoDimension.screenHorizontalPadding,
        bottom: EvoDimension.screenBottomPadding,
      ),
      child: TertiaryButton(
        text: EvoStrings.changeEmailBtn,
        onTap: _changeEmail,
      ),
    );
  }

  void _sendVerificationCode() {
    verifyEmailCubit.verify(email: widget.email, token: widget.sessionToken);
  }

  void _changeEmail() {
    InputEmailScreen.pushNamed(
      email: widget.email,
      sessionToken: widget.sessionToken,
      onPopSuccess: widget.onPopSuccess,
    );
  }

  @override
  void onVerifyEmailSuccess(BaseEntity entity) {
    widget.onPopSuccess(ChallengeSuccessModel(
      entity: entity,
      resendData: ResendDataModel(
        contactInfo: widget.email,
        sessionToken: widget.sessionToken,
      ),
    ));
  }

  @override
  void onDuplicateEmailError(ErrorUIModel error) {
    InputEmailScreen.pushReplacementNamed(
      email: widget.email,
      sessionToken: widget.sessionToken,
      isDuplicate: true,
      duplicateErrorMessage: error.userMessage,
      onPopSuccess: widget.onPopSuccess,
    );
  }
}
