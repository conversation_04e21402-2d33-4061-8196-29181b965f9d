// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/functions.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../model/challenge_success_model.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../../../widget/buttons.dart';
import '../../../widget/evo_text_field.dart';
import '../../../widget/no_app_bar_wrapper.dart';
import 'cubit/validate_email_cubit.dart';
import 'cubit/validate_email_state.dart';
import 'cubit/verify_email_cubit.dart';
import 'cubit/verify_email_state.dart';
import 'verify_email_mixin.dart';

class InputEmailArg extends PageBaseArg {
  final String? email;

  final bool? isDuplicate;

  final String? duplicateErrorMessage;

  final String? sessionToken;

  final ChallengeSuccessCallback onPopSuccess;

  InputEmailArg({
    required this.onPopSuccess,
    this.isDuplicate,
    this.duplicateErrorMessage,
    this.email,
    this.sessionToken,
  });
}

class InputEmailScreen extends PageBase {
  final String? email;

  /// If the DOP-registered email is duplicate or not
  final bool? isDuplicate;

  final String? duplicateErrorMessage;

  final String? sessionToken;

  final ChallengeSuccessCallback onPopSuccess;

  const InputEmailScreen({
    required this.onPopSuccess,
    this.email,
    this.isDuplicate,
    this.duplicateErrorMessage,
    this.sessionToken,
    super.key,
  });

  @override
  State<InputEmailScreen> createState() => _InputEmailScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.inputEmailScreen.routeName);

  static void pushNamed({
    required ChallengeSuccessCallback onPopSuccess,
    String? email,
    bool? isDuplicate,
    String? duplicateErrorMessage,
    String? sessionToken,
  }) {
    navigatorContext?.pushNamed(
      Screen.inputEmailScreen.name,
      extra: InputEmailArg(
        email: email,
        isDuplicate: isDuplicate,
        duplicateErrorMessage: duplicateErrorMessage,
        sessionToken: sessionToken,
        onPopSuccess: onPopSuccess,
      ),
    );
  }

  static void pushReplacementNamed({
    required ChallengeSuccessCallback onPopSuccess,
    String? email,
    bool? isDuplicate,
    String? duplicateErrorMessage,
    String? sessionToken,
  }) {
    navigatorContext?.pushReplacementNamed(
      Screen.inputEmailScreen.name,
      extra: InputEmailArg(
        email: email,
        isDuplicate: isDuplicate,
        duplicateErrorMessage: duplicateErrorMessage,
        sessionToken: sessionToken,
        onPopSuccess: onPopSuccess,
      ),
    );
  }
}

class _InputEmailScreenState extends EvoPageStateBase<InputEmailScreen>
    with VerifyEmailMixin<InputEmailScreen> {
  late final ValidateEmailCubit _validateEmailCubit =
      context.read<ValidateEmailCubit?>() ?? ValidateEmailCubit();

  late final TextEditingController _controller = TextEditingController(text: widget.email);

  late final FocusNode _focusNode = FocusNode();

  late final bool _isDopEmailDuplicate = widget.isDuplicate == true;

  @override
  void initState() {
    super.initState();
    SchedulerBinding.instance.addPostFrameCallback((_) {
      if (_isDopEmailDuplicate) {
        verifyEmailCubit.setDuplicateState(error: widget.duplicateErrorMessage);
      } else {
        _validateEmailCubit.validate(widget.email);
      }
      commonUtilFunction.delayAndRequestFocus(_focusNode);
    });
  }

  @override
  void onVerifyEmailSuccess(BaseEntity entity) {
    widget.onPopSuccess(ChallengeSuccessModel(
      entity: entity,
      resendData: ResendDataModel(
        contactInfo: _controller.text,
        sessionToken: widget.sessionToken,
      ),
    ));
  }

  @override
  void onDuplicateEmailError(ErrorUIModel error) {
    // Ignored. Handled in the text field.
  }

  @override
  Widget getContentWidget(BuildContext context) {
    Widget content = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: <Widget>[
        _buildTitle(),
        if (_isDopEmailDuplicate) _buildDescription1(),
        if (_isDopEmailDuplicate) _buildDescription2(),
        _buildTextField(),
        const Spacer(),
        _buildSendCodeBtn(),
      ],
    );

    if (_isDopEmailDuplicate) {
      content = NoAppBarWrapper(
        resizeToAvoidBottomInset: false,
        child: PopScope(
          canPop: false,
          child: content,
        ),
      );
    } else {
      content = Scaffold(
        appBar: EvoAppBar(),
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: content,
        ),
      );
    }

    return provideVerifyEmailCubit(
      child: BlocProvider<ValidateEmailCubit>(
        create: (_) => _validateEmailCubit,
        child: content,
      ),
    );
  }

  Widget _buildTitle() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
      child: Text(
        _isDopEmailDuplicate ? EvoStrings.inputDuplicateEmailTitle : EvoStrings.inputEmailTitle,
        style: evoTextStyles.semibold(TextSize.h3, color: evoColors.grayText),
      ),
    );
  }

  Widget _buildDescription1() {
    return Padding(
      padding: EdgeInsets.only(
        top: 8.w,
        left: EvoDimension.screenHorizontalPadding,
        right: EvoDimension.screenHorizontalPadding,
      ),
      child: Text(
        EvoStrings.inputDuplicateEmailDescription,
        style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
      ),
    );
  }

  Widget _buildDescription2() {
    return Padding(
      padding: EdgeInsets.only(
        top: 20.w,
        left: EvoDimension.screenHorizontalPadding,
        right: EvoDimension.screenHorizontalPadding,
      ),
      child: RichText(
        textScaler: MediaQuery.of(context).textScaler,
        text: TextSpan(
          style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
          children: <InlineSpan>[
            TextSpan(text: EvoStrings.inputDuplicateEmailContactSupport1),
            TextSpan(
              text: EvoStrings.inputDuplicateEmailContactSupport2,
              recognizer: TapGestureRecognizer()..onTap = _openCustomerSupport,
              style: evoTextStyles
                  .regular(TextSize.base, color: evoColors.secondaryBase)
                  .copyWith(decoration: TextDecoration.underline),
            ),
            TextSpan(text: '.'),
          ],
        ),
      ),
    );
  }

  void _openCustomerSupport() {
    // TODO: Show customer support UI
  }

  Widget _buildTextField() {
    return Padding(
      padding: EdgeInsets.all(EvoDimension.screenHorizontalPaddingWithTextField),
      child: BlocSelector<VerifyEmailCubit, VerifyEmailState, String?>(
        selector: (VerifyEmailState state) {
          return state is VerifyEmailFailureDuplicate
              ? (state.error.userMessage ?? EvoStrings.errorDuplicateEmail)
              : null;
        },
        builder: (_, String? error) {
          return EvoTextField(
            textEditingController: _controller,
            focusNode: _focusNode,
            errMessage: error,
            keyboardType: TextInputType.emailAddress,
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => _verifyEmail(),
            onChanged: _onChanged,
          );
        },
      ),
    );
  }

  Widget _buildSendCodeBtn() {
    return Padding(
      padding: EdgeInsets.only(
        left: EvoDimension.screenHorizontalPadding,
        right: EvoDimension.screenHorizontalPadding,
        bottom: EvoDimension.screenBottomPadding,
      ),
      child: Builder(
        builder: (BuildContext context) {
          final bool isValidEmail = context.select<ValidateEmailCubit, bool>(_isValidEmail);
          final bool isDuplicateEmail = context.select<VerifyEmailCubit, bool>(_isDuplicateEmail);
          return PrimaryButton(
            text: EvoStrings.sendEmailCodeBtn2,
            onTap: isValidEmail && !isDuplicateEmail ? _verifyEmail : null,
          );
        },
      ),
    );
  }

  bool _isValidEmail(ValidateEmailCubit cubit) {
    final ValidateEmailState state = cubit.state;
    return state is ValidateEmailStatus && state.isValid;
  }

  bool _isDuplicateEmail(VerifyEmailCubit cubit) {
    return cubit.state is VerifyEmailFailureDuplicate;
  }

  void _verifyEmail() {
    final bool isValidEmail = _isValidEmail(_validateEmailCubit);
    final bool hasDuplicateEmail = _isDuplicateEmail(verifyEmailCubit);
    if (isValidEmail && !hasDuplicateEmail) {
      verifyEmailCubit.verify(email: _controller.text, token: widget.sessionToken);
    }
  }

  void _onChanged(String email) {
    verifyEmailCubit.reset();
    _validateEmailCubit.validate(email);
  }
}
