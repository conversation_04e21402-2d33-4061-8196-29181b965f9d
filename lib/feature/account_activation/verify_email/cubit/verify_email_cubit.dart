// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/request/activate_account_request.dart';
import '../../../../data/response/account_activation_entity.dart';
import '../../mock/mock_account_activation_use_case.dart';
import 'verify_email_state.dart';

class VerifyEmailCubit extends CommonCubit<VerifyEmailState> {
  final AuthenticationRepo authRepo;

  VerifyEmailCubit({required this.authRepo}) : super(VerifyEmailInitial());

  Future<void> verify({String? email, String? token}) async {
    emit(VerifyEmailLoading());

    final AccountActivationEntity entity = await authRepo.activateAccount(
      request: ActivateAccountVerifyEmailRequest(
        email: email,
        sessionToken: token,
      ),
      mockConfig: MockConfig(
        enable: false,
        statusCode: CommonHttpClient.DUPLICATE,
        fileName: getMockAccountActivationFileNameByCase(
          MockAccountActivationUseCase.getVerifyEmailErrorDuplicate,
        ),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(VerifyEmailSuccess(entity));
      return;
    }

    final ErrorUIModel error = ErrorUIModel.fromEntity(entity);
    if (entity.statusCode == CommonHttpClient.DUPLICATE &&
        entity.verdict == AccountActivationEntity.verdictDuplicate) {
      emit(VerifyEmailFailureDuplicate(error: error));
      return;
    }
    switch (entity.statusCode) {
      case CommonHttpClient.INVALID_TOKEN:
        emit(VerifyEmailFailureTokenExpired());
      case CommonHttpClient.LIMIT_EXCEEDED:
      case CommonHttpClient.LOCKED_RESOURCE:
        emit(VerifyEmailFailureLimitExceeded(error: error));
      default:
        emit(VerifyEmailFailure(error: error));
    }
  }

  void reset() {
    emit(VerifyEmailInitial());
  }

  void setDuplicateState({String? error}) {
    emit(VerifyEmailFailureDuplicate(error: ErrorUIModel(userMessage: error)));
  }
}
