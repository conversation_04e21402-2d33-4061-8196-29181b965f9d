// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

sealed class VerifyEmailState extends BlocState {}

class VerifyEmailInitial extends VerifyEmailState {}

class VerifyEmailLoading extends VerifyEmailState {}

class VerifyEmailSuccess extends VerifyEmailState {
  final BaseEntity entity;

  VerifyEmailSuccess(this.entity);
}

class VerifyEmailFailure extends VerifyEmailState {
  final ErrorUIModel error;

  VerifyEmailFailure({required this.error});
}

class VerifyEmailFailureTokenExpired extends VerifyEmailState {}

class VerifyEmailFailureLimitExceeded extends VerifyEmailFailure {
  VerifyEmailFailureLimitExceeded({required super.error});
}

class VerifyEmailFailureDuplicate extends VerifyEmailFailure {
  VerifyEmailFailureDuplicate({required super.error});
}
