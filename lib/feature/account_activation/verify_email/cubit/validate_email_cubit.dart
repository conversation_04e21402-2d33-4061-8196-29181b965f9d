// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../../resources/ui_strings.dart';
import '../../../../util/validator/email_validator.dart';
import 'validate_email_state.dart';

class ValidateEmailCubit extends CommonCubit<ValidateEmailState> {
  ValidateEmailCubit() : super(ValidateEmailInitial());

  void validate(String? email) {
    final bool isValid = EmailValidator.validate(email);
    emit(ValidateEmailStatus(isValid));
  }
}
