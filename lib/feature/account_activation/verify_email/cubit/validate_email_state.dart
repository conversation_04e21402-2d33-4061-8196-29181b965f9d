// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/bloc_state.dart';

sealed class ValidateEmailState extends BlocState {}

class ValidateEmailInitial extends ValidateEmailState {}

class ValidateEmailStatus extends ValidateEmailState {
  final bool isValid;

  ValidateEmailStatus(this.isValid);
}
