// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/response/account_activation_entity.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/no_app_bar_wrapper.dart';
import 'activation_status_ui_model.dart';

enum ActivationStatus {
  rejected,
  processing,
  none,
  exist,
  cancelled;

  static ActivationStatus? fromVerdict(String? verdict) {
    return switch (verdict) {
      AccountActivationEntity.verdictStatusRejected => ActivationStatus.rejected,
      AccountActivationEntity.verdictStatusProcessing => ActivationStatus.processing,
      AccountActivationEntity.verdictStatusNone => ActivationStatus.none,
      AccountActivationEntity.verdictStatusExisting => ActivationStatus.exist,
      AccountActivationEntity.verdictStatusCancelled => ActivationStatus.cancelled,
      _ => null,
    };
  }
}

class ActivationStatusScreenArg extends PageBaseArg {
  final ActivationStatus status;

  ActivationStatusScreenArg({required this.status});
}

class ActivationStatusScreen extends PageBase {
  final ActivationStatus status;

  const ActivationStatusScreen({
    required this.status,
    super.key,
  });

  static Future<void> pushNamed({required ActivationStatus status}) async {
    return navigatorContext?.pushNamed(
      Screen.activationStatusScreen.name,
      extra: ActivationStatusScreenArg(status: status),
    );
  }

  @override
  EvoPageStateBase<ActivationStatusScreen> createState() => ActivationStatusScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.activationStatusScreen.routeName);
}

@visibleForTesting
class ActivationStatusScreenState extends EvoPageStateBase<ActivationStatusScreen> {
  @visibleForTesting
  late ActivationStatusUiModel uiModel = ActivationStatusUiModel.create(
    widget.status,
  );

  @override
  Widget getContentWidget(BuildContext context) {
    return NoAppBarWrapper(
      child: PopScope(
        canPop: false,
        child: Padding(
          padding: EdgeInsets.only(
            left: EvoDimension.screenHorizontalPadding,
            right: EvoDimension.screenHorizontalPadding,
            bottom: EvoDimension.screenBottomPadding,
          ),
          child: Column(
            children: <Widget>[
              Spacer(),
              _buildIcon(uiModel.iconAsset),
              EvoDimension.space16,
              _buildTitle(uiModel.title),
              EvoDimension.space8,
              _buildDescription(uiModel.description),
              Spacer(),
              _buildActions(uiModel.actions),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIcon(String iconAsset) {
    final double size = 140.w;
    return evoImageProvider.asset(
      iconAsset,
      width: size,
      height: size,
    );
  }

  Widget _buildTitle(String title) {
    return Text(
      title,
      style: evoTextStyles.bold(TextSize.h3),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDescription(String description) {
    return Text(
      description,
      style: evoTextStyles.regular(
        TextSize.base,
        color: evoColors.grayBase,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildActions(List<Widget> actions) {
    final List<Widget> widgets = <Widget>[];

    for (final (int index, Widget action) in actions.indexed) {
      widgets.add(action);
      if (index < actions.length - 1) {
        widgets.add(EvoDimension.space8);
      }
    }

    return Column(
      children: widgets,
    );
  }
}
