// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/common_package/url_launcher.dart';

import '../../../flavors/flavor_manager.dart';
import '../../../resources/images.dart';
import '../../../resources/ui_strings.dart';
import '../../../util/url_launcher_uri_wrapper.dart';
import '../../../widget/buttons.dart';
import '../../login/new_device/verify_username/verify_username_screen.dart';
import '../../welcome/welcome_screen.dart';
import 'activation_status_screen.dart';

abstract class ActivationStatusUiModel {
  abstract final String iconAsset;
  abstract final String title;
  abstract final String description;
  abstract final List<Widget> actions;

  static ActivationStatusUiModel create(ActivationStatus status) {
    switch (status) {
      case ActivationStatus.rejected:
        return RejectedStatusUiModel();
      case ActivationStatus.processing:
        return ProcessingStatusUiModel();
      case ActivationStatus.none:
        return NoneStatusUiModel();
      case ActivationStatus.exist:
        return ExistStatusUiModel();
      case ActivationStatus.cancelled:
        return CancelledStatusUiModel();
    }
  }
}

class RejectedStatusUiModel extends ActivationStatusUiModel {
  @override
  String title = EvoStrings.activationRejectedTitle;

  @override
  String description = EvoStrings.activationRejectedDesc;

  @override
  String iconAsset = EvoImages.imgActivationStatusReject;

  @override
  List<Widget> actions = <Widget>[
    PrimaryButton(
      text: EvoStrings.backToHomePage,
      onTap: () {
        WelcomeScreen.goNamed();
      },
    ),
  ];
}

class ProcessingStatusUiModel extends ActivationStatusUiModel {
  @override
  String title = EvoStrings.activationProcessingTitle;

  @override
  String description = EvoStrings.activationProcessingDesc;

  @override
  String iconAsset = EvoImages.imgActivationStatusProcessing;

  @override
  List<Widget> actions = <Widget>[
    PrimaryButton(
      text: EvoStrings.backToHomePage,
      onTap: () {
        WelcomeScreen.goNamed();
      },
    ),
  ];
}

class NoneStatusUiModel extends ActivationStatusUiModel {
  @override
  String title = EvoStrings.activationNotFoundTitle;

  @override
  String description = EvoStrings.activationNotFoundDesc;

  @override
  String iconAsset = EvoImages.imgActivationStatusNone;

  @override
  List<Widget> actions = <Widget>[
    PrimaryButton(
      text: EvoStrings.ctaApplyNow,
      onTap: () {
        // Open DOP flow in browser using FlavorHelper
        final String url = FlavorManager.getDOPLink();
        urlLauncherWrapper.launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      },
    ),
    SecondaryButton(
      text: EvoStrings.backToHomePage,
      onTap: () {
        WelcomeScreen.goNamed();
      },
    )
  ];
}

class ExistStatusUiModel extends ActivationStatusUiModel {
  @override
  String title = EvoStrings.activationExistTitle;

  @override
  String description = EvoStrings.activationExistDesc;

  @override
  String iconAsset = EvoImages.imgActivationStatusExisting;

  @override
  List<Widget> actions = <Widget>[
    PrimaryButton(
      text: EvoStrings.login,
      onTap: () {
        VerifyUsernameScreen.goNamed();
      },
    ),
    SecondaryButton(
      text: EvoStrings.backToHomePage,
      onTap: () {
        WelcomeScreen.goNamed();
      },
    )
  ];
}

class CancelledStatusUiModel extends ActivationStatusUiModel {
  @override
  String title = EvoStrings.activationCancelledTitle;

  @override
  String description = EvoStrings.activationCancelledDesc;

  @override
  String iconAsset = EvoImages.imgActivationStatusCancelled;

  @override
  List<Widget> actions = <Widget>[
    PrimaryButton(
      text: EvoStrings.backToHomePage,
      onTap: () {
        WelcomeScreen.goNamed();
      },
    ),
  ];
}
