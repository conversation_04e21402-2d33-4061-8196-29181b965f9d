part of 'mobile_number_check_cubit.dart';

@immutable
sealed class MobileNumberCheckState implements BlocState {}

class MobileNumberCheckInitial extends MobileNumberCheckState {}

class MobileNumberCheckLoading extends MobileNumberCheckState {}

class MobileNumberCheckSuccess extends MobileNumberCheckState {
  final BaseEntity entity;

  MobileNumberCheckSuccess({required this.entity});
}

class MobileNumberCheckFailed extends MobileNumberCheckState {
  final ErrorUIModel errorUIModel;

  MobileNumberCheckFailed(this.errorUIModel);
}

class MobileNumberCheckFailedBadRequest extends MobileNumberCheckFailed {
  MobileNumberCheckFailedBadRequest(super.errorUIModel);
}

class MobileNumberCheckFailedLimitExceeded extends MobileNumberCheckFailed {
  MobileNumberCheckFailedLimitExceeded(super.errorUIModel);
}
