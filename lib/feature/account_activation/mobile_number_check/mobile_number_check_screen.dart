import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../model/challenge_success_model.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/extension.dart';
import '../../../util/formatter/phone_number_formatter.dart';
import '../../../util/functions.dart';
import '../../../util/screen_util.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../../../widget/buttons.dart';
import '../../../widget/evo_text_field.dart';
import '../../login/new_device/verify_username/verify_username_screen.dart';
import '../../welcome/welcome_screen.dart';
import '../handler/account_activation_challenge_handler.dart';
import 'mobile_number_check_cubit.dart';

class MobileNumberCheckScreen extends PageBase {
  static Future<void> pushNamed() async {
    return navigatorContext?.pushNamed(Screen.mobileNumberCheckScreen.name);
  }

  const MobileNumberCheckScreen({super.key});

  @override
  State<MobileNumberCheckScreen> createState() => MobileNumberCheckScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.mobileNumberCheckScreen.routeName);
}

@visibleForTesting
class MobileNumberCheckScreenState extends EvoPageStateBase<MobileNumberCheckScreen> {
  late final MobileNumberCheckCubit _cubit = context.read<MobileNumberCheckCubit?>() ??
      MobileNumberCheckCubit(
        authRepo: getIt.get<AuthenticationRepo>(),
      );

  @visibleForTesting
  late AccountActivationChallengeHandler challengeHandler;

  /// _phoneController & _phoneFocusNode will be disposed inside of EvoTextField widget
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _phoneFocusNode = FocusNode();

  String get _phoneNumber {
    return _phoneController.text.replaceAll(' ', '').addPrefixCountryCode();
  }

  @override
  void initState() {
    super.initState();
    challengeHandler = AccountActivationChallengeHandler(
      onError: handleEvoApiError,
    );
  }

  @override
  void onTopVisiblePageChanged(bool isOnTopAndVisible) {
    super.onTopVisiblePageChanged(isOnTopAndVisible);

    if (!isOnTopAndVisible) {
      return;
    }

    /// Prevent mismatch with previous cached username
    /// when user force to doing account activation flow again
    appState.username = null;
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<MobileNumberCheckCubit>(
      create: (BuildContext context) => _cubit,
      child: BlocListener<MobileNumberCheckCubit, MobileNumberCheckState>(
        listener: (_, MobileNumberCheckState state) {
          _handleMobileNumberCheckState(state);
        },
        child: Scaffold(
          appBar: _buildAppBar(),
          resizeToAvoidBottomInset: false,
          body: Padding(
            padding: EdgeInsets.symmetric(
              vertical: 8.w,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                _buildTitle(),
                SizedBox(height: 16.w - EvoTextField.focusedBorderPadding),
                _buildPhoneTextField(),
                const Spacer(),
                _buildFooter(),
                SizedBox(height: EvoDimension.screenBottomPadding)
              ],
            ),
          ),
        ),
      ),
    );
  }

  EvoAppBar _buildAppBar() {
    return EvoAppBar();
  }

  Widget _buildTitle() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: EvoDimension.screenHorizontalPadding,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            EvoStrings.mobileNumberCheckTitle,
            style: evoTextStyles.semibold(
              TextSize.h3,
            ),
          ),
          SizedBox(height: 4.w),
          Text(
            EvoStrings.mobileNumberCheckDesc,
            style: evoTextStyles.regular(
              TextSize.base,
              color: evoColors.grayBase,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneTextField() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: EvoDimension.screenHorizontalPaddingWithTextField,
      ),
      child: BlocBuilder<MobileNumberCheckCubit, MobileNumberCheckState>(
        builder: (_, MobileNumberCheckState state) {
          String? errorMessage;
          if (state is MobileNumberCheckFailedBadRequest) {
            errorMessage = state.errorUIModel.userMessage;
          }

          return EvoTextField(
            focusNode: _phoneFocusNode,
            textEditingController: _phoneController,
            maxLines: 1,
            inputFormatters: <TextInputFormatter>[
              PhoneNumberFormatter(),
            ],
            prefixBuilder: (EdgeInsets padding) {
              return Padding(
                padding: padding.copyWith(right: 4.sp),
                child: Text(
                  EvoStrings.countryPhoneCode,
                ),
              );
            },
            keyboardType: TextInputType.phone,
            errMessage: errorMessage,
            hintText: EvoStrings.unKnowPhone,
          );
        },
      ),
    );
  }

  Widget _buildFooter() {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: EvoDimension.screenHorizontalPadding,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _buildDescription(),
          EvoDimension.space16,
          _buildActiveAccountButton(),
          EvoDimension.space16,
          _buildAlreadyHaveAccountWidget(),
        ],
      ),
    );
  }

  void _onTapLoginBtn() {
    FocusManager.instance.primaryFocus?.unfocus();

    _cubit.checkMobileNumber(
      phoneNumber: _phoneNumber,
    );
  }

  Widget _buildAlreadyHaveAccountWidget() {
    return GestureDetector(
      onTap: () {
        VerifyUsernameScreen.pushNamed();
      },
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: evoTextStyles.regular(TextSize.base),
          children: <InlineSpan>[
            const TextSpan(text: EvoStrings.alreadyHaveAnAccountText),
            TextSpan(
                text: EvoStrings.login,
                style: TextStyle(
                  color: evoColors.primaryBase,
                )),
          ],
        ),
      ),
    );
  }

  /// By clicking "Activate Account", I confirm that I understand and agree to the Terms and Conditions and Privacy Policy of EVO.
  Widget _buildDescription() {
    return RichText(
      textAlign: TextAlign.center,
      text: TextSpan(
        style: evoTextStyles.regular(TextSize.xs),
        children: <InlineSpan>[
          const TextSpan(text: EvoStrings.activationAccountChunk1),
          TextSpan(
            text: EvoStrings.activationAccountTermsAndConditions,
            style: TextStyle(color: evoColors.secondaryBase),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                // TODO nam-pham-ts open privacy policy
                commonLog('openTermsAndConditions');
              },
          ),
          const TextSpan(text: EvoStrings.activationAccountChunk2),
          TextSpan(
              text: EvoStrings.activationAccountPrivacyPolicy,
              style: TextStyle(color: evoColors.secondaryBase),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  // TODO nam-pham-ts open privacy policy
                  commonLog('Privacy Policy');
                }),
          const TextSpan(text: EvoStrings.activationAccountChunk3),
        ],
      ),
    );
  }

  Widget _buildActiveAccountButton() {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _phoneController,
      builder: (_, TextEditingValue value, __) {
        final bool isEnabled = value.text.isNotEmpty;
        return PrimaryButton(
          text: EvoStrings.activeAccountText,
          onTap: isEnabled ? _onTapLoginBtn : null,
        );
      },
    );
  }

  void _handleMobileNumberCheckState(MobileNumberCheckState state) {
    if (state is MobileNumberCheckLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    if (state is MobileNumberCheckSuccess) {
      challengeHandler.nextChallenge(ChallengeSuccessModel(
        entity: state.entity,
        resendData: ResendDataModel(contactInfo: _phoneNumber),
      ));
      return;
    }

    if (state is MobileNumberCheckFailed && state is! MobileNumberCheckFailedBadRequest) {
      _handleMobileNumberCheckFailed(state);
      return;
    }
  }

  void _handleMobileNumberCheckFailed(MobileNumberCheckFailed state) {
    if (state is MobileNumberCheckFailedLimitExceeded) {
      evoDialogFunction.showDialogConfirm(
        dialogId: EvoDialogId.activateAccountErrorLimitExceededDialog,
        title: state.errorUIModel.userMessageTitle ?? EvoStrings.maxTriesReached,
        content: state.errorUIModel.userMessage ?? EvoStrings.tryAgainLater,
        textPositive: EvoStrings.backToHomePage,
        alertType: DialogAlertType.error,
        autoClosePopupWhenClickCTA: true,
        onClickPositive: WelcomeScreen.goNamed,
      );
      return;
    }
    handleEvoApiError(state.errorUIModel);
  }
}
