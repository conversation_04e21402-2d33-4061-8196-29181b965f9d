import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/request/activate_account_request.dart';
import '../../../data/response/account_activation_entity.dart';
import '../mock/mock_account_activation_use_case.dart';

part 'mobile_number_check_state.dart';

class MobileNumberCheckCubit extends CommonCubit<MobileNumberCheckState> {
  final AuthenticationRepo authRepo;

  MobileNumberCheckCubit({
    required this.authRepo,
  }) : super(
          MobileNumberCheckInitial(),
        );

  Future<void> checkMobileNumber({String? phoneNumber}) async {
    emit(MobileNumberCheckLoading());

    final AccountActivationEntity entity = await authRepo.activateAccount(
      request: ActivateAccountVerifyPhoneNumberRequest(
        phoneNumber: phoneNumber,
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifyOtpChallengeType),
      ),
    );

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(MobileNumberCheckSuccess(entity: entity));
      return;
    }

    final ErrorUIModel errorUiModel = ErrorUIModel.fromEntity(entity);
    if (errorUiModel.statusCode == CommonHttpClient.BAD_REQUEST) {
      emit(MobileNumberCheckFailedBadRequest(errorUiModel));
      return;
    }
    if (errorUiModel.statusCode == CommonHttpClient.LIMIT_EXCEEDED ||
        errorUiModel.statusCode == CommonHttpClient.LOCKED_RESOURCE) {
      emit(MobileNumberCheckFailedLimitExceeded(errorUiModel));
      return;
    }
    emit(MobileNumberCheckFailed(errorUiModel));
  }
}
