import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/buttons.dart';
import '../../../widget/no_app_bar_wrapper.dart';

class CommonErrorScreenArg extends PageBaseArg {
  final String title;
  final String description;
  final VoidCallback onTap;
  final String buttonText;

  CommonErrorScreenArg({
    required this.title,
    required this.description,
    required this.onTap,
    required this.buttonText,
  });
}

class CommonErrorScreen extends PageBase {
  final String title;
  final String description;
  final VoidCallback onTap;
  final String buttonText;

  const CommonErrorScreen(
      {required this.title,
      required this.description,
      required this.buttonText,
      required this.onTap,
      super.key});

  static void pushNamed(
      {required String title,
      required String description,
      required String buttonText,
      required VoidCallback onTap}) {
    navigatorContext?.pushNamed(
      Screen.commonErrorScreen.name,
      extra: CommonErrorScreenArg(
        onTap: onTap,
        title: title,
        description: description,
        buttonText: buttonText,
      ),
    );
  }

  static void pushReplacementNamed(
      {required String title,
      required String description,
      required String buttonText,
      required VoidCallback onTap}) {
    navigatorContext?.pushReplacementNamed(
      Screen.commonErrorScreen.name,
      extra: CommonErrorScreenArg(
        onTap: onTap,
        title: title,
        description: description,
        buttonText: buttonText,
      ),
    );
  }

  @override
  State<CommonErrorScreen> createState() => _CommonErrorScreenState<CommonErrorScreen>();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: Screen.commonErrorScreen.name,
      );
}

class _CommonErrorScreenState<T extends PageBase> extends EvoPageStateBase<CommonErrorScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return NoAppBarWrapper(
      child: PopScope(
        canPop: false,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
          child: Column(
            children: <Widget>[
              const Spacer(),
              evoImageProvider.asset(EvoImages.icAlertError, width: 140.w, height: 140.w),
              EvoDimension.space16,
              Text(
                widget.title,
                textAlign: TextAlign.center,
                style: evoTextStyles.bold(TextSize.h3, color: evoColors.grayText),
              ),
              EvoDimension.space8,
              Text(
                widget.description,
                textAlign: TextAlign.center,
                style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
              ),
              const Spacer(),
              PrimaryButton(
                text: widget.buttonText,
                onTap: _onTap,
              ),
              SizedBox(height: EvoDimension.screenBottomPadding),
            ],
          ),
        ),
      ),
    );
  }

  void _onTap() {
    navigatorContext?.pop();
    widget.onTap();
  }
}
