import 'package:flutter/foundation.dart';

import '../../model/evo_dialog_id.dart';
import '../../resources/resources.dart';
import '../../util/dialog_functions.dart';
import '../../util/functions.dart';

class AuthorizationSessionExpiredPopup {
  @visibleForTesting
  bool isShowingPopup = false;

  bool checkCanShowPopup() {
    if (isShowingPopup) {
      return false;
    }

    return true;
  }

  Future<void> show() async {
    isShowingPopup = true;

    await evoDialogFunction.showDialogConfirm(
      alertType: DialogAlertType.error,
      dialogId: EvoDialogId.unAuthorizationSessionBottomSheet,
      title: EvoStrings.titleSessionTokenExpired,
      content: EvoStrings.contentSessionTokenExpiredSignIn,
      textPositive: EvoStrings.ctaLogInAgain,
      isDismissible: false,
      autoClosePopupWhenClickCTA: true,
      onClickPositive: () {
        evoUtilFunction.openAuthenticationScreen(isClearNavigationStack: true);
      },
    );

    isShowingPopup = false;
  }
}
