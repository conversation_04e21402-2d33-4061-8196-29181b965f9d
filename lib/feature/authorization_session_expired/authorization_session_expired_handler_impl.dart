import 'dart:async';

import 'package:flutter/foundation.dart';

import '../../base/modules/core/app_state.dart';
import '../../prepare_for_app_initiation.dart';
import '../../util/functions.dart';
import 'authorization_session_expired.dart';

class AuthorizationSessionExpiredHandlerImpl extends AuthorizationSessionExpiredHandler {
  @visibleForTesting
  StreamController<UnauthorizedSessionState> authorizationSessionExpireController =
      StreamController<UnauthorizedSessionState>.broadcast();

  @visibleForTesting
  final AppState appState = getIt.get<AppState>();

  @override
  Future<void> emitUnauthorized(UnauthorizedSessionState state) async {
    /// Just emit event when user is login
    if (!appState.isUserLogIn) {
      return;
    }

    appState.isUserLogIn = false;

    switch (state) {
      case UnauthorizedSessionState.forcedLogout:
        await evoUtilFunction.clearAllUserData();
        break;
      case UnauthorizedSessionState.invalidToken:
      case UnauthorizedSessionState.unknown:
        evoUtilFunction.clearUserInfoAppState();
        await evoUtilFunction.clearDataOnTokenInvalid();
        break;
    }

    authorizationSessionExpireController.sink.add(state);
  }

  @override
  void close() {
    authorizationSessionExpireController.close();
  }

  @override
  Stream<UnauthorizedSessionState> getStreamSubscription() {
    return authorizationSessionExpireController.stream;
  }
}
