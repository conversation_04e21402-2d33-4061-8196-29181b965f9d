import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../data/repository/common_repo.dart';
import '../../data/response/force_update_entity.dart';
import '../../model/evo_dialog_id.dart';
import '../../base/modules/core/app_state.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/evo_flutter_wrapper.dart';
import '../../util/functions.dart';
import '../../util/mock_file_name_utils/mock_common_file_name.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../util/secure_storage_helper/secure_storage_helper_impl.dart';

/// TODO update value when config value is ready
mixin CheckForceUpdateHandlerMixin {
  static const String urlStoreAndroid =
      'https://play.google.com/store/apps/details?id=vn.goevo.evo';
  static const String urlStoreIos = 'https://apps.apple.com/app/id/**********';

  final EvoLocalStorageHelper _localStorageHelper = getIt.get<EvoLocalStorageHelper>();
  final CommonRepo _commonRepo = getIt.get<CommonRepo>();

  Future<void> checkForceUpdate() async {
    final ForceUpdateEntity forceUpdateEntity = await _commonRepo.getForceUpdate(
        mockConfig: MockConfig(
      enable: false,
      fileName: getForceUpdateMockFileName(),
    ));

    if (forceUpdateEntity.statusCode == CommonHttpClient.SUCCESS) {
      if (forceUpdateEntity.hasNewerVersion == true) {
        await handleAppUpdate(forceUpdateEntity);
      } else {
        await _localStorageHelper.delete(key: EvoSecureStorageHelperImpl.latestVersionIgnore);
      }
    }
  }

  @visibleForTesting
  Future<void> handleAppUpdate(ForceUpdateEntity entity) async {
    final AppState appState = getIt<AppState>();
    if (entity.latestVersion == appState.appVersion) {
      return;
    }

    if (entity.forceToUpdate == true) {
      await showRequestUpdateDialog(isForceUpdate: true);
    } else {
      final bool isNotIgnoreVersion =
          await _localStorageHelper.getLatestVersionIgnore() != entity.latestVersion;

      commonLog(isNotIgnoreVersion);

      if (isNotIgnoreVersion) {
        await showRequestUpdateDialog(
          onNegativeClick: () {
            handleIgnoreUpdate(entity.latestVersion);
          },
        );
      }
    }
  }

  @visibleForTesting
  Future<void> showRequestUpdateDialog({
    bool isForceUpdate = false,
    VoidCallback? onNegativeClick,
  }) async {
    final Widget imageHeader = evoImageProvider.asset(
      EvoImages.bgAppUpdate,
      fit: BoxFit.fitWidth,
    );

    return evoUtilFunction.showDialogBottomSheet(
      dialogId: EvoDialogId.newAppVersionBottomSheet,
      header: imageHeader,
      title: EvoStrings.forceUpdateSubDesc,
      isDismissible: !isForceUpdate,
      textNegative: isForceUpdate ? null : EvoStrings.forceUpdateSkip,
      textPositive: EvoStrings.forceUpdateAgree,
      content: EvoStrings.forceUpdateDescription,
      onClickNegative: () {
        onNegativeClick?.call();
      },
      onClickPositive: () {
        handleOpenStore();
      },
    );
  }

  @visibleForTesting
  Future<void> handleOpenStore() async {
    if (evoFlutterWrapper.isIOS()) {
      commonUtilFunction.commonLaunchUrlString(urlStoreIos,
          mode: CommonLaunchUrlMode.externalApplication);
    } else if (evoFlutterWrapper.isAndroid()) {
      commonUtilFunction.commonLaunchUrlString(urlStoreAndroid,
          mode: CommonLaunchUrlMode.externalApplication);
    }
  }

  @visibleForTesting
  Future<void> handleIgnoreUpdate(String? latestVersion) async {
    await _localStorageHelper.setLatestVersionIgnore(latestVersion);
    navigatorContext?.pop();
  }
}
