// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../model/challenge_success_model.dart';
import '../../../../resources/resources.dart';
import '../../../../util/dialog_functions.dart';
import '../../../../util/functions.dart';
import '../../util/screen_util.dart';
import '../../widget/action_button_widget.dart';
import '../../widget/app_bar_wrapper.dart';
import 'security_method.dart';
import 'select_security_method_cubit.dart';
import 'select_security_method_state.dart';

enum SelectSecurityMethodFlow {
  resetPin,
  forgotUsername,
}

class SelectSecurityMethodArg extends PageBaseArg {
  final SelectSecurityMethodFlow flow;

  final List<SecurityMethod> methods;

  final String? sessionToken;

  final ChallengeSuccessCallback onSuccess;

  SelectSecurityMethodArg({
    required this.flow,
    required this.methods,
    required this.onSuccess,
    this.sessionToken,
  });
}

class SelectSecurityMethodScreen extends PageBase {
  final SelectSecurityMethodFlow flow;

  final List<SecurityMethod> methods;

  final String? sessionToken;

  final ChallengeSuccessCallback onSuccess;

  const SelectSecurityMethodScreen({
    required this.flow,
    required this.methods,
    required this.onSuccess,
    this.sessionToken,
    super.key,
  });

  @override
  State<SelectSecurityMethodScreen> createState() => _SelectSecurityMethodScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.selectSecurityMethodScreen.routeName);

  static void pushNamed({
    required SelectSecurityMethodFlow flow,
    required List<SecurityMethod> methods,
    required ChallengeSuccessCallback onSuccess,
    String? sessionToken,
  }) {
    navigatorContext?.pushNamed(
      Screen.selectSecurityMethodScreen.name,
      extra: SelectSecurityMethodArg(
        flow: flow,
        methods: methods,
        sessionToken: sessionToken,
        onSuccess: onSuccess,
      ),
    );
  }
}

class _SelectSecurityMethodScreenState extends EvoPageStateBase<SelectSecurityMethodScreen> {
  late final SelectSecurityMethodCubit _cubit =
      context.read<SelectSecurityMethodCubit?>() ?? SelectSecurityMethodCubit();

  late final SessionDialogType _dialogType = switch (widget.flow) {
    SelectSecurityMethodFlow.resetPin => SessionDialogType.resetPin,
    // TODO: Implement with forgot username flow
    SelectSecurityMethodFlow.forgotUsername => throw UnimplementedError(),
  };

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<SelectSecurityMethodCubit>(
      create: (_) => _cubit,
      child: BlocListener<SelectSecurityMethodCubit, SelectSecurityMethodState>(
        listener: (_, SelectSecurityMethodState state) => _listenSelectSecurityMethodState(state),
        child: AppBarWrapper(
          contentPadding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              _buildTitle(),
              EvoDimension.space4,
              _buildDesc(),
              ...widget.methods.map(_buildSecurityMethodTile),
            ],
          ),
        ),
      ),
    );
  }

  void _listenSelectSecurityMethodState(SelectSecurityMethodState state) {
    if (state is SelectSecurityMethodLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }
    evoUtilFunction.hideHudLoading();

    if (state is SelectSecurityMethodSuccess) {
      widget.onSuccess.call(ChallengeSuccessModel(entity: state.entity));
      return;
    }
    if (state is SelectSecurityMethodFailureSessionExpired) {
      evoDialogFunction.showDialogSessionTokenExpired(type: _dialogType);
      return;
    }
    if (state is SelectSecurityMethodFailureLimitExceeded) {
      evoDialogFunction.showDialogErrorLimitExceeded(
        type: _dialogType,
        content: state.error.userMessage,
      );
      return;
    }
    if (state is SelectSecurityMethodFailure) {
      handleEvoApiError(state.error);
    }
  }

  Widget _buildTitle() {
    return Text(
      EvoStrings.selectSecurityMethodTitle,
      style: evoTextStyles.semibold(TextSize.h3),
    );
  }

  Widget _buildDesc() {
    return Text(
      EvoStrings.selectSecurityMethodDesc,
      style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
    );
  }

  Widget _buildSecurityMethodTile(SecurityMethod method) {
    late final String title;
    late final String icon;
    switch (method) {
      case SecurityMethod.otp:
        title = EvoStrings.selectSecurityMethodOtp;
        icon = EvoImages.icPhoneAndroid;
      case SecurityMethod.emailOtp:
        title = EvoStrings.selectSecurityMethodEmailOtp;
        icon = EvoImages.icEmail;
    }

    return Padding(
      padding: EdgeInsets.only(top: 16.w),
      child: ActionButtonWidget(
        title: title,
        icon: icon,
        onPress: () => _cubit.select(method),
      ),
    );
  }
}
