// coverage:ignore-file
// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import 'security_method.dart';
import 'select_security_method_state.dart';

class SelectSecurityMethodCubit extends CommonCubit<SelectSecurityMethodState> {
  SelectSecurityMethodCubit() : super(SelectSecurityMethodInitial());

  // TODO: integrate API
  Future<void> select(SecurityMethod method) async {
    emit(SelectSecurityMethodLoading());

    await Future<void>.delayed(const Duration(seconds: 1));

    emit(SelectSecurityMethodSuccess(entity: BaseEntity()));
  }
}
