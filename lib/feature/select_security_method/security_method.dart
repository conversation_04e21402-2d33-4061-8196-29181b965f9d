// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

enum SecurityMethod {
  otp(otpValue),
  emailOtp(emailOtpValue);

  final String value;

  const SecurityMethod(this.value);

  static const String otpValue = 'otp';
  static const String emailOtpValue = 'email_otp';

  static SecurityMethod fromValue(String value) {
    return switch (value) {
      otpValue => SecurityMethod.otp,
      emailOtpValue => SecurityMethod.emailOtp,
      _ => throw UnimplementedError(),
    };
  }
}
