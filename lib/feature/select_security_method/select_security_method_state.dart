// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

sealed class SelectSecurityMethodState extends BlocState {}

class SelectSecurityMethodInitial extends SelectSecurityMethodState {}

class SelectSecurityMethodLoading extends SelectSecurityMethodState {}

class SelectSecurityMethodSuccess extends SelectSecurityMethodState {
  final BaseEntity entity;

  SelectSecurityMethodSuccess({required this.entity});
}

class SelectSecurityMethodFailure extends SelectSecurityMethodState {
  final ErrorUIModel error;

  SelectSecurityMethodFailure({required this.error});
}

class SelectSecurityMethodFailureSessionExpired extends SelectSecurityMethodFailure {
  SelectSecurityMethodFailureSessionExpired({required super.error});
}

class SelectSecurityMethodFailureLimitExceeded extends SelectSecurityMethodFailure {
  SelectSecurityMethodFailureLimitExceeded({required super.error});
}
