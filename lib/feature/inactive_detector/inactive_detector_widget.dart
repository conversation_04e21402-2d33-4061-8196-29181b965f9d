// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../data/constants.dart';
import '../../model/evo_dialog_id.dart';
import '../../resources/resources.dart';
import '../../util/dialog_functions.dart';
import '../../util/extension.dart';
import '../../util/functions.dart';
import '../../widget/countdown/onetime_countdown_builder.dart';
import '../login/old_device/previous_login_screen.dart';
import 'cubit/inactive_detector_cubit.dart';
import 'cubit/inactive_detector_state.dart';

/// Controller class that provides external control over the inactive detector functionality.
///
/// This controller allows parent widgets to programmatically enable or disable
/// the inactivity detection without directly accessing the cubit.
class InactiveDetectorController {
  /// Function to enable inactivity detection.
  /// When called, starts monitoring user activity and app lifecycle changes.
  void Function()? enable;

  /// Function to disable inactivity detection.
  /// When called, stops monitoring and cancels any pending inactivity timers.
  void Function()? disable;
}

/// A widget that wraps its child to provide automatic user inactivity detection.
///
/// This widget monitors user interactions and app lifecycle changes to detect
/// when a user becomes inactive. When inactivity is detected, it shows warning
/// dialogs and can automatically logout users for security purposes.
///
/// Features:
/// - Monitors pointer events (taps, scrolls, etc.) to detect user activity
/// - Tracks app lifecycle changes (foreground/background transitions)
/// - Shows countdown dialogs when inactivity is detected
/// - Automatically signs out users after extended inactivity
/// - Provides external control through [InactiveDetectorController]
///
/// Usage:
/// ```dart
/// InactiveDetectorWidget(
///   controller: myController, // Optional external control
///   child: MyAppContent(),
/// )
/// ```
class InactiveDetectorWidget extends StatefulWidget {
  /// The child widget that will be wrapped with inactivity detection.
  /// All user interactions with this widget tree will be monitored.
  final Widget child;

  /// Optional controller for external management of the detector.
  /// Allows parent widgets to enable/disable detection programmatically.
  final InactiveDetectorController? controller;

  /// Optional custom cubit instance for testing or custom behavior.
  /// If not provided, a default cubit will be created.
  final InactiveDetectorCubit? cubit;

  const InactiveDetectorWidget({
    required this.child,
    this.controller,
    this.cubit,
    super.key,
  });

  @override
  State<InactiveDetectorWidget> createState() => InactiveDetectorWidgetState();
}

/// State class for [InactiveDetectorWidget] that handles the actual detection logic.
///
/// This class implements [WidgetsBindingObserver] to monitor app lifecycle changes
/// and manages the cubit that handles inactivity detection business logic.
@visibleForTesting
class InactiveDetectorWidgetState extends State<InactiveDetectorWidget>
    with WidgetsBindingObserver {
  /// The cubit that manages inactivity detection state and business logic.
  /// Uses the provided cubit from widget or creates a default one.
  @visibleForTesting
  late final InactiveDetectorCubit cubit = widget.cubit ??
      InactiveDetectorCubit(
        evoUtilFunction: evoUtilFunction,
      );

  @override
  void initState() {
    super.initState();
    // Register this widget as an observer for app lifecycle changes
    WidgetsBinding.instance.addObserver(this);

    // Wire up the controller functions to cubit methods for external control
    widget.controller?.enable = cubit.startDetection;
    widget.controller?.disable = cubit.stopDetection;
  }

  @override
  void dispose() {
    // Clean up the lifecycle observer to prevent memory leaks
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  /// Handles app lifecycle state changes to manage inactivity detection behavior.
  ///
  /// When the app goes to background, different inactivity rules may apply.
  /// When the app returns to foreground, we need to check if the user was
  /// inactive while the app was in background.
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        // App returned to foreground - check for background inactivity
        cubit.onAppGoForeground();
        break;
      case AppLifecycleState.paused:
        // App went to background - start background inactivity tracking
        cubit.onAppGoBackground();
        break;
      case AppLifecycleState.detached:
      case AppLifecycleState.inactive:
      case AppLifecycleState.hidden:
        // These states don't require special handling for inactivity detection
        break;
    }
  }

  /// Builds the widget tree with inactivity detection capabilities.
  ///
  /// Uses a [Listener] widget to capture all pointer events (taps, scrolls, etc.)
  /// and a [BlocListener] to respond to inactivity detection state changes.
  @override
  Widget build(BuildContext context) {
    return Listener(
      /// Translucent behavior allows this widget to receive events while also
      /// permitting child widgets to receive the same events. This ensures
      /// normal app functionality while monitoring user activity.
      behavior: HitTestBehavior.translucent,

      /// Captures any pointer down event (tap, scroll start, etc.) and
      /// notifies the cubit to reset the inactivity timer.
      onPointerDown: (_) {
        cubit.onUserAction();
      },
      child: BlocProvider<InactiveDetectorCubit>(
        create: (_) => cubit,
        child: BlocListener<InactiveDetectorCubit, InactiveDetectorState>(
          /// Listens for state changes from the cubit and handles them appropriately
          /// (e.g., showing dialogs, navigating to login screen)
          listener: (BuildContext context, InactiveDetectorState state) {
            handleOnStateChanged(state);
          },
          child: widget.child,
        ),
      ),
    );
  }

  /// Shows the initial inactivity warning dialog with countdown timer.
  ///
  /// This dialog appears when user inactivity is first detected. It provides:
  /// - A warning message about inactivity
  /// - A countdown timer showing time until automatic logout
  /// - Options to continue session or logout immediately
  ///
  /// The dialog is non-dismissible to ensure user acknowledgment.
  /// If the countdown reaches zero, it automatically shows the logout dialog.
  @visibleForTesting
  void showInactiveDialog() {
    evoDialogFunction.showDialogConfirm(
      isDismissible: false,
      // Prevent dismissal by tapping outside
      dialogId: EvoDialogId.inactiveDialog,
      title: EvoStrings.inactiveDialogTitle,
      textPositive: EvoStrings.inactiveDialogPositiveButton,
      textNegative: EvoStrings.inactiveDialogNegativeButton,

      // Footer contains the countdown timer with dynamic text
      footer: OnetimeCountdownBuilder(
        durationInSec: DurationConstants.defaultDurationLogoutAfterInactiveInSec,
        onFinish: () {
          // When countdown finishes, close this dialog and show logout dialog
          navigatorContext?.pop();
          showLogoutDialog();
        },
        builder: (int seconds) => RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
            children: <TextSpan>[
              const TextSpan(text: EvoStrings.inactiveDialogContent),
              // Dynamic countdown text with proper pluralization
              TextSpan(
                text: seconds.pluralize(
                  pluralForm: EvoStrings.pluralSecond,
                  singularForm: EvoStrings.singularSecond,
                ),
                style: evoTextStyles.regular(TextSize.base, color: evoColors.grayText),
              ),
            ],
          ),
        ),
      ),
      onClickPositive: () {
        // User chose to continue session - restart the inactivity detector
        // Note: detector is stopped when dialog is shown to prevent multiple dialogs
        cubit.startDetection();
      },
      onClickNegative: () {
        // User chose to logout immediately
        cubit.logout();
      },
      autoClosePopupWhenClickCTA: true,
    );
  }

  /// Shows the final logout confirmation dialog.
  ///
  /// This dialog appears when the countdown timer in the inactivity dialog expires,
  /// or when the user explicitly chooses to logout. It informs the user that
  /// they will be signed out due to inactivity and provides a single option to
  /// proceed with the logout process.
  ///
  /// The dialog is non-dismissible to ensure the logout process completes.
  @visibleForTesting
  void showLogoutDialog() {
    evoDialogFunction.showDialogConfirm(
      dialogId: EvoDialogId.inactiveLogoutDialog,
      textPositive: EvoStrings.ctaLogInAgain,
      content: EvoStrings.inactiveLogoutDialogDescription,
      title: EvoStrings.inactiveLogoutDialogTitle,
      isDismissible: false,
      // Prevent dismissal to ensure logout completes
      autoClosePopupWhenClickCTA: true,
      onClickPositive: () {
        // Proceed with logout process
        cubit.logout();
      },
    );
  }

  /// Handles state changes from the [InactiveDetectorCubit].
  ///
  /// This method responds to different states emitted by the cubit:
  /// - [InactiveDetectorDetectedState]: Shows the inactivity warning dialog
  /// - [InactiveDetectorLogoutSuccessState]: Navigates to the login screen
  ///
  /// Additional states may be handled here as the feature evolves.
  @visibleForTesting
  void handleOnStateChanged(InactiveDetectorState state) {
    if (state is InactiveDetectorLogoutSuccessState) {
      // Logout completed successfully - navigate to login screen
      PreviousLogInScreen.goNamed();
      return;
    }

    if (state is InactiveDetectorDetectedState) {
      // Inactivity detected - show warning dialog with countdown
      showInactiveDialog();
      return;
    }
  }
}
