// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/bloc_state.dart';

/// Base abstract class for all inactive detector states.
///
/// This class extends [BlocState] to provide a common interface for all states
/// related to user inactivity detection. Each concrete state represents a
/// different phase in the inactivity detection and handling process.
abstract class InactiveDetectorState extends BlocState {}

/// Initial state when the inactive detector is first created or reset.
///
/// This is the default state that indicates:
/// - The detector is ready to monitor user activity
/// - No inactivity has been detected yet
/// - The system is in a normal, active state
///
/// Transitions from this state occur when:
/// - User inactivity is detected → [InactiveDetectorDetectedState]
/// - logout process is initiated → [InactiveDetectorLogoutSuccessState]
class InactiveDetectorInitializeState extends InactiveDetectorState {}

/// State emitted when user inactivity has been detected.
///
/// This state is triggered when the user has been inactive for the configured
/// duration (either foreground or background inactivity timeout). When this
/// state is emitted, the UI should:
/// - Display an inactivity warning dialog
/// - Show a countdown timer for automatic logout
/// - Provide options for the user to continue or logout
///
/// This state represents the first warning phase before automatic logout.
/// The detector stops monitoring while in this state to prevent multiple
/// dialogs from appearing simultaneously.
class InactiveDetectorDetectedState extends InactiveDetectorState {}

/// State emitted when the logout process has completed successfully.
///
/// This state indicates that:
/// - User data has been cleared from the device
/// - Authentication tokens have been invalidated
/// - The app is ready to navigate to the login screen
///
/// When this state is emitted, the UI should:
/// - Navigate to the login screen
/// - Clear any remaining user-specific UI state
/// - Reset the app to its initial, unauthenticated state
///
/// This is a terminal state in the inactivity detection flow.
class InactiveDetectorLogoutSuccessState extends InactiveDetectorState {}
