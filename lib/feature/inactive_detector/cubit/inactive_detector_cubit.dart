// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../base/modules/core/app_state.dart';
import '../../../data/constants.dart';
import '../../../util/functions.dart';
import 'inactive_detector_state.dart';

/// A Cubit that manages user inactivity detection for security purposes.
///
/// This cubit monitors user activity and automatically signs out users when they
/// remain inactive for specified durations. It handles different timeout periods
/// for foreground and background app states to enhance security.
///
/// Key features:
/// - Tracks user activity with configurable timeout periods
/// - Handles foreground inactivity
/// - Handles background inactivity
/// - Automatically signs out users when timeout is reached
/// - Emits states for UI to show inactivity warnings
class InactiveDetectorCubit extends CommonCubit<InactiveDetectorState> {
  /// Utility functions for clearing user information and app state
  @visibleForTesting
  final EvoUtilFunction evoUtilFunction;

  /// Duration in seconds before considering user inactive in foreground (default: 300 seconds)
  @visibleForTesting
  final int foregroundInactiveDurationInSec;

  /// Duration in seconds before considering user inactive in background (default: 300 seconds)
  @visibleForTesting
  final int backgroundInactiveDurationInSec;

  /// Flag indicating whether the app is currently in foreground state
  @visibleForTesting
  bool isOnForeground = true;

  /// Flag indicating whether the inactivity detector is currently enabled
  @visibleForTesting
  bool isEnabled = false;

  /// Timestamp when the app last entered background state
  /// Used to calculate background inactivity duration
  @visibleForTesting
  DateTime? lastEnterBackgroundDateTime;

  /// Timer that triggers inactivity detection when it expires
  /// Automatically cancelled and reset on user activity
  @visibleForTesting
  Timer? timer;

  /// Creates an InactiveDetectorCubit with configurable timeout durations.
  ///
  /// [evoUtilFunction] - Required utility functions for app state management
  /// [foregroundInactiveDurationInSec] - Optional foreground timeout (default: 300 seconds)
  /// [backgroundInactiveDurationInSec] - Optional background timeout (default: 300 seconds)
  InactiveDetectorCubit({
    required this.evoUtilFunction,
    this.foregroundInactiveDurationInSec = DurationConstants.defaultForegroundInactiveDurationInSec,
    this.backgroundInactiveDurationInSec = DurationConstants.defaultBackgroundInactiveDurationInSec,
  }) : super(InactiveDetectorInitializeState());

  /// Cleanup method called when the cubit is disposed.
  /// Ensures the detector is properly disabled to prevent memory leaks.
  @override
  Future<void> close() {
    stopDetection();
    return super.close();
  }

  /// Enables the inactivity detector and starts the timer.
  ///
  /// Cancels any existing timer and creates a new one with the foreground
  /// inactivity duration. When the timer expires, it will trigger the
  /// inactivity detection callback.
  void startDetection() {
    timer?.cancel();
    timer = Timer(Duration(seconds: foregroundInactiveDurationInSec), onInactiveTimerCompleted);
    isEnabled = true;
  }

  /// Disables the inactivity detector and stops all timers.
  ///
  /// This method should be called when inactivity detection is no longer
  /// needed, such as during logout or when the feature is turned off.
  void stopDetection() {
    timer?.cancel();
    timer = null;
    isEnabled = false;
  }

  /// Called when user performs any action (touch, tap, etc.).
  ///
  /// If the detector is enabled, this resets the inactivity timer,
  /// effectively extending the user's active session. This should be
  /// called on any user interaction to prevent premature timeout.
  void onUserAction() {
    if (isEnabled == false) {
      return;
    }
    resetDetector();
  }

  /// Called when the app transitions from background to foreground.
  ///
  /// This method checks if the user has been inactive in the background
  /// for too long. If the background inactivity duration has been exceeded,
  /// it automatically signs out the user. Otherwise, it resets the detector
  /// to start monitoring foreground activity.
  void onAppGoForeground() {
    if (isEnabled == false || isOnForeground) {
      return;
    }
    isOnForeground = true;
    if (isReachedBackgroundInactiveDuration()) {
      logout();
      return;
    }
    resetDetector();
  }

  /// Called when the app transitions from foreground to background.
  ///
  /// Records the timestamp when the app entered background state.
  /// This timestamp is used later to calculate how long the app
  /// has been in the background when it returns to foreground.
  /// The detector remains enabled but the timer is cancelled since
  /// foreground inactivity detection is not needed while in background.
  void onAppGoBackground() {
    if (isEnabled == false || isOnForeground == false) {
      return;
    }
    isOnForeground = false;
    lastEnterBackgroundDateTime = DateTime.now();

    timer?.cancel();
    timer = null;
  }

  /// Signs out the user due to inactivity.
  ///
  /// This method:
  /// 1. Disables the inactivity detector
  /// 2. Clears all user information from app state
  /// 3. Emits a sign-out success state to notify the UI
  ///
  /// The UI should respond by navigating to the login screen.
  void logout() {
    stopDetection();
    evoUtilFunction.clearUserInfoAppState();
    emit(InactiveDetectorLogoutSuccessState());
  }

  /// Checks if the background inactivity duration has been exceeded.
  ///
  /// Compares the current time with the timestamp when the app entered
  /// background state. Returns true if the difference exceeds the
  /// configured background inactivity duration.
  ///
  /// Returns false if no background timestamp is recorded.
  @visibleForTesting
  bool isReachedBackgroundInactiveDuration() {
    if (lastEnterBackgroundDateTime == null) {
      return false;
    }
    final int diffInSec = DateTime.now().difference(lastEnterBackgroundDateTime!).inSeconds;
    return diffInSec >= backgroundInactiveDurationInSec;
  }

  /// Callback triggered when the inactivity timer expires.
  ///
  /// This method is called when the user has been inactive for the
  /// configured foreground duration. It only triggers if the app is
  /// currently in foreground state. When triggered, it:
  /// 1. Emits an inactivity detected state (to show warning dialog)
  /// 2. Disables the detector to prevent further triggers
  @visibleForTesting
  void onInactiveTimerCompleted() {
    if (isOnForeground == false) {
      return;
    }
    stopDetection();
    emit(InactiveDetectorDetectedState());
  }

  /// Resets the inactivity timer to extend the user's active session.
  ///
  /// Cancels the current timer and creates a new one with the full
  /// foreground inactivity duration. This effectively restarts the
  /// countdown, giving the user a fresh timeout period.
  @visibleForTesting
  void resetDetector() {
    timer?.cancel();
    timer = Timer(Duration(seconds: foregroundInactiveDurationInSec), onInactiveTimerCompleted);
  }
}
