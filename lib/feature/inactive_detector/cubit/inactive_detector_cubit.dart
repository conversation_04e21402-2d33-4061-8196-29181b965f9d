import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../base/modules/core/app_state.dart';
import '../../../util/functions.dart';
import 'inactive_detector_state.dart';

class InactiveDetectorCubit extends CommonCubit<InactiveDetectorState> {
  final AuthenticationRepo authenticationRepo;
  final EvoUtilFunction evoUtilFunction;
  final AppState appState;

  InactiveDetectorCubit({
    required this.authenticationRepo,
    required this.evoUtilFunction,
    required this.appState,
  }) : super(InactiveDetectorInitialize());

  /// when user is inactive after 120 seconds, app force user to sign out
  /// this action is totally different from user sign out at Profile screen
  /// with action, app just clear all user data in AppState (i.e: user info, token, etc.)
  void signOut() {
    evoUtilFunction.clearUserInfoAppState();
    emit(SignOutSuccess());
  }
}
