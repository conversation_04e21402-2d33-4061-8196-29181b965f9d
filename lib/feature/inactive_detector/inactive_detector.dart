import 'dart:async';

import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../data/repository/authentication_repo.dart';
import '../../model/evo_dialog_id.dart';
import '../../base/modules/core/app_state.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/dialog_functions.dart';
import '../../util/functions.dart';
import '../login/old_device/login_on_old_device_screen.dart';
import 'cubit/inactive_detector_cubit.dart';
import 'cubit/inactive_detector_state.dart';
import 'inactive_detector_timer.dart';
import 'inactive_detector_timer_impl.dart';

class InactiveDetectorController {
  void Function()? enable;

  void Function()? disable;
}

class InactiveDetector extends StatefulWidget {
  final Widget child;
  final InactiveDetectorController? detectorController;

  const InactiveDetector({required this.child, this.detectorController, super.key});

  @override
  State<InactiveDetector> createState() => _InactiveDetectorState();
}

class _InactiveDetectorState extends State<InactiveDetector> with WidgetsBindingObserver {
  late InActiveDetectorTimer _inactiveDetector;
  bool isForeground = true;
  final InactiveDetectorCubit _detectorCubit = InactiveDetectorCubit(
    authenticationRepo: getIt.get<AuthenticationRepo>(),
    evoUtilFunction: evoUtilFunction,
    appState: getIt.get<AppState>(),
  );

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _inactiveDetector = InActiveDetectorTimerImpl(
      onInactive: () {
        if (isForeground) {
          showInActive();
        }
      },
      onIdleAWhile: () {
        if (isForeground) {
          showIdleAwhile();
        }
      },
    );

    widget.detectorController?.enable = _enableDetectorTimer;
    widget.detectorController?.disable = _disableDetectorTimer;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.addObserver(this);
    _disableDetectorTimer();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    isForeground = state == AppLifecycleState.resumed;
    if (!isForeground) {
      return;
    }

    switch (_inactiveDetector.state) {
      case InactiveDetectorTimerState.idleAWhile:
        showIdleAwhile();
        break;
      case InactiveDetectorTimerState.inActive:
        showInActive();
        break;
      case InactiveDetectorTimerState.active:
      case InactiveDetectorTimerState.initial:

        /// DO NOTHING
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      /// Translucent targets both receive events within their bounds and permit
      /// targets visually behind them to also receive events.
      behavior: HitTestBehavior.translucent,
      onPointerDown: (PointerDownEvent event) {
        /// reset timer when user interact with app
        _inactiveDetector.reset();
      },
      onPointerMove: (PointerMoveEvent event) {
        /// reset timer when user interact with app
        _inactiveDetector.reset();
      },
      onPointerUp: (PointerUpEvent event) {
        /// reset timer when user interact with app
        _inactiveDetector.reset();
      },
      child: BlocProvider<InactiveDetectorCubit>(
        create: (_) => _detectorCubit,
        child: BlocListener<InactiveDetectorCubit, InactiveDetectorState>(
          listener: (BuildContext context, InactiveDetectorState state) {
            if (state is SignOutSuccess) {
              LoginOnOldDeviceScreen.goNamed();
            }
          },
          child: widget.child,
        ),
      ),
    );
  }

  bool isShowIdleAWhileDialog = false;

  Future<void> showIdleAwhile() async {
    isShowIdleAWhileDialog = true;

    await evoDialogFunction.showDialogConfirm(
        alertType: DialogAlertType.warning,
        dialogId: EvoDialogId.warningIdleAWhileDialog,
        title: EvoStrings.idleAWhileTitle,
        textPositive: EvoStrings.idleAWhileKeepMeLoggedInCTA,
        textNegative: EvoStrings.idleAWhileLogMeOutCTA,
        footer: RichText(
          textAlign: TextAlign.center,
          text: TextSpan(
            style: evoTextStyles.regular(TextSize.sm),
            children: <TextSpan>[
              const TextSpan(text: EvoStrings.idleAWhileContent),
              TextSpan(
                text:
                    '${InActiveDetectorTimerImpl.defaultTimeOutInSec} ${EvoStrings.idleAWhileDurationInSec}',
                style: evoTextStyles.bold(TextSize.sm),
              ),
            ],
          ),
        ),
        onClickPositive: () {
          _enableDetectorTimer();
        },
        onClickNegative: () {
          _disableDetectorTimer();
          _detectorCubit.signOut();
        },
        onDismiss: () {
          _enableDetectorTimer();
        },
        autoClosePopupWhenClickCTA: true);

    isShowIdleAWhileDialog = false;
  }

  void showInActive() {
    if (isShowIdleAWhileDialog) {
      navigatorContext?.pop();
    }

    evoDialogFunction.showDialogConfirm(
      alertType: DialogAlertType.warning,
      dialogId: EvoDialogId.warningInActiveDialog,
      textPositive: EvoStrings.ctaLogInAgain,
      content: EvoStrings.inActiveDescription,
      title: EvoStrings.inActiveTitle,
      isDismissible: false,
      autoClosePopupWhenClickCTA: true,
      onClickPositive: () {
        _disableDetectorTimer();
        _detectorCubit.signOut();
      },
    );
  }

  void _enableDetectorTimer() {
    _inactiveDetector.start();
  }

  void _disableDetectorTimer() {
    _inactiveDetector.stop();
  }
}
