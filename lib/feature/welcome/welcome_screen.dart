import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../base/evo_page_state_base.dart';
import '../../resources/resources.dart';
import '../../util/screen_util.dart';
import '../../widget/appbar/evo_appbar.dart';
import '../account_activation/mobile_number_check/mobile_number_check_screen.dart';
import '../login/new_device/verify_username/verify_username_screen.dart';

class WelcomeScreen extends PageBase {
  const WelcomeScreen({super.key});

  static Future<void> goNamed() async {
    return navigatorContext?.goNamed(Screen.welcomeScreen.name);
  }

  @override
  EvoPageStateBase<WelcomeScreen> createState() => _WelcomeScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.welcomeScreen.routeName);
}

class _WelcomeScreenState extends EvoPageStateBase<WelcomeScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: evoColors.defaultWhite,
      appBar: EvoAppBar(
        leading: null,
        statusBarIconForAndroid: Brightness.dark,
        statusBarIconForIos: Brightness.light,
      ),
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.only(
              left: EvoDimension.screenHorizontalPadding,
              right: EvoDimension.screenHorizontalPadding,
              bottom: EvoDimension.screenBottomPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Align(
                child: imageProvider.asset(
                  EvoImages.imgBrandName,
                  fit: BoxFit.fill,
                  height: 64.w,
                  width: 163.w,
                  color: evoColors.primaryBase,
                ),
              ),
              Expanded(
                child: ConstrainedBox(
                  constraints: BoxConstraints.loose(Size.square(328.w)),
                  child: imageProvider.asset(
                    EvoImages.imgWelcome,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              _buildActiveBtn(),
              EvoDimension.space4,
              _buildLoginBtn(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActiveBtn() {
    return CommonButton(
      onPressed: () {
        MobileNumberCheckScreen.pushNamed();
      },
      style: evoButtonStyles.primary(ButtonSize.large),
      isWrapContent: false,
      child: const Text(EvoStrings.activateAccountMessage),
    );
  }

  Widget _buildLoginBtn() {
    return Center(
      child: GestureDetector(
        onTap: () {
          VerifyUsernameScreen.pushNamed();
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w),
          child: RichText(
            text: TextSpan(
              style: evoTextStyles.regular(TextSize.base),
              children: <TextSpan>[
                const TextSpan(text: EvoStrings.alreadyHaveAccountMessage),
                TextSpan(
                  text: EvoStrings.login,
                  style: TextStyle(color: evoColors.primaryBase),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
