import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../welcome_screen.dart';
import 'introduction_page.dart';
import 'widget/dot_indicator.dart';

class IntroductionScreen extends PageBase {
  const IntroductionScreen({super.key});

  static Future<void> goNamed() async {
    return navigatorContext?.goNamed(Screen.introductionScreen.name);
  }

  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Curve animationCurve = Curves.fastOutSlowIn;

  @override
  EvoPageStateBase<IntroductionScreen> createState() => _IntroductionScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.introductionScreen.routeName);
}

class _IntroductionScreenState extends EvoPageStateBase<IntroductionScreen> {
  final PageController _pageController = PageController();

  final List<String> _imageAssetList = <String>[
    EvoImages.imgIntroduction1,
    EvoImages.imgIntroduction2,
    EvoImages.imgIntroduction3,
  ];

  final List<String> _descriptionList = <String>[
    EvoStrings.introductionDescription1,
    EvoStrings.introductionDescription2,
    EvoStrings.introductionDescription3,
  ];

  final List<Color> _backgroundColor = <Color>[
    evoColors.primaryBase,
    evoColors.secondaryBase,
    evoColors.accentBase,
  ];

  final List<Color> _skipButtonColor = <Color>[
    evoColors.primaryText,
    evoColors.secondaryText,
    evoColors.accentText,
  ];


  int get _lastIndex => _imageAssetList.length - 1;
  bool get _isSkipButtonShowing => _curIndex < _lastIndex;

  int _curIndex = 0;

  Timer? _timer;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setTimer();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _setTimer() {
    _timer?.cancel();
    if (_curIndex < _lastIndex) {
      _timer = Timer(const Duration(seconds: 10), () {
        _pageController.nextPage(
          duration: IntroductionScreen.animationDuration,
          curve: IntroductionScreen.animationCurve,
        );
      });
    }
  }

  void _onPageChanged(int index) {
    setState(() {
      _curIndex = index;
      _setTimer();
    });
  }

  Future<void> _onSkip() async {
    await getIt.get<CommonSharedPreferencesHelper>().setPassedTutorial(true);
    WelcomeScreen.goNamed();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: evoColors.transparent,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.light,
      ),
      child: AnimatedContainer(
        color: _backgroundColor[_curIndex],
        duration: IntroductionScreen.animationDuration,
        curve: IntroductionScreen.animationCurve,
        child: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Padding(
                padding: EdgeInsets.symmetric(
                    horizontal: EvoDimension.screenHorizontalPadding, vertical: 24.w),
                child: evoImageProvider.asset(
                  EvoImages.imgBrandName,
                  height: 32.w,
                  color: evoColors.white,
                  fit: BoxFit.contain,
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                child: Row(
                  children: <Widget>[
                    DotIndicator(count: _imageAssetList.length, currentIndex: _curIndex),
                    const Spacer(),
                    _buildSkipButton(),
                  ],
                ),
              ),
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: _imageAssetList.length,
                  onPageChanged: _onPageChanged,
                  itemBuilder: (_, int index) => IntroductionPage(
                    image: _imageAssetList[index],
                    description: _descriptionList[index],
                    onSkip: index == _lastIndex ? _onSkip : null,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSkipButton() {
    return GestureDetector(
      onTap: _isSkipButtonShowing ? _onSkip : null,
      child: AnimatedOpacity(
        duration: IntroductionScreen.animationDuration,
        curve: IntroductionScreen.animationCurve,
        opacity: _isSkipButtonShowing ? 1 : 0,
        child: AnimatedContainer(
          height: 32.w,
          duration: IntroductionScreen.animationDuration,
          curve: IntroductionScreen.animationCurve,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(EvoDimension.borderRadius),
            color: _skipButtonColor[_curIndex],
          ),
          child: Padding(
            padding: EdgeInsets.only(left: 12.w, right: 8.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  EvoStrings.ctaSkip,
                  style: evoTextStyles.semibold(TextSize.base, color: evoColors.white),
                ),
                EvoDimension.space4,
                evoImageProvider.asset(
                  EvoImages.icArrowForward,
                  width: 16.w,
                  height: 16.w,
                  color: evoColors.white,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
