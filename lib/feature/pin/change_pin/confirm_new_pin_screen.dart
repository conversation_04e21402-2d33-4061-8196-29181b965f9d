import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../data/repository/user_repo.dart';
import '../../../model/challenge_success_model.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/global.dart';
import '../../../resources/ui_strings.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/functions.dart';
import '../../../util/screen_util.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../cubit/confirm_new_pin/confirm_new_pin_cubit.dart';
import '../widgets/confirm_new_pin/confirm_new_pin_widget.dart';
import 'change_pin_arg.dart';
import 'create_new_pin_flow.dart';

class ConfirmNewPinScreen extends PageBase {
  final String pin;
  final String? sessionToken;
  final CreateNewPinFlow flow;
  final ChallengeSuccessCallback onConfirmNewPinSuccess;

  const ConfirmNewPinScreen({
    required this.pin,
    required this.sessionToken,
    required this.flow,
    required this.onConfirmNewPinSuccess,
    super.key,
  });

  @override
  EvoPageStateBase<ConfirmNewPinScreen> createState() => _ConfirmNewPinState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: Screen.confirmNewPinScreen.name,
      );

  static Future<void> pushNamed({
    required String pin,
    required CreateNewPinFlow flow,
    required ChallengeSuccessCallback onSuccess,
    String? sessionToken,
  }) async {
    return navigatorContext?.pushNamed(
      Screen.confirmNewPinScreen.name,
      extra: ConfirmNewPinArgs(
        pin: pin,
        sessionToken: sessionToken,
        flow: flow,
        onSuccess: onSuccess,
      ),
    );
  }

  static Future<void> pushReplacementNamed({
    required String pin,
    required String sessionToken,
    required CreateNewPinFlow flow,
    required ChallengeSuccessCallback onSuccess,
  }) async {
    return navigatorContext?.pushReplacementNamed(
      Screen.confirmNewPinScreen.name,
      extra: ConfirmNewPinArgs(
        pin: pin,
        sessionToken: sessionToken,
        flow: flow,
        onSuccess: onSuccess,
      ),
    );
  }
}

class _ConfirmNewPinState extends EvoPageStateBase<ConfirmNewPinScreen> {
  late final ConfirmNewPinCubit _cubit = context.read<ConfirmNewPinCubit?>() ??
      ConfirmNewPinCubit(
        userRepo: getIt.get<UserRepo>(),
        authRepo: getIt.get<AuthenticationRepo>(),
      );

  final TextEditingController inputController = TextEditingController();

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: EvoAppBar(),
      body: BlocProvider<ConfirmNewPinCubit>(
        create: (BuildContext context) => _cubit,
        child: BlocConsumer<ConfirmNewPinCubit, ConfirmNewPinState>(
            listener: (_, ConfirmNewPinState state) {
          _handleStateChanged(state);
        }, builder: (BuildContext context, ConfirmNewPinState state) {
          return Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: ConfirmNewPinWidget(
                inputController: inputController,
                onSubmit: _onSubmit,
                errorMessage: _getErrorMessage(state),
                onChange: (String text) {
                  if (text.isEmpty) {
                    _cubit.resetState();
                  }
                },
              ));
        }),
      ),
    );
  }

  void _onSubmit(String confirmPin) {
    _cubit.onConfirm(
      pin: widget.pin,
      sessionToken: widget.sessionToken,
      confirmPin: confirmPin,
      flow: widget.flow,
    );
  }

  String? _getErrorMessage(ConfirmNewPinState state) {
    switch (state) {
      case PinUnmatchedState():
        return EvoStrings.mpinNotMatch;
      case ConfirmNewPinBadRequest():
        return state.error.userMessage;
      default:
        return null;
    }
  }

  void _handleStateChanged(ConfirmNewPinState state) {
    if (state is PinUnmatchedState) {
      return;
    }

    if (state is ConfirmLoadingState) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    switch (state) {
      case ConfirmNewPinSessionExpired():
        handleSessionExpired();
        return;
      case ConfirmNewPinFailure():
        handleEvoApiError(state.error);
        return;
      case ConfirmNewPinSuccess():
        widget.onConfirmNewPinSuccess(ChallengeSuccessModel(entity: state.entity));
        return;
      default:
        return;
    }
  }

  void handleSessionExpired() {
    switch (widget.flow) {
      case CreateNewPinFlow.createPin:
        evoDialogFunction.showDialogSessionTokenExpired(
            type: SessionDialogType.activateAccount,
            onClickPositive: () {
              navigatorContext?.popUntilNamed(
                Screen.mobileNumberCheckScreen.name,
              );
            });
      case CreateNewPinFlow.resetPin:
        evoDialogFunction.showDialogSessionTokenExpired(
            type: SessionDialogType.resetPin,
            onClickPositive: () {
              navigatorContext?.popUntilNamed(
                Screen.mainScreen.name,
              );
            });
      case CreateNewPinFlow.changePin:

        /// TODO: hoang-nguyen-2 update for case change pin
        commonLog('Change pin session expired');
    }
  }
}
