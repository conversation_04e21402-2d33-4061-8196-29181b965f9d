import 'package:flutter_common_package/base/page_base.dart';

import '../../../model/challenge_success_model.dart';
import 'create_new_pin_flow.dart';

class CreateNewPinArgs extends PageBaseArg {
  final String? sessionToken;
  final CreateNewPinFlow flow;
  final ChallengeSuccessCallback onSuccess;

  CreateNewPinArgs({
    required this.onSuccess,
    required this.sessionToken,
    required this.flow,
  });
}

class ConfirmNewPinArgs extends CreateNewPinArgs {
  final String pin;

  ConfirmNewPinArgs({
    required this.pin,
    required super.onSuccess,
    required super.sessionToken,
    required super.flow,
  });
}
