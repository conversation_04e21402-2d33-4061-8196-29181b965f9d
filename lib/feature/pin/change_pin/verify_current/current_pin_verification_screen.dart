// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../data/repository/user_repo.dart';
import '../../../../model/challenge_success_model.dart';
import '../../../../model/evo_dialog_id.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/dialog_functions.dart';
import '../../../../util/functions.dart';
import '../../../../widget/appbar/evo_support_appbar.dart';
import '../../../../widget/evo_mpin_code/evo_mpin_code_widget.dart';
import '../../new_pin_success_screen.dart';
import '../../reset_pin/reset_pin_handler.dart';
import '../create_new_pin_flow.dart';
import '../create_new_pin_screen.dart';
import 'cubit/current_pin_verification_cubit.dart';

class CurrentPINVerificationArg extends PageBaseArg {
  final String sessionToken;

  CurrentPINVerificationArg({required this.sessionToken});
}

/// [CurrentPINVerificationScreen] will be used in [ChangePin] flow
/// for verifying user's pin before moving on to create new pin
class CurrentPINVerificationScreen extends PageBase {
  final String sessionToken;

  const CurrentPINVerificationScreen({
    required this.sessionToken,
    super.key,
  });

  @override
  EvoPageStateBase<CurrentPINVerificationScreen> createState() =>
      _CurrentPINVerificationScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: Screen.currentPinVerificationScreen.name,
      );

  static Future<void> pushNamed({
    required String sessionToken,
  }) async {
    return navigatorContext?.pushNamed(Screen.currentPinVerificationScreen.name,
        extra: CurrentPINVerificationArg(
          sessionToken: sessionToken,
        ));
  }
}

class _CurrentPINVerificationScreenState extends EvoPageStateBase<CurrentPINVerificationScreen> {
  TextEditingController mpinInputController = TextEditingController();
  late final CurrentPINVerificationCubit _cubit = CurrentPINVerificationCubit(
    userRepo: getIt.get<UserRepo>(),
    appState: appState,
  );

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: EvoSupportAppbar(),
      body: SafeArea(
        child: _buildItemBody(),
      ),
    );
  }

  Widget _buildItemBody() {
    return BlocProvider<CurrentPINVerificationCubit>(
      create: (_) => _cubit,
      child: BlocConsumer<CurrentPINVerificationCubit, CurrentPINVerificationState>(
        listener: (_, CurrentPINVerificationState state) {
          _handleStateChanged(state);
        },
        builder: (_, CurrentPINVerificationState state) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  EvoStrings.inputPinTitle,
                  style: evoTextStyles.bold(TextSize.xl2),
                ),
                _buildPinWidget(state),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildPinWidget(CurrentPINVerificationState state) {
    final String? errorMessage = state is VerifyCurrentPinBadRequest ? state.errorMessage : null;
    return EvoMPINCodeWidget(
      textEditingController: mpinInputController,
      title: EvoStrings.inputPinDesc,
      errorMessage: errorMessage,
      onSubmit: (String pin) {
        _cubit.verifyCurrentPin(
          pin: pin,
          sessionToken: widget.sessionToken,
        );
      },
      onResetPin: _onRequestResetPin,
    );
  }

  Future<void> _showLockedResourceDialog(String? errorMessage) {
    return evoDialogFunction.showDialogConfirm(
        isDismissible: false,
        alertType: DialogAlertType.error,
        textPositive: EvoStrings.backToProfile,
        dialogId: EvoDialogId.changeMPINLockedResourceDialog,
        content: errorMessage,
        title: EvoStrings.changeMPINLockedResourceTitle,
        onClickPositive: () {
          /// close current dialog
          navigatorContext?.popUntilNamed(Screen.mainScreen.name);
        });
  }

  void _handleStateChanged(CurrentPINVerificationState state) {
    if (state is VerifyCurrentPinLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    switch (state) {
      case VerifyCurrentPinSuccess():
        CreateNewPinScreen.pushReplacement(
          sessionToken: state.sessionToken,
          flow: CreateNewPinFlow.changePin,
          onSuccess: _onConfirmNewPinSuccess,
        );
        break;
      case VerifyCurrentPinFailure():
        handleEvoApiError(state.error);
        return;
      case VerifyCurrentPinLocked():
        _showLockedResourceDialog(state.errorMessage);
        return;
      default:
        return;
    }
  }

  void _onRequestResetPin() {
    final String? phoneNumber = appState.userInfo.value?.phoneNumber;

    getIt.get<ResetPinHandler>().requestResetPin(
          phoneNumber: phoneNumber,
          onError: handleEvoApiError,
          entryScreenName: widget.routeSettings.name,
        );
  }

  void _onConfirmNewPinSuccess(ChallengeSuccessModel _) {
    NewPinSuccessScreen.pushReplacementNamed(
      buttonText: EvoStrings.backToProfile,
      onNext: () {
        navigatorContext?.popUntilNamed(
          Screen.profileScreen.name,
        );
      },
    );
  }
}
