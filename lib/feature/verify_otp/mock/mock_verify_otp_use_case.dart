enum MockVerifyOtpUseCase {
  getSignInOtpSuccess('sign_in_otp_success.json'),
  getVerifySignInOtpSuccess('verify_sign_in_otp_success.json'),
  getVerifySignInOtpIncorrectOtp('verify_sign_in_otp_incorrect_otp.json'),
  getVerifySignInResendOtpLimitExceeded('verify_sign_in_resend_otp_limit_exceeded.json'),
  getActivateAccountVerifyOtpSuccess('get_activate_account_verify_otp_success.json'),
  // Activation Status
  getActivateAccountVerifyOtpRejected('get_activate_account_verify_otp_rejected.json'),
  getActivateAccountVerifyOtpProcessing('get_activate_account_verify_otp_processing.json'),
  getActivateAccountVerifyOtpNone('get_activate_account_verify_otp_none.json'),
  getActivateAccountVerifyOtpExisting('get_activate_account_verify_otp_existing.json'),
  getActivateAccountVerifyOtpCancelled('get_activate_account_verify_otp_cancelled.json');

  final String value;

  const MockVerifyOtpUseCase(this.value);
}

String getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase mockCase) {
  return mockCase.value;
}
