import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../base/evo_page_state_base.dart';
import '../../data/repository/authentication_repo.dart';
import '../../model/evo_dialog_id.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/dialog_functions.dart';
import '../../util/functions.dart';
import '../../widget/appbar/evo_appbar.dart';
import '../welcome/welcome_screen.dart';
import 'cubit/verify_otp_cubit.dart';
import 'handler/verify_otp_handler_factory.dart';
import 'widget/evo_otp_widget.dart';
import 'widget/evo_otp_widget_controller.dart';

enum VerifyOtpType {
  signIn,
  resetPin,
  activateCard,
  activateAccount,
  email,
}

enum ResendStatus { disabled, success }

class VerifyOtpPageArg extends PageBaseArg {
  final VerifyOtpType verifyOtpType;
  final String? contactInfo;
  final String? sessionToken;
  final int? otpResendSecs;
  final int? otpValiditySecs;
  final String? resendSessionToken;
  final void Function(VerifyOtpState)? onPopSuccess;

  VerifyOtpPageArg({
    required this.verifyOtpType,
    this.contactInfo,
    this.otpResendSecs,
    this.otpValiditySecs,
    this.onPopSuccess,
    this.sessionToken,
    this.resendSessionToken,
  });
}

class VerifyOtpPage extends PageBase {
  static Future<void> pushNamed(
      {required String? contactInfo,
      required int? otpResendSecs,
      required int? otpValiditySecs,
      required VerifyOtpType verifyOtpType,
      void Function(VerifyOtpState)? onPopSuccess,
      String? resendSessionToken,
      String? sessionToken}) async {
    return navigatorContext?.pushNamed(Screen.verifyOtpScreen.name,
        extra: VerifyOtpPageArg(
          verifyOtpType: verifyOtpType,
          contactInfo: contactInfo,
          otpResendSecs: otpResendSecs,
          otpValiditySecs: otpValiditySecs,
          onPopSuccess: onPopSuccess,
          sessionToken: sessionToken,
          resendSessionToken: resendSessionToken,
        ));
  }

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.verifyOtpScreen.routeName);

  final VerifyOtpType verifyOtpType;

  /// The contact information (phone number or email address) to which the OTP was sent.
  final String? contactInfo;
  final int? otpResendSecs;
  final int? otpValiditySecs;
  final String? sessionToken;
  final String? resendSessionToken;
  final void Function(VerifyOtpState)? onPopSuccess;

  const VerifyOtpPage({
    required this.verifyOtpType,
    super.key,
    this.contactInfo,
    this.otpResendSecs,
    this.otpValiditySecs,
    this.onPopSuccess,
    this.sessionToken,
    this.resendSessionToken,
  });

  @override
  EvoPageStateBase<VerifyOtpPage> createState() => _SignInOtpPageState();
}

class _SignInOtpPageState extends EvoPageStateBase<VerifyOtpPage> {
  late VerifyOtpCubit _cubit;
  final OtpWidgetController otpWidgetController = OtpWidgetController();

  late int otpResendSecs;
  late int otpValiditySecs;
  String? errorText;
  String? sessionToken;

  @override
  void initState() {
    _cubit = context.read<VerifyOtpCubit?>() ??
        VerifyOtpCubit(
            verifyHandler: VerifyOtpHandlerFactory.createHandler(
                widget.verifyOtpType, getIt.get<AuthenticationRepo>()));
    otpResendSecs = widget.otpResendSecs ?? 0;
    otpValiditySecs = widget.otpValiditySecs ?? 0;
    sessionToken = widget.sessionToken;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      otpWidgetController.startCountdown();
    });
    super.initState();
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<VerifyOtpCubit>(
        create: (_) => _cubit,
        child: BlocConsumer<VerifyOtpCubit, VerifyOtpState>(
            listener: (BuildContext context, VerifyOtpState state) {
          _handleListener(state);
        }, builder: (BuildContext context, VerifyOtpState state) {
          return Scaffold(
              appBar: EvoAppBar(),
              body: Padding(
                padding: EdgeInsets.only(
                    left: EvoDimension.screenHorizontalPadding,
                    right: EvoDimension.screenHorizontalPadding,
                    bottom: EvoDimension.screenBottomPadding),
                child: Column(children: [
                  _buildHeaderWidget(),
                  EvoDimension.space16,
                  _buildOtpWidget(),
                ]),
              ));
        }));
  }

  Widget _buildHeaderWidget() {
    final List<Widget> widgets = [
      Align(
        alignment: Alignment.centerLeft,
        child: Text(
          EvoStrings.verifyOtpScreenTitle,
          style: evoTextStyles.semibold(TextSize.h3),
        ),
      )
    ];

    final String? desc = _getDesc();
    if (desc != null) {
      widgets.addAll(<Widget>[
        EvoDimension.space4,
        Text(desc, style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase)),
      ]);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: widgets,
    );
  }

  String? _getDesc() {
    if (widget.verifyOtpType == VerifyOtpType.email) {
      return EvoStrings.verifyEmailOtpScreenDesc;
    }
    return EvoStrings.verifyOtpScreenDesc;
  }

  Widget _buildOtpWidget() {
    return EvoOtpWidget(
      onSubmit: (String value) {
        errorText = null;
        _cubit.verifyOtp(value, sessionToken);
      },
      errorText: errorText,
      otpValiditySecs: otpValiditySecs,
      controller: otpWidgetController,
      onResendOtp: () {
        errorText = null;
        _cubit.resendOtp(widget.contactInfo ?? '', widget.resendSessionToken);
      },
    );
  }

  void _showDialogBlock({String? title, String? content}) {
    evoDialogFunction.showDialogConfirm(
        alertType: DialogAlertType.error,
        title: title,
        content: content,
        dialogId: EvoDialogId.otpBlockedErrorDialog,
        isDismissible: false,
        textPositive: EvoStrings.backToHomePage,
        onClickPositive: () {
          WelcomeScreen.goNamed();
        });
  }

  void _handleOtpComplete(VerifyOtpState state) {
    navigatorContext?.pop();
    widget.onPopSuccess?.call(state);
  }

  void _handleShowLoading(VerifyOtpState state) {
    state is VerifyOtpLoading ? evoUtilFunction.showHudLoading() : evoUtilFunction.hideHudLoading();
  }

  void _handleSessionTokenExpired() {
    final SessionDialogType? type = SessionDialogType.fromVerifyOtpType(widget.verifyOtpType);
    final void Function()? onClickPositive = switch (type) {
      SessionDialogType.activateAccount => () {
          navigatorContext?.popUntilNamed(Screen.mobileNumberCheckScreen.name);
        },
      _ => null,
    };

    evoDialogFunction.showDialogSessionTokenExpired(
      type: type,
      onClickPositive: onClickPositive,
    );
  }

  void _handleListener(VerifyOtpState state) {
    _handleShowLoading(state);
    switch (state) {
      case ResendOtpSuccess():
        otpResendSecs = state.otpResendSecs ?? 0;
        sessionToken = state.sessionToken;
        otpWidgetController.startCountdown();
        return;
      case VerifyOtpSuccess() || VerifyOtpThirdPartyError():
        _handleOtpComplete(state);
        return;
      case LimitExceedOtp():
        _showDialogBlock(
          content: state.errorText ?? EvoStrings.tryAgainLater,
          title: state.isResent ? EvoStrings.limitResendOtp : EvoStrings.maxTriesReached,
        );
        return;
      case VerifyOtpFailed():
        if (state.unknownError) {
          handleEvoApiError(state.error);
        } else {
          errorText = state.error.userMessage ?? EvoStrings.otpInvalidMsg;
        }
        return;
      case VerifyOtpSessionExpired():
        _handleSessionTokenExpired();
        return;
      case VerifyOtpInitial():
      case VerifyOtpLoading():
        return;
      case VerifyEmailOtpDuplicateEmailError():
        _handleOtpComplete(state.copyWith(
          email: widget.contactInfo,
          sessionToken: widget.resendSessionToken,
        ));
    }
  }
}
