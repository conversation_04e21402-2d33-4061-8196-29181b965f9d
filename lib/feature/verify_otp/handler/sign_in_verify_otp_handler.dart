import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/response/sign_in_entity.dart';
import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';
import '../mock/mock_verify_otp_use_case.dart';
import 'verify_otp_handler.dart';

class SignInVerifyOtpHandler extends VerifyOtpHandler {
  final AuthenticationRepo authRepo;

  SignInVerifyOtpHandler({
    required this.authRepo,
  });

  @override
  Future<ResendOtpModel> resendOtp(String contactInfo, Map<String, String>? data) async {
    final SignInEntity entity = await authRepo.signIn(TypeLogin.otp,
        phoneNumber: contactInfo,
        mockConfig: MockConfig(
          enable: false,
          fileName: getMockVerifyOtpFileNameByCase(
            MockVerifyOtpUseCase.getVerifySignInOtpSuccess,
          ),
          statusCode: CommonHttpClient.INVALID_TOKEN,
        ));

    return getOtpModelByEntity(
      entity,
      onSuccess: () {
        return ResendOtpModel.success(
          resendOtpSuccess: ResendOtpSuccess(
            sessionToken: entity.sessionToken,
            otpResendSecs: entity.otpResendSecs,
            otpValiditySecs: entity.otpValiditySecs,
          ),
        );
      },
      onError: (OtpErrorType type) {
        return ResendOtpModel.error(
          errorType: type,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      },
    );
  }

  @override
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data) async {
    final String sessionToken = data?['sessionToken'] ?? '';
    final SignInEntity entity = await authRepo.signIn(
      TypeLogin.verifyOTP,
      otp: otp,
      sessionToken: sessionToken,
      mockConfig: MockConfig(
        enable: true,
        fileName: getMockVerifyOtpFileNameByCase(
          MockVerifyOtpUseCase.getVerifySignInOtpSuccess,
        ),
      ),
    );

    return getOtpModelByEntity(
      entity,
      onSuccess: () {
        return VerifyOtpModel.success(
          successModel: OtpSuccessModel(
            challengeType: entity.challengeType,
            sessionToken: entity.sessionToken,
            ekycClientSettings: entity.ekycClientSettings,
          ),
        );
      },
      onError: (OtpErrorType type) {
        return VerifyOtpModel.error(
          errorType: type,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      },
    );
  }
}
