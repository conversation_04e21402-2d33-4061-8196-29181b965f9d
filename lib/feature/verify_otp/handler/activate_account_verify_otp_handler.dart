import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/request/activate_account_request.dart';
import '../../../data/response/account_activation_entity.dart';
import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';
import '../mock/mock_verify_otp_use_case.dart';
import 'verify_otp_handler.dart';

class ActivateAccountVerifyOtpHandler extends VerifyOtpHandler {
  final AuthenticationRepo authRepo;

  ActivateAccountVerifyOtpHandler({
    required this.authRepo,
  });

  @override
  Future<ResendOtpModel> resendOtp(String contactInfo, Map<String, String>? _) async {
    final AccountActivationEntity entity = await authRepo.activateAccount(
        request: ActivateAccountVerifyPhoneNumberRequest(
          phoneNumber: contactInfo,
        ),
        mockConfig: MockConfig(
          enable: false,
          statusCode: CommonHttpClient.LIMIT_EXCEEDED,
          fileName: getMockVerifyOtpFileNameByCase(
            MockVerifyOtpUseCase.getVerifySignInResendOtpLimitExceeded,
          ),
        ));

    return getOtpModelByEntity(
      entity,
      onSuccess: () {
        return ResendOtpModel.success(
          resendOtpSuccess: ResendOtpSuccess(
            sessionToken: entity.sessionToken,
            otpResendSecs: entity.otpResendSecs,
            otpValiditySecs: entity.otpValiditySecs,
          ),
        );
      },
      onError: (OtpErrorType type) {
        return ResendOtpModel.error(
          errorType: type,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      },
    );
  }

  @override
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data) async {
    String? sessionToken = '';
    if (data?.containsKey('sessionToken') == true) {
      sessionToken = data?['sessionToken'];
    }

    final AccountActivationEntity entity = await authRepo.activateAccount(
        request: ActivateAccountVerifyOTPRequest(
          otp: otp,
          sessionToken: sessionToken,
        ),
        mockConfig: MockConfig(
          enable: false,
          fileName: getMockVerifyOtpFileNameByCase(
            MockVerifyOtpUseCase.getVerifySignInResendOtpLimitExceeded,
          ),
        ));
    return generateVerifyOtpModelFromEntity(entity);
  }

  @visibleForTesting
  VerifyOtpModel generateVerifyOtpModelFromEntity(AccountActivationEntity entity) {
    return getOtpModelByEntity(
      entity,
      onSuccess: () {
        return VerifyOtpModel.success(
          successModel: OtpSuccessModel(
            challengeType: entity.challengeType,
            sessionToken: entity.sessionToken,
            verdict: entity.verdict,
          ),
        );
      },
      onError: (OtpErrorType type) {
        return VerifyOtpModel.error(
          errorType: type,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      },
    );
  }
}
