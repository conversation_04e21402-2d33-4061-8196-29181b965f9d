
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';

enum OtpErrorType {
  sessionExpired,
  limitExceeded,
  unknown,
  oneLastTry,
  invalidParams,

  /// fails due to third-party service issues
  thirdParty,
  duplicateEmail,
}

class VerifyOtpModel {
  OtpSuccessModel? successModel;
  ErrorUIModel? errorUIModel;
  OtpErrorType? errorType;

  bool get isSuccess => successModel != null;

  factory VerifyOtpModel.success({required OtpSuccessModel successModel}) {
    return VerifyOtpModel._(successModel: successModel);
  }

  factory VerifyOtpModel.error({
    required OtpErrorType errorType,
    required ErrorUIModel errorUIModel,
  }) {
    return VerifyOtpModel._(errorType: errorType, errorUIModel: errorUIModel);
  }

  VerifyOtpModel._({
    this.successModel,
    this.errorUIModel,
    this.errorType,
  });
}

class ResendOtpModel {
  ResendOtpSuccess? resendOtpSuccess;
  ErrorUIModel? errorUIModel;
  OtpErrorType? errorType;

  bool get isSuccess => resendOtpSuccess != null;

  factory ResendOtpModel.success({required ResendOtpSuccess resendOtpSuccess}) {
    return ResendOtpModel._(resendOtpSuccess: resendOtpSuccess);
  }

  factory ResendOtpModel.error({
    required OtpErrorType errorType,
    required ErrorUIModel errorUIModel,
  }) {
    return ResendOtpModel._(
      errorType: errorType,
      errorUIModel: errorUIModel,
    );
  }

  ResendOtpModel._({
    this.resendOtpSuccess,
    this.errorUIModel,
    this.errorType,
  });
}

abstract class VerifyOtpHandler {
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data);

  /// The [contactInfo] can be a phone number or an email address.
  Future<ResendOtpModel> resendOtp(String contactInfo, Map<String, String>? data);

  OtpErrorType getOtpErrorTypeByStatusCode(BaseEntity? entity) {
    final int? statusCode = entity?.statusCode;
    return switch (statusCode) {
      CommonHttpClient.BAD_REQUEST => OtpErrorType.invalidParams,
      CommonHttpClient.INVALID_TOKEN => OtpErrorType.sessionExpired,
      CommonHttpClient.LIMIT_EXCEEDED ||
      CommonHttpClient.LOCKED_RESOURCE =>
        OtpErrorType.limitExceeded,
      _ => OtpErrorType.unknown,
    };
  }

  T getOtpModelByEntity<T>(
    BaseEntity entity, {
    required T Function() onSuccess,
    required T Function(OtpErrorType type) onError,
  }) {
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      return onSuccess();
    }
    final OtpErrorType errorType = getOtpErrorTypeByStatusCode(entity);
    return onError(errorType);
  }
}
