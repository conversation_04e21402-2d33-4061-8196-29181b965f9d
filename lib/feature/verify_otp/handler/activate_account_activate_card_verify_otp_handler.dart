import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/request/activate_account_request.dart';
import '../../../data/response/account_activation_entity.dart';
import '../../account_activation/mock/mock_account_activation_use_case.dart';
import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';
import 'verify_otp_handler.dart';

class ActivateAccountActivateCardVerifyOtpHandler extends VerifyOtpHandler {
  final AuthenticationRepo authRepo;

  ActivateAccountActivateCardVerifyOtpHandler({required this.authRepo});

  @override
  Future<ResendOtpModel> resendOtp(String contactInfo, Map<String, String>? data) async {
    final AccountActivationEntity entity = await authRepo.activateAccount(
        request: ActivateAccountActivateCardRequest(
          sessionToken: data?['sessionToken'],
          skip: false,
        ),
        mockConfig: MockConfig(
          enable: false,
          fileName: getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getCreateUsernameBadRequest,
          ),
        ));

    return getOtpModelByEntity(
      entity,
      onSuccess: () {
        return ResendOtpModel.success(
          resendOtpSuccess: ResendOtpSuccess(
            sessionToken: entity.sessionToken,
            otpResendSecs: entity.otpResendSecs,
            otpValiditySecs: entity.otpValiditySecs,
          ),
        );
      },
      onError: (OtpErrorType type) {
        return ResendOtpModel.error(
          errorType: type,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      },
    );
  }

  @override
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data) async {
    final AccountActivationEntity entity = await authRepo.activateAccount(
        request: ActivateAccountActivateCardVerifyOtpRequest(
          otp: otp,
          sessionToken: data?['sessionToken'],
        ),
        mockConfig: MockConfig(
          enable: false,
          fileName: getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getNoneChallengeType,
          ),
        ));

    return getOtpModelByEntity(
      entity,
      onSuccess: () {
        return VerifyOtpModel.success(
          successModel: OtpSuccessModel(
            challengeType: entity.challengeType,
            sessionToken: entity.sessionToken,
            verdict: entity.verdict,
          ),
        );
      },
      onError: (OtpErrorType type) {
        return VerifyOtpModel.error(
          errorType: type,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      },
    );
  }

  @override
  OtpErrorType getOtpErrorTypeByStatusCode(BaseEntity? entity) {
    late OtpErrorType errorType;
    if (_isThirdPartyError(entity?.verdict)) {
      errorType = OtpErrorType.thirdParty;
    } else {
      errorType = super.getOtpErrorTypeByStatusCode(entity);
    }

    return errorType;
  }

  bool _isThirdPartyError(String? verdict) {
    return <String>[
      AccountActivationEntity.verdictInvalidEntityId,
      AccountActivationEntity.verdictInvalidKitNo,
      AccountActivationEntity.verdictKitNotMappedToEntity
    ].contains(verdict);
  }
}
