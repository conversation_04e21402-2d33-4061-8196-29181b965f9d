import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/request/activate_account_request.dart';
import '../../../data/response/account_activation_entity.dart';
import '../../account_activation/mock/mock_account_activation_use_case.dart';
import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';
import 'verify_otp_handler.dart';

class ActivateAccountActivateCardVerifyOtpHandler implements VerifyOtpHandler {
  final AuthenticationRepo authRepo;

  ActivateAccountActivateCardVerifyOtpHandler({required this.authRepo});

  @override
  Future<ResendOtpModel> resendOtp(String contactInfo, Map<String, String>? data) async {
    final AccountActivationEntity entity = await authRepo.activateAccount(
        request: ActivateAccountActivateCardRequest(
          sessionToken: data?['sessionToken'],
          skip: false,
        ),
        mockConfig: MockConfig(
          enable: false,
          fileName: getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getCreateUsernameBadRequest,
          ),
        ));

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      return ResendOtpModel.success(
        resendOtpSuccess: ResendOtpSuccess(
          sessionToken: entity.sessionToken,
          otpResendSecs: entity.otpResendSecs,
          otpValiditySecs: entity.otpValiditySecs,
        ),
      );
    }

    final OtpErrorType errorType = _getOtpErrorTypeByStatusCode(entity.statusCode);
    return ResendOtpModel.error(
      errorType: errorType,
      errorUIModel: ErrorUIModel.fromEntity(entity),
    );
  }

  @override
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data) async {
    final AccountActivationEntity entity = await authRepo.activateAccount(
        request: ActivateAccountActivateCardVerifyOtpRequest(
          otp: otp,
          sessionToken: data?['sessionToken'],
        ),
        mockConfig: MockConfig(
          enable: false,
          fileName: getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getNoneChallengeType,
          ),
        ));

    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      return VerifyOtpModel.success(
        successModel: OtpSuccessModel(
          challengeType: entity.challengeType,
          sessionToken: entity.sessionToken,
          verdict: entity.verdict,
        ),
      );
    }

    final OtpErrorType errorType = _getVerifyOtpErrorType(entity);
    return VerifyOtpModel.error(
      errorType: errorType,
      errorUIModel: ErrorUIModel.fromEntity(entity),
    );
  }

  OtpErrorType _getVerifyOtpErrorType(
    AccountActivationEntity entity,
  ) {
    final int? statusCode = entity.statusCode;

    late OtpErrorType errorType;
    if (_isThirdPartyError(entity.verdict)) {
      errorType = OtpErrorType.thirdParty;
    } else {
      errorType = _getOtpErrorTypeByStatusCode(statusCode);
    }

    return errorType;
  }

  bool _isThirdPartyError(String? verdict) {
    return <String>[
      AccountActivationEntity.verdictInvalidEntityId,
      AccountActivationEntity.verdictInvalidKitNo,
      AccountActivationEntity.verdictKitNotMappedToEntity
    ].contains(verdict);
  }

  OtpErrorType _getOtpErrorTypeByStatusCode(int? statusCode) {
    return switch (statusCode) {
      CommonHttpClient.BAD_REQUEST => OtpErrorType.invalidParams,
      CommonHttpClient.INVALID_TOKEN => OtpErrorType.sessionExpired,
      CommonHttpClient.LIMIT_EXCEEDED ||
      CommonHttpClient.LOCKED_RESOURCE =>
        OtpErrorType.limitExceeded,
      _ => OtpErrorType.unknown,
    };
  }
}
