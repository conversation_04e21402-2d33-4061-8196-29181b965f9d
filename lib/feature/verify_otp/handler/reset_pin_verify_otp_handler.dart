import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/request/reset_pin_request.dart';
import '../../../data/response/reset_pin_entity.dart';
import '../../../util/mock_file_name_utils/mock_authentication_file_name.dart';
import '../../pin/mock/mock_pin_use_case.dart';
import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';
import 'verify_otp_handler.dart';

class ResetPinVerifyOtpHandler extends VerifyOtpHandler {
  final AuthenticationRepo authRepo;

  ResetPinVerifyOtpHandler({required this.authRepo});

  @override
  Future<ResendOtpModel> resendOtp(String contactInfo, Map<String, String>? data) async {
    final ResetPinEntity entity = await authRepo.resetPin(
        request: InitializeResetPinRequest(phoneNumber: contactInfo),
        mockConfig: MockConfig(
          enable: false,
          fileName: getRequestResetPinMockFileName(),
        ));
    return getOtpModelByEntity(
      entity,
      onSuccess: () {
        return ResendOtpModel.success(
          resendOtpSuccess: ResendOtpSuccess(
            sessionToken: entity.sessionToken,
            otpResendSecs: entity.otpResendSecs,
            otpValiditySecs: entity.otpValiditySecs,
          ),
        );
      },
      onError: (OtpErrorType type) {
        return ResendOtpModel.error(
          errorType: type,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      },
    );
  }

  @override
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data) async {
    final String sessionToken = data?['sessionToken'] ?? '';
    final ResetPinEntity entity = await authRepo.resetPin(
      request: ResetPinVerifyOTPRequest(
        otp: otp,
        sessionToken: sessionToken,
      ),
      mockConfig: MockConfig(
        enable: true,
        fileName: getMockPinFileNameByCase(
          MockPinUseCase.getResetPinVerifyOTPSuccess,
        ),
      ),
    );

    return getOtpModelByEntity(
      entity,
      onSuccess: () {
        return VerifyOtpModel.success(
          successModel: OtpSuccessModel(
            challengeType: entity.challengeType,
            sessionToken: entity.sessionToken,
            ekycClientSettings: entity.ekycClientSettings,
          ),
        );
      },
      onError: (OtpErrorType type) {
        return VerifyOtpModel.error(
          errorType: type,
          errorUIModel: ErrorUIModel.fromEntity(entity),
        );
      },
    );
  }
}
