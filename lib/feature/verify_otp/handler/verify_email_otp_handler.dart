// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/request/activate_account_request.dart';
import '../../../data/response/account_activation_entity.dart';
import '../../account_activation/mock/mock_account_activation_use_case.dart';
import '../cubit/otp_success_model.dart';
import '../cubit/verify_otp_cubit.dart';
import 'verify_otp_handler.dart';

class VerifyEmailOtpHandler extends VerifyOtpHandler {
  final AuthenticationRepo authRepo;

  VerifyEmailOtpHandler({required this.authRepo});

  @override
  Future<ResendOtpModel> resendOtp(String contactInfo, Map<String, String>? data) async {
    final AccountActivationEntity entity = await authRepo.activateAccount(
      request: ActivateAccountVerifyEmailRequest(
        email: contactInfo,
        sessionToken: data?['sessionToken'],
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockAccountActivationFileNameByCase(
          MockAccountActivationUseCase.getVerifyEmailOtpChallengeType,
        ),
      ),
    );

    return getOtpModelByEntity(
      entity,
      onSuccess: () => ResendOtpModel.success(
        resendOtpSuccess: ResendOtpSuccess(
          sessionToken: entity.sessionToken,
          otpResendSecs: entity.otpResendSecs,
          otpValiditySecs: entity.otpValiditySecs,
        ),
      ),
      onError: (OtpErrorType type) => ResendOtpModel.error(
        errorType: type,
        errorUIModel: ErrorUIModel.fromEntity(entity),
      ),
    );
  }

  @override
  Future<VerifyOtpModel> verifyOtp(String otp, Map<String, String>? data) async {
    final AccountActivationEntity entity = await authRepo.activateAccount(
        request: ActivateAccountVerifyEmailOTPRequest(
          otp: otp,
          sessionToken: data?['sessionToken'],
        ),
        mockConfig: MockConfig(
          enable: false,
          fileName: getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getNoneChallengeType,
          ),
        ));

    return getOtpModelByEntity(
      entity,
      onSuccess: () => VerifyOtpModel.success(
        successModel: OtpSuccessModel(
          challengeType: entity.challengeType,
          sessionToken: entity.sessionToken,
          verdict: entity.verdict,
        ),
      ),
      onError: (OtpErrorType type) => VerifyOtpModel.error(
        errorType: type,
        errorUIModel: ErrorUIModel.fromEntity(entity),
      ),
    );
  }

  @override
  OtpErrorType getOtpErrorTypeByStatusCode(BaseEntity? entity) {
    if (entity?.statusCode == CommonHttpClient.DUPLICATE &&
        entity?.verdict == AccountActivationEntity.verdictDuplicate) {
      return OtpErrorType.duplicateEmail;
    }
    return super.getOtpErrorTypeByStatusCode(entity);
  }
}
