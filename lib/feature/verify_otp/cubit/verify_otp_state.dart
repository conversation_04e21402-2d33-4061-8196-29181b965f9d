part of 'verify_otp_cubit.dart';

@immutable
sealed class VerifyOtpState implements BlocState {}

class VerifyOtpInitial extends VerifyOtpState {}

class VerifyOtpLoading extends VerifyOtpState {}

class LimitExceedOtp extends VerifyOtpState {
  final String? errorText;
  final bool isResent;

  LimitExceedOtp({required this.errorText, this.isResent = false});
}

class ResendOtpSuccess extends VerifyOtpState {
  final int? otpResendSecs;
  final String? sessionToken;
  final int? otpValiditySecs;

  ResendOtpSuccess({
    required this.otpResendSecs,
    required this.sessionToken,
    required this.otpValiditySecs,
  });
}

class VerifyOtpFailed extends VerifyOtpState {
  final ErrorUIModel error;
  final bool unknownError;

  VerifyOtpFailed({
    required this.error,
    this.unknownError = true,
  });
}

class VerifyOtpSessionExpired extends VerifyOtpState {}

class VerifyOtpSuccess extends VerifyOtpState {
  final OtpSuccessModel uiModel;

  VerifyOtpSuccess(this.uiModel);
}

class VerifyOtpThirdPartyError extends VerifyOtpState {
  final ErrorUIModel error;

  VerifyOtpThirdPartyError(this.error);
}

/// This state is for the email OTP step only, it is emitted when the target email is duplicate.
class VerifyEmailOtpDuplicateEmailError extends VerifyOtpState {
  final ErrorUIModel error;
  final String? email;
  final String? sessionToken;

  VerifyEmailOtpDuplicateEmailError(
    this.error, {
    this.email,
    this.sessionToken,
  });

  VerifyEmailOtpDuplicateEmailError copyWith({
    String? email,
    String? sessionToken,
  }) {
    return VerifyEmailOtpDuplicateEmailError(
      error,
      email: email,
      sessionToken: sessionToken,
    );
  }
}
