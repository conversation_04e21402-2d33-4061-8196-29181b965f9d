// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/request/reset_pin_request.dart';
import '../../../../data/response/reset_pin_entity.dart';
import '../../../pin/mock/mock_pin_use_case.dart';
import 'selfie_verification_strategy.dart';

/// Strategy for handling selfie verification during PIN reset flow.
/// This strategy calls the reset PIN API with face authentication request.
class ResetPinSelfieStrategy implements SelfieVerificationStrategy {
  final AuthenticationRepo _authRepo;

  const ResetPinSelfieStrategy(this._authRepo);

  @override
  Future<BaseEntity> verifySelfie({
    required String selfieType,
    required String? sessionToken,
    List<String>? imageIds,
    List<String>? videoIds,
  }) async {
    final ResetPinEntity resetPinEntity = await _authRepo.resetPin(
      request: ResetPinFaceAuthRequest(
        imageIds: imageIds,
        videoIds: videoIds,
        selfieType: selfieType,
        sessionToken: sessionToken,
      ),
      mockConfig: MockConfig(
        enable: true,
        fileName: getMockPinFileNameByCase(
          MockPinUseCase.getResetPinFaceAuthSuccess,
        ),
      ),
    );

    return resetPinEntity;
  }
}
