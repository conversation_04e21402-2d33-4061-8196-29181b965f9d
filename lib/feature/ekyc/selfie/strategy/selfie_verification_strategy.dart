// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/data/response/base_entity.dart';

/// Abstract strategy interface for selfie verification across different flow types.
/// Each concrete strategy implements the specific logic for verifying selfies
/// in different authentication flows (reset P<PERSON>, sign in, account activation, etc.).
abstract class SelfieVerificationStrategy {
  /// Verifies selfie for the specific flow type this strategy handles.
  ///
  /// Parameters:
  /// - [selfieType]: The type of selfie verification (e.g., liveness mode value)
  /// - [sessionToken]: Session token for authentication
  /// - [imageIds]: List of captured image IDs from the selfie session
  /// - [videoIds]: List of captured video IDs from the selfie session
  ///
  /// Returns a [BaseEntity] containing the verification result.
  Future<BaseEntity> verifySelfie({
    required String selfieType,
    required String? sessionToken,
    List<String>? imageIds,
    List<String>? videoIds,
  });
}
