// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import '../../../../data/repository/authentication_repo.dart';
import '../selfie_verification_flow_type.dart';
import 'activate_account_selfie_strategy.dart';
import 'reset_pin_selfie_strategy.dart';
import 'selfie_verification_strategy.dart';
import 'log_in_selfie_strategy.dart';

/// Factory class responsible for creating appropriate selfie verification strategies
/// based on the flow type. This factory encapsulates the strategy creation logic
/// and provides a clean interface for obtaining the correct strategy implementation.
class SelfieVerificationStrategyFactory {
  /// Creates and returns the appropriate strategy for the given flow type.
  ///
  /// Parameters:
  /// - [flowType]: The type of selfie verification flow
  /// - [authRepo]: Authentication repository dependency for API calls
  ///
  /// Returns the concrete strategy implementation for the specified flow type.
  SelfieVerificationStrategy create(
    SelfieVerificationFlowType flowType,
    AuthenticationRepo authRepo,
  ) {
    return switch (flowType) {
      SelfieVerificationFlowType.resetPin => ResetPinSelfieStrategy(authRepo),
      SelfieVerificationFlowType.logIn => LogInSelfieStrategy(authRepo),
      SelfieVerificationFlowType.activateAccount => ActivateAccountSelfieStrategy(authRepo),
    };
  }
}
