// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/request/activate_account_request.dart';
import '../../../../data/response/account_activation_entity.dart';
import '../../../account_activation/mock/mock_account_activation_use_case.dart';
import 'selfie_verification_strategy.dart';

/// Strategy for handling selfie verification during account activation flow.
/// This strategy calls the activate account API with selfie verification request.
class ActivateAccountSelfieStrategy implements SelfieVerificationStrategy {
  final AuthenticationRepo _authRepo;

  const ActivateAccountSelfieStrategy(this._authRepo);

  @override
  Future<BaseEntity> verifySelfie({
    required String selfieType,
    required String? sessionToken,
    List<String>? imageIds,
    List<String>? videoIds,
  }) async {
    final AccountActivationEntity entity = await _authRepo.activateAccount(
      request: ActivateAccountVerifySelfieRequest(
        selfieType: selfieType,
        imageIds: imageIds,
        videoIds: videoIds,
        sessionToken: sessionToken,
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockAccountActivationFileNameByCase(
          MockAccountActivationUseCase.getCreateUsernameChallengeType,
        ),
      ),
    );

    return entity;
  }
}
