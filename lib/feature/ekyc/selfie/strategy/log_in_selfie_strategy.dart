// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/request/login_new_device_request.dart';
import '../../../../data/response/login_new_device_entity.dart';
import '../../../login/mock/mock_login_new_device_file_name_use_case.dart';
import 'selfie_verification_strategy.dart';

/// Strategy for handling selfie verification during sign-in flow.
/// This strategy calls the login new device API with selfie authentication request.
class LogInSelfieStrategy implements SelfieVerificationStrategy {
  final AuthenticationRepo _authRepo;

  const LogInSelfieStrategy(this._authRepo);

  @override
  Future<BaseEntity> verifySelfie({
    required String selfieType,
    required String? sessionToken,
    List<String>? imageIds,
    List<String>? videoIds,
  }) async {
    final LoginNewDeviceEntity loginNewDeviceEntity = await _authRepo.loginNewDevice(
      request: SelfieAuthRequest(
        sessionToken: sessionToken,
        imageIds: imageIds,
        videoIds: videoIds,
        selfieType: selfieType,
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockLoginNewDeviceFileNameByCase(
          MockLoginNewDeviceFileNameUseCase.getVerifySelfieSuccess,
        ),
      ),
    );

    return loginNewDeviceEntity;
  }
}
