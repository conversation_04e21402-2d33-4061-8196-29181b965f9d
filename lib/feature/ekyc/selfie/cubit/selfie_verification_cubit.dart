import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/facial_verification_handler.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_initialize_args.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_initialize_result.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_start_capturing_args.dart';
import 'package:flutter_common_package/feature/ekyc/facial_verification/models/facial_verification_start_capturing_result.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/response/account_activation_entity.dart';
import '../model/selfie_error_ui_model.dart';
import '../selfie_verification_flow_type.dart';
import '../strategy/selfie_verification_strategy.dart';
import '../strategy/selfie_verification_strategy_factory.dart';
import 'selfie_error_factory.dart';

part 'selfie_verification_state.dart';

class SelfieVerificationCubit extends CommonCubit<SelfieVerificationState> {
  final FacialVerificationHandler selfieHandler;
  final SelfieVerificationErrorFactory errorFactory;
  final SelfieVerificationStrategyFactory selfieVerificationStrategyFactory;
  final AuthenticationRepo authRepo;

  SelfieVerificationCubit({
    required this.selfieHandler,
    required this.errorFactory,
    required this.authRepo,
    required this.selfieVerificationStrategyFactory
  }) : super(SelfieVerificationInitial());

  Future<void> initialize(String? sessionToken) async {
    if (sessionToken == null) {
      commonLog(
        'session_token should not be null',
        methodName: 'initialize',
      );
      emit(SelfieVerificationFailure(
          error: SelfieErrorUiModel(
        actionType: ErrorActionType.blocked,
      )));
      return;
    }

    emit(SelfieVerificationLoading());
    final FacialVerificationInitializeResult initResult = await selfieHandler.initialize(
        args: FacialVerificationInitializeArgs(
      sessionToken: sessionToken,
      languageCode: 'en',
    ));

    if (initResult is FacialVerificationInitializeErrorResult) {
      _handleInitializedError(
        bridgeError: initResult.bridgeErrorReason,
        errorEntity: initResult.apiErrorResponse,
      );
      return;
    }

    emit(InitializeBridgeSuccess());
    return;
  }

  Future<void> captureSelfie({
    required EkycBridgeLivenessMode liveMode,
  }) async {
    emit(SelfieVerificationProcessing());
    final FacialVerificationStartCapturingResult result = await selfieHandler.startCapturing(
        args: FacialVerificationStartCapturingArgs(
      livenessMode: liveMode,
    ));

    if (result is! FacialVerificationStartCapturingSuccessResult) {
      _handleCaptureError(result);
      return;
    }

    emit(SelfieCapturingSuccess(
      imageIds: result.imageIds,
      videoIds: result.videoIds,
    ));
  }

  void _handleInitializedError({
    EkycBridgeErrorReason? bridgeError,
    BaseEntity? errorEntity,
  }) {
    final SelfieErrorUiModel errorUiModel = errorFactory.create(
      entity: errorEntity,
      bridgeError: bridgeError,
    );
    emit(SelfieVerificationFailure(error: errorUiModel));
  }

  void _handleCaptureError(FacialVerificationStartCapturingResult result) {
    late SelfieErrorUiModel errorUiModel;

    if (result is FacialVerificationStartCapturingErrorResult) {
      errorUiModel = errorFactory.create(
        entity: result.apiErrorResponse,
        bridgeError: result.bridgeErrorReason,
      );
    } else {
      errorUiModel = errorFactory.create();
    }

    emit(SelfieVerificationFailure(error: errorUiModel));
  }

  Future<void> verifySelfie({
    required SelfieVerificationFlowType flowType,
    required EkycBridgeLivenessMode liveMode,
    required String? sessionToken,
    List<String>? imageIds,
    List<String>? videoIds,
  }) async {
    final SelfieVerificationStrategy strategy = selfieVerificationStrategyFactory.create(
      flowType,
      authRepo,
    );

    final BaseEntity entity = await strategy.verifySelfie(
      selfieType: liveMode.value,
      imageIds: imageIds,
      videoIds: videoIds,
      sessionToken: sessionToken,
    );

    if (checkEntityIsSuccess(entity)) {
      emit(SelfieVerificationSuccess(entity));
    } else {
      final SelfieErrorUiModel error = errorFactory.create(entity: entity);
      emit(SelfieVerificationFailure(error: error));
    }
  }

  final List<String> _successVerdicts = <String>[
    BaseEntity.verdictSuccess,
    // In the account activation flow, if the next challenge is `verify_email`,
    // the success verdict could be `success_and_email_exists`, which is not an eKYC error.
    AccountActivationEntity.verdictSuccessEmailExists,
  ];

  @visibleForTesting
  bool checkEntityIsSuccess(BaseEntity entity) {
    return entity.statusCode == CommonHttpClient.SUCCESS &&
        _successVerdicts.contains(entity.verdict);
  }
}
