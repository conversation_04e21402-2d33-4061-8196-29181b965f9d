import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../resources/global.dart';
import '../../../../resources/ui_strings.dart';
import '../model/selfie_error_ui_model.dart';

class SelfieVerificationErrorFactory {
  /// A function like [PageStateBase.getMessageByErrorCode].
  final String Function(int?)? getMessageByErrorCode;

  SelfieVerificationErrorFactory({
    this.getMessageByErrorCode,
  });

  SelfieErrorUiModel create({
    BaseEntity? entity,
    EkycBridgeErrorReason? bridgeError,
  }) {
    if (bridgeError != null) {
      return createBridgeError(bridgeError);
    }
    if (entity != null) {
      return createApiError(entity);
    }
    return createUnknownError();
  }

  @visibleForTesting
  SelfieErrorUiModel createApiError(BaseEntity entity) {
    return SelfieErrorUiModel(
      actionType: getApiErrorActionType(entity),
      title: getApiErrorTitle(entity),
      description: getApiErrorDescription(entity),
    );
  }

  @visibleForTesting
  ErrorActionType getApiErrorActionType(BaseEntity entity) {
    switch (entity.statusCode) {
      case CommonHttpClient.INVALID_TOKEN:
        return ErrorActionType.showExpiredToken;
      case CommonHttpClient.LOCKED_RESOURCE:
      case CommonHttpClient.LIMIT_EXCEEDED:
        return ErrorActionType.blocked;
      case CommonHttpClient.UNKNOWN_ERRORS:
        // For error code 500, non-null verdict indicates a retry error action
        return entity.verdict != null ? ErrorActionType.retry : ErrorActionType.blocked;
      default:
        return ErrorActionType.retry;
    }
  }

  @visibleForTesting
  String getApiErrorTitle(BaseEntity entity) {
    if (entity.userMessageTitle != null) {
      return entity.userMessageTitle!;
    }
    switch (entity.statusCode) {
      case CommonHttpClient.LIMIT_EXCEEDED:
      case CommonHttpClient.LOCKED_RESOURCE:
        return EvoStrings.selfieMaxTriesReached;
      case CommonHttpClient.UNKNOWN_ERRORS:
        // For error code 500, non-null verdict indicates a different UI
        return entity.verdict != null
            ? EvoStrings.selfieErrorTitle
            : EvoStrings.errorUnknownErrorTitle;
      default:
        return EvoStrings.selfieErrorTitle;
    }
    ;
  }

  @visibleForTesting
  String getApiErrorDescription(BaseEntity entity) {
    if (entity.userMessage != null) {
      return entity.userMessage!;
    }
    switch (entity.statusCode) {
      case CommonHttpClient.LIMIT_EXCEEDED:
      case CommonHttpClient.LOCKED_RESOURCE:
        return EvoStrings.tryAgainLater;
      case CommonHttpClient.UNKNOWN_ERRORS:
        // For error code 500, non-null verdict indicates a different UI
        return entity.verdict != null
            ? EvoStrings.selfieErrorSubtitle
            : EvoStrings.errorUnknownErrorDesc
                .replaceVariableByValue(<String>[ContactInfo.supportNumber]);
      default:
        return getMessageByErrorCode?.call(entity.statusCode) ?? EvoStrings.selfieErrorSubtitle;
    }
  }

  @visibleForTesting
  SelfieErrorUiModel createUnknownError() {
    return SelfieErrorUiModel(
      actionType: ErrorActionType.retry,
    );
  }

  @visibleForTesting
  SelfieErrorUiModel createBridgeError(EkycBridgeErrorReason error) {
    switch (error) {
      case EkycBridgeErrorReason.userCancelled:
        return SelfieErrorUiModel(
          actionType: ErrorActionType.ignore,
        );
      case EkycBridgeErrorReason.exceedLimit:
        return SelfieErrorUiModel(
            title: EvoStrings.maxTriesReached,
            description: EvoStrings.tryAgainLater,
            actionType: ErrorActionType.blocked);
      case EkycBridgeErrorReason.sessionExpired:
      case EkycBridgeErrorReason.initWithInvalidSession:
        return SelfieErrorUiModel(
          actionType: ErrorActionType.showExpiredToken,
        );
      default:
        return createUnknownError();
    }
  }
}
