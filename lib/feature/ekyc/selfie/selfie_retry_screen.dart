import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/buttons.dart';
import '../../../widget/no_app_bar_wrapper.dart';

class SelfieRetryScreenArg extends PageBaseArg {
  final String? error;
  final VoidCallback onRetry;

  SelfieRetryScreenArg({required this.onRetry, this.error});
}

class SelfieRetryScreen extends PageBase {
  final String? error;
  final VoidCallback onRetry;

  const SelfieRetryScreen({required this.onRetry, this.error, super.key});

  static void pushNamed({required VoidCallback onRetry, String? error}) {
    navigatorContext?.pushNamed(
      Screen.selfieRetryScreen.name,
      extra: SelfieRetryScreenArg(onRetry: onRetry, error: error),
    );
  }

  @override
  State<SelfieRetryScreen> createState() => _SelfieRetryScreenState<SelfieRetryScreen>();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.selfieRetryScreen.routeName);
}

class _SelfieRetryScreenState<T extends PageBase> extends EvoPageStateBase<SelfieRetryScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return NoAppBarWrapper(
      child: PopScope(
        canPop: false,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
          child: Column(
            children: <Widget>[
              const Spacer(),
              evoImageProvider.asset(EvoImages.icAlertUnsuccessful, width: 140.w, height: 140.w),
              EvoDimension.space16,
              Text(
                EvoStrings.selfieErrorTitle,
                textAlign: TextAlign.center,
                style: evoTextStyles.bold(TextSize.h3, color: evoColors.grayText),
              ),
              EvoDimension.space8,
              Text(
                widget.error ?? EvoStrings.selfieErrorSubtitle,
                textAlign: TextAlign.center,
                style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
              ),
              const Spacer(),
              PrimaryButton(text: EvoStrings.retry, onTap: _retry),
              SizedBox(height: EvoDimension.screenBottomPadding),
            ],
          ),
        ),
      ),
    );
  }

  void _retry() {
    navigatorContext?.pop();
    widget.onRetry();
  }
}
