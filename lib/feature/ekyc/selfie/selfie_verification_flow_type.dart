// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import '../../../util/dialog_functions.dart';

enum SelfieVerificationFlowType {
  logIn,
  resetPin,
  activateAccount;

  SessionDialogType get dialogType {
    return switch (this) {
      SelfieVerificationFlowType.logIn => SessionDialogType.newDeviceLogIn,
      SelfieVerificationFlowType.resetPin => SessionDialogType.resetPin,
      SelfieVerificationFlowType.activateAccount => SessionDialogType.activateAccount,
    };
  }
}
