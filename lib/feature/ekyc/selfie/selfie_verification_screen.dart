import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../model/challenge_success_model.dart';
import '../../../resources/resources.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../../camera_permission/camera_permission_builder.dart';
import '../../camera_permission/cubit/camera_permission_state.dart';
import 'cubit/selfie_error_factory.dart';
import 'selfie_verification_flow_type.dart';
import 'widgets/selfie_verification_selfie_body.dart';
import 'widgets/selfie_verification_state_widget.dart';

class SelfieVerificationScreenArgs extends PageBaseArg {
  final String? sessionToken;
  final ChallengeSuccessCallback onPopSuccess;
  final SelfieVerificationFlowType flowType;

  SelfieVerificationScreenArgs({
    required this.onPopSuccess,
    required this.flowType,
    this.sessionToken,
  });
}

class SelfieVerificationScreen extends PageBase {
  final String? sessionToken;
  final ChallengeSuccessCallback onPopSuccess;
  final SelfieVerificationFlowType flowType;

  const SelfieVerificationScreen({
    required this.onPopSuccess,
    required this.flowType,
    this.sessionToken,
    super.key,
  });

  static Future<void> pushNamed({
    required SelfieVerificationFlowType flowType,
    required ChallengeSuccessCallback onPopSuccess,
    String? sessionToken,
  }) async {
    return navigatorContext?.pushNamed(
      Screen.selfieVerificationScreen.name,
      extra: SelfieVerificationScreenArgs(
        flowType: flowType,
        sessionToken: sessionToken,
        onPopSuccess: onPopSuccess,
      ),
    );
  }

  static Future<void> pushReplacementNamed({
    required SelfieVerificationFlowType flowType,
    required ChallengeSuccessCallback onPopSuccess,
    String? sessionToken,
  }) async {
    return navigatorContext?.pushReplacementNamed(
      Screen.selfieVerificationScreen.name,
      extra: SelfieVerificationScreenArgs(
        flowType: flowType,
        sessionToken: sessionToken,
        onPopSuccess: onPopSuccess,
      ),
    );
  }

  @override
  EvoPageStateBase<SelfieVerificationScreen> createState() => _SelfieVerificationIntroScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: Screen.selfieVerificationScreen.routeName,
      );
}

class _SelfieVerificationIntroScreenState extends EvoPageStateBase<SelfieVerificationScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      appBar: EvoAppBar(leading: null),
      body: CameraPermissionBuilder(builder: (_, CameraPermissionState state) {
        return SafeArea(
          child: _buildContent(state),
        );
      }),
    );
  }

  Widget _buildContent(CameraPermissionState state) {
    return switch (state) {
      CameraPermissionInitial() => SelfieVerificationStateWidget(
          status: VerificationStatus.initializing,
        ),
      CameraPermissionDenied() => SelfieVerificationStateWidget(
          status: VerificationStatus.none,
        ),
      CameraPermissionGranted() => SelfieVerificationSelfieBody(
          onPopSuccess: widget.onPopSuccess,
          flowType: widget.flowType,
          sessionToken: widget.sessionToken,
          errorFactory: SelfieVerificationErrorFactory(
            getMessageByErrorCode: getMessageByErrorCode,
          ),
        )
    };
  }
}
