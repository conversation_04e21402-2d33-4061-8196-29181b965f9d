import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../model/challenge_success_model.dart';

typedef VerifierSuccessCallback = void Function(ChallengeSuccessModel);
typedef VerifierErrorCallback = void Function(ErrorUIModel?);

/// Strategy interface for handling different login new device challenges
abstract class BaseNewDeviceLoginVerifier {
  /// Handle the specific login challenge
  ///
  /// [model] - The model containing the challenge data and any additional information
  /// [onSuccess] - Callback for when the challenge is successfully completed
  /// [onError] - Callback for error handling
  void handle({
    required ChallengeSuccessModel model,
    required VerifierSuccessCallback onSuccess,
    required VerifierErrorCallback onError,
  });
}
