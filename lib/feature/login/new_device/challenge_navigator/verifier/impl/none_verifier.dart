import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../../model/challenge_success_model.dart';
import '../../../../../main_screen/main_screen.dart';
import '../base_new_device_login_verifier.dart';

/// Strategy for handling the completion of the login flow
class NoneVerifier implements BaseNewDeviceLoginVerifier {
  @override
  void handle({
    required ChallengeSuccessModel model,
    required void Function(ChallengeSuccessModel) onSuccess,
    required void Function(ErrorUIModel?) onError,
  }) {
    // Login flow is complete, navigate to the main screen
    MainScreen.goNamed(isLoggedIn: true);
  }
}
