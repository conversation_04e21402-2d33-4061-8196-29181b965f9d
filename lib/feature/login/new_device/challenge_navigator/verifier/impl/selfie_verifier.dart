import '../../../../../../data/response/login_new_device_entity.dart';
import '../../../../../../model/challenge_success_model.dart';
import '../../../../../ekyc/selfie/selfie_verification_flow_type.dart';
import '../../../../../ekyc/selfie/selfie_verification_screen.dart';
import '../../../verify_mpin/new_device_verify_mpin_screen.dart';
import '../base_new_device_login_verifier.dart';

/// Strategy for handling Selfie verification challenges
class SelfieVerifier implements BaseNewDeviceLoginVerifier {
  @override
  void handle({
    required ChallengeSuccessModel model,
    required VerifierSuccessCallback onSuccess,
    required VerifierErrorCallback onError,
  }) {
    final LoginNewDeviceEntity entity = model.entity as LoginNewDeviceEntity;

    SelfieVerificationScreen.pushReplacementNamed(
        flowType: SelfieVerificationFlowType.logIn,
        onPopSuccess: onSuccess,
        sessionToken: entity.sessionToken);
  }
}
