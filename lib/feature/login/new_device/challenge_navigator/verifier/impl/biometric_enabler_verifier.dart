import '../../../../../../data/response/login_new_device_entity.dart';
import '../../../../../../model/challenge_success_model.dart';
import '../../../../../biometric/activate_biometric/activate_biometric_page.dart';
import '../../../../../biometric/biometric_token/providers/biometric_token_provider_config.dart';
import '../base_new_device_login_verifier.dart';

/// Strategy for handling biometric enable challenges
class BiometricEnablerVerifier implements BaseNewDeviceLoginVerifier {
  @override
  void handle({
    required ChallengeSuccessModel model,
    required VerifierSuccessCallback onSuccess,
    required VerifierErrorCallback onError,
  }) {
    final LoginNewDeviceEntity entity = model.entity as LoginNewDeviceEntity;

    ActivateBiometricScreen.pushNamed(
      onSuccess: onSuccess,
      sessionToken: entity.sessionToken,
      flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
    );
  }
}
