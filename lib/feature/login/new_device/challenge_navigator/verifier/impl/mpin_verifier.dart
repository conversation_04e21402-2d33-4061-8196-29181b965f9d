import '../../../../../../data/response/login_new_device_entity.dart';
import '../../../../../../model/challenge_success_model.dart';
import '../../../verify_mpin/new_device_verify_mpin_screen.dart';
import '../base_new_device_login_verifier.dart';

/// Strategy for handling MPIN verification challenges
class MPinVerifier implements BaseNewDeviceLoginVerifier {
  @override
  void handle({
    required ChallengeSuccessModel model,
    required VerifierSuccessCallback onSuccess,
    required VerifierErrorCallback onError,
  }) {
    final LoginNewDeviceEntity entity = model.entity as LoginNewDeviceEntity;
    final String username = model.additionalData?[ChallengeSuccessModel.usernameKey];

    NewDeviceVerifyMPinScreen.pushNamed(
      sessionToken: entity.sessionToken,
      username: username,
      onPopSuccess: onSuccess,
    );
  }
}
