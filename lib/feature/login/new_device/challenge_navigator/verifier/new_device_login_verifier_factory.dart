import '../new_device_login_challenge_type.dart';
import 'base_new_device_login_verifier.dart';
import 'impl/biometric_enabler_verifier.dart';
import 'impl/mpin_verifier.dart';
import 'impl/none_verifier.dart';
import 'impl/selfie_verifier.dart';

/// Factory for creating login new device challenge strategies
class NewDeviceLoginVerifierFactory {
  /// Returns the appropriate verifier for the given challenge type
  BaseNewDeviceLoginVerifier? getStrategy(NewDeviceLoginChallengeType type) {
    switch (type) {
      case NewDeviceLoginChallengeType.verifyPin:
        return MPinVerifier();
      case NewDeviceLoginChallengeType.faceAuth:
        return SelfieVerifier();
      case NewDeviceLoginChallengeType.enableBiometric:
        return BiometricEnablerVerifier();
      case NewDeviceLoginChallengeType.none:

        /// complete challenge user
        return NoneVerifier();
      default:
        return null;
    }
  }
}
