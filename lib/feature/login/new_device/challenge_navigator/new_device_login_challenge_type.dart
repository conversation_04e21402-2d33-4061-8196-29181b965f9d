/// Enum representing the different types of login challenges for new devices
enum NewDeviceLoginChallengeType {
  verifyPin('verify_pin'),
  faceAuth('face_auth'),
  enableBiometric('biometric_token'),
  none('none'),
  unsupported('unsupported');

  final String value;

  const NewDeviceLoginChallengeType(this.value);

  static NewDeviceLoginChallengeType fromString(String? value) {
    switch (value) {
      case 'verify_pin':
        return NewDeviceLoginChallengeType.verifyPin;
      case 'face_auth':
        return NewDeviceLoginChallengeType.faceAuth;
      case 'biometric_token':
        return NewDeviceLoginChallengeType.enableBiometric;
      case 'none':
        return NewDeviceLoginChallengeType.none;
      default:
        return NewDeviceLoginChallengeType.unsupported;
    }
  }
}
