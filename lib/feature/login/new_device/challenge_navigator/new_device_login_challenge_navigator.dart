import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/base_new_device_login_verifier.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/new_device_login_verifier_factory.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../data/response/login_new_device_entity.dart';
import '../../../../model/challenge_success_model.dart';
import 'new_device_login_challenge_type.dart';

/// Handler for navigating through login new device challenges
class NewDeviceLoginChallengeNavigator {
  final void Function(ErrorUIModel? uiModel) _onError;
  final NewDeviceLoginVerifierFactory verifierFactory;

  NewDeviceLoginChallengeNavigator({
    required void Function(ErrorUIModel? uiModel) onError,
    required this.verifierFactory,
  }) : _onError = onError;

  /// Handles the next challenge in the login flow
  void nextChallenge(ChallengeSuccessModel model) {
    if (model.entity is! LoginNewDeviceEntity) {
      commonLog('Entity ${model.entity.runtimeType} must be an instance of LoginNewDeviceEntity',
          methodName: 'nextChallenge');
      _onError(ErrorUIModel());
      return;
    }

    final LoginNewDeviceEntity loginNewDeviceEntity = model.entity as LoginNewDeviceEntity;
    final NewDeviceLoginChallengeType type =
        NewDeviceLoginChallengeType.fromString(loginNewDeviceEntity.challengeType);

    // Get the appropriate verifier and handle the challenge
    final BaseNewDeviceLoginVerifier? strategy = verifierFactory.getStrategy(type);
    strategy?.handle(
      model: model,
      onSuccess: nextChallenge,
      onError: _onError,
    );
  }
}
