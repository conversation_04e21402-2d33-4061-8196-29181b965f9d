// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

sealed class NewDeviceVerifyMPinState implements BlocState {}

class NewDeviceVerifyMPinInitial extends NewDeviceVerifyMPinState {}

class NewDeviceVerifyMPinLoading extends NewDeviceVerifyMPinState {}

class NewDeviceVerifyMPinSuccess extends NewDeviceVerifyMPinState {
  final BaseEntity entity;

  NewDeviceVerifyMPinSuccess(this.entity);
}

class NewDeviceVerifyMPinFailure extends NewDeviceVerifyMPinState {
  final ErrorUIModel error;

  NewDeviceVerifyMPinFailure({required this.error});
}

class NewDeviceVerifyMPinFailureBadRequest extends NewDeviceVerifyMPinFailure {
  NewDeviceVerifyMPinFailureBadRequest({required super.error});
}

class NewDeviceVerifyMPinFailureSessionExpired extends NewDeviceVerifyMPinFailure {
  NewDeviceVerifyMPinFailureSessionExpired({required super.error});
}

class NewDeviceVerifyMPinFailureLimitExceeded extends NewDeviceVerifyMPinFailure {
  NewDeviceVerifyMPinFailureLimitExceeded({required super.error});
}
