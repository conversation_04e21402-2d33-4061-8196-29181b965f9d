// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../base/evo_page_state_base.dart';
import '../../../../data/repository/authentication_repo.dart';
import '../../../../model/challenge_success_model.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/dialog_functions.dart';
import '../../../../util/functions.dart';
import '../../../../util/validator/mpin_validator.dart';
import '../../../../widget/evo_mpin_code/evo_mpin_code_widget.dart';
import '../../../../widget/no_app_bar_wrapper.dart';
import 'new_device_verify_mpin_cubit.dart';
import 'new_device_verify_mpin_state.dart';

class NewDeviceVerifyMPinArg extends PageBaseArg {
  final String? username;

  final String? sessionToken;

  final ChallengeSuccessCallback onPopSuccess;

  NewDeviceVerifyMPinArg({
    required this.onPopSuccess,
    this.username,
    this.sessionToken,
  });
}

class NewDeviceVerifyMPinScreen extends PageBase {
  final String? username;

  final String? sessionToken;

  final ChallengeSuccessCallback onPopSuccess;

  const NewDeviceVerifyMPinScreen({
    required this.onPopSuccess,
    this.username,
    this.sessionToken,
    super.key,
  });

  @override
  State<NewDeviceVerifyMPinScreen> createState() => _NewDeviceVerifyMPinScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.newDeviceVerifyMPinScreen.routeName);

  static void pushNamed({
    required ChallengeSuccessCallback onPopSuccess,
    String? username,
    String? sessionToken,
  }) {
    navigatorContext?.pushNamed(
      Screen.newDeviceVerifyMPinScreen.name,
      extra: NewDeviceVerifyMPinArg(
        username: username,
        sessionToken: sessionToken,
        onPopSuccess: onPopSuccess,
      ),
    );
  }
}

class _NewDeviceVerifyMPinScreenState extends EvoPageStateBase<NewDeviceVerifyMPinScreen> {
  late final NewDeviceVerifyMPinCubit _cubit = context.read<NewDeviceVerifyMPinCubit?>() ??
      NewDeviceVerifyMPinCubit(
          authRepo: getIt.get<AuthenticationRepo>(), mpinValidator: MpinValidator());

  final TextEditingController _controller = TextEditingController();

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<NewDeviceVerifyMPinCubit>(
      create: (_) => _cubit,
      child: BlocListener<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
        listener: (_, NewDeviceVerifyMPinState state) => _listenNewDeviceVerifyMPinState(state),
        child: NoAppBarWrapper(
          child: PopScope(
            canPop: false,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  _buildTitle(),
                  EvoDimension.space4,
                  _buildMPinField(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _listenNewDeviceVerifyMPinState(NewDeviceVerifyMPinState state) {
    if (state is NewDeviceVerifyMPinLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }
    evoUtilFunction.hideHudLoading();

    if (state is NewDeviceVerifyMPinSuccess) {
      widget.onPopSuccess.call(ChallengeSuccessModel(entity: state.entity));
      return;
    }
    if (state is NewDeviceVerifyMPinFailureSessionExpired) {
      evoDialogFunction.showDialogSessionTokenExpired();
      return;

    }
    if (state is NewDeviceVerifyMPinFailureLimitExceeded) {
      evoDialogFunction.showDialogErrorLimitExceeded(
        type: SessionDialogType.newDeviceLogIn,
        content: state.error.userMessage,
      );
      return;
    }
    if (state is NewDeviceVerifyMPinFailureBadRequest) {
      // ignore this state, since it will be handled by the MPIN field
      return;
    }
    if (state is NewDeviceVerifyMPinFailure) {
      handleEvoApiError(state.error);
    }
  }

  Widget _buildTitle() {
    return Text(
      EvoStrings.verifyMPinTitle.replaceVariableByValue([widget.username ?? '']),
      style: evoTextStyles.semibold(TextSize.h3),
    );
  }

  Widget _buildMPinField() {
    return BlocBuilder<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
      buildWhen: (NewDeviceVerifyMPinState prev, NewDeviceVerifyMPinState curr) =>
          prev is NewDeviceVerifyMPinFailureBadRequest ||
          curr is NewDeviceVerifyMPinFailureBadRequest,
      builder: (_, NewDeviceVerifyMPinState state) {
        String? error;
        if (state is NewDeviceVerifyMPinFailureBadRequest) {
          error = state.error.userMessage;
        }
        return EvoMPINCodeWidget(
          textEditingController: _controller,
          title: EvoStrings.verifyMPinDesc,
          errorMessage: error,
          onChange: _cubit.onChangePin,
          onResetPin: _onResetPin,
          onSubmit: _onSubmit,
        );
      },
    );
  }

  void _onSubmit(String pin) {
    _cubit.verifyPin(pin: pin, sessionToken: widget.sessionToken);
  }

  void _onResetPin() {
    // TODO: do reset MPIN flow
  }
}
