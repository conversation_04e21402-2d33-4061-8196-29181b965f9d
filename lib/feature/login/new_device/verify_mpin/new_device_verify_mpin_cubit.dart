// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/login/mock/mock_login_new_device_file_name_use_case.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/request/login_new_device_request.dart';
import '../../../../data/response/login_new_device_entity.dart';
import '../../../../util/validator/mpin_validator.dart';
import 'new_device_verify_mpin_state.dart';

class NewDeviceVerifyMPinCubit extends CommonCubit<NewDeviceVerifyMPinState> {
  final AuthenticationRepo authRepo;
  final MpinValidator mpinValidator;

  NewDeviceVerifyMPinCubit({required this.authRepo, required this.mpinValidator})
      : super(NewDeviceVerifyMPinInitial());

  Future<void> verifyPin({required String pin, required String? sessionToken}) async {
    final String trimmedValue = pin.trim();
    final String? validateMessage = mpinValidator.validate(trimmedValue);
    if (validateMessage != null) {
      emit(NewDeviceVerifyMPinFailureBadRequest(error: ErrorUIModel(userMessage: validateMessage)));
      return;
    }

    if (sessionToken?.isEmpty ?? false) {
      emit(NewDeviceVerifyMPinFailureSessionExpired(error: ErrorUIModel()));
      return;
    }

    emit(NewDeviceVerifyMPinLoading());

    final LoginNewDeviceEntity result = await authRepo.loginNewDevice(
        request: VerifyMPinRequest(
          pin: trimmedValue,
          sessionToken: sessionToken!,
        ),
        mockConfig: MockConfig(
            enable: false,
            fileName: getMockLoginNewDeviceFileNameByCase(
                MockLoginNewDeviceFileNameUseCase.getVerifyMPinSuccess)));

    _handleVerifyResult(result);
  }

  void onChangePin(String? pin) {
    if (pin == null || pin.isEmpty) {
      emit(NewDeviceVerifyMPinInitial());
    }
  }

  void _handleVerifyResult(LoginNewDeviceEntity entity) {
    final int? statusCode = entity.statusCode;
    if (statusCode == CommonHttpClient.SUCCESS) {
      emit(NewDeviceVerifyMPinSuccess(entity));
      return;
    }

    final ErrorUIModel error = ErrorUIModel.fromEntity(entity);
    switch (statusCode) {
      case CommonHttpClient.BAD_REQUEST:
        emit(NewDeviceVerifyMPinFailureBadRequest(error: error));
        break;
      case CommonHttpClient.LIMIT_EXCEEDED:
      case CommonHttpClient.LOCKED_RESOURCE:
        emit(NewDeviceVerifyMPinFailureLimitExceeded(error: error));
        break;
      case CommonHttpClient.INVALID_TOKEN:
        emit(NewDeviceVerifyMPinFailureSessionExpired(error: error));
        break;
      default:
        emit(NewDeviceVerifyMPinFailure(error: error));
    }
  }
}
