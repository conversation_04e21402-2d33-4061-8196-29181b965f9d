import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/response/login_new_device_entity.dart';

sealed class VerifyUsernameState extends BlocState {}

class VerifyUsernameInitial extends VerifyUsernameState {}

class VerifyUsernameLoading extends VerifyUsernameState {}

class VerifyUsernameSuccess extends VerifyUsernameState {
  final String username;
  final LoginNewDeviceEntity entity;

  VerifyUsernameSuccess({required this.username, required this.entity});
}

class VerifyUsernameError extends VerifyUsernameState {
  final ErrorUIModel error;

  VerifyUsernameError({required this.error});
}

class VerifyUsernameLockedState extends VerifyUsernameState {
  final ErrorUIModel error;

  VerifyUsernameLockedState({required this.error});
}

class InvalidUsernameErrorState extends VerifyUsernameState {
  final String? errorMessage;

  InvalidUsernameErrorState({required this.errorMessage});
}

class UsernameModifiedState extends VerifyUsernameState {
  final String username;

  UsernameModifiedState({required this.username});
}
