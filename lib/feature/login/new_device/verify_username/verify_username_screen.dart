import 'package:evoapp/widget/appbar/evo_appbar_leading_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/response/login_new_device_entity.dart';
import '../../../../model/challenge_success_model.dart';
import '../../../../model/evo_dialog_id.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../resources/resources.dart';
import '../../../../util/dialog_functions.dart';
import '../../../../util/functions.dart';
import '../../../../util/screen_util.dart';
import '../../../../util/validator/username_validator.dart';
import '../../../../widget/app_bar_wrapper.dart';
import '../../../../widget/appbar/evo_appbar.dart';
import '../../../../widget/evo_text_field.dart';
import '../../../../widget/no_app_bar_wrapper.dart';
import '../../../../widget/question_cta_text.dart';
import '../../../welcome/welcome_screen.dart';
import '../challenge_navigator/new_device_login_challenge_navigator.dart';
import '../challenge_navigator/verifier/new_device_login_verifier_factory.dart';
import 'verify_username_cubit.dart';
import 'verify_username_state.dart';

class VerifyUsernameScreen extends PageBase {
  @visibleForTesting
  final VerifyUsernameCubit? cubit;

  const VerifyUsernameScreen({@visibleForTesting this.cubit, super.key});

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.verifyUsernameScreen.routeName);

  @override
  State<VerifyUsernameScreen> createState() => VerifyUsernameScreenState();

  static void pushNamed() {
    navigatorContext?.pushNamed(Screen.verifyUsernameScreen.name);
  }

  static void goNamed() {
    navigatorContext?.goNamed(Screen.verifyUsernameScreen.name);
  }

  static void pushReplacementNamed() {
    navigatorContext?.pushReplacementNamed(Screen.verifyUsernameScreen.name);
  }
}

@visibleForTesting
class VerifyUsernameScreenState extends PageStateBase<VerifyUsernameScreen> {
  late final VerifyUsernameCubit _cubit = widget.cubit ??
      VerifyUsernameCubit(
          usernameValidator: UsernameValidator(),
          authenticationRepo: getIt.get<AuthenticationRepo>());

  final TextEditingController _usernameFieldController = TextEditingController();

  final FocusNode _usernameFocusNode = FocusNode();

  @visibleForTesting
  late NewDeviceLoginChallengeNavigator loginNewDeviceChallengeHandler;

  @override
  void initState() {
    super.initState();
    commonUtilFunction.delayAndRequestFocus(_usernameFocusNode);

    loginNewDeviceChallengeHandler = NewDeviceLoginChallengeNavigator(
        verifierFactory: NewDeviceLoginVerifierFactory(), onError: handleApiCommonError);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<VerifyUsernameCubit>(
      create: (_) => _cubit,
      child: BlocListener<VerifyUsernameCubit, VerifyUsernameState>(
        listener: (_, VerifyUsernameState state) => _listenVerifyUsernameState(state),
        child: PopScope(
          canPop: false,
          onPopInvokedWithResult: (_, __) => _onPop(),
          child: AppBarWrapper(
            contentPadding: EdgeInsets.zero,
            customAppbar: EvoAppBar(
              leading: EvoAppBarLeadingButton(onPressed: _onPop),
            ),
            child: _buildBodyWidget(),
          ),
        ),
      ),
    );
  }

  Widget _buildBodyWidget() {
    return Padding(
      padding: EdgeInsets.only(bottom: EvoDimension.screenBottomPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          EvoDimension.space8,
          Padding(
            padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
            child: Text(
              EvoStrings.verifyUsernameTitle,
              style: evoTextStyles.bold(TextSize.h3, color: evoColors.grayText),
            ),
          ),
          EvoDimension.space16,
          Padding(
            padding:
                EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPaddingWithTextField),
            child: _buildUsernameField(),
          ),
          Padding(
            padding: EdgeInsets.only(
                top: 16.w,
                right: EvoDimension.screenHorizontalPadding,
                left: EvoDimension.screenHorizontalPadding),
            child: QuestionCtaText(
              question: EvoStrings.forgotUsernameTitle,
              cta: EvoStrings.ctaRecover,
              onTap: _recoverUsername,
              ctaColor: evoColors.secondaryBase,
            ),
          ),
          const Spacer(),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
            child: _buildContinueBtn(),
          )
        ],
      ),
    );
  }

  Widget _buildUsernameField() {
    return BlocSelector<VerifyUsernameCubit, VerifyUsernameState, String?>(
      selector: (VerifyUsernameState state) {
        if (state is InvalidUsernameErrorState) {
          return state.errorMessage ?? EvoStrings.defaultInvalidUsername;
        }
        return null;
      },
      builder: (_, String? error) {
        return EvoTextField(
          focusNode: _usernameFocusNode,
          textEditingController: _usernameFieldController,
          maxLines: 1,
          errMessage: error,
          keyboardType: TextInputType.text,
          onChanged: _cubit.updateUsername,
          onSubmitted: (_) => _verifyUsername(),
        );
      },
    );
  }

  Widget _buildContinueBtn() {
    return BlocBuilder<VerifyUsernameCubit, VerifyUsernameState>(
      // Only rebuilds when the username transitions between empty/non-empty states
      // Ignores updates when the non-emptiness state hasn't changed
      // Returns false for all other state changes
      buildWhen: (VerifyUsernameState prev, VerifyUsernameState current) {
        if (current is UsernameModifiedState) {
          return prev is! UsernameModifiedState ||
              prev.username.isNotEmpty != current.username.isNotEmpty;
        }
        if (current is InvalidUsernameErrorState || current is VerifyUsernameError) {
          return true;
        }
        return false;
      },
      builder: (BuildContext context, VerifyUsernameState state) {
        final bool isEnable = state is UsernameModifiedState && state.username.isNotEmpty;
        return CommonButton(
          onPressed: isEnable ? _verifyUsername : null,
          isWrapContent: false,
          style: evoButtonStyles.primary(ButtonSize.large),
          child: const Text(EvoStrings.ctaContinue),
        );
      },
    );
  }

  void _listenVerifyUsernameState(VerifyUsernameState state) {
    if (state is VerifyUsernameLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }
    evoUtilFunction.hideHudLoading();

    switch (state) {
      case VerifyUsernameSuccess(
          :final String username,
          :final LoginNewDeviceEntity entity,
        ):
        evoDialogFunction.showDialogConfirm(
            dialogId: EvoDialogId.proceedLoginOnNewDeviceDialog,
            title: EvoStrings.logInOnNewDeviceTitle,
            content: EvoStrings.logInOnNewDeviceDesc,
            textPositive: EvoStrings.ctaProceed,
            textNegative: EvoStrings.ctaCancel,
            isDismissible: false,
            onClickPositive: () {
              /// dismiss popup
              navigatorContext?.pop();

              loginNewDeviceChallengeHandler.nextChallenge(ChallengeSuccessModel(
                  entity: entity,
                  additionalData: <String, dynamic>{ChallengeSuccessModel.usernameKey: username}));
            },
            onClickNegative: () {
              /// dismiss popup
              navigatorContext?.pop();

              WelcomeScreen.goNamed();
            });

        return;
      case VerifyUsernameError(:final ErrorUIModel error):
        handleApiCommonError(error);
        return;
      case VerifyUsernameLockedState():
        evoDialogFunction.showDialogErrorLimitExceeded(
          type: SessionDialogType.logIn,
          content: state.error.userMessage,
        );
        return;
      default:
        break;
    }
  }

  void _verifyUsername() {
    evoUtilFunction.hideKeyboard();
    _cubit.verify(_usernameFieldController.text);
  }

  Future<void> _onPop() async {
    await evoUtilFunction.hideKeyboard();
    WelcomeScreen.goNamed();
  }

  void _recoverUsername() {
    // TODO: Implement recover username flow
  }
}
