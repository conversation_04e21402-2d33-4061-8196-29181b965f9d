import 'package:evoapp/data/request/login_new_device_request.dart';
import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/login/mock/mock_login_new_device_file_name_use_case.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../util/validator/username_validator.dart';
import 'verify_username_state.dart';

class VerifyUsernameCubit extends BlocBase<VerifyUsernameState> {
  final UsernameValidator usernameValidator;
  final AuthenticationRepo authenticationRepo;

  VerifyUsernameCubit({
    required this.usernameValidator,
    required this.authenticationRepo,
  }) : super(VerifyUsernameInitial());

  Future<void> verify(String username) async {
    final String trimmedUsername = username.trim();
    final String? validationResult = usernameValidator.validate(trimmedUsername);
    if (validationResult != null) {
      emit(InvalidUsernameErrorState(errorMessage: validationResult));
      return;
    }

    emit(VerifyUsernameLoading());

    final LoginNewDeviceEntity result = await authenticationRepo.loginNewDevice(
      request: UserNameRequest(username: trimmedUsername),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockLoginNewDeviceFileNameByCase(
            MockLoginNewDeviceFileNameUseCase.getVerifyUsernameSuccess),
      ),
    );

    _handleLoginNewDeviceResult(username: trimmedUsername, result: result);
  }

  void updateUsername(String username) {
    emit(UsernameModifiedState(username: username));
  }

  void _handleLoginNewDeviceResult(
      {required String username, required LoginNewDeviceEntity result}) {
    final int? statusCode = result.statusCode;
    if (statusCode == CommonHttpClient.SUCCESS) {
      emit(VerifyUsernameSuccess(username: username, entity: result));
      return;
    }

    final ErrorUIModel errorUiModel = ErrorUIModel.fromEntity(result);

    ///invalid username failure
    if (statusCode == CommonHttpClient.BAD_REQUEST || statusCode == CommonHttpClient.NOT_FOUND) {
      emit(InvalidUsernameErrorState(errorMessage: errorUiModel.userMessage));
      return;
    }

    ///Locked because user try failed multiple times at authentication steps (verify MPIN, or face)
    if (statusCode == CommonHttpClient.LOCKED_RESOURCE) {
      emit(VerifyUsernameLockedState(error: errorUiModel));
      return;
    }

    /// other failure
    emit(VerifyUsernameError(error: errorUiModel));
  }
}
