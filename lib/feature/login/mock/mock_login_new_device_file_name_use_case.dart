enum MockLoginNewDeviceFileNameUseCase {
  getVerifyUsernameSuccess('login_new_device_verify_username_success.json'),
  getVerifyUsernameBadRequest('login_new_device_verify_username_bad_request.json'),
  getVerifyMPinSuccess('login_new_device_verify_mpin_success.json'),
  getVerifyMPinBadRequest('login_new_device_verify_mpin_bad_request.json'),
  getVerifySelfieSuccess('login_new_device_verify_selfie_success.json'),
  getVerifyBiometricTokenSuccess('login_new_device_verify_biometric_token_success.json');

  final String value;

  const MockLoginNewDeviceFileNameUseCase(this.value);
}

String getMockLoginNewDeviceFileNameByCase(MockLoginNewDeviceFileNameUseCase mockCase) {
  return mockCase.value;
}
