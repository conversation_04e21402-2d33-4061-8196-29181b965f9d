import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../widget/no_app_bar_wrapper.dart';
import '../../profile/cubit/user_profile_cubit.dart';
import 'biometric/biometric_cubit.dart';
import 'components/login_biometric_button.dart';
import 'components/login_mpin_widget.dart';
import 'components/login_submit_button.dart';
import 'components/login_title_widget.dart';
import 'handlers/login_authentication_handler.dart';
import 'handlers/login_state_handler.dart';
import 'pincode/validate_mpin_cubit.dart';
import 'previous_login_cubit.dart';
import 'providers/login_cubit_provider.dart';

part 'login_content_widget.dart';

class PreviousLogInScreen extends PageBase {
  const PreviousLogInScreen({super.key});

  static Future<void> goNamed() async {
    return navigatorContext?.goNamed(
      Screen.previousLogInScreen.name,
    );
  }

  static Future<void> pushNamed() async {
    return navigatorContext?.pushNamed(
      Screen.previousLogInScreen.name,
    );
  }

  @override
  State<PreviousLogInScreen> createState() => _PreviousLogInState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.previousLogInScreen.routeName);
}

class _PreviousLogInState extends EvoPageStateBase<PreviousLogInScreen> {
  /// This variable determine when this page is launched first time & biometric authentication is available,
  /// Biometric Authentication is triggered automatically.
  bool _isAutoBiometricAuthAtFirstLaunch = true;

  @override
  void initState() {
    super.initState();
    appState.biometricStatusChangeNotifier.addListener(_initSetup);
  }

  @override
  void dispose() {
    appState.biometricStatusChangeNotifier.removeListener(_initSetup);
    super.dispose();
  }

  void _initSetup() {
    // This method is called when biometric status changes
    // The actual initialization is handled in _LoginContentWidget
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      /// Block user from pop screen back to MainScreen
      canPop: false,
      child: NoAppBarWrapper(
        child: LoginCubitProvider(
          child: LoginContentWidget(
            isAutoBiometricAuthAtFirstLaunch: _isAutoBiometricAuthAtFirstLaunch,
            onAutoBiometricUsed: () {
              _isAutoBiometricAuthAtFirstLaunch = false;
            },
            onInitSetup: _initSetup,
            onHandleApiError: handleEvoApiError,
          ),
        ),
      ),
    );
  }

  @override
  Future<void> handleEvoApiError(ErrorUIModel? errorUIModel) async {
    /// If error code is  [CommonHttpClient.INVALID_TOKEN] we will re-initial this page.
    if (errorUIModel?.statusCode == CommonHttpClient.INVALID_TOKEN) {
      _initSetup();
    }
    super.handleEvoApiError(errorUIModel);
  }

  @override
  bool hasListenAuthorizationSessionExpired() => false;
}
