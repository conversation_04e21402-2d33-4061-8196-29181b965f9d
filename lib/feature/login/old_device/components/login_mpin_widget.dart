import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';

import '../../../../resources/resources.dart';
import '../../../../widget/evo_mpin_code/evo_mpin_code_widget.dart';
import '../pincode/validate_mpin_cubit.dart';

/// Controller for accessing MPIN widget state from parent
class LoginMPINController {
  FocusNode? _focusNode;
  TextEditingController? _textEditingController;

  void attach(
      {required FocusNode focusNode, required TextEditingController textEditingController}) {
    _focusNode = focusNode;
    _textEditingController = textEditingController;
  }

  void detach() {
    /// focusNode and textEditingController are being disposed when calling EvoMPINCodeWidget
    /// then we don't need to dispose them again
    _focusNode = null;
    _textEditingController = null;
  }

  String get text => _textEditingController?.text ?? '';

  void requestFocus() {
    _focusNode?.requestFocus();
  }

  void clear() {
    _textEditingController?.clear();
  }
}

/// Widget that handles MPIN input and validation with its own controllers
class LoginMPINWidget extends StatefulWidget {
  final void Function(String?) onSubmit;
  final void Function(String?) onChange;
  final LoginMPINController? controller;
  final VoidCallback? onResetPin;

  const LoginMPINWidget({
    super.key,
    required this.onSubmit,
    required this.onChange,
    this.controller,
    this.onResetPin,
  });

  @override
  State<LoginMPINWidget> createState() => _LoginMPINWidgetState();
}

class _LoginMPINWidgetState extends State<LoginMPINWidget> {
  late final TextEditingController _textEditingController;
  late final FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _textEditingController = TextEditingController();
    _focusNode = FocusNode();

    // Attach controller if provided
    widget.controller?.attach(focusNode: _focusNode, textEditingController: _textEditingController);
  }

  @override
  void dispose() {
    // Detach controller first
    widget.controller?.detach();

    // Note: Controllers are being disposed when calling widget.controller?._detach();
    // so we skip manual disposal to avoid double disposal errors

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocSelector<ValidateMPINCubit, ValidateMPINState, String?>(
      selector: (ValidateMPINState state) {
        if (state is ValidateMPINFailureState) {
          return state.errorMessage ?? CommonStrings.otherGenericErrorMessage;
        }
        return null;
      },
      builder: (_, String? errorMessage) {
        return EvoMPINCodeWidget(
          title: EvoStrings.verifyMPinDesc,
          textEditingController: _textEditingController,
          errorMessage: errorMessage,
          onSubmit: (String? mPin) {
            widget.onSubmit(mPin);
          },
          autoFocus: false,
          onChange: widget.onChange,
          onResetPin: widget.onResetPin ??
              () {
                // TODO: implement recover mpin flow
              },
          focusNode: _focusNode,
        );
      },
    );
  }
}
