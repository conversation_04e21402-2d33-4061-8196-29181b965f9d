import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../resources/resources.dart';
import '../pincode/validate_mpin_cubit.dart';

/// Widget that displays the main login submit button
class LoginSubmitButton extends StatelessWidget {
  final VoidCallback onPressed;

  const LoginSubmitButton({
    required this.onPressed, super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocSelector<ValidateMPINCubit, ValidateMPINState, bool>(
      selector: (ValidateMPINState state) {
        return state is ValidateMPINValid;
      },
      builder: (_, bool enable) {
        return CommonButton(
          onPressed: enable ? onPressed : null,
          style: evoButtonStyles.primary(ButtonSize.large),
          isWrapContent: false,
          child: const Text(EvoStrings.login),
        );
      },
    );
  }
}
