import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';

import 'package:flutter_common_package/util/extension.dart';

import '../../../../resources/resources.dart';
import '../../../profile/cubit/user_profile_cubit.dart';

/// Widget that displays the login title with username
class LoginTitleWidget extends StatelessWidget {
  const LoginTitleWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocSelector<UserProfileCubit, UserProfileState, String?>(
      selector: (UserProfileState state) {
        if (state is GetUsernameSuccess) {
          return state.username;
        }
        return null;
      },
      builder: (_, String? username) {
        if (username == null) {
          return const SizedBox.shrink();
        }

        final String title = EvoStrings.verifyMPinTitle.replaceVariableByValue(<String>[username]);

        return Text(
          title,
          style: evoTextStyles.semibold(TextSize.h3),
        );
      },
    );
  }
}
