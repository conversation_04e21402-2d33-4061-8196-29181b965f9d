import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_button.dart';

import '../../../../resources/resources.dart';
import '../../../../util/screen_util.dart';
import '../biometric/biometric_cubit.dart';

/// Widget that displays the biometric authentication button
class LoginBiometricButton extends StatelessWidget {
  final VoidCallback onPressed;

  const LoginBiometricButton({
    required this.onPressed,
    super.key,
  });

  /// Checks if biometric option is available for the given state
  bool _isBiometricOptionAvailable(BiometricState state) {
    return state is BiometricAuthenticationAvailable ||
        state is BiometricAuthUserDismiss ||
        state is BiometricAuthSuccess;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BiometricCubit, BiometricState>(
      buildWhen: (BiometricState prev, BiometricState curr) {
        // Since all allowed states return the same widget, only rebuild when visibility changes
        final bool isPrevStateBiometricAvailable = _isBiometricOptionAvailable(prev);
        final bool isCurrentStateBiometricAvailable = _isBiometricOptionAvailable(curr);

        // Rebuild only when visibility changes:
        // - not allowed → allowed (show button)
        // - allowed → not allowed (hide button)
        // Don't rebuild when:
        // allowed → allowed (same widget)
        // not allowed → not allowed (same widget)
        return isPrevStateBiometricAvailable != isCurrentStateBiometricAvailable;
      },
      builder: (_, BiometricState state) {
        // Return SizedBox.shrink() if current state is not Available Biometric Option
        if (!_isBiometricOptionAvailable(state)) {
          return const SizedBox.shrink();
        }

        return Padding(
          padding: EdgeInsets.only(top: 8.w),
          child: CommonButton(
            onPressed: onPressed,
            style: evoButtonStyles.tertiary(ButtonSize.large),
            isWrapContent: false,
            child: const Text(
              EvoStrings.loginScreenLoginWithBiometric,
            ),
          ),
        );
      },
    );
  }
}
