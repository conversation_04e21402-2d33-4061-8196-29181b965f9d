import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../../../data/request/login_previous_device_request.dart';
import '../../../data/response/login_previous_device_entity.dart';
import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/token_utils/jwt_helper.dart';
import 'mock_file/mock_previous_login_device_usecase.dart';

part 'previous_login_state.dart';

class PreviousLogInCubit extends CommonCubit<PreviousLogInState> {
  final EvoLocalStorageHelper localStorageHelper;
  final AuthenticationRepo authRepo;
  final JwtHelper jwtHelper;

  PreviousLogInCubit({
    required this.localStorageHelper,
    required this.authRepo,
    required this.jwtHelper,
  }) : super(PreviousLogInInitial());

  Future<void> loginWithMPIN(String? mpin) async {
    emit(PreviousLogInLoading());

    if (mpin == null || mpin.isEmpty) {
      /// in case user try to login with empty MPIN. It mean there are no user information on local device
      /// so we treat it as session expired. This is rare case
      emit(PreviousLogInFailure(loginType: LoginType.mPin, error:  ErrorUIModel()));
      return;
    }

    /// ensure deviceToken is available before call api
    final String? deviceToken = await localStorageHelper.getDeviceToken();
    if (!jwtHelper.isCanUse(deviceToken)) {
      emit(PreviousLogInSessionExpired(loginType: LoginType.mPin, error:  ErrorUIModel()));
      return;
    }

    final LoginPreviousDeviceEntity entity = await authRepo.loginPrevDevice(
      request: LoginWithMPINRequest(
        pin: mpin,
        deviceToken: deviceToken!,
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: MockPreviousLoginDeviceUseCase.getMockFileNameByType(
            MockPreviousLoginDeviceUseCase.success),
      ),
    );

    await _handleLoginResult(loginType: LoginType.mPin, entity: entity);
  }

  Future<void> loginWithBiometricToken() async {
    emit(PreviousLogInLoading());

    //TODO login with biometric token - call api
    final String? biometricToken = await localStorageHelper.getBiometricToken();

    await Future<void>.delayed(Duration(seconds: 2));

    emit(PreviousLogInSuccess());
  }

  Future<void> _handleLoginResult(
      {required LoginType loginType, required LoginPreviousDeviceEntity entity}) async {
    if (entity.statusCode == CommonHttpClient.SUCCESS) {
      emit(PreviousLogInSuccess());
      return;
    }

    final ErrorUIModel error = ErrorUIModel.fromEntity(entity);
    switch (entity.statusCode) {
      case CommonHttpClient.BAD_REQUEST:
        emit(PreviousLogInBadRequest(loginType: loginType, error: error));
        return;
      case CommonHttpClient.INVALID_TOKEN:
        await evoUtilFunction.clearAllUserData();
        emit(PreviousLogInSessionExpired(loginType: loginType, error: error));
        return;
      case CommonHttpClient.LIMIT_EXCEEDED:
      case CommonHttpClient.LOCKED_RESOURCE:
        emit(PreviousLogInLimitedExceeded(loginType: loginType, error: error));
        return;
      default:
        emit(PreviousLogInFailure(loginType: loginType, error: error));
        break;
    }
  }
}
