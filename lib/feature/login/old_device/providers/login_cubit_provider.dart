import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:nested/nested.dart';

import '../../../../data/repository/authentication_repo.dart';
import '../../../../data/repository/user_repo.dart';
import '../../../../prepare_for_app_initiation.dart';
import '../../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../biometric/utils/biometrics_authenticate.dart';
import '../../../profile/cubit/user_profile_cubit.dart';
import '../../utils/login_old_device_utils.dart';
import '../biometric/biometric_cubit.dart';
import '../pincode/validate_mpin_cubit.dart';
import '../previous_login_cubit.dart';

/// Provider widget to manage and initialize all cubits needed for the login screen
class LoginCubitProvider extends StatelessWidget {
  final Widget child;

  const LoginCubitProvider({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: <SingleChildWidget>[
        BlocProvider<PreviousLogInCubit>(
          create: (BuildContext context) => _createLoginCubit(context),
        ),
        BlocProvider<BiometricCubit>(
          create: (BuildContext context) => _createBiometricCubit(context),
        ),
        BlocProvider<ValidateMPINCubit>(
          create: (BuildContext context) => _createValidateMPINCubit(context),
        ),
        BlocProvider<UserProfileCubit>(
          create: (BuildContext context) => _createUserProfileCubit(context),
        ),
      ],
      child: child,
    );
  }

  PreviousLogInCubit _createLoginCubit(BuildContext context) {
    return context.read<PreviousLogInCubit?>() ??
        PreviousLogInCubit(
          localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          authRepo: getIt.get<AuthenticationRepo>(),
          jwtHelper: getIt.get<JwtHelper>(),
        );
  }

  BiometricCubit _createBiometricCubit(BuildContext context) {
    return context.read<BiometricCubit?>() ??
        BiometricCubit(
          loginOldDeviceUtils: getIt.get<LoginOldDeviceUtils>(),
          bioAuth: getIt.get<BiometricsAuthenticate>(),
        );
  }

  ValidateMPINCubit _createValidateMPINCubit(BuildContext context) {
    return context.read<ValidateMPINCubit?>() ?? ValidateMPINCubit();
  }

  UserProfileCubit _createUserProfileCubit(BuildContext context) {
    return context.read<UserProfileCubit?>() ??
        UserProfileCubit(
          userRepo: getIt.get<UserRepo>(),
          appState: getIt.get<AppState>(),
          localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
        );
  }
}
