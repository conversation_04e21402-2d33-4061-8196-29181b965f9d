part of 'previous_login_screen.dart';

class LoginContentWidget extends StatefulWidget {
  final bool isAutoBiometricAuthAtFirstLaunch;
  final VoidCallback onAutoBiometricUsed;
  final VoidCallback onInitSetup;
  final Future<void> Function(ErrorUIModel?) onHandleApiError;

  const LoginContentWidget({
    required this.isAutoBiometricAuthAtFirstLaunch,
    required this.onAutoBiometricUsed,
    required this.onInitSetup,
    required this.onHandleApiError,
    super.key,
  });

  @override
  State<LoginContentWidget> createState() => _LoginContentWidgetState();
}

class _LoginContentWidgetState extends State<LoginContentWidget> {
  late final LoginAuthenticationHandler _authHandler;
  late final LoginStateHandler _stateHandler;
  late final LoginMPINController _mpinController;

  @override
  void initState() {
    super.initState();
    _mpinController = LoginMPINController();
    _initializeHandlers();
    _initSetup();
  }

  void _initializeHandlers() {
    _authHandler = LoginAuthenticationHandler(
      loginCubit: context.read<PreviousLogInCubit>(),
      biometricCubit: context.read<BiometricCubit>(),
      mPinController: _mpinController,
    );

    _stateHandler = LoginStateHandler(
      pinCodeCubit: context.read<ValidateMPINCubit>(),
      loginCubit: context.read<PreviousLogInCubit>(),
      biometricCubit: context.read<BiometricCubit>(),
      mpinController: _mpinController,
      authHandler: _authHandler,
      onHandleApiError: widget.onHandleApiError,
    );
  }

  void _initSetup() {
    _authHandler.initialize();
    context.read<UserProfileCubit>().getUsername();
    widget.onInitSetup();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: <BlocListener<dynamic, dynamic>>[
        BlocListener<PreviousLogInCubit, PreviousLogInState>(
          listener: (_, PreviousLogInState state) {
            _handleLoginState(state);
          },
        ),
        BlocListener<BiometricCubit, BiometricState>(
          listener: (_, BiometricState state) {
            _handleBiometricState(state);
          },
        ),
      ],
      child: Padding(
        padding: EdgeInsets.only(
          left: EvoDimension.screenHorizontalPadding,
          right: EvoDimension.screenHorizontalPadding,
          bottom: EvoDimension.screenBottomPadding,
        ),
        child: Column(
          children: <Widget>[
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    const LoginTitleWidget(),
                    EvoDimension.space4,
                    _buildMPINCodeWidget(),
                  ],
                ),
              ),
            ),
            EvoDimension.space16,
            LoginSubmitButton(
              onPressed: _authHandler.loginWithMPIN,
            ),
            _buildBiometricButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildBiometricButton() {
    return LoginBiometricButton(
      onPressed: () {
        _mpinController.clear();
        _authHandler.authenticateWithBiometric();
      },
    );
  }

  Widget _buildMPINCodeWidget() {
    return LoginMPINWidget(
      controller: _mpinController,
      onSubmit: (String? mPin) {
        context.read<ValidateMPINCubit>().updateValidState();
        _authHandler.loginWithMPIN(mPin);
      },
      onChange: (String? value) {
        context.read<ValidateMPINCubit>().onTextChange(value);
      },
      onResetPin: () {
        // TODO: implement recover mpin flow
      },
    );
  }

  Future<void> _handleLoginState(PreviousLogInState state) async {
    await _stateHandler.handleLoginState(state);
  }

  Future<void> _handleBiometricState(BiometricState state) async {
    await _stateHandler.handleBiometricState(
      state,
      isAutoBiometricAuthAtFirstLaunch: widget.isAutoBiometricAuthAtFirstLaunch,
      onAutoBiometricUsed: widget.onAutoBiometricUsed,
    );
  }
}
