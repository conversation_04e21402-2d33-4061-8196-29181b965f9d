import 'package:flutter/material.dart';

import '../../../../prepare_for_app_initiation.dart';
import '../../../main_screen/main_screen.dart';
import '../biometric/biometric_cubit.dart';
import '../components/login_mpin_widget.dart';
import '../previous_login_cubit.dart';

/// Handles authentication-related logic for the login screen
class LoginAuthenticationHandler {
  final PreviousLogInCubit _loginCubit;
  final BiometricCubit _biometricCubit;
  final LoginMPINController? _mpinController;
  final AppState _appState;

  LoginAuthenticationHandler({
    required PreviousLogInCubit loginCubit,
    required BiometricCubit biometricCubit,
    LoginMPINController? mPinController,
  })  : _loginCubit = loginCubit,
        _biometricCubit = biometricCubit,
        _mpinController = mPinController,
        _appState = getIt.get<AppState>();

  /// Initializes the authentication setup
  void initialize() {
    _biometricCubit.initialize();
  }

  /// Handles successful login
  void handleLoginSuccess() {
    _updateUserLoginStatus(true);
    MainScreen.goNamed(isLoggedIn: true);
  }

  /// Performs login with MPIN
  void loginWithMPIN([String? mPin]) {
    final String mPinToUse = mPin ?? _mpinController?.text ?? '';
    _loginCubit.loginWithMPIN(mPinToUse);
  }

  /// Performs biometric authentication
  void authenticateWithBiometric() {
    _biometricCubit.authenticate();
  }

  /// Updates user login status
  void _updateUserLoginStatus(bool isLogin) {
    _appState.isUserLogIn = isLogin;

    /// Start detect inactive when user login
    if (_appState.isUserLogIn) {
      _appState.inactiveDetectorController.enable?.call();
    } else {
      _appState.inactiveDetectorController.disable?.call();
    }
  }
}
