import 'package:evoapp/feature/login/old_device/biometric/biometric_cubit.dart';
import 'package:evoapp/feature/login/old_device/components/login_mpin_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../data/request/login_previous_device_request.dart';
import '../../../../resources/ui_strings.dart';
import '../../../../util/dialog_functions.dart';
import '../../../../util/functions.dart';
import '../pincode/validate_mpin_cubit.dart';
import '../previous_login_cubit.dart';
import 'login_authentication_handler.dart';

/// Handles state changes and error handling for the login screen
class LoginStateHandler {
  final ValidateMPINCubit _pinCodeCubit;
  final LoginAuthenticationHandler _authHandler;
  final Future<void> Function(ErrorUIModel?) _onHandleApiError;
  final PreviousLogInCubit _loginCubit;
  final BiometricCubit _biometricCubit;
  final LoginMPINController? _mpinController;

  LoginStateHandler({
    required ValidateMPINCubit pinCodeCubit,
    required LoginAuthenticationHandler authHandler,
    required PreviousLogInCubit loginCubit,
    required BiometricCubit biometricCubit,
    required Future<void> Function(ErrorUIModel?) onHandleApiError,
    LoginMPINController? mpinController,
  })  : _pinCodeCubit = pinCodeCubit,
        _authHandler = authHandler,
        _loginCubit = loginCubit,
        _biometricCubit = biometricCubit,
        _mpinController = mpinController,
        _onHandleApiError = onHandleApiError;

  /// Handles login state changes including API error handling
  Future<void> handleLoginState(PreviousLogInState state) async {
    if (state is PreviousLogInLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    if (state is PreviousLogInSuccess) {
      _authHandler.handleLoginSuccess();
    } else if (state is PreviousLogInFailure) {
      await _handleLoginError(state);
    }
  }

  /// Handles biometric authentication state changes
  Future<void> handleBiometricState(
    BiometricState state, {
    required bool isAutoBiometricAuthAtFirstLaunch,
    required VoidCallback onAutoBiometricUsed,
  }) async {
    if (state is BiometricAuthenticationAvailable && isAutoBiometricAuthAtFirstLaunch) {
      _biometricCubit.authenticate(shouldEmitUnavailableState: true);
      onAutoBiometricUsed();
      return;
    }

    if (state is BiometricAuthSuccess) {
      _loginCubit.loginWithBiometricToken();
      return;
    }

    if (state is BiometricAuthUserDismiss || state is BiometricAuthenticationUnavailable) {
      /// Open keyboard to enter pin code
      /// should be called after a short delay to avoid conflict with biometric animation dismiss
      /// choose 100ms as a safe delay
      await Future<void>.delayed(Duration(milliseconds: 100), () {
        _mpinController?.requestFocus();
      });
      return;
    }
  }

  /// Handles login errors including API error delegation
  Future<void> _handleLoginError(PreviousLogInFailure state) async {
    if (state is PreviousLogInBadRequest) {
      if (state.loginType == LoginType.mPin) {
        final String errorUserMsg = state.error.userMessage ?? EvoStrings.defaultInvalidMPIN;
        _pinCodeCubit.setMPINFailure(errorUserMsg);
      }
      return;
    }

    /// clear pin code in UI/UX
    _mpinController?.clear();
    switch (state) {
      case PreviousLogInLimitedExceeded():
        return evoDialogFunction.showDialogErrorLimitExceeded(
            type: SessionDialogType.previousLogIn, content: state.error.userMessage);
      case PreviousLogInSessionExpired():
        return evoDialogFunction.showDialogSessionTokenExpired(
            type: SessionDialogType.previousLogIn);
      default:
        // Handle API errors through the provided callback
        await _onHandleApiError(state.error);
        return;
    }
  }
}
