import 'package:flutter_common_package/base/common_cubit.dart';


part 'validate_mpin_state.dart';

class ValidateMPINCubit extends CommonCubit<ValidateMPINState> {
  ValidateMPINCubit() : super(ValidateMPINInitial());

  void onTextChange(String? value) {
    if (value == null || value.isEmpty) {
      emit(ValidateMPINInitial());
      return;
    }
  }

  void setMPINFailure(String message) {
    emit(ValidateMPINFailureState(message));
  }

  void updateValidState() {
    emit(ValidateMPINValid());
  }
}
