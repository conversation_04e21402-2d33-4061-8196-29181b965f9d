part of 'previous_login_cubit.dart';

sealed class PreviousLogInState extends BlocState {}

class PreviousLogInInitial extends PreviousLogInState {}

class PreviousLogInLoading extends PreviousLogInState {}

class Previous<PERSON>ogIn<PERSON>uc<PERSON> extends PreviousLogInState {}

class PreviousLogInFailure extends PreviousLogInState {
  final ErrorUIModel error;
  final LoginType loginType;

  PreviousLogInFailure({
    required this.error,
    required this.loginType,
  });
}

class PreviousLogInBadRequest extends PreviousLogInFailure {
  PreviousLogInBadRequest({required super.error, required super.loginType});
}

class PreviousLogInSessionExpired extends PreviousLogInFailure {
  PreviousLogInSessionExpired({required super.error, required super.loginType});
}

class PreviousLogInLimitedExceeded extends PreviousLogInFailure {
  PreviousLogInLimitedExceeded({required super.error, required super.loginType});
}
