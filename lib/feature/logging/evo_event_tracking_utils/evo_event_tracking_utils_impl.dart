import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/modules/core/app_state.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../feature_toggle.dart';
import '../evo_event_tracking_screen_id.dart';
import 'evo_event_tracking_utils.dart';

EvoEventTrackingUtils get evoEventTrackingUtils => getIt.get<EvoEventTrackingUtils>();

class EvoEventTrackingUtilsImpl implements EvoEventTrackingUtils {
  final EventTrackingUtils _eventTrackingUtils = getIt.get<EventTrackingUtils>();

  final AppState _appState = getIt.get<AppState>();

  final FeatureToggle _featureToggle = getIt.get<FeatureToggle>();

  @override
  void sendEvoUserEvent({
    required String eventActionId,
    Map<String, dynamic>? metaData,
  }) {
    final String screenNumberId = _appState.currentScreenId.name;
    final String eventId = EventTrackingUtils.buildEventId(
      screenNumberId: screenNumberId,
      eventActionId: eventActionId,
    );
    commonLog('User Event Id: $eventId');

    sendEvoActionEvent(
      eventId: eventId,
      metaData: metaData,
    );
  }

  @override
  void sendEvoSpecialEvent({
    required String eventActionId,
    EventTrackingScreenId? screenId,
    Map<String, dynamic>? metaData,
  }) {
    final String specialScreenId = screenId?.name ?? EvoEventTrackingScreenId.special.name;
    final String eventId = EventTrackingUtils.buildEventId(
      screenNumberId: specialScreenId,
      eventActionId: eventActionId,
    );
    commonLog('Special Event Id: $eventId');

    sendEvoActionEvent(
      eventId: eventId,
      metaData: metaData,
    );
  }

  @visibleForTesting
  Future<void> sendEvoActionEvent({
    required String eventId,
    Map<String, dynamic>? metaData,
  }) async {
    if (!_featureToggle.enableEventTrackingFeature) {
      return;
    }

    await _eventTrackingUtils.sendUserActionEvent(
      eventId: eventId,
      metaData: prepareMetaData(metaData: metaData),
    );
  }

  @visibleForTesting
  Map<String, dynamic>? prepareMetaData({
    Map<String, dynamic>? metaData,
  }) {
    final Map<String, dynamic> optionalData = <String, dynamic>{};

    if (metaData != null) {
      optionalData.addAll(metaData);
    }

    return optionalData.isNotEmpty ? optionalData : null;
  }
}
