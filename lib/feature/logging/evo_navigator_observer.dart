import 'package:flutter/material.dart';

import '../../base/modules/core/app_state.dart';
import '../../prepare_for_app_initiation.dart';

class EvoNavigatorObserver extends RouteObserver<ModalRoute<Object?>> {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    setUrlPath(currentRoute: route, previousRoute: previousRoute);
  }

  /// When screen A navigate to screen B
  /// then screen B back to screen A
  /// [route] is screen B
  /// [previousRoute] is screen A
  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    setUrlPath(currentRoute: previousRoute, previousRoute: route);
  }

  @visibleForTesting
  void setUrlPath({
    Route<dynamic>? currentRoute,
    Route<dynamic>? previousRoute,
  }) {
    /// [PopupRoute] is routes of popup
    /// E.g: BottomSheet, Dropdown, Popup Menu, Search Anchor, etc..
    if (currentRoute is PopupRoute<dynamic> && currentRoute.settings.name == null) {
      return;
    }

    final AppState appState = getIt.get<AppState>();
    appState.eventTrackingSharedData.urlPath = currentRoute?.settings.name;
    appState.eventTrackingSharedData.previousUrlPath = previousRoute?.settings.name;
  }
}
