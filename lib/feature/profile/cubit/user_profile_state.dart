part of 'user_profile_cubit.dart';

sealed class UserProfileState extends BlocState {}

class UserProfileInitial extends UserProfileState {}

class UserProfileLoading extends UserProfileState {
  UserProfileLoading();
}

class UserProfileLoadedSuccess extends UserProfileState {
  final UserInformationEntity? user;

  UserProfileLoadedSuccess({this.user});
}

class UserProfileLoadedFail extends UserProfileState {
  final ErrorUIModel? error;

  UserProfileLoadedFail({this.error});
}

class GetUsernameSuccess extends UserProfileState {
  final String username;

  GetUsernameSuccess({required this.username});
}
