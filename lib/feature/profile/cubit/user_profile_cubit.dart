import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/user_repo.dart';
import '../../../data/response/user_entity.dart';
import '../../../data/response/user_information_entity.dart';
import '../../../base/modules/core/app_state.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../mock/mock_profile_use_case.dart';

part 'user_profile_state.dart';

class UserProfileCubit extends CommonCubit<UserProfileState> {
  final UserRepo userRepo;
  final EvoLocalStorageHelper localStorageHelper;
  final AppState appState;

  UserProfileCubit({
    required this.userRepo,
    required this.appState,
    required this.localStorageHelper,
  }) : super(UserProfileInitial());

  Future<void> fetchUserInfoFromServer() async {
    emit(UserProfileLoading());

    final UserEntity userInfo = await userRepo.getUserInfo(
      mockConfig: MockConfig(
          enable: false,
          fileName: getMockProfileFileNameByCase(
            MockProfileUseCase.getUserInformationSuccess,
          )),
    );

    if (userInfo.statusCode == CommonHttpClient.SUCCESS) {
      appState.userInfo.value = userInfo.userInformation;
      localStorageHelper.setUserFullName(userInfo.userInformation?.fullName);

      emit(UserProfileLoadedSuccess(user: appState.userInfo.value));
    } else {
      emit(UserProfileLoadedFail(error: ErrorUIModel.fromEntity(userInfo)));
    }
  }

  Future<void> getLocalUserInfo() async {
    emit(UserProfileLoading());

    appState.userInfo.value = await _getUserInfoWithLocalData();

    emit(UserProfileLoadedSuccess(user: appState.userInfo.value));
  }

  Future<UserInformationEntity> _getUserInfoWithLocalData() async {
    final String? fullName = await localStorageHelper.getUserFullName();
    final String? phoneNumber = await localStorageHelper.getUserPhoneNumber();

    final UserInformationEntity currentUserInfo =
        appState.userInfo.value ?? const UserInformationEntity();
    return currentUserInfo.copyWith(
      fullName: fullName,
      phoneNumber: phoneNumber,
    );
  }
}
