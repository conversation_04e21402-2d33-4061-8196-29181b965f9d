import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../../../base/modules/core/app_state.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../biometric/biometric_token/biometric_token_usability_mixin.dart';
import '../../../../biometric/utils/bio_auth_result.dart';
import '../../../../biometric/utils/biometric_functions.dart';
import '../../../../biometric/utils/biometrics_authenticate.dart';

part 'activate_biometric_state.dart';

class ActivateBiometricCubit extends CommonCubit<ActivateBiometricState>
    with BiometricTokenUsabilityMixin {
  final BiometricsAuthenticate bioAuth;
  final EvoLocalStorageHelper secureStorageHelper;
  final AppState appState;

  ActivateBiometricCubit({
    required this.bioAuth,
    required this.secureStorageHelper,
    required this.appState,
  }) : super(ActivateBiometricInitState());

  Future<void> initialize() async {
    appState.biometricStatusChangeNotifier.addListener(listenOnBiometricStatusChanged);

    final bool isDeviceSupportBioMetrics = await bioAuth.isDeviceSupportBiometrics();

    if (!isDeviceSupportBioMetrics) {
      emit(BiometricUnsupported());
      return;
    }

    emit(BiometricSupported());
  }

  Future<void> checkBioAuthState() async {
    final [
      bool isBiometricTokenUnUsable,
      bool isEnableAuthByBiometrics,
    ] = await Future.wait(<Future<bool>>[
      checkAndHandleBiometricTokenUnUsable(),
      secureStorageHelper.isEnableBiometricAuthenticator()
    ]);

    if (isEnableAuthByBiometrics && !isBiometricTokenUnUsable) {
      emit(BiometricActivated());
      return;
    }

    emit(BiometricDeactivated());
  }

  Future<void> toggleBioAuth(bool value) async {
    final bool hasEnrolledBiometrics = await biometricsTokenModule.hasEnrolledBiometrics();
    if (hasEnrolledBiometrics) {
      if (value) {
        /// TODO: handle biometric token module on related ticket
      } else {
        await biometricsTokenModule.disableBiometricAuthenticationFeature();
        checkBioAuthState();
      }
    } else {
      biometricFunctions.handleBioError(bioError: BioAuthError.notEnrolled);
    }
  }

  @override
  Future<void> close() {
    appState.biometricStatusChangeNotifier.removeListener(listenOnBiometricStatusChanged);
    return super.close();
  }

  @visibleForTesting
  void listenOnBiometricStatusChanged() {
    /// check biometric auth again for update state
    checkBioAuthState();
  }
}
