import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

part 'sign_out_state.dart';

class SignOutCubit extends CommonCubit<SignOutState> {
  final AuthenticationRepo authenticationRepo;

  SignOutCubit(this.authenticationRepo) : super(SignOutInitial());

  Future<void> signOut() async {
    await authenticationRepo.logout();
    emit(SignOutSuccess());
  }
}
