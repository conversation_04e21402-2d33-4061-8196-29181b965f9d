import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../../../base/modules/core/app_state.dart';
import '../../../../../data/repository/user_repo.dart';
import '../../../../../data/request/change_pin_request.dart';
import '../../../../../data/response/auth_challenge_type.dart';
import '../../../../../data/response/change_pin_entity.dart';
import '../../../../../prepare_for_app_initiation.dart';
import '../../../../pin/mock/mock_pin_use_case.dart';
import '../../../../pin/models/change_pin_status.dart';

part 'profile_change_pin_state.dart';

class ProfileChangePinCubit extends CommonCubit<ProfileChangePinState> {
  ProfileChangePinCubit({
    required this.userRepo,
    required this.appState,
  }) : super(ChangePinInitialState());

  final UserRepo userRepo;
  final AppState appState;

  /// Initialize change MPIN flow and check
  /// whether user be blocked if exceed limit rate
  /// if [sessionToken] is null consider as failure
  Future<void> initChangePin() async {
    emit(ChangePinLoading());

    final ChangePinEntity entity = await userRepo.changePin(
      request: InitializeChangePinSessionRequest(),
      mockConfig: MockConfig(
          enable: false,
          statusCode: CommonHttpClient.LOCKED_RESOURCE,
          fileName: getMockPinFileNameByCase(
            MockPinUseCase.getChangePinVerifyCurrentLockedResource,
          )),
    );

    if (entity.statusCode == CommonHttpClient.LOCKED_RESOURCE) {
      appState.changePinStatusNotifier.value = ChangePinStatus.locked;
      emit(ChangePinLocked(error: ErrorUIModel.fromEntity(entity)));
      return;
    }

    final String? sessionToken = entity.sessionToken;

    if (sessionToken == null) {
      commonLog('session_token should not null');
      emit(ChangePinFailure(
        error: ErrorUIModel.fromEntity(entity),
      ));
      return;
    }

    appState.changePinStatusNotifier.value = ChangePinStatus.available;

    emit(ChangePinAvailable(
      sessionToken: sessionToken,
      type: AuthChallengeType.fromString(entity.challengeType),
    ));
  }
}
