// coverage:ignore-file
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/refreshable_view.dart';
import 'package:flutter_common_package/widget/shimmer/shimmer_animation.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../base/modules/core/app_state.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/response/auth_challenge_type.dart';
import '../../../data/response/user_information_entity.dart';
import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../widget/action_button_widget.dart';
import '../../../widget/appbar/evo_appbar.dart';
import '../../biometric/utils/biometric_functions.dart';
import '../../biometric/utils/biometrics_authenticate.dart';
import '../../login/old_device/login_on_old_device_screen.dart';
import '../../pin/change_pin/verify_current/current_pin_verification_screen.dart';
import '../cubit/user_profile_cubit.dart';
import 'biometric_enabled_success_screen.dart';
import 'cubit/activate_biometric/activate_biometric_cubit.dart';
import 'cubit/sign_out/sign_out_cubit.dart';
import 'cubit/user/profile_change_pin_cubit.dart';
import 'widget/profile_info_section.dart';
import 'widget/profile_privacy_security_section.dart';

class ProfileScreen extends PageBase {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.profileScreen.routeName);
}

class _ProfileScreenState extends EvoPageStateBase<ProfileScreen>
    with AutomaticKeepAliveClientMixin {
  final AppState _appState = getIt.get<AppState>();

  late final UserProfileCubit _userCubit = context.read<UserProfileCubit?>() ??
      UserProfileCubit(
        userRepo: getIt.get<UserRepo>(),
        localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
        appState: appState,
      );

  late final SignOutCubit _signOutCubit =
      context.read<SignOutCubit?>() ?? SignOutCubit(getIt.get<AuthenticationRepo>());

  late final ProfileChangePinCubit _changePinCubit = context.read<ProfileChangePinCubit?>() ??
      ProfileChangePinCubit(
        userRepo: getIt.get<UserRepo>(),
        appState: _appState,
      );

  late final ActivateBiometricCubit _biometricCubit = context.read<ActivateBiometricCubit?>() ??
      ActivateBiometricCubit(
        bioAuth: getIt.get<BiometricsAuthenticate>(),
        secureStorageHelper: getIt.get<EvoLocalStorageHelper>(),
        appState: _appState,
      );

  final RefreshController _refreshController = RefreshController();

  final Widget sectionSpacing = const SizedBox(height: 24);

  final Widget itemSpacing = const SizedBox(height: 16);

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
    _initBiometric();
    super.initState();
  }

  void _initBiometric() {
    _biometricCubit.initialize();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _refreshController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildVisibilityDetectorPage(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: evoColors.background,
      appBar: _buildAppBar(),
      body: ShimmerAnimation(
        child: _contentWidget(),
      ),
    );
  }

  EvoAppBar _buildAppBar() {
    return EvoAppBar(
      leading: null,
      actions: <Widget>[
        Padding(
          padding: const EdgeInsets.only(right: 24),
          child: CommonButton(
            onPressed: _showConfirmSignOutAccount,
            style: evoButtonStyles.utility(
              ButtonSize.small,
              padding: const EdgeInsets.symmetric(
                vertical: 4,
                horizontal: 15,
              ),
            ),
            child: Row(
              children: <Widget>[
                imageProvider.asset(
                  EvoImages.icSwitchAccount,
                  width: 24,
                  height: 24,
                  fit: BoxFit.cover,
                ),
                const SizedBox(width: 8),
                const Text(EvoStrings.logout),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _contentWidget() {
    final List<Widget> itemWidgets = _buildItems();
    final int itemCount = itemWidgets.length;
    return MultiBlocProvider(
      providers: <BlocProvider<dynamic>>[
        BlocProvider<UserProfileCubit>(
          create: (_) => _userCubit,
        ),
        BlocProvider<SignOutCubit>(
          create: (_) => _signOutCubit,
        ),
        BlocProvider<ProfileChangePinCubit>(create: (_) => _changePinCubit),
        BlocProvider<ActivateBiometricCubit>(create: (_) => _biometricCubit)
      ],
      child: MultiBlocListener(
        listeners: <BlocListener<dynamic, dynamic>>[
          BlocListener<UserProfileCubit, UserProfileState>(
            listener: _handleUserStateChanged,
          ),
          BlocListener<SignOutCubit, SignOutState>(
            listener: (_, SignOutState state) {
              _listenSignOutUser(context, state);
            },
          ),
          BlocListener<ProfileChangePinCubit, ProfileChangePinState>(
              listener: (_, ProfileChangePinState state) {
            _handleChangePinStateChanged(state);
          }),
          BlocListener<ActivateBiometricCubit, ActivateBiometricState>(
              listener: (_, ActivateBiometricState state) => _listenBiometricStateChanged(state))
        ],
        child: Scaffold(
          body: RefreshableView(
            controller: _refreshController,
            onRefresh: _reloadData,
            child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                itemCount: itemCount,
                itemBuilder: (BuildContext context, int index) {
                  return itemWidgets[index];
                }),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildItems() {
    return <Widget>[
      _getProfileSection(),
      sectionSpacing,
      _getSectionTitleWidget(EvoStrings.privacySecuritySection),
      _getPrivacySecuritySection(),
      sectionSpacing,
      _getSectionTitleWidget(EvoStrings.helpSection),
      _getHelpSection(),
      sectionSpacing,
      _getSectionTitleWidget(EvoStrings.aboutSection),
      _getAboutSection(),
      const SizedBox(height: 37),
    ];
  }

  void _loadData() {
    _userCubit.fetchUserInfoFromServer();
  }

  void _listenSignOutUser(BuildContext context, SignOutState state) {
    if (state is SignOutSuccess) {
      updateUserLoginStatus(false);
      LoginOnOldDeviceScreen.goNamed();
    }
  }

  void _showConfirmSignOutAccount() {
    evoDialogFunction.showDialogConfirm(
      title: EvoStrings.logoutTitle,
      content: EvoStrings.logoutSubtitle,
      textPositive: EvoStrings.ctaProceed,
      autoClosePopupWhenClickCTA: true,
      isDismissible: false,
      onClickPositive: () {
        _signOutCubit.signOut();
      },
      textNegative: EvoStrings.ctaCancel,
      dialogId: EvoDialogId.confirmLogOutDialog,
    );
  }

  void _reloadData() {
    _userCubit.fetchUserInfoFromServer();
  }

  void _handleUserStateChanged(_, UserProfileState state) {
    if (state is UserProfileLoading) {
      return;
    }

    if (_refreshController.isRefresh) {
      _refreshController.refreshCompleted(resetFooterState: true);
    }
  }

  void _handleChangePinStateChanged(ProfileChangePinState state) {
    if (state is ChangePinLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    if (state is ChangePinLocked) {
      _showChangePinBlockedPopUp(state.error);
      return;
    }

    if (state is ChangePinAvailable) {
      switch (state.type) {
        case AuthChallengeType.verifyPin:
          CurrentPINVerificationScreen.pushNamed(
            sessionToken: state.sessionToken,
          );
          break;
        default:
          commonLog('unsupported challenge_type: ${state.type}');
          showSnackBarError(EvoStrings.unknownError);
      }
      return;
    }

    if (state is ChangePinFailure) {
      handleEvoApiError(state.error);
      return;
    }
  }

  void _showChangePinBlockedPopUp(ErrorUIModel? error) {
    evoDialogFunction.showDialogConfirm(
        alertType: DialogAlertType.error,
        textPositive: EvoStrings.backToProfile,
        dialogId: EvoDialogId.defaultErrorDialog,
        title: EvoStrings.changeMPINLockedResourceTitle,
        content: error?.userMessage,
        onClickPositive: () {
          navigatorContext?.pop();
        });
  }

  Widget _getProfileSection() {
    return ValueListenableBuilder<UserInformationEntity?>(
      valueListenable: _appState.userInfo,
      builder: (BuildContext context, UserInformationEntity? user, Widget? child) {
        return ProfileInfoSection(user: user);
      },
    );
  }

  Widget _getPrivacySecuritySection() {
    return BlocBuilder<ActivateBiometricCubit, ActivateBiometricState>(
      buildWhen: (_, ActivateBiometricState state) {
        return state is BiometricActivated ||
            state is BiometricDeactivated ||
            state is BiometricUnsupported ||
            state is BiometricSupported;
      },
      builder: (_, ActivateBiometricState state) {
        final BiometricActivationStatus biometricStatus = switch (state) {
          BiometricUnsupported() => BiometricActivationStatus.unavailable,
          BiometricActivated() => BiometricActivationStatus.activated,
          _ => BiometricActivationStatus.deactivated,
        };

        return ProfilePrivacySecuritySection(
          biometricStatus: biometricStatus,
          onToggleBiometric: _biometricCubit.toggleBioAuth,
          onChangePin: _changePinCubit.initChangePin,
        );
      },
    );
  }

  Widget _getSectionTitleWidget(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: evoTextStyles.bold(
          TextSize.xl,
          color: evoColors.greyScale90,
        ),
      ),
    );
  }

  void _listenBiometricStateChanged(ActivateBiometricState state) {
    switch (state) {
      case BiometricSupported():
        _biometricCubit.checkBioAuthState();
        return;
      case RetrieveBiometricTokenSuccess():
        BiometricEnabledSuccessScreen.pushNamed();
        return;
      case RetrieveBiometricTokenBioAuthFailure():
        biometricFunctions.handleBioError(bioError: state.error);
        return;
      case RetrieveBiometricTokenFailure():
        showSnackBarError(
          state.errorMessage ?? EvoStrings.unknownError,
        );
        return;
      default:
        return;
    }
  }

  Widget _getHelpSection() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        ActionButtonWidget(
          title: EvoStrings.creditCardItemTitle,
          icon: EvoImages.icCard,
          onPress: () {
            evoUtilFunction.openInAppWebView(
              title: EvoStrings.creditCardItemTitle,
              url: WebsiteUrl.evoCardActivationTutorialUrl,
            );
          },
        ),
        itemSpacing,
        ActionButtonWidget(
          title: EvoStrings.faqItemTitle,
          icon: EvoImages.icQuestionMarkCircle,
          onPress: () {
            evoUtilFunction.openInAppWebView(
              title: EvoStrings.faqItemTitle,
              url: WebsiteUrl.evoFaqUrl,
            );
          },
        ),
        itemSpacing,
        ActionButtonWidget(
          title: EvoStrings.contactItemTitle,
          icon: EvoImages.icProfileMail,
          onPress: () {
            evoUtilFunction.openInAppWebView(
              title: EvoStrings.contactItemTitle,
              url: WebsiteUrl.evoContactUrl,
            );
          },
        ),
      ],
    );
  }

  Widget _getAboutSection() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        ActionButtonWidget(
          title: EvoStrings.aboutItemTitle,
          icon: EvoImages.icEvoSm,
          iconColor: evoColors.primary100,
          onPress: () {
            evoUtilFunction.openInAppWebView(
              title: EvoStrings.contactItemTitle,
              url: WebsiteUrl.evoAboutUsUrl,
            );
          },
        ),
        itemSpacing,
        ActionButtonWidget(
          title: EvoStrings.tncItemTitle,
          icon: EvoImages.icDocumentText,
          onPress: () {
            evoUtilFunction.openInAppWebView(
              title: EvoStrings.tncItemTitle,
              url: WebsiteUrl.evoTermsAndConditionsUrl,
            );
          },
        ),
        itemSpacing,
        ActionButtonWidget(
          title: EvoStrings.policyItemTitle,
          icon: EvoImages.icPolicy,
          onPress: () {
            evoUtilFunction.openInAppWebView(
              title: EvoStrings.policyItemTitle,
              url: WebsiteUrl.evoPolicyUrl,
            );
          },
        ),
      ],
    );
  }
}
