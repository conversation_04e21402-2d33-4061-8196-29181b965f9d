import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../resources/resources.dart';
import '../../../util/screen_util.dart';
import '../../../widget/buttons.dart';
import '../../../widget/no_app_bar_wrapper.dart';
import 'widgets/card_widget.dart';

class ActivateCardSuccessArg extends PageBaseArg {
  final CardType cardType;
  final VoidCallback? onPopSuccess;

  ActivateCardSuccessArg({
    required this.cardType,
    this.onPopSuccess,
  });
}

class ActivateCardSuccessScreen extends PageBase {
  final CardType cardType;
  final VoidCallback? onPopSuccess;

  const ActivateCardSuccessScreen({
    required this.cardType,
    this.onPopSuccess,
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _ActivateCardSuccessScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings =>
      RouteSettings(name: Screen.activateCardSuccessScreen.routeName);

  static void pushNamed({
    required CardType cardType,
    VoidCallback? onPopSuccess,
  }) {
    navigatorContext?.pushNamed(
      Screen.activateCardSuccessScreen.name,
      extra: ActivateCardSuccessArg(
        cardType: cardType,
        onPopSuccess: onPopSuccess,
      ),
    );
  }
}

class _ActivateCardSuccessScreenState extends EvoPageStateBase<ActivateCardSuccessScreen> {
  @override
  Widget getContentWidget(BuildContext context) {
    return PopScope(
      canPop: false,
      child: NoAppBarWrapper(
        child: Padding(
          padding: EdgeInsets.only(
            left: EvoDimension.screenHorizontalPadding,
            right: EvoDimension.screenHorizontalPadding,
            bottom: EvoDimension.screenBottomPadding,
          ),
          child: Column(
            children: <Widget>[
              Spacer(),
              _buildImageText(),
              Spacer(),
              _buildButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageText() {
    final bool isVirtual = widget.cardType == CardType.virtual;
    final String text =
        isVirtual ? EvoStrings.activateVirtualCardSuccess : EvoStrings.activatePhysicalCardSuccess;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        evoImageProvider.asset(
          EvoImages.imgIntroduction1,
          width: 328.w,
          fit: BoxFit.contain,
        ),
        Text(
          text,
          textAlign: TextAlign.center,
          style: evoTextStyles.bold(TextSize.h1),
        ),
      ],
    );
  }

  Widget _buildButton() {
    return PrimaryButton(
      text: EvoStrings.ctaProceed,
      onTap: _onTap,
    );
  }

  void _onTap() {
    widget.onPopSuccess?.call();
  }
}
