// coverage:ignore-file
// implement on this ticket https://trustingsocial1.atlassian.net/browse/ENBCC-528

import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/permission/permission_handler_mixin.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../base/evo_page_state_base.dart';
import '../../data/repository/user_repo.dart';
import '../../model/evo_dialog_id.dart';
import '../../model/user_status.dart';
import '../../base/modules/core/app_state.dart';
import '../../prepare_for_app_initiation.dart';
import '../../resources/resources.dart';
import '../../util/dialog_functions.dart';
import '../../util/secure_storage_helper/secure_storage_helper.dart';
import '../check_force_update/check_force_update_handler_mixin.dart';
import '../profile/cubit/user_profile_cubit.dart';
import '../profile/profile_screen/profile_page.dart';
import 'bloc/main_cubit.dart';
import 'bottom_bar_item_model.dart';
import 'bottom_bar_item_widget.dart';
import 'card_page/cards_page.dart';
import 'home_page/home_page.dart';
import 'main_screen_controller.dart';
import 'main_screen_dialog_handler/main_screen_dialog_handler.dart';
import 'main_screen_initial_action/main_screen_initial_action.dart';
import 'navigation_tab_history.dart';
import 'pay_card_page/pay_card_page.dart';
import 'usage_page/usage_page.dart';
import 'welcome_onboard_bottom_sheet.dart';

class MainScreenArg extends PageBaseArg {
  bool isLoggedIn;
  MainScreenSubPage? initialPage;
  MainScreenInitialAction? initialAction;
  bool? isCardActivated;

  MainScreenArg({
    required this.isLoggedIn,
    this.initialPage,
    this.initialAction,
    this.isCardActivated,
  });
}

class MainScreen extends PageBase {
  /// Make sure you call [EvoPageStateBase.updateUserLoginStatus] function if need
  static Future<void> goNamed({
    required bool isLoggedIn,
    MainScreenSubPage? initialPage,
    MainScreenInitialAction? initialAction,
    bool? isCardActivated,
  }) async {
    return navigatorContext?.goNamed(
      Screen.mainScreen.name,
      extra: MainScreenArg(
        isLoggedIn: isLoggedIn,
        initialPage: initialPage,
        initialAction: initialAction,
        isCardActivated: isCardActivated,
      ),
    );
  }

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.mainScreen.routeName);

  final bool isLoggedIn;
  final bool? isCardActivated;

  final MainScreenSubPage? initialPage;
  final MainScreenInitialAction? initialAction;

  const MainScreen({
    required this.isLoggedIn,
    super.key,
    this.initialPage = MainScreenSubPage.home,
    this.initialAction,
    this.isCardActivated,
  });

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends EvoPageStateBase<MainScreen>
    with
        AutomaticKeepAliveClientMixin,
        SingleTickerProviderStateMixin,
        PermissionHandlerMixin,
        CheckForceUpdateHandlerMixin
    implements MainScreenController {
  static const int numberOfItemNavigationTabHistory = 2;

  final MainCubit _mainCubit = MainCubit(
    appState: getIt.get<AppState>(),
    mainScreenDialogHandler: MainScreenDialogHandler(),
  );

  DateTime? currentBackPressTime;

  final List<BottomBarItemModel> _bottomBarData = <BottomBarItemModel>[];

  late TabController _pageController;

  final NavigationHistoryStack navigationHistorySubPage = NavigationHistoryStack(
      defaultPage: MainScreenSubPage.home, size: numberOfItemNavigationTabHistory);

  late final UserProfileCubit _userProfileCubit = UserProfileCubit(
    userRepo: getIt.get<UserRepo>(),
    appState: appState,
    localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
  );

  @override
  void initState() {
    super.initState();

    _initBottomBarModel();

    navigationHistorySubPage.push(widget.initialPage ?? MainScreenSubPage.home);

    _pageController = TabController(
      initialIndex: MainScreenSubPage.home.index,
      length: _bottomBarData.length,
      vsync: this,
    );

    SchedulerBinding.instance.addPostFrameCallback((_) async {
      WelcomeOnboardBottomSheet.show(isCardActivated: widget.isCardActivated);

      // force update if needed
      await checkForceUpdate().then((_) {
        _mainCubit.handleFeatureProcessed(FeaturesWithDialogDisplay.forceUpdate);
      }).then((_) => _handleInitialAction(widget.initialAction));

      // check decree consent if need
      if (widget.isLoggedIn) {
        _handleAfterLoggedIn();
      }
    });

    /// get user info such as fullName, gender, birthday,...
    /// for improve UI/UX more friendly
    _userProfileCubit.fetchUserInfoFromServer();
  }

  @override
  void didUpdateWidget(covariant MainScreen oldWidget) {
    super.didUpdateWidget(oldWidget);
    _initBottomBarModel();
  }

  @override
  bool get wantKeepAlive => true;

  /// ** Edge case ** :
  /// Because MainPage contains subPage HomePage, HistoryPage, PromotionPage, ProfilePage (Bottom Navigation Menu)
  /// so when navigate to MainPage => MainPage & 1 of 4 above pages return isTopVisible = true
  /// => [handleBiometricChangedIfNeed] is called 2 times when app resumed. to ensure [handleBiometricChangedIfNeed]
  /// is called 1 time, set [hasRouteObserver()] = false
  @override
  bool hasRouteObserver() => false;

  @override
  void jumpToPage(MainScreenSubPage screenChild) {
    _showPageAtIndex(screenChild.pageIndex);
  }

  @override
  MainScreenSubPage getCurrentSubPage() {
    return navigationHistorySubPage.top;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return getContentWidget(context);
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<MainCubit>(
      create: (_) => _mainCubit,
      child: BlocListener<MainCubit, MainState>(
        listener: (_, MainState state) => _onListenMainStateChange(state),
        child: Scaffold(
          // Note: Fix for show SnackBar on FAB and TabBar
          // Link: https://stackoverflow.com/a/58834439/10262450
          body: PopScope(
            canPop: false,
            onPopInvokedWithResult: (bool didPop, _) {
              _onWillPop();
            },
            child: Scaffold(
              body: TabBarView(
                controller: _pageController,
                physics: const NeverScrollableScrollPhysics(),
                children: _bottomBarData.map((BottomBarItemModel e) => e.page).toList(),
              ),
              bottomNavigationBar: BottomAppBar(
                color: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: _buildBottomBarWidget(),
              ),
              resizeToAvoidBottomInset: false,
            ),
          ),
          resizeToAvoidBottomInset: false,
        ),
      ),
    );
  }

  Widget _buildBottomBarWidget() {
    final List<Widget> bottomBarItemWidgets = <Widget>[];

    for (int index = 0; index < _bottomBarData.length; index++) {
      final BottomBarItemModel element = _bottomBarData[index];
      bottomBarItemWidgets.add(
        Flexible(
          fit: FlexFit.tight,
          child: BottomBarItemWidget(
            label: element.label,
            icon: element.icon,
            isSelected: index == navigationHistorySubPage.top.pageIndex,
            onTap: () {
              _changeTabIfNeed(index);
            },
          ),
        ),
      );
    }

    return Wrap(
      children: <Widget>[
        SizedBox(
          height: 80,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: bottomBarItemWidgets,
          ),
        )
      ],
    );
  }

  void _initBottomBarModel() {
    _bottomBarData.clear();
    _bottomBarData.addAll(<BottomBarItemModel>[
      BottomBarItemModel(
        label: EvoStrings.bottomBarHomeLabel,
        icon: EvoImages.icBottomBarHome,
        page: HomePage(mainScreenController: this),
      ),
      BottomBarItemModel(
        label: EvoStrings.bottomBarCardLabel,
        icon: EvoImages.icBottomBarCards,
        page: CardsPage(mainPageController: this),
      ),
      BottomBarItemModel(
        label: EvoStrings.bottomBarPayCardLabel,
        icon: EvoImages.icBanknotes,
        page: const PayCardPage(),
      ),
      BottomBarItemModel(
        label: EvoStrings.bottomBarUsageLabel,
        icon: EvoImages.icArrowsRightLeft,
        page: const UsagePage(),
      ),
      BottomBarItemModel(
        label: EvoStrings.bottomBarProfileLabel,
        icon: EvoImages.icBottomBarProfile,
        page: const ProfileScreen(),
      )
    ]);
  }

  void _changeTabIfNeed(int index) {
    if (navigationHistorySubPage.top.pageIndex != index) {
      _showPageAtIndex(index);
    }
  }

  void _showPageAtIndex(int index) {
    setState(() {
      navigationHistorySubPage.push(MainScreenSubPage.getByIndex(index));

      /// Duration of animation when change page is 200ms. It's a good duration for user experience
      /// can be changed if needed
      _pageController.animateTo(index,
          duration: const Duration(milliseconds: 200), curve: Curves.easeInOut);
    });
  }

  /// Exist app if the current page is [HomePage] and the user clicked Back button 2 times within 2 seconds.
  /// Otherwise show toast to guide them
  Future<bool> _onWillPop() async {
    if (navigationHistorySubPage.top == MainScreenSubPage.home &&
        navigationHistorySubPage.length == 1) {
      final DateTime now = DateTime.now();
      if (currentBackPressTime == null ||
          commonUtilFunction.isOverDuration(
              timeToCheck: currentBackPressTime!, durationInMilliseconds: 2000)) {
        currentBackPressTime = now;
        showSnackBarNeutral(EvoStrings.existWarning);
        return Future<bool>.value(false);
      }
      return Future<bool>.value(true);
    } else {
      final MainScreenSubPage subPage = navigationHistorySubPage.pop();
      jumpToPage(subPage);
      return Future<bool>.value(false);
    }
  }

  void _handleInitialAction(MainScreenInitialAction? initialAction) {
    initialAction?.process();
  }

  void _onListenMainStateChange(MainState state) {
    if (state is AllFeaturesWithDialogProcessed) {
      /// This state is emitted when all features [forceUpdate dialog, requestActiveBiometric dialog] processed
      /// So we can handle other business logics here. For example: show dialog, navigate to other screen, etc.
    }
  }

  Future<void> _handleAfterLoggedIn() async {
    final AppState appState = getIt.get<AppState>();
    if (appState.actionAfterLogin != null) {
      appState.actionAfterLogin?.call();

      // reset callback after called
      appState.actionAfterLogin = null;
    } else {
      _handleUserStatus();
    }
  }

  /// handle User Status if status has just activated
  Future<void> _handleUserStatus() async {
    if (appState.userStatus == UserStatus.hasJustActivated) {
      final String? fullName = appState.userInfo.value?.fullName;
      await evoDialogFunction.showDialogConfirm(
          dialogId: EvoDialogId.welcomeNewUserDialog,
          title: EvoStrings.welcomeNewUserTitle.replaceVariableByValue(<String>[fullName ?? '']),
          titleTextStyle: evoTextStyles.bold(TextSize.xl2),
          textPositive: EvoStrings.ctaLetsGo,
          dialogHorizontalPadding: 24.0,
          imageHeader: evoImageProvider.asset(EvoImages.virtualCardActivated, fit: BoxFit.fitWidth),
          autoClosePopupWhenClickCTA: true);
    }
  }
}
