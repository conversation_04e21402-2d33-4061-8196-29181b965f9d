// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/cupertino.dart';

import '../../resources/resources.dart';
import '../../util/dialog_functions.dart';
import '../../util/screen_util.dart';

class WelcomeOnboardBottomSheet extends StatelessWidget {
  final bool isCardActivated;

  const WelcomeOnboardBottomSheet(this.isCardActivated, {super.key});

  static void show({required bool? isCardActivated}) {
    if (isCardActivated == null) {
      return;
    }
    evoDialogFunction.showDialogBottomSheet(
      hasCloseButton: true,
      title: EvoStrings.welcomeOnboardTitle,
      content: WelcomeOnboardBottomSheet(isCardActivated),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: isCardActivated ? _buildCardActivated() : _buildCardNotActivated(),
    );
  }

  List<Widget> _buildCardActivated() {
    return <Widget>[
      _buildImage(),
      EvoDimension.space4,
      Text(
        EvoStrings.welcomeOnboardCardActivatedTitle,
        style: evoTextStyles.regular(TextSize.base),
      ),
      EvoDimension.space4,
      _buildPrefixedRow('1.', EvoStrings.welcomeOnboardCardActivatedItem1),
      _buildPrefixedRow('2.', EvoStrings.welcomeOnboardCardActivatedItem2),
      _buildPrefixedRow('3.', EvoStrings.welcomeOnboardCardActivatedItem3),
    ];
  }

  List<Widget> _buildCardNotActivated() {
    return <Widget>[
      _buildImage(),
      EvoDimension.space4,
      Text(
        EvoStrings.welcomeOnboardCardNotActivated,
        style: evoTextStyles.regular(TextSize.base),
      ),
    ];
  }

  Widget _buildImage() {
    return evoImageProvider.asset(
      EvoImages.imgWelcomeOnboardDialog,
      height: 160.w,
      fit: BoxFit.contain,
    );
  }

  Widget _buildPrefixedRow(String prefix, String text) {
    final TextStyle style = evoTextStyles.regular(TextSize.s, color: evoColors.grayBase);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        SizedBox(
          width: 16.sp,
          child: Center(child: Text(prefix, style: style)),
        ),
        Expanded(
          child: Text(text, style: style),
        ),
        EvoDimension.space16,
      ],
    );
  }
}
