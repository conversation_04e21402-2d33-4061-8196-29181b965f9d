// coverage:ignore-file
// implement on this ticket https://trustingsocial1.atlassian.net/browse/ENBCC-528

import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';

import '../../../base/modules/core/app_state.dart';
import '../main_screen_dialog_handler/main_screen_dialog_handler.dart';

part 'main_state.dart';

class MainCubit extends CommonCubit<MainState> {
  final AppState appState;
  final MainScreenDialogHandler _mainScreenDialogHandler;

  MainCubit({
    required this.appState,
    required MainScreenDialogHandler mainScreenDialogHandler,
  })  : _mainScreenDialogHandler = mainScreenDialogHandler,
        super(MainInitial());

  void handleFeatureProcessed(FeaturesWithDialogDisplay featureDialog) {
    _mainScreenDialogHandler.handleFeatureProcessed(featureDialog);
    if (_mainScreenDialogHandler.isAllFeaturesWithDialogProcessed) {
      emit(AllFeaturesWithDialogProcessed());
    }
  }
}
