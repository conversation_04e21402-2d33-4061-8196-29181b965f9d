import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import '../utils/bio_auth_result.dart';

enum BiometricAuthenticationFailureType {
  biometrics,
  apiError,
  noSupportFlow,
  unknown;
}

/// Result types for biometric authentication enablement operations using async/await pattern
/// This replaces the callback-based BiometricTokenModuleCallback approach
/// Represents the outcome of enabling biometric authentication for the user
sealed class BiometricAuthenticationResult {}

/// Successful biometric authentication enablement
/// Contains the biometric token and any additional data from the authentication process
class BiometricAuthenticationSuccess extends BiometricAuthenticationResult {
  final BaseEntity? entity;

  BiometricAuthenticationSuccess({required this.entity});
}

/// Failed biometric authentication enablement
/// Contains detailed error information for proper error handling and user feedback
class BiometricAuthenticationFailure extends BiometricAuthenticationResult {
  /// The specific type of error that occurred
  final BiometricAuthenticationFailureType errorType;

  /// User-friendly error message for display
  final String? userMessage;

  /// API error details if the failure was due to an API call
  final ErrorUIModel? apiError;

  /// Biometric-specific error if the failure was due to biometric authentication
  final BioAuthError? biometricError;


  BiometricAuthenticationFailure({
    required this.errorType,
    this.userMessage,
    this.apiError,
    this.biometricError,
  });

  /// Get the most appropriate error message for display to the user
  String get displayMessage {
    if (userMessage != null && userMessage!.isNotEmpty) {
      return userMessage!;
    }
    if (apiError?.userMessage != null && apiError!.userMessage!.isNotEmpty) {
      return apiError!.userMessage!;
    }
    if (biometricError != null) {
      return BioAuthError.getErrMsg(biometricError);
    }
    return EvoStrings.unknownError;
  }

  /// Check if this is a biometric-related error (device/sensor issues)
  bool get isBiometricError =>
      errorType == BiometricAuthenticationFailureType.biometrics;

  /// Check if this is an API-related error (network/server issues)
  bool get isApiError => errorType == BiometricAuthenticationFailureType.apiError;

  @override
  String toString() =>
      'BiometricAuthenticationFailure(type: $errorType, message: $displayMessage';
}

/// Extension methods for easier result handling and pattern matching
extension BiometricAuthenticationResultExtensions
    on BiometricAuthenticationResult {
  /// Check if the authentication was successful
  bool get isSuccess => this is BiometricAuthenticationSuccess;

  /// Check if the authentication failed
  bool get isFailure => this is BiometricAuthenticationFailure;

  /// Get success result or null
  BiometricAuthenticationSuccess? get asSuccess =>
      this is BiometricAuthenticationSuccess
          ? this as BiometricAuthenticationSuccess
          : null;

  /// Get failure result or null
  BiometricAuthenticationFailure? get asFailure =>
      this is BiometricAuthenticationFailure
          ? this as BiometricAuthenticationFailure
          : null;
}
