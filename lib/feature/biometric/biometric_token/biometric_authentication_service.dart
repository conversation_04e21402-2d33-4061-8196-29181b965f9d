// ignore_for_file: avoid_catches_without_on_clauses

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/log_error_mixin.dart';
import 'package:local_auth/local_auth.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

import '../../../base/modules/core/app_state.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/functions.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/token_utils/jwt_helper.dart';
import '../../logging/evo_logging_event.dart';
import '../model/biometric_status_change_notifier.dart';
import '../utils/bio_auth_result.dart';
import '../utils/biometrics_authenticate.dart';
import 'biometric_authentication_result.dart';
import 'providers/biometric_retrieval_result.dart';
import 'providers/biometric_token_provider.dart';
import 'providers/biometric_token_provider_config.dart';

class BiometricAuthenticationService with LogErrorMixin {
  final BiometricsAuthenticate biometricsAuthenticate;
  final EvoLocalStorageHelper secureStorageHelper;
  final TsBioDetectChanged bioDetectChanged;
  final JwtHelper jwtHelper;
  final BiometricTokenProviderFactory _providerFactory;

  @visibleForTesting
  bool isProcessing = false;

  BiometricAuthenticationService({
    required this.biometricsAuthenticate,
    required this.secureStorageHelper,
    required this.bioDetectChanged,
    required this.jwtHelper,
    BiometricTokenProviderFactory? providerFactory,
  }) : _providerFactory = providerFactory ?? BiometricTokenProviderFactory();

  /// Register a custom biometric token provider
  void registerProvider(BiometricTokenProvider provider) {
    _providerFactory.registerProvider(provider);
  }

  /// Unregister a biometric token provider
  void unregisterProvider(BiometricTokenProvider provider) {
    _providerFactory.unregisterProvider(provider);
  }

  /// Get all registered providers
  List<BiometricTokenProvider> getAllProviders() {
    return _providerFactory.getAllProviders();
  }

  /// Check if a provider exists for the given flow
  bool hasProviderForFlow(EnableBiometricAuthenticationFlow flow) {
    return _providerFactory.getProvider(flow) != null;
  }

  /// Enable biometric authentication with specified flow using async/await
  /// Returns BiometricAuthenticationResult
  Future<BiometricAuthenticationResult> enableWithFlow({
    required BiometricTokenProviderConfig config,
  }) async {
    return _runLocking(() async {
      try {
        // Extract flow from config - single source of truth
        final EnableBiometricAuthenticationFlow flow = config.flow;

        // Validate that the flow is supported
        if (!hasProviderForFlow(flow)) {
          return BiometricAuthenticationFailure(
            errorType: BiometricAuthenticationFailureType.noSupportFlow,
            userMessage: 'Unsupported flow: $flow.',
          );
        }

        final BioAuthResult bioAuthResult = await biometricsAuthenticate.authenticate(
          /// TODO: hoang-nguyen-2 update when finalize the brand name
          localizedReason: EvoStrings.localizedReasonForUsingBiometrics,
          cancelButton: EvoStrings.ignoreTitle,
        );

        if (bioAuthResult.isAuthSuccess) {
          // Handle UI loading at the module level, not in providers
          evoUtilFunction.showHudLoading();

          final BiometricTokenRetrievalResult biometricRetrievalResult =
              await _getBiometricToken(config);

          final BiometricAuthenticationResult enableResult =
              await _handleBiometricTokenRetrievalResult(biometricRetrievalResult);
          evoUtilFunction.hideHudLoading();
          return enableResult;
        } else {
          return BiometricAuthenticationFailure(
            errorType: BiometricAuthenticationFailureType.biometrics,
            biometricError: bioAuthResult.error,
          );
        }
      } catch (e) {
        return BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.biometrics,
          userMessage: 'Unexpected error during biometric authentication: $e',
        );
      }
    });
  }

  Future<BiometricAuthenticationResult> skipEnableWithFlow({
    required BiometricTokenProviderConfig config,
  }) async {
    return _runLocking(() async {
      if (!hasProviderForFlow(config.flow)) {
        return BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.noSupportFlow,
          userMessage: 'Unsupported flow: ${config.flow}',
        );
      }

      final BiometricTokenProvider? provider = _providerFactory.getProvider(config.flow);
      if (provider == null) {
        return BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.noSupportFlow,
          userMessage: 'Unsupported flow: ${config.flow}.',
        );
      }
      final BiometricTokenRetrievalResult skipResult = await provider.skipEnableBiometric(config);

      return _handleBiometricTokenRetrievalResult(skipResult);
    });
  }

  /// Get biometric token using the appropriate provider based on flow
  /// UI concerns (loading indicators) are handled at this level
  Future<BiometricTokenRetrievalResult> _getBiometricToken(
      BiometricTokenProviderConfig config) async {
    final BiometricTokenProvider? provider = _providerFactory.getProvider(config.flow);

    if (provider == null) {
      return BiometricTokenRetrievalFailure(
        errorType: BiometricAuthenticationFailureType.unknown,
        errorMessage: 'No provider found for flow: ${config.flow}',
      );
    }

    return provider.getBiometricToken(config);
  }

  /// Handle biometric token actions for async/await API
  /// Returns BiometricRetrievalResult instead of using callbacks
  Future<BiometricAuthenticationResult> _handleBiometricTokenRetrievalResult(
      BiometricTokenRetrievalResult result) async {
    switch (result) {
      case BiometricTokenRetrievalSuccess():
        await enableBiometricAuthenticationFeature(result.biometricToken);
        return BiometricAuthenticationSuccess(entity: result.baseEntity);
      case BiometricTokenSkipEnableSuccess():
        return BiometricAuthenticationSuccess(entity: result.baseEntity);

      case BiometricTokenRetrievalChallenge():
        //TODO For challenges, we need to execute them and return the result
        //return await _handleChallengeActionAsync(result);
        return BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.unknown,
        );

      case BiometricTokenRetrievalFailure():
        return BiometricAuthenticationFailure(
          errorType: result.errorType,
          apiError: result.apiError,
        );
    }
  }

  /// Pls note that, when [getBiometricChanged] is returns true,
  /// consider to call [disableBiometricAuthenticationFeature]
  /// to clear all biometric feature
  Future<bool> getBiometricChanged() async {
    bool isBiometricsChanged;
    // Platform messages may fail, so we use a try/catch PlatformException.
    // We also handle the message potentially returning null.
    try {
      isBiometricsChanged = await bioDetectChanged.isBiometricChanged() ?? false;
    } catch (e) {
      logException(
        eventType: EvoEventType.biometrics,
        methodName: 'getBiometricChanged',
        exception: e,
      );
      isBiometricsChanged = false;
    }

    return isBiometricsChanged;
  }

  Future<bool> hasEnrolledBiometrics() async {
    final List<BiometricType> enrolledBiometrics =
        await biometricsAuthenticate.getAvailableBiometricType();
    return enrolledBiometrics.isNotEmpty;
  }

  Future<bool> isEnableBiometricAuthenticator() {
    return secureStorageHelper.isEnableBiometricAuthenticator();
  }

  @visibleForTesting
  Future<void> enableBiometricAuthenticationFeature(String? biometricToken) async {
    if (biometricToken != null && biometricToken.isNotEmpty) {
      await secureStorageHelper.setBiometricToken(biometricToken);
      await secureStorageHelper.setBiometricAuthenticator(true);
      await bioDetectChanged.initialize();

      getIt.get<AppState>().biometricStatusChangeNotifier.update(BiometricStatus.usable);
    }
  }

  Future<void> disableBiometricAuthenticationFeature() async {
    await evoUtilFunction.clearBiometricAuthenticationData();
  }

  Future<bool> isBiometricTokenUsable() async {
    final String? biometricToken = await secureStorageHelper.getBiometricToken();
    return jwtHelper.isCanUse(biometricToken);
  }

  /// Dispose resources
  void dispose() {
    _providerFactory.dispose();
  }

  /// Prevents concurrent biometric operations by tracking processing state
  /// Returns failure if already processing
  FutureOr<BiometricAuthenticationResult> _runLocking(
      FutureOr<BiometricAuthenticationResult> Function() fn) async {
    if (isProcessing) {
      return BiometricAuthenticationFailure(
        errorType: BiometricAuthenticationFailureType.unknown,
        userMessage: 'Biometric authentication is already in progress',
      );
    }

    isProcessing = true;
    final BiometricAuthenticationResult result = await fn();
    isProcessing = false;

    return result;
  }
}
