import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../base/ext_biometric_token_entity.dart';
import '../biometric_authentication_result.dart';

/// Result of biometric token retrieval from server
sealed class BiometricTokenRetrievalResult {}

/// Successfully retrieved biometric token
class BiometricTokenRetrievalSuccess extends BiometricTokenRetrievalResult {
  final String? biometricToken;
  final BaseEntity? baseEntity;

  BiometricTokenRetrievalSuccess({required this.biometricToken, required this.baseEntity});
}

class BiometricTokenSkipEnableSuccess extends BiometricTokenRetrievalResult {
  final BaseEntity? baseEntity;

  BiometricTokenSkipEnableSuccess({required this.baseEntity});
}

/// Server requires additional challenge before providing token
class BiometricTokenRetrievalChallenge extends BiometricTokenRetrievalResult {
  BiometricTokenRetrievalChallenge(this.challengeType);

  final ChallengeType challengeType;
}

/// Error during token retrieval - supports generic, API, and biometric errors
class BiometricTokenRetrievalFailure extends BiometricTokenRetrievalResult {
  BiometricTokenRetrievalFailure({
    required this.errorType,
    this.errorMessage,
    this.apiError,
  });

  /// Factory constructor for generic errors
  BiometricTokenRetrievalFailure.generic({
    required BiometricAuthenticationFailureType errorType,
    String? errorMessage,
  }) : this(
          errorType: errorType,
          errorMessage: errorMessage,
        );

  /// Factory constructor for API errors
  BiometricTokenRetrievalFailure.api({
    required ErrorUIModel error,
  }) : this(
          errorType: BiometricAuthenticationFailureType.apiError,
          apiError: error,
        );

  final BiometricAuthenticationFailureType errorType;
  final String? errorMessage;
  final ErrorUIModel? apiError;

  /// Get the most appropriate error message for display
  String? get displayMessage {
    // Priority: explicit errorMessage > API error message > biometric error message
    if (errorMessage != null && errorMessage!.isNotEmpty) {
      return errorMessage;
    }
    if (apiError?.userMessage != null && apiError!.userMessage!.isNotEmpty) {
      return apiError!.userMessage;
    }
    return null;
  }

  /// Check if this is an API-related error
  bool get isApiError => errorType == BiometricAuthenticationFailureType.apiError;
}

/// Extension methods for easier result handling and pattern matching
extension BiometricTokenRetrievalResultExtensions on BiometricTokenRetrievalResult {
  /// Check if the token retrieval was successful
  bool get isSuccess => this is BiometricTokenRetrievalSuccess;

  /// Check if the token retrieval requires a challenge
  bool get isChallenge => this is BiometricTokenRetrievalChallenge;

  /// Check if the token retrieval failed
  bool get isFailure => this is BiometricTokenRetrievalFailure;

  /// Get success result or null
  BiometricTokenRetrievalSuccess? get asSuccess =>
      this is BiometricTokenRetrievalSuccess ? this as BiometricTokenRetrievalSuccess : null;

  /// Get challenge result or null
  BiometricTokenRetrievalChallenge? get asChallenge =>
      this is BiometricTokenRetrievalChallenge ? this as BiometricTokenRetrievalChallenge : null;

  /// Get failure result or null
  BiometricTokenRetrievalFailure? get asFailure =>
      this is BiometricTokenRetrievalFailure ? this as BiometricTokenRetrievalFailure : null;

  /// Get the biometric token if successful
  String? get biometricToken {
    if (this is BiometricTokenRetrievalSuccess) {
      return (this as BiometricTokenRetrievalSuccess).biometricToken;
    }
    return null;
  }

  /// Get the challenge type if challenge is required
  ChallengeType? get challengeType {
    if (this is BiometricTokenRetrievalChallenge) {
      return (this as BiometricTokenRetrievalChallenge).challengeType;
    }
    return null;
  }

  /// Get the error type for any failure
  BiometricAuthenticationFailureType? get errorType {
    if (this is BiometricTokenRetrievalFailure) {
      return (this as BiometricTokenRetrievalFailure).errorType;
    }
    return null;
  }

  /// Get the error message for any failure
  String? get errorMessage {
    if (this is BiometricTokenRetrievalFailure) {
      return (this as BiometricTokenRetrievalFailure).displayMessage;
    }
    return null;
  }
}
