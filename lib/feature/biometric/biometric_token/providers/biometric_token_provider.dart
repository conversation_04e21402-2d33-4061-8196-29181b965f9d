import 'package:flutter/cupertino.dart';

import '../biometric_authentication_result.dart';
import 'biometric_retrieval_result.dart';
import 'biometric_token_provider_config.dart';

/// Abstract provider for biometric token retrieval
/// Each concrete provider supports exactly one flow
abstract class BiometricTokenProvider {
  /// Retrieves biometric token based on the provided configuration
  Future<BiometricTokenRetrievalResult> getBiometricToken(BiometricTokenProviderConfig config);

  /// Gets the specific flow that this provider supports
  EnableBiometricAuthenticationFlow get supportedFlow;

  Future<BiometricTokenRetrievalResult> skipEnableBiometric(BiometricTokenProviderConfig config);

  @visibleForTesting
  @protected
  // Validate that this provider is being used for the correct flow
  BiometricTokenRetrievalResult? validateFlow(EnableBiometricAuthenticationFlow flow) {
    if (flow != supportedFlow) {
      return BiometricTokenRetrievalFailure(
        errorType: BiometricAuthenticationFailureType.noSupportFlow,
        errorMessage: '$runtimeType only is not supported for $flow',
      );
    }

    return null;
  }
}

/// Factory for creating and managing biometric token providers
/// Ensures only one provider instance per flow type is registered
class BiometricTokenProviderFactory {
  final Map<EnableBiometricAuthenticationFlow, BiometricTokenProvider> _providers = {};

  /// Registers a provider for its supported flow
  /// If a provider for the same flow already exists, it will be replaced
  void registerProvider(BiometricTokenProvider provider) {
    _providers[provider.supportedFlow] = provider;
  }

  /// Unregisters a provider by removing it from the supported flow
  void unregisterProvider(BiometricTokenProvider provider) {
    final flow = provider.supportedFlow;
    if (_providers[flow] == provider) {
      _providers.remove(flow);
    }
  }

  /// Gets the provider that supports the specified flow
  BiometricTokenProvider? getProvider(EnableBiometricAuthenticationFlow flow) {
    return _providers[flow];
  }

  /// Returns all registered providers as an unmodifiable list
  List<BiometricTokenProvider> getAllProviders() => List.unmodifiable(_providers.values);

  /// Dispose all providers and clear resources
  void dispose() {
    _providers.clear();
  }
}
