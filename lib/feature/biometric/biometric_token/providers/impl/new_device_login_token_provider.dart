// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

// coverage:ignore-file
// skip coverage for now, will implement on the same ticket later

import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../../../data/repository/authentication_repo.dart';
import '../../../../../data/request/login_new_device_request.dart';
import '../../../../../data/response/login_new_device_entity.dart';
import '../../../../login/mock/mock_login_new_device_file_name_use_case.dart';
import '../../biometric_authentication_result.dart';
import '../biometric_retrieval_result.dart';
import '../biometric_token_provider.dart';
import '../biometric_token_provider_config.dart';

class NewDeviceLoginTokenProvider extends BiometricTokenProvider {
  static const String sessionKey = 'session_token';
  final AuthenticationRepo authRepo;

  NewDeviceLoginTokenProvider({
    required this.authRepo,
  });

  @override
  EnableBiometricAuthenticationFlow get supportedFlow =>
      EnableBiometricAuthenticationFlow.newDeviceLogin;

  @override
  Future<BiometricTokenRetrievalResult> getBiometricToken(
      BiometricTokenProviderConfig config) async {
    final BiometricTokenRetrievalResult? validateResult = validateFlow(config.flow);
    if (validateResult != null) {
      return validateResult;
    }

    final LoginNewDeviceEntity entity = await authRepo.loginNewDevice(
      request: EnableBiometricRequest(
        skip: false,
        sessionToken: _getSessionToken(config),
      ),
      mockConfig: MockConfig(
          enable: false,
          fileName: getMockLoginNewDeviceFileNameByCase(
            MockLoginNewDeviceFileNameUseCase.getVerifyBiometricTokenSuccess,
          )),
    );

    if (entity.statusCode != CommonHttpClient.SUCCESS) {
      final ErrorUIModel errorModel = ErrorUIModel.fromEntity(entity);
      return BiometricTokenRetrievalFailure(
        errorType: BiometricAuthenticationFailureType.apiError,
        apiError: errorModel,
      );
    }

    return BiometricTokenRetrievalSuccess(
      biometricToken: entity.biometricToken,
      baseEntity: entity,
    );
  }

  @override
  Future<BiometricTokenRetrievalResult> skipEnableBiometric(
      BiometricTokenProviderConfig config) async {
    final BiometricTokenRetrievalResult? validateResult = validateFlow(config.flow);
    if (validateResult != null) {
      return validateResult;
    }

    final LoginNewDeviceEntity entity = await authRepo.loginNewDevice(
      request: EnableBiometricRequest(
        skip: true,
        sessionToken: _getSessionToken(config),
      ),
      mockConfig: MockConfig(
          enable: false,
          fileName: getMockLoginNewDeviceFileNameByCase(
            MockLoginNewDeviceFileNameUseCase.getVerifyBiometricTokenSuccess,
          )),
    );

    if (entity.statusCode != CommonHttpClient.SUCCESS) {
      final ErrorUIModel errorModel = ErrorUIModel.fromEntity(entity);
      return BiometricTokenRetrievalFailure(
        errorType: BiometricAuthenticationFailureType.apiError,
        apiError: errorModel,
      );
    }

    return BiometricTokenSkipEnableSuccess(baseEntity: entity);
  }

  String? _getSessionToken(BiometricTokenProviderConfig config) {
    return config.additionalParams?.let((Map<String, dynamic> params) {
      if (params.containsKey(sessionKey)) {
        return params[sessionKey];
      }
      return null;
    });
  }
}
