import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../../../data/repository/authentication_repo.dart';
import '../../../../../data/request/activate_account_request.dart';
import '../../../../../data/response/account_activation_entity.dart';
import '../../../../account_activation/mock/mock_account_activation_use_case.dart';
import '../../biometric_authentication_result.dart';
import '../biometric_retrieval_result.dart';
import '../biometric_token_provider.dart';
import '../biometric_token_provider_config.dart';

/// Provider specifically for account activation flow
/// Handles biometric token retrieval during account activation process
class AccountActivationTokenProvider extends BiometricTokenProvider {
  static const String sessionKey = 'session_token';
  final AuthenticationRepo authRepo;

  static BiometricTokenProviderConfig createConfig({required String? sessionToken}) {
    return BiometricTokenProviderConfig(
        flow: EnableBiometricAuthenticationFlow.accountActivation,
        additionalParams: <String, String?>{
          sessionKey: sessionToken,
        });
  }

  AccountActivationTokenProvider({
    required this.authRepo,
  });

  @override
  EnableBiometricAuthenticationFlow get supportedFlow =>
      EnableBiometricAuthenticationFlow.accountActivation;

  String? _getSessionToken(BiometricTokenProviderConfig config) =>
      config.additionalParams?[sessionKey];

  @override
  Future<BiometricTokenRetrievalResult> getBiometricToken(
      BiometricTokenProviderConfig config) async {
    final BiometricTokenRetrievalResult? validateResult = validateFlow(config.flow);
    if (validateResult != null) {
      return validateResult;
    }

    final AccountActivationEntity entity = await authRepo.activateAccount(
      request: ActivateAccountEnableBiometricRequest(
        skip: false,
        sessionToken: _getSessionToken(config),
      ),
      mockConfig: _getDefaultMockConfig(),
    );

    if (entity.statusCode != CommonHttpClient.SUCCESS) {
      final ErrorUIModel errorModel = ErrorUIModel.fromEntity(entity);
      return BiometricTokenRetrievalFailure(
          errorType: BiometricAuthenticationFailureType.apiError, apiError: errorModel);
    }

    return BiometricTokenRetrievalSuccess(
        biometricToken: entity.biometricToken, baseEntity: entity);
  }

  MockConfig _getDefaultMockConfig() {
    return MockConfig(
      enable: false,
      statusCode: CommonHttpClient.INVALID_TOKEN,
      fileName: getMockAccountActivationFileNameByCase(
        MockAccountActivationUseCase.getEnableBiometricSuccess,
      ),
    );
  }

  @override
  Future<BiometricTokenRetrievalResult> skipEnableBiometric(
      BiometricTokenProviderConfig config) async {
    final BiometricTokenRetrievalResult? validateResult = validateFlow(config.flow);
    if (validateResult != null) {
      return validateResult;
    }

    final AccountActivationEntity entity = await authRepo.activateAccount(
      request: ActivateAccountEnableBiometricRequest(
        skip: true,
        sessionToken: _getSessionToken(config),
      ),
      mockConfig: MockConfig(
        enable: false,
        fileName: getMockAccountActivationFileNameByCase(
          MockAccountActivationUseCase.getSkipEnableBiometricSuccess,
        ),
      ),
    );

    if (entity.statusCode != CommonHttpClient.SUCCESS) {
      final ErrorUIModel errorModel = ErrorUIModel.fromEntity(entity);
      return BiometricTokenRetrievalFailure(
          errorType: BiometricAuthenticationFailureType.apiError, apiError: errorModel);
    }

    return BiometricTokenSkipEnableSuccess(baseEntity: entity);
  }
}
