/// Enum to define different biometric token retrieval flows
/// Currently supports two main flows as per business requirements
enum EnableBiometricAuthenticationFlow {
  /// Flow for account activation process
  /// Used when user is setting up biometric authentication for the first time
  accountActivation,

  /// Flow for new device login process
  newDeviceLogin,

  /// Flow for profile settings management
  /// Used when user is managing biometric settings in their profile
  profileSettings,
}

/// Configuration for biometric token retrieval
class BiometricTokenProviderConfig {
  final EnableBiometricAuthenticationFlow flow;
  final Map<String, dynamic>? additionalParams;

  const BiometricTokenProviderConfig({
    required this.flow,
    this.additionalParams,
  });

  BiometricTokenProviderConfig copyWith({
    EnableBiometricAuthenticationFlow? flow,
    Map<String, dynamic>? additionalParams,
  }) {
    return BiometricTokenProviderConfig(
      flow: flow ?? this.flow,
      additionalParams: additionalParams ?? this.additionalParams,
    );
  }
}
