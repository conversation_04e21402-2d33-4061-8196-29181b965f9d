import 'package:flutter_common_package/util/utils.dart';

import '../../../base/modules/core/app_state.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/evo_snackbar.dart';
import '../model/biometric_status_change_notifier.dart';
import '../utils/biometric_status_helper.dart';
import 'biometric_authentication_service.dart';

mixin BiometricTokenUsabilityMixin {
  final AppState _appState = getIt.get<AppState>();
  final BiometricAuthenticationService biometricsTokenModule = getIt.get<BiometricAuthenticationService>();

  Future<bool> checkAndHandleBiometricTokenUnUsable() async {
    final bool isUnUsable = await checkBiometricTokenUnUsable();

    if (isUnUsable) {
      await biometricsTokenModule.disableBiometricAuthenticationFeature();
      _appState.biometricStatusChangeNotifier.update(BiometricStatus.biometricTokenUnusable);
    }

    return isUnUsable;
  }

  Future<bool> checkBiometricTokenUnUsable() async {
    final bool isEnableBiometric = await biometricsTokenModule.isEnableBiometricAuthenticator();
    if (!isEnableBiometric) {
      return false;
    }

    final bool isUnUsable = (await biometricsTokenModule.isBiometricTokenUsable()) == false;

    return isUnUsable;
  }

  Future<void> handleBiometricTokenUnUsable() async {
    final bool isUnUsable = await checkAndHandleBiometricTokenUnUsable();
    if (isUnUsable && _appState.isUserLogIn) {
      await showBiometricTokenUnUsableToastAndUpdateStatus();
    }
  }

  Future<bool?> showBiometricTokenUnUsableToast() async {
    return getIt.get<EvoSnackBar>().show(
          EvoStrings.biometricTokenUnUsableMessage.replaceVariableByValue(
            <String>[
              _appState.bioTypeInfo.biometricTypeName,
            ],
          ),
          typeSnackBar: SnackBarType.error,
        );
  }

  Future<void> showBiometricTokenUnUsableToastAndUpdateStatus() async {
    await showBiometricTokenUnUsableToast();
    await getIt.get<BiometricStatusHelper>().updateBiometricStatus();
  }
}
