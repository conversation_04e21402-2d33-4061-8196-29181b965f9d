import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../utils/bio_auth_result.dart';

@immutable
abstract class ActivateBiometricState {}

class ActivateBiometricInitState extends ActivateBiometricState {}

class ActivateBiometricSuccess extends ActivateBiometricState {
  final BaseEntity entity;
  final bool skip;

  ActivateBiometricSuccess({
    required this.entity,
    required this.skip,
  });
}

class ActivateBiometricLocalAuthError extends ActivateBiometricState {
  final BioAuthError? error;

  ActivateBiometricLocalAuthError({
    required this.error,
  });
}

class ActivateBiometricUnknownError extends ActivateBiometricState {}

class ActivateBiometricInvalidToken extends ActivateBiometricState {}

class ActivateBiometricApiError extends ActivateBiometricState {
  final ErrorUIModel? error;

  ActivateBiometricApiError({this.error});
}

class ActivateBiometricLoading extends ActivateBiometricState {}
