import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

import '../../../data/repository/authentication_repo.dart';
import '../biometric_token/biometric_authentication_result.dart';
import '../biometric_token/biometric_authentication_service.dart';
import '../biometric_token/providers/biometric_token_provider_config.dart';
import '../biometric_token/providers/impl/account_activation_token_provider.dart';
import '../biometric_token/providers/impl/new_device_login_token_provider.dart';
import '../utils/biometrics_authenticate.dart';
import 'activate_biometric_state.dart';

class ActivateBiometricCubit extends CommonCubit<ActivateBiometricState> {
  final BiometricsAuthenticate bioAuth;

  final BiometricAuthenticationService biometricAuthenticationService;

  final AuthenticationRepo authRepo;

  final EnableBiometricAuthenticationFlow flow;

  ActivateBiometricCubit({
    required this.bioAuth,
    required this.biometricAuthenticationService,
    required this.authRepo,
    required this.flow,
  }) : super(ActivateBiometricInitState()) {
    biometricAuthenticationService
      ..registerProvider(AccountActivationTokenProvider(authRepo: authRepo))
      ..registerProvider(NewDeviceLoginTokenProvider(authRepo: authRepo));
  }

  @override
  Future<void> close() async {
    biometricAuthenticationService.dispose();
    await super.close();
  }

  Future<void> enableBiometricAuthentication(String? sessionToken) async {
    final BiometricAuthenticationResult result =
        await biometricAuthenticationService.enableWithFlow(
      config: _getConfig(sessionToken: sessionToken, flow: flow),
    );

    handleBiometricAuthenticationResult(
      result: result,
      skip: false,
    );
  }

  Future<void> skipActivateBiometric(String? sessionToken) async {
    emit(ActivateBiometricLoading());

    final BiometricAuthenticationResult result = await biometricAuthenticationService
        .skipEnableWithFlow(config: _getConfig(sessionToken: sessionToken, flow: flow));

    handleBiometricAuthenticationResult(result: result, skip: true);
  }

  @visibleForTesting
  void handleBiometricAuthenticationResult({
    required BiometricAuthenticationResult result,
    required bool skip,
  }) {
    if (result is BiometricAuthenticationFailure) {
      onHandlingBiometricAuthenticationFailure(result);
      return;
    }

    if (result is BiometricAuthenticationSuccess && result.entity != null) {
      emit(ActivateBiometricSuccess(
        entity: result.entity!,
        skip: skip,
      ));
      return;
    }

    emit(ActivateBiometricUnknownError());
  }

  @visibleForTesting
  void onHandlingBiometricAuthenticationFailure(BiometricAuthenticationFailure failure) {
    if (failure.isApiError) {
      _handleApiError(failure.apiError);
      return;
    }

    if (failure.isBiometricError) {
      emit(ActivateBiometricLocalAuthError(error: failure.biometricError));
      return;
    }

    emit(ActivateBiometricUnknownError());
  }

  void _handleApiError(ErrorUIModel? apiError) {
    if (apiError?.statusCode == CommonHttpClient.INVALID_TOKEN) {
      emit(ActivateBiometricInvalidToken());
      return;
    }

    emit(ActivateBiometricApiError(error: apiError));
  }

  BiometricTokenProviderConfig _getConfig({
    required EnableBiometricAuthenticationFlow flow,
    String? sessionToken,
  }) {
    final String key = switch (flow) {
      EnableBiometricAuthenticationFlow.accountActivation =>
        AccountActivationTokenProvider.sessionKey,
      EnableBiometricAuthenticationFlow.newDeviceLogin => NewDeviceLoginTokenProvider.sessionKey,
      EnableBiometricAuthenticationFlow.profileSettings => throw UnimplementedError(),
    };

    return BiometricTokenProviderConfig(
      flow: flow,
      additionalParams: <String, String?>{
        key: sessionToken,
      },
    );
  }
}
