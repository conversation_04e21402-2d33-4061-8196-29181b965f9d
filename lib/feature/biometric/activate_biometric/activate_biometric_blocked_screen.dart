// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../model/challenge_success_model.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/functions.dart';
import '../../../util/screen_util.dart';
import '../../../widget/buttons.dart';
import '../../../widget/no_app_bar_wrapper.dart';
import '../biometric_token/biometric_authentication_service.dart';
import '../biometric_token/providers/biometric_token_provider_config.dart';
import '../utils/biometrics_authenticate.dart';
import 'activate_biometric_cubit.dart';
import 'activate_biometric_state.dart';

class ActivateBiometricBlockedScreenArg extends PageBaseArg {
  final String? sessionToken;
  final ChallengeSuccessCallback? onPopSuccess;
  final EnableBiometricAuthenticationFlow flow;

  ActivateBiometricBlockedScreenArg({
    required this.sessionToken,
    required this.flow,
    this.onPopSuccess,
  });
}

class ActivateBiometricBlockedScreen extends PageBase {
  final String? sessionToken;
  final ChallengeSuccessCallback? onPopSuccess;
  final EnableBiometricAuthenticationFlow flow;

  const ActivateBiometricBlockedScreen({
    required this.sessionToken,
    this.onPopSuccess,
    required this.flow,
    super.key,
  });

  static void pushReplacementNamed({
    required String? sessionToken,
    required EnableBiometricAuthenticationFlow flow,
    ChallengeSuccessCallback? onPopSuccess,
  }) {
    navigatorContext?.pushReplacementNamed(
      Screen.activateBiometricBlockedScreen.name,
      extra: ActivateBiometricBlockedScreenArg(
        sessionToken: sessionToken,
        onPopSuccess: onPopSuccess,
        flow: flow,
      ),
    );
  }

  @override
  State<ActivateBiometricBlockedScreen> createState() => ActivateBiometricBlockedScreenState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(
        name: Screen.activateBiometricBlockedScreen.name,
      );
}

@visibleForTesting
class ActivateBiometricBlockedScreenState extends EvoPageStateBase<ActivateBiometricBlockedScreen> {
  late ActivateBiometricCubit activeBiometricCubit;

  @override
  void initState() {
    super.initState();

    activeBiometricCubit = context.read<ActivateBiometricCubit?>() ??
        ActivateBiometricCubit(
          bioAuth: getIt.get<BiometricsAuthenticate>(),
          biometricAuthenticationService: getIt.get<BiometricAuthenticationService>(),
          authRepo: getIt.get<AuthenticationRepo>(),
          flow: widget.flow,
        );
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<ActivateBiometricCubit>(
      create: (_) => activeBiometricCubit,
      child: NoAppBarWrapper(
        child: BlocListener<ActivateBiometricCubit, ActivateBiometricState>(
          listener: (BuildContext context, ActivateBiometricState currState) {
            _listenActiveBiometric(currState);
          },
          child: PopScope(
            canPop: false,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: EvoDimension.screenHorizontalPadding),
              child: Column(
                children: <Widget>[
                  const Spacer(),
                  evoImageProvider.asset(EvoImages.icAlertError, width: 140.w, height: 140.w),
                  EvoDimension.space16,
                  Text(
                    EvoStrings.enableBiometricBlockedTitle,
                    textAlign: TextAlign.center,
                    style: evoTextStyles.bold(TextSize.h3, color: evoColors.grayText),
                  ),
                  EvoDimension.space8,
                  Text(
                    EvoStrings.enableBiometricBlockedDesc,
                    textAlign: TextAlign.center,
                    style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
                  ),
                  const Spacer(),
                  PrimaryButton(
                    text: EvoStrings.ctaProceed,
                    onTap: _onTap,
                  ),
                  SizedBox(height: EvoDimension.screenBottomPadding),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onTap() {
    activeBiometricCubit.skipActivateBiometric(widget.sessionToken);
  }

  void _listenActiveBiometric(ActivateBiometricState state) {
    if (state is ActivateBiometricLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    if (state is ActivateBiometricSuccess) {
      _onComplete(state.entity);
      return;
    }

    if (state is ActivateBiometricInvalidToken) {
      _showSessionExpiredDialog();
      return;
    }

    if (state is ActivateBiometricApiError) {
      handleEvoApiError(state.error);
      return;
    }

    if (state is ActivateBiometricUnknownError) {
      showSnackBarError(EvoStrings.unknownError);
      return;
    }
  }

  void _onComplete(BaseEntity entity) {
    navigatorContext?.pop();
    widget.onPopSuccess?.call(ChallengeSuccessModel(entity: entity));
  }

  void _showSessionExpiredDialog() {
    evoDialogFunction.showDialogSessionTokenExpired(
      type: SessionDialogType.fromEnableBiometricAuthenticationFlow(widget.flow),
    );
  }
}
