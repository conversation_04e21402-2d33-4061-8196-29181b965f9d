import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/extension.dart';

import '../../../base/evo_page_state_base.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../model/challenge_success_model.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/functions.dart';
import '../../../widget/buttons.dart';
import '../../../widget/no_app_bar_wrapper.dart';
import '../biometric_token/biometric_authentication_service.dart';
import '../biometric_token/providers/biometric_token_provider_config.dart';
import '../utils/biometric_functions.dart';
import '../utils/biometrics_authenticate.dart';
import 'activate_biometric_blocked_screen.dart';
import 'activate_biometric_cubit.dart';
import 'activate_biometric_state.dart';
import 'activate_biometric_success_screen.dart';

class ActivateBiometricScreenArg extends PageBaseArg {
  final ChallengeSuccessCallback onSuccess;
  final String? sessionToken;
  final EnableBiometricAuthenticationFlow flow;

  ActivateBiometricScreenArg({
    required this.onSuccess,
    required this.flow,
    this.sessionToken,
  });
}

class ActivateBiometricScreen extends PageBase {
  final ChallengeSuccessCallback onSuccess;
  final String? sessionToken;
  final EnableBiometricAuthenticationFlow flow;

  static Future<void> pushNamed({
    required ChallengeSuccessCallback onSuccess,
    String? sessionToken,
    EnableBiometricAuthenticationFlow flow = EnableBiometricAuthenticationFlow.accountActivation,
  }) async {
    return navigatorContext?.pushNamed(
      Screen.activateBiometricScreen.name,
      extra: ActivateBiometricScreenArg(
        onSuccess: onSuccess,
        sessionToken: sessionToken,
        flow: flow,
      ),
    );
  }

  const ActivateBiometricScreen({
    required this.onSuccess,
    required this.flow,
    this.sessionToken,
    super.key,
  });

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;

  @override
  RouteSettings get routeSettings => RouteSettings(name: Screen.activateBiometricScreen.routeName);

  @override
  EvoPageStateBase<ActivateBiometricScreen> createState() => ActivateBiometricScreenState();
}

class ActivateBiometricScreenState extends EvoPageStateBase<ActivateBiometricScreen> {
  late ActivateBiometricCubit activeBiometricCubit;

  @override
  void initState() {
    super.initState();

    activeBiometricCubit = context.read<ActivateBiometricCubit?>() ??
        ActivateBiometricCubit(
          bioAuth: getIt.get<BiometricsAuthenticate>(),
          biometricAuthenticationService: getIt.get<BiometricAuthenticationService>(),
          authRepo: getIt.get<AuthenticationRepo>(),
          flow: widget.flow,
        );
  }

  @override
  Widget getContentWidget(BuildContext context) {
    return BlocProvider<ActivateBiometricCubit>(
      create: (_) => activeBiometricCubit,
      child: NoAppBarWrapper(
        child: BlocListener<ActivateBiometricCubit, ActivateBiometricState>(
          listener: (BuildContext context, ActivateBiometricState currState) {
            _listenActiveBiometric(currState);
          },
          child: PopScope(
            canPop: false,
            child: Padding(
              padding: EdgeInsets.only(
                left: EvoDimension.screenHorizontalPadding,
                right: EvoDimension.screenHorizontalPadding,
                bottom: EvoDimension.screenBottomPadding,
              ),
              child: Column(children: <Widget>[
                _buildTitle(),
                EvoDimension.space4,
                _buildDesc(),
                Spacer(),
                ..._buildCTAs(),
              ]),
            ),
          ),
        ),
      ),
    );
  }

  Text _buildTitle() {
    return Text(
      EvoStrings.activeBiometricTitle,
      style: evoTextStyles.semibold(
        TextSize.h3,
      ),
    );
  }

  Text _buildDesc() {
    return Text(
      EvoStrings.activeBiometricDesc,
      style: evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
    );
  }

  List<Widget> _buildCTAs() {
    return <Widget>[
      PrimaryButton(
        onTap: () => activeBiometricCubit.enableBiometricAuthentication(
          widget.sessionToken,
        ),
        text: EvoStrings.ctaYes,
      ),
      EvoDimension.space8,
      TertiaryButton(
        onTap: () => activeBiometricCubit.skipActivateBiometric(
          widget.sessionToken,
        ),
        text: EvoStrings.ctaSkip,
      ),
    ];
  }

  void _listenActiveBiometric(ActivateBiometricState state) {
    if (state is ActivateBiometricLoading) {
      evoUtilFunction.showHudLoading();
      return;
    }

    evoUtilFunction.hideHudLoading();

    if (state is ActivateBiometricSuccess) {
      _onComplete(state);
      return;
    }

    if (state is ActivateBiometricLocalAuthError) {
      if (state.error?.isBiometricLocked() == true) {
        ActivateBiometricBlockedScreen.pushReplacementNamed(
          sessionToken: widget.sessionToken,
          onPopSuccess: widget.onSuccess,
          flow: widget.flow,
        );
        return;
      }

      biometricFunctions.handleBioError(
        bioError: state.error,
      );
      return;
    }

    if (state is ActivateBiometricInvalidToken) {
      evoDialogFunction.showDialogSessionTokenExpired(
        type: SessionDialogType.fromEnableBiometricAuthenticationFlow(widget.flow),
      );
      return;
    }

    if (state is ActivateBiometricApiError) {
      handleEvoApiError(state.error);
      return;
    }

    if (state is ActivateBiometricUnknownError) {
      showSnackBarError(EvoStrings.unknownError);
      return;
    }
  }

  void _onComplete(ActivateBiometricSuccess state) {
    final (BaseEntity entity, bool skip) = (state.entity, state.skip);

    if (skip) {
      navigatorContext?.pop();
      widget.onSuccess(ChallengeSuccessModel(entity: entity));
      return;
    }

    ActivateBiometricSuccessScreen.pushReplacementNamed(onProceed: () {
      widget.onSuccess(ChallengeSuccessModel(entity: entity));
    });
  }
}
