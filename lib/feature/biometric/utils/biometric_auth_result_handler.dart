import 'package:local_auth/error_codes.dart' as auth_error;

import '../../../util/evo_flutter_wrapper.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import 'bio_auth_result.dart';

class BiometricAuthResultHandler {
  final EvoLocalStorageHelper secureStorageHelper;
  final EvoFlutterWrapper evoFlutterWrapper;

  BiometricAuthResultHandler({
    required this.secureStorageHelper,
    required this.evoFlutterWrapper,
  });

  Future<BioAuthResult> getBioAuthResult({
    required bool authenticated,
    required String? errCode,
    required bool previouslyHasEnrolled,
    required bool currentlyHasEnrolled,
  }) async {
    if (evoFlutterWrapper.isIOS()) {
      return _getIOSBioAuthResult(
        authenticated: authenticated,
        errCode: errCode,
        previouslyHasEnrolled: previouslyHasEnrolled,
        currentlyHasEnrolled: currentlyHasEnrolled,
      );
    }

    if (evoFlutterWrapper.isAndroid()) {
      return _getAndroidBioAuthResult(
        authenticated: authenticated,
        errCode: errCode,
        currentlyHasEnrolled: currentlyHasEnrolled,
      );
    }

    return BioAuthResult.error(BioAuthError.unknown);
  }

  Future<BioAuthResult> _getIOSBioAuthResult({
    required bool authenticated,
    required String? errCode,
    required bool previouslyHasEnrolled,
    required bool currentlyHasEnrolled,
  }) async {
    /// User haven't enrolled any biometrics on the device
    if (errCode == auth_error.notEnrolled) {
      return BioAuthResult.error(BioAuthError.notEnrolled);
    }

    if (authenticated) {
      await setDidAllowedSystemPermissionIfNeeded();
      return BioAuthResult.success();
    }

    /// Biometric is locked due to previous exceeding attempts
    if (errCode == null) {
      return BioAuthResult.error(BioAuthError.permanentlyLockedOut);
    }

    /// App's biometric permission switched off
    if (!currentlyHasEnrolled) {
      /// Flag to check if user allowed system permission for the app, for the first time
      final bool didAllowPermission = await secureStorageHelper.didAllowBiometricPermission();

      if (!didAllowPermission) {
        return BioAuthResult.error(BioAuthError.iosUserDenied);
      }

      /// If previously has enrolled and after authenticate app's biometric permission switched off
      /// so that mean it's have been locked by system
      if (previouslyHasEnrolled) {
        return BioAuthResult.error(BioAuthError.permanentlyLockedOut);
      }

      return BioAuthResult.error(BioAuthError.notEnrolled);
    }

    /// User allowed system permission for the app,
    await setDidAllowedSystemPermissionIfNeeded();

    if (errCode == auth_error.notAvailable) {
      return BioAuthResult.error(BioAuthError.userDismiss);
    }

    return BioAuthResult.error(BioAuthError.unknown);
  }

  /// return [BioAuthError.userDismiss]. If user click Cancel on button System Challenge Biometric Dialog.
  /// return [BioAuthError.permanentlyLockedOut] | [BioAuthError.androidLockedOut]. If biometric is locked by system
  /// return [BioAuthError.notEnrolled]. If biometric is locked by system or has not enrolled any biometrics on the device.
  Future<BioAuthResult> _getAndroidBioAuthResult({
    required bool authenticated,
    required String? errCode,
    required bool currentlyHasEnrolled,
  }) async {
    if (authenticated) {
      return BioAuthResult.success();
    }

    if (!currentlyHasEnrolled || errCode == auth_error.notEnrolled) {
      return BioAuthResult.error(BioAuthError.notEnrolled);
    }

    if (errCode == null) {
      return BioAuthResult.error(BioAuthError.userDismiss);
    } else if (errCode == auth_error.permanentlyLockedOut) {
      return BioAuthResult.error(BioAuthError.permanentlyLockedOut);
    } else if (errCode == auth_error.lockedOut) {
      return BioAuthResult.error(BioAuthError.androidLockedOut);
    } else {
      return BioAuthResult.error(BioAuthError.unknown);
    }
  }

  Future<void> setDidAllowedSystemPermissionIfNeeded() async {
    final bool didAllowedSystemPermission = await secureStorageHelper.didAllowBiometricPermission();
    if (!didAllowedSystemPermission) {
      await secureStorageHelper.setDidAllowBiometricPermission();
    }
  }
}
