import 'package:flutter_common_package/util/utils.dart';

import '../../../base/modules/core/app_state.dart';
import '../../../prepare_for_app_initiation.dart';
import '../biometric_token/biometric_change_mixin.dart';
import '../biometric_token/biometric_token_usability_mixin.dart';
import '../model/biometric_status_change_notifier.dart';
import 'biometric_status_helper.dart';

class BiometricStatusHelperImpl extends BiometricStatusHelper
    with BiometricTokenUsabilityMixin, BiometricChangeMixin {
  @override
  Future<void> updateBiometricStatus() async {
    if (!await biometricsTokenModule.isEnableBiometricAuthenticator()) {
      _updateBiometricStatus(BiometricStatus.notSetup);
      return;
    }
    if (await checkBiometricChange()) {
      _updateBiometricStatus(BiometricStatus.deviceSettingChanged);
      disableBiometric();
      return;
    }

    if (await checkBiometricTokenUnUsable()) {
      _updateBiometricStatus(BiometricStatus.biometricTokenUnusable);
      disableBiometric();
      return;
    }

    _updateBiometricStatus(BiometricStatus.usable);
  }

  void _updateBiometricStatus(BiometricStatus status) {
    final AppState appState = getIt.get<AppState>();
    commonLog(
        'Biometric status changed from : ${appState.biometricStatusChangeNotifier.value} to $status');

    appState.biometricStatusChangeNotifier.update(status);
  }
}
