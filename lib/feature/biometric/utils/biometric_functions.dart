import 'package:app_settings/app_settings.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';

import '../../../model/evo_dialog_id.dart';
import '../../../prepare_for_app_initiation.dart';
import '../../../resources/resources.dart';
import '../../../util/app_settings_wrapper.dart';
import '../../../util/dialog_functions.dart';
import '../../../util/evo_snackbar.dart';
import '../model/biometric_ui_model.dart';
import 'bio_auth_result.dart';
import 'biometrics_authenticate.dart';

BiometricFunctions get biometricFunctions => getIt.get<BiometricFunctions>();

class BiometricFunctions {
  BiometricTypeUIModel getBiometricUIModel(TsBiometricType biometricType) {
    switch (biometricType) {
      case TsBiometricType.face:
        return BiometricTypeUIModel.face();
      case TsBiometricType.finger:
        return BiometricTypeUIModel.finger();
      case TsBiometricType.androidBio:
      case TsBiometricType.unknown:
      default:
        return BiometricTypeUIModel.faceAndFinger();
    }
  }

  Future<void> handleBioError({
    required BioAuthError? bioError,
    VoidCallback? onActionWhenDismiss,
  }) async {
    final BioAuthError error = bioError ?? BioAuthError.notEnrolled;
    switch (error) {
      case BioAuthError.userDismiss:
        onActionWhenDismiss?.call();
        return;
      case BioAuthError.notEnrolled:
      case BioAuthError.permanentlyLockedOut:
      case BioAuthError.androidLockedOut:
      case BioAuthError.iosUserDenied:
        await showSecuritySettingDialog(
          error: error,
        );
        return;
      case BioAuthError.unknown:
        getIt.get<EvoSnackBar>().show(
              EvoStrings.unknownError,
              typeSnackBar: SnackBarType.error,
              durationInMilliSec: SnackBarDuration.short.value,
            );
        return;
    }
  }

  //for Android, open Security & Biometrics settings in System Setting App
  //for iOS, open setting of our app in System Setting App
  @visibleForTesting
  Future<void> showSecuritySettingDialog({
    required BioAuthError error,
  }) async {
    final (String title, String description) = switch (error) {
      BioAuthError.iosUserDenied => (
          EvoStrings.biometricNeededTitle,
          EvoStrings.biometricNeededDesc,
        ),
      BioAuthError.permanentlyLockedOut || BioAuthError.androidLockedOut => (
          EvoStrings.biometricLockedTitle,
          EvoStrings.openDeviceSecuritySettingToUnlockDesc,
        ),
      _ => (
          EvoStrings.biometricRequiredTitle,
          EvoStrings.openDeviceSecuritySettingDesc,
        ),
    };

    await evoDialogFunction.showDialogConfirm(
        dialogId: EvoDialogId.openDeviceSecuritySettingDialog,
        title: title,
        content: description,
        textPositive: EvoStrings.settingTitle,
        textNegative: EvoStrings.ignoreTitle,
        onClickPositive: () {
          /// dismiss popup
          navigatorContext?.pop();
          appSettingsWrapper.openAppSettings(type: AppSettingsType.security);
        },
        onClickNegative: () {
          /// dismiss popup
          navigatorContext?.pop();
        });
  }
}
