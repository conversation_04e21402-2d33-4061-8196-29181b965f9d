// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_common_package/data/response/base_entity.dart';

import '../feature/main_screen/welcome_onboard_bottom_sheet.dart';

class ChallengeSuccessModel {
  static const String usernameKey = 'username';

  final BaseEntity? entity;

  /// Resend OTP feature in challenges `verify_otp` and `verify_email_otp` needs specific data
  /// but BE does not return it directly in the response.
  ///
  /// In the challenge `verify_email_otp`, resend OTP feature needs user email and session token
  /// from its previous challenge `verify_email`, the current challenge's token doesn't work.
  /// To have the needed data, after a successful `verify_email` challenge, mobile side can set
  /// available data to this model before passing it to the next challenge `verify_email_otp`.
  ///
  /// In the challenge `verify_otp`, the resend OTP feature needs phone number only, no session token.
  /// To have the needed data, after the first `none` challenge, mobile side can set available data
  /// to this model before passing it to the next challenge `verify_otp`.
  final ResendDataModel? resendData;

  /// The account activation flow ends with the `none` challenge. After that, the user is redirected
  /// to [MainScreen]. [isCardActivated] is used to determine whether to show [WelcomeOnboardBottomSheet].
  final bool? isCardActivated;

  /// Additional data that might be needed for specific challenges
  final Map<String, dynamic>? additionalData;

  ChallengeSuccessModel({
    this.entity,
    this.resendData,
    this.isCardActivated,
    this.additionalData,
  });
}

class ResendDataModel {
  final String? contactInfo;
  final String? sessionToken;

  ResendDataModel({
    this.contactInfo,
    this.sessionToken,
  });
}

typedef ChallengeSuccessCallback = void Function(ChallengeSuccessModel);
