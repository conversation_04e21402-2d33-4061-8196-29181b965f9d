enum EvoDialogId {
  /// Bottom sheet
  newAppVersionBottomSheet('new_app_version_bottom_sheet'),
  unAuthorizationSessionBottomSheet('un_authorization_session_bottom_sheet'),
  requestEnableActiveBiometricBottomSheet('request_enable_active_biometric_bottom_sheet'),
  biometricChangedWarningBottomSheet('biometric_changed_warning_bottom_sheet'),

  /// Dialog
  common('common_dialog'),
  inputPhoneNumberLimitOTPErrorDialog('input_phone_number_limit_otp_error_dialog'),
  otpBlockedErrorDialog('otp_blocked_error_dialog'),
  signInSessionTokenExpiredErrorDialog('sign_in_session_token_expired_error_dialog'),
  resetPinSessionTokenExpiredErrorDialog('reset_pin_session_token_expired_error_dialog'),
  activateAccountSessionTokenExpiredErrorDialog(
      'activate_account_session_token_expired_error_dialog'),
  blockInsecureDeviceDialog('block_insecure_device_dialog'),
  openDeviceSecuritySettingDialog('open_device_security_setting_dialog'),
  loginLimitedExceededDialog('login_limited_exceeded_dialog'),
  defaultErrorDialog('default_error_dialog'),
  loginOnNewDeviceDialog('login_on_new_device_dialog'),
  confirmLogOutDialog('confirm_logout_dialog'),
  confirmSwitchAccountDialog('confirm_switch_account_dialog'),
  warningIdleAWhileDialog('warning_idle_a_while_dialog'),
  warningInActiveDialog('warning_inactive_dialog'),
  changeMPINLockedResourceDialog('change_mpin_locked_resource_dialog'),
  forceLogoutDialog('force_logout_dialog'),
  activeAccountErrorApplicationNotFoundDialog('active_account_error_application_not_found_dialog'),
  activeAccountErrorApplicationRejectedDialog('active_account_error_application_rejected_dialog'),
  activeAccountErrorApplicationPendingDialog('active_account_error_application_pending_dialog'),
  activateAccountErrorLimitExceededDialog('activate_account_error_limit_exceeded_dialog'),
  signInErrorLimitExceededDialog('sign_in_error_limit_exceeded_dialog'),
  resetPinErrorLimitExceededDialog('reset_pin_error_limit_exceeded_dialog'),
  welcomeNewUserDialog('welcome_new_user_dialog'),
  proceedLoginOnNewDeviceDialog('proceed_login_on_new_device_dialog');

  const EvoDialogId(this.id);

  final String id;
}
