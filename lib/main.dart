// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/global_key_provider.dart';

// ignore: depend_on_referenced_packages
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';

import 'base/modules/core/app_state.dart';
import 'feature/inactive_detector/inactive_detector_widget.dart';
import 'feature/server_logging/install_source_checker_module.dart';
import 'prepare_for_app_initiation.dart';
import 'resources/dimensions.dart';
import 'resources/evo_theme.dart';
import 'util/navigator/evo_router.dart';
import 'util/screen_util.dart';
import 'widget/hud_loading/hud_loading.dart';

enum StatusApp {
  tutorial,

  /// The user had logged in but:
  /// - Logged out manually
  /// - Logged out due to token expiration
  hadLoggedIn,

  /// The user is not logged in:
  /// - The user is a new user
  /// - The user switched accounts
  nonUser,

  /// Evo app prevents the Rooted/JailBroken device from using it
  insecureDevice,
}

class MyApp extends StatelessWidget with InstallSourceCheckerModule {
  const MyApp({super.key});

  ///if use [MaterialApp]
  ///MUST add [evoNavigatorKey] to navigatorKey
  ///Ex:
  /// [MaterialApp] ( navigatorKey: evoNavigatorKey )
  /// Because this key use for get current context in function "Enable activate_biometric when user logged in"
  @override
  Widget build(BuildContext context) {
    logInstallationSource();
    FlutterNativeSplash.remove();
    ScreenUtil().init(
      context: context,
      design: const Size(
        EvoDimension.figmaScreenWidth,
        EvoDimension.figmaScreenHeight,
      ),
    );
    return KeyboardDismissOnTap(
      child: InactiveDetectorWidget(
        controller: getIt.get<AppState>().inactiveDetectorController,
        child: getMaterialApp(context),
      ),
    );
  }

  TransitionBuilder getAppBuilder() {
    return HudLoading.instance.init();
  }

  MaterialApp getMaterialApp(BuildContext context) => MaterialApp.router(
        title: 'EVO',
        scaffoldMessengerKey: globalKeyProvider.scaffoldMessengerKey,
        theme: EvoTheme.themeData,
        localizationsDelegates: const <LocalizationsDelegate<dynamic>>[
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate
        ],
        routerConfig: evoRouter,
        builder: getAppBuilder(),
      );
}
