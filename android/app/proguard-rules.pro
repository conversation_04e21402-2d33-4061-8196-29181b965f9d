# Proguard configuration for Scan NFC with release mode
-keep class  net.sf.scuba.smartcards.** { *; }
-keep class org.jmrtd.** { *; }
-keep class net.sf.scuba.** {*;}
-keep class org.bouncycastle.** {*;}
-keep class org.ejbca.** {*;}

# previously missing below rules only show warning, after gradle 8 they become become error, so we need to add those rules
-dontwarn kotlinx.android.parcel.Parcelize
-dontwarn kotlinx.parcelize.Parcelize