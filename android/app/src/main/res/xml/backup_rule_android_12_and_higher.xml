<?xml version="1.0" encoding="utf-8"?>
<!--Document extraction:
        https://developer.android.com/guide/topics/data/autobackup#include-exclude-android-12-->
<data-extraction-rules>
    <cloud-backup disableIfNoEncryptionCapabilities="true">
        <!--Syntax for include and exclude elements
            Docs: https://developer.android.com/guide/topics/data/autobackup#xml-include-exclude-->
        <exclude
            domain="sharedpref"
            path="FlutterSharedPreferences.xml" />
    </cloud-backup>
    <device-transfer>
        <!--FlutterSharedPreferences.xml : file name save data sharePreferences -->
        <!--Full path : data/data/{application package}/share_pref/FlutterSharedPreferences.xml -->
        <exclude
            domain="sharedpref"
            path="FlutterSharedPreferences.xml" />
    </device-transfer>
</data-extraction-rules>