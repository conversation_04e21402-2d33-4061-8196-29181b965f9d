plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    // START: FlutterFire Configuration
    id "com.google.firebase.crashlytics"
    // END: FlutterFire Configuration
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}


def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    //starting with Gradle 8.x, the namespace property in the build.gradle file replaces the need for the package attribute in the AndroidManifest.xml.
    //https://developer.android.com/build/releases/past-releases/agp-8-0-0-release-notes#namespace-dsl
    namespace "com.trustingsocial.kyko"
    ndkVersion flutter.ndkVersion
    compileSdkVersion 34

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.trustingsocial.kyko"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-build-configuration.
        minSdkVersion 21
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        // Enabling multidex support.
        multiDexEnabled true

        //  Supported ABIs
        ndk {
            abiFilters "arm64-v8a", "armeabi-v7a", "x86_64"
        }
    }

    signingConfigs {
        debug {
            // use the value provided by Github Action Secret if available (System Env)
            // else, using the default debug keystore of Android
            def signingKeyAlias = System.getenv("SIGNING_KEY_ALIAS")
            if (signingKeyAlias) {
                keyAlias signingKeyAlias
                keyPassword System.getenv("SIGNING_STORE_PASSWORD")
                storeFile file("keystore/evo_keystore.jks")
                storePassword System.getenv("SIGNING_KEY_PASSWORD")
            }
        }

        release {
            // use local if available, else use the value provided by Github Action Secret
            keyAlias keystoreProperties['keyAlias'] ? keystoreProperties['keyAlias'] : System.getenv("SIGNING_KEY_ALIAS")
            keyPassword keystoreProperties['keyPassword'] ? keystoreProperties['keyPassword'] : System.getenv("SIGNING_STORE_PASSWORD")
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : file("keystore/evo_keystore.jks")
            storePassword keystoreProperties['storePassword'] ? keystoreProperties['storePassword'] : System.getenv("SIGNING_KEY_PASSWORD")
        }
    }

    flavorDimensions "env"
    productFlavors {
        stag {
            dimension "env"
            applicationIdSuffix ".stag"
        }

        uat {
            dimension "env"
            applicationIdSuffix ".uat"
        }

        prod {
            dimension "env"
        }
    }

    buildTypes {
        release {
            productFlavors.prod.signingConfig signingConfigs.release
            productFlavors.uat.signingConfig signingConfigs.release
            productFlavors.stag.signingConfig signingConfigs.release

            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'),
                    'proguard-rules.pro'
        }

        debug {
            applicationIdSuffix ".debug"
        }
    }

}

flutter {
    source '../..'
}

dependencies {
    /// adding dependencies here
    /// example:
    ///  - implementation 'com.google.firebase:firebase-analytics:20.0.0'
}
