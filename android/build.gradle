def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
def pubCacheLocalPath = localProperties.getProperty('pubCacheLocalPath')
if (flutterRoot == null && pubCacheLocalPath == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "${pubCacheLocalPath ?: flutterRoot}/.pub-cache/git/tv_flutter_sdk_evo-5851d0b26cd8ff1eaba8aa9abe738c2559292012/android/repo" }
    }
}

rootProject.buildDir = '../build'

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}

subprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            project.android {
                if (namespace == null) {
                    //starting with Gradle 8.x, the namespace property in the build.gradle file replaces the need for the package attribute in the AndroidManifest.xml.
                    //https://developer.android.com/build/releases/past-releases/agp-8-0-0-release-notes#namespace-dsl
                    //We do this to add a namespace to a plugin that has not yet migrated to support it.
                    namespace project.group
                }
            }
        }
    }
}

subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
