import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:{{projectName}}/{{projectName}}_method_channel.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  MethodChannel{{pluginDartClass}} platform = MethodChannel{{pluginDartClass}}();
  const MethodChannel channel = MethodChannel('{{projectName}}');

  setUp(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
      channel,
      (MethodCall methodCall) async {
        return '42';
      },
    );
  });

  tearDown(() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(channel, null);
  });

  test('getPlatformVersion', () async {
    expect(await platform.getPlatformVersion(), '42');
  });
}
