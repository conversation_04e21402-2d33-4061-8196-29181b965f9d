#For fix issue: https://github.com/flutter/flutter/issues/27997
file=./test/coverage_helper_test.dart
echo "// Helper file to make coverage work for all dart files\n" > $file
echo "// ignore_for_file: unused_import" >> $file
# Get all file .dart and don't contain "part of".
find ./lib -name '*.dart' -print | xargs grep -iL "part of" | cut -c6- | awk -v package=evoapp '{printf "import '\''package:%s%s'\'';\n", package, $1}' >> $file
echo "\nvoid main(){}" >> $file