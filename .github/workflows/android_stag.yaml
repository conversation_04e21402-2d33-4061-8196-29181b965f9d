name: Build Android STAGING
on:
  push:
    branches: [ 'master' ]

concurrency:
  group: android-stag

jobs:
  android-build-stag:
    uses: tsocial/flutter-common-package/.github/workflows/android_build_and_push_firebase.yml@release/v4.x.x
    with:
      VAULT_KEY: ph-stag-evo-mobile-distribution-firebase
      TESTER_GROUPS: "business-team, development-team"
      OUTPUT_TYPE: apk
      ENVIRONMENT: STAGING
      BUILD_ENV: stag
      BUILD_TYPE: Release
      FIREBASE_APP_ID: "1:250588238171:android:da8d2508815b44a1ae8a7e"
      BUILD_NUMBER: ${{ vars.BUILD_NUMBER_ANDROID_STAGING }}
      BUILD_NUMBER_VAR_NAME: "BUILD_NUMBER_ANDROID_STAGING"
      REPOSITORY: ${{ github.event.repository.name }}
      # Be aware that the flutter_version in check.yml needs to be updated to the same version.
      FLUTTER_VERSION: "3.24.5"
    secrets: inherit