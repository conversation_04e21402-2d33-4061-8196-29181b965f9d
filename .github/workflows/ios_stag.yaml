name: Build iOS STAGING
on:
  push:
    branches: [ 'master' ]

permissions: read-all
concurrency:
  group: ${{ github.workflow }}

jobs:
  ios-build:
    uses: tsocial/flutter-common-package/.github/workflows/ios_build.yml@release/v4.x.x
    with:
      APPSTORE_APP_ID: "6739208658"
      FLAVOR: stag
      ENVIRONMENT: STAGING
      FLUTTER_VERSION: "3.24.5"
      RUNNER: "self-hosted-macos-15-1"
      XCODE_VERSION: "16.2"
    secrets:
      PROVISIONING_APP_BASE64_CONTENT: ${{ secrets.STAG_PROVISIONING_APP_BASE64_CONTENT }}
      PROVISIONING_ONESIGNAL_BASE64_CONTENT: ${{ secrets.STAG_PROVISIONING_ONESIGNAL_BASE64_CONTENT }}
      # Re-declare
      ORG_CI_TS_TOKEN: ${{ secrets.ORG_CI_TS_TOKEN }}
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      P12_BASE64_CONTENT: ${{ secrets.P12_BASE64_CONTENT }}
      KEY_CHAIN_PASS: ${{ secrets.KEY_CHAIN_PASS }}
      CER_PASS: ${{ secrets.CER_PASS }}
      P8_CONTENT: ${{ secrets.P8_CONTENT }}
      P8_KEY_ID: ${{ secrets.P8_KEY_ID }}
      P8_ISSUER_ID: ${{ secrets.P8_ISSUER_ID }}