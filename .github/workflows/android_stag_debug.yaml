name: Build Android STAGING DEBUG
on:
  push:
    branches: [ 'master' ]

concurrency:
  group: android-stag

jobs:
  android-build-stag-debug:
    uses: tsocial/flutter-common-package/.github/workflows/android_build_and_push_firebase.yml@release/v4.x.x
    with:
      VAULT_KEY: ph-stag-evo-mobile-distribution-firebase
      TESTER_GROUPS: "development-team"
      OUTPUT_TYPE: apk
      ENVIRONMENT: STAGING
      BUILD_ENV: stag
      BUILD_TYPE: Debug
      FIREBASE_APP_ID: "1:250588238171:android:913ae89c394f54ffae8a7e"
      BUILD_NUMBER: ${{ vars.BUILD_NUMBER_ANDROID_STAGING_DEBUG }}
      BUILD_NUMBER_VAR_NAME: "BUILD_NUMBER_ANDROID_STAGING_DEBUG"
      REPOSITORY: ${{ github.event.repository.name }}
      # Be aware that the flutter_version in check.yml needs to be updated to the same version.
      FLUTTER_VERSION: "3.24.5"
    secrets: inherit
