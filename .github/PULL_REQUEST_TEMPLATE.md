<!-- PR Guidelines: https://trustingsocial1.atlassian.net/wiki/spaces/EE/pages/3773202605/EVO+Mobile+-+Github+Pull+Request+PR+Guideline
Tricks to shorten your content: 
1. You can use the <details> tag to collapse/expand the content. 
  For example: <details close> <summary>Title of this block</summary> Put your content here </details> 
2. Make an image smaller with <img alt="image description" src="your-image-url" height="200"> 
3. Display asset horizontally. E.g,
    ```
    Android | iOS | Foo
    :-: | :-: | :-:
    <video src='src-1' height="180" /> | <video src='src-2' height="180" /> | <video src='src-3' height="180" />
    ```
-->

# Overview 🚀
<!-- Link to the Jira ticket related to this PR -->
## Jira ticket 🎯

<!-- Describe the context -->
## Context 🌐

<!-- Add the related documents such as dive-in or sequence diagram, API spec, etc. -->
## Document 📚

<!-- Tell your future self why have you made these changes -->
<!-- If this is a bug fix, explain technically why this happened -->
# Explanation of Your Solution 💡
<!-- If this task requires changes to the shared packages, please provide the PR link here -->
## Common PR (if applicable) - Breaking changes 🚨? Yes / No

<!-- Solution Details -->
## Solution Details 📝

<!-- Input evidence on how did you verify this change (unit test, manual test, etc.) -->
# Testing evidence 🔍

<!-- Assuming the worst case, what can be broken when deploying this change to production? -->
# Potential risks ⚡️

