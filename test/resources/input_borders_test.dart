import 'package:evoapp/resources/input_borders.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  setUpAll(() {
    getIt.registerLazySingleton<EvoInputBorders>(() => EvoInputBorders());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('EvoInputBorders values', () {
    // Default border
    expect(
        evoInputBorders.defaultBorder,
        UnderlineInputBorder(
          borderSide: BorderSide(
            color: evoColors.textHint,
            width: 1.5,
          ),
        ));

    expect(
        evoInputBorders.defaultFocusedBorder,
        UnderlineInputBorder(
          borderSide: BorderSide(
            color: evoColors.primary,
            width: 1.5,
          ),
        ));

    expect(
        evoInputBorders.defaultErrorBorder,
        UnderlineInputBorder(
          borderSide: BorderSide(
            color: evoColors.error,
            width: 1.5,
          ),
        ));

    // Non border
    expect(evoInputBorders.nonBorder, InputBorder.none);
  });
}
