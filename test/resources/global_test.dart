import 'dart:ui';

import 'package:evoapp/resources/global.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify constants', () {
    //currency code
    expect(defaultCurrencySymbol, '₱');
    expect(defaultCurrencyLocale, 'fil_PH');

    ///User phone number format, just show last 3 char
    expect(maxLengthPhoneNumber, 10);
    expect(defaultPrefixNationalCode, '63');

    /// Linked card size
    expect(linkedCardImagePadding, 20);

    /// default locale
    expect(defaultLocale, const Locale('en'));

    /// default timeline format
    expect(defaultDateTimeFormat, 'MMM dd, yyyy');
  });

  test('verify WebsiteUrl', () {
    expect(WebsiteUrl.evoTermsAndConditionsUrl,
        'https://www.goevo.vn/dieu-khoan-va-dieu-kien-evo-app');
    expect(WebsiteUrl.evoFaqUrl, 'https://www.goevo.vn/cau-hoi-thuong-gap-evo-app');
    expect(WebsiteUrl.evoAboutUsUrl, 'https://www.goevo.vn/ve-chung-toi');

    expect(WebsiteUrl.evoPolicyUrl, 'https://www.goevo.vn/ve-chung-toi');
    expect(WebsiteUrl.evoAboutUsUrl, 'https://www.goevo.vn/ve-chung-toi');

    expect(WebsiteUrl.evoCardActivationTutorialUrl,
        'https://www.goevo.vn/app/huong-dan-kich-hoat-the');
  });

  test('verify ContactInfo', () {
    expect(ContactInfo.contactSupportEmail, '<EMAIL>');
  });

  test('verify AppStartPhase', () {
    expect(AppStartPhase.splashScreenInit.toFPMetricName(), 'splash_screen_init');
    expect(AppStartPhase.prepareForAppInitiation.toFPMetricName(), 'prepare_for_app_initiation');
  });
}
