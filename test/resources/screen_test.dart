import 'package:evoapp/resources/resources.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('test get screen by name', () {
    expect(
      Screen.byValue(Screen.splashScreenName),
      Screen.splashScreen,
    );
    expect(
      Screen.byValue(Screen.introductionScreenName),
      Screen.introductionScreen,
    );
    expect(Screen.byValue(Screen.welcomeScreenName), Screen.welcomeScreen);
    expect(
      Screen.byValue(Screen.mainScreenName),
      Screen.mainScreen,
    );
    expect(
      Screen.byValue(Screen.verifyUsernameScreenName),
      Screen.verifyUsernameScreen,
    );
    expect(
      Screen.byValue(Screen.verifyOtpScreenName),
      Screen.verifyOtpScreen,
    );
    expect(
      Screen.byValue(Screen.homePageName),
      Screen.homePage,
    );
    expect(
      Screen.byValue(Screen.cardsPageName),
      Screen.cardsPage,
    );
    expect(
      Screen.byValue(Screen.payCardPageName),
      Screen.payCardPage,
    );
    expect(
      Screen.byValue(Screen.usagePageName),
      Screen.usagePage,
    );
    expect(
      Screen.byValue(Screen.profileScreenName),
      Screen.profileScreen,
    );
    expect(
      Screen.byValue(Screen.errorScreenName),
      Screen.errorScreen,
    );
    expect(
      Screen.byValue(Screen.previousLogInScreenName),
      Screen.previousLogInScreen,
    );
    expect(
      Screen.byValue(Screen.newDeviceVerifyMPinScreenName),
      Screen.newDeviceVerifyMPinScreen,
    );
    expect(
      Screen.byValue(Screen.activateBiometricScreenName),
      Screen.activateBiometricScreen,
    );
    expect(
      Screen.byValue(Screen.currentPinVerificationScreenName),
      Screen.currentPinVerificationScreen,
    );
    expect(
      Screen.byValue(Screen.createNewPinScreenName),
      Screen.createNewPinScreen,
    );
    expect(
      Screen.byValue(Screen.confirmNewPinScreenName),
      Screen.confirmNewPinScreen,
    );
    expect(
      Screen.byValue(Screen.newPinSuccessScreenName),
      Screen.newPinSuccessScreen,
    );
    expect(
      Screen.byValue(Screen.biometricEnabledSuccessScreenName),
      Screen.biometricEnabledSuccessScreen,
    );

    expect(
      Screen.byValue(Screen.mobileNumberCheckScreenName),
      Screen.mobileNumberCheckScreen,
    );

    expect(
      Screen.byValue(Screen.faceCaptureCheckScreenName),
      Screen.faceCaptureCheckScreen,
    );

    expect(
      Screen.byValue(Screen.createUsernameScreenName),
      Screen.createUsernameScreen,
    );

    expect(
      Screen.byValue(Screen.selfieVerificationScreenName),
      Screen.selfieVerificationScreen,
    );

    expect(
      Screen.byValue(Screen.activateVirtualCardScreenName),
      Screen.activateVirtualCardScreen,
    );

    expect(
      Screen.byValue(Screen.virtualCardActivatedScreenName),
      Screen.virtualCardActivatedScreen,
    );

    expect(
      Screen.byValue(Screen.activateCardSuccessScreenName),
      Screen.activateCardSuccessScreen,
    );

    expect(
      Screen.byValue(Screen.verifyPinPrivilegeActionScreenName),
      Screen.verifyPinPrivilegeActionScreen,
    );

    expect(
      Screen.byValue(Screen.selfieRetryScreenName),
      Screen.selfieRetryScreen,
    );

    expect(
      Screen.byValue(Screen.selfieLockedScreenName),
      Screen.selfieLockedScreen,
    );

    expect(
      Screen.byValue(Screen.selfieSuccessScreenName),
      Screen.selfieSuccessScreen,
    );

    expect(
      Screen.byValue(Screen.verifyEmailScreenName),
      Screen.verifyEmailScreen,
    );

    expect(
      Screen.byValue(Screen.inputEmailScreenName),
      Screen.inputEmailScreen,
    );

    expect(
      Screen.byValue(Screen.last4DigitsCheckScreenName),
      Screen.last4DigitsCheckScreen,
    );

    expect(
      Screen.byValue(Screen.transactionDetailsScreenName),
      Screen.transactionDetailsScreen,
    );

    expect(
      Screen.byValue(Screen.activationStatusScreenName),
      Screen.activationStatusScreen,
    );

    expect(
      Screen.byValue(Screen.commonErrorScreenName),
      Screen.commonErrorScreen,
    );

    expect(
      Screen.byValue(Screen.createPinScreenName),
      Screen.createPinScreen,
    );

    expect(
      Screen.byValue(Screen.loginScreenName),
      Screen.loginScreen,
    );

    expect(
      Screen.byValue(Screen.activateBiometricBlockedScreenName),
      Screen.activateBiometricBlockedScreen,
    );

    expect(
      Screen.byValue(Screen.activateBiometricSuccessScreenName),
      Screen.activateBiometricSuccessScreen,
    );

    expect(
      Screen.byValue(Screen.selectSecurityMethodScreenName),
      Screen.selectSecurityMethodScreen,
    );
  });

  group('test byRouteName', () {
    test('should byRouteName() return correctly', () {
      final String routeName = Screen.splashScreen.routeName;
      expect(
        Screen.byRouteName(routeName),
        Screen.splashScreen,
      );
    });

    test('should byRouteName() return mainScreen if routeName invalid', () {
      const String routeName = 'invalid_route_name';
      expect(
        Screen.byRouteName(routeName),
        Screen.mainScreen,
      );
    });
  });
}
