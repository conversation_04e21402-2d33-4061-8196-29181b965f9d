import 'package:evoapp/resources/button_dimensions.dart';
import 'package:flutter/src/painting/edge_insets.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late EvoButtonDimensions evoButtonDimensions;

  setUpAll(() {
    evoButtonDimensions = EvoButtonDimensions();
  });

  test('test getCornerRadius function', () {
    expect(evoButtonDimensions.getCornerRadius(ButtonSize.small), 8);
    expect(evoButtonDimensions.getCornerRadius(ButtonSize.medium), 8);
    expect(evoButtonDimensions.getCornerRadius(ButtonSize.large), 8);
  });

  test('Deprecated getFontSize return null', () {
    expect(evoButtonDimensions.getFontSize(ButtonSize.small), null);
  });

  group('test getPadding function', () {
    test('test getPadding for small button', () {
      final EdgeInsets padding = evoButtonDimensions.getPadding(ButtonSize.small);
      expect(padding, EdgeInsets.symmetric(horizontal: 12));
    });

    test('test getPadding for medium button', () {
      final EdgeInsets padding = evoButtonDimensions.getPadding(ButtonSize.medium);
      expect(padding, EdgeInsets.symmetric(horizontal: 16));
    });

    test('test getPadding for large button', () {
      final EdgeInsets padding = evoButtonDimensions.getPadding(ButtonSize.large);
      expect(padding, EdgeInsets.symmetric(horizontal: 16));
    });
  });
}
