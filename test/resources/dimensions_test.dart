import 'package:evoapp/resources/dimensions.dart';
import 'package:evoapp/util/screen_util.dart';
import 'package:evoapp/widget/evo_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late ScreenUtil screenUtil;

  setUpAll(() {
    screenUtil = ScreenUtil();
  });

  test('should return the correct value', () {
    expect(EvoDimension.figmaScreenHeight, 800);
    expect(EvoDimension.figmaScreenWidth, 360);
  });

  group('test Card Ratio constant & Spacer', () {
    Future<void> initScreenUtils(WidgetTester tester) async {
      /// IMPORTANTLY, don't remove the MediaQuery widget.
      /// because it simulates the screen size in the test environment
      /// and it is necessary to test the ScreenUtil class.
      const Size designSize = Size(360, 800);
      const Size screenSize = Size(760, 1624);
      return tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(size: screenSize),
          // Provide the desired screen size
          child: Builder(
            builder: (BuildContext context) {
              screenUtil.init(context: context, design: designSize);
              return MaterialApp(
                home: Scaffold(
                  body: Container(),
                ),
              );
            },
          ),
        ),
      );
    }

    testWidgets('test borderRadius', (WidgetTester tester) async {
      await initScreenUtils(tester);

      expect(EvoDimension.borderRadius, 8.w);
    });

    testWidgets('test Card Ratio constant', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.defaultCardRatio, 342.w / 216.h);
    });

    testWidgets('test screenHorizontalPadding', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.screenHorizontalPadding, 16.w);
    });

    testWidgets('test screenHorizontalPaddingWithTextField', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.screenHorizontalPaddingWithTextField,
          EvoDimension.screenHorizontalPadding - EvoTextField.focusedBorderPadding);
    });

    testWidgets('test screenBottomPadding', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.screenBottomPadding, 54.w);
    });

    testWidgets('test space4', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.space4, isA<SizedBox>());
      expect(EvoDimension.space4.width, 4.w);
      expect(EvoDimension.space4.height, 4.w);
    });

    testWidgets('test space8', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.space8, isA<SizedBox>());
      expect(EvoDimension.space8.width, 8.w);
      expect(EvoDimension.space8.height, 8.w);
    });

    testWidgets('test space16', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.space16, isA<SizedBox>());
      expect(EvoDimension.space16.width, 16.w);
      expect(EvoDimension.space16.height, 16.w);
    });

    testWidgets('test space24', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.space24, isA<SizedBox>());
      expect(EvoDimension.space24.width, 24.w);
      expect(EvoDimension.space24.height, 24.w);
    });

    testWidgets('test space32', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.space32, isA<SizedBox>());
      expect(EvoDimension.space32.width, 32.w);
      expect(EvoDimension.space32.height, 32.w);
    });

    testWidgets('test space48', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.space48, isA<SizedBox>());
      expect(EvoDimension.space48.width, 48.w);
      expect(EvoDimension.space48.height, 48.w);
    });

    testWidgets('test space64', (WidgetTester tester) async {
      /// setup
      await initScreenUtils(tester);

      /// assert
      expect(EvoDimension.space64, isA<SizedBox>());
      expect(EvoDimension.space64.width, 64.w);
      expect(EvoDimension.space64.height, 64.w);
    });
  });
}
