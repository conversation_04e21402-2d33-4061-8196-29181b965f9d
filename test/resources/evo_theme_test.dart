import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/evo_theme.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  setUpAll(() {
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
  });

  group('EvoTheme', () {
    test('themeData has correct scaffoldBackgroundColor', () {
      final ThemeData theme = EvoTheme.themeData;
      expect(theme.scaffoldBackgroundColor, evoColors.grayBackground);
    });

    test('themeData has correct fontFamily', () {
      final ThemeData theme = EvoTheme.themeData;
      expect(theme.textTheme.bodyMedium?.fontFamily, EvoTextStyles.defaultFontFamily);
    });

    test('themeData is not null', () {
      final ThemeData theme = EvoTheme.themeData;
      expect(theme, isNotNull);
    });

    test('themeData has correct textSelectionTheme', () {
      final ThemeData data = EvoTheme.themeData;
      expect(
        data.textSelectionTheme,
        TextSelectionThemeData(
          cursorColor: evoColors.info100,
          selectionColor: evoColors.info80,
          selectionHandleColor: evoColors.info100,
        ),
      );
    });

    test('themeData has correct appBarTheme', () {
      final ThemeData data = EvoTheme.themeData;
      expect(data.appBarTheme, AppBarTheme(surfaceTintColor: Colors.white));
    });
  });
}
