import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late EvoButtonDimensions buttonDimensions;

  setUpAll(() {
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
    getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());

    buttonDimensions = getIt.get<CommonButtonDimensions>() as EvoButtonDimensions;
  });

  group('EvoButtonStyles', () {
    test('getMinSize return correct size', () async {
      Size size = evoButtonStyles.getMinSize(ButtonSize.small);
      expect(size, Size(0, 38));

      size = evoButtonStyles.getMinSize(ButtonSize.medium);
      expect(size, Size(0, 44));

      size = evoButtonStyles.getMinSize(ButtonSize.large);
      expect(size, Size(0, 48));
    });

    group('onState()', () {
      test('should always resolve all when all is NOT null', () async {
        final WidgetStateProperty<Color?> stateProperty = evoButtonStyles.onState(
          all: Colors.red,
          pressed: Colors.blue,
          disabled: Colors.white,
        );

        expect(stateProperty.resolve(<WidgetState>{}), Colors.red);
        expect(stateProperty.resolve(<WidgetState>{WidgetState.hovered}), Colors.red);
        expect(stateProperty.resolve(<WidgetState>{WidgetState.pressed}), Colors.red);
        expect(stateProperty.resolve(<WidgetState>{WidgetState.disabled}), Colors.red);
      });

      test('should resolve disabled when disabled arg is NOT null AND WidgetState is disabled',
          () async {
        final WidgetStateProperty<Color?> stateProperty =
            evoButtonStyles.onState(disabled: Colors.red);

        expect(stateProperty.resolve(<WidgetState>{WidgetState.disabled}), Colors.red);
        expect(stateProperty.resolve(<WidgetState>{}), isNot(Colors.red));
        expect(stateProperty.resolve(<WidgetState>{WidgetState.hovered}), isNot(Colors.red));
      });

      test('should resolve pressed when pressed arg is NOT null AND WidgetState is pressed',
          () async {
        final WidgetStateProperty<Color?> stateProperty =
            evoButtonStyles.onState(pressed: Colors.red);

        expect(stateProperty.resolve(<WidgetState>{WidgetState.pressed}), Colors.red);
        expect(stateProperty.resolve(<WidgetState>{}), isNot(Colors.red));
        expect(stateProperty.resolve(<WidgetState>{WidgetState.hovered}), isNot(Colors.red));
      });

      test('should resolve fallback when fallback arg is NOT null AND other args are null',
          () async {
        final WidgetStateProperty<Color?> stateProperty =
            evoButtonStyles.onState(fallback: Colors.red);

        expect(stateProperty.resolve(<WidgetState>{WidgetState.pressed}), Colors.red);
        expect(stateProperty.resolve(<WidgetState>{WidgetState.disabled}), Colors.red);
        expect(stateProperty.resolve(<WidgetState>{WidgetState.hovered}), Colors.red);
      });
    });

    group('primary()', () {
      test('should return correct styles for small button', () {
        final ButtonStyle style = evoButtonStyles.primary(ButtonSize.small);

        // Test overlay color
        expect(style.overlayColor?.resolve(<WidgetState>{}), evoColors.transparent);

        // Test minimum size
        expect(style.minimumSize?.resolve(<WidgetState>{}), Size(0, 38));

        // Test background color states
        expect(style.backgroundColor?.resolve(<WidgetState>{}), evoColors.primaryBase);
        expect(style.backgroundColor?.resolve(<WidgetState>{WidgetState.pressed}),
            evoColors.primaryDark);
        expect(style.backgroundColor?.resolve(<WidgetState>{WidgetState.disabled}),
            evoColors.grayBorders);

        // Test foreground (text) color
        expect(style.foregroundColor?.resolve(<WidgetState>{}), evoColors.white);
        expect(
            style.foregroundColor?.resolve(<WidgetState>{WidgetState.disabled}), evoColors.white);
      });

      test('should apply zero padding when hasPadding is false', () {
        final ButtonStyle style = evoButtonStyles.primary(ButtonSize.medium, hasPadding: false);
        expect(style.padding?.resolve(<WidgetState>{}), EdgeInsets.zero);
      });
    });

    group('secondary()', () {
      test('should return correct styles for small button', () {
        final ButtonStyle style = evoButtonStyles.secondary(ButtonSize.small);

        // Test overlay color
        expect(style.overlayColor?.resolve(<WidgetState>{}), evoColors.transparent);

        // Test minimum size
        expect(style.minimumSize?.resolve(<WidgetState>{}), Size(0, 38));

        // Test border side states
        expect(style.side?.resolve(<WidgetState>{})?.color, evoColors.primaryBase);
        expect(
            style.side?.resolve(<WidgetState>{WidgetState.pressed})?.color, evoColors.primaryDark);
        expect(
            style.side?.resolve(<WidgetState>{WidgetState.disabled})?.color, evoColors.grayBorders);

        // Test foreground (text) color states
        expect(style.foregroundColor?.resolve(<WidgetState>{}), evoColors.primaryBase);
        expect(style.foregroundColor?.resolve(<WidgetState>{WidgetState.pressed}),
            evoColors.primaryDark);
        expect(style.foregroundColor?.resolve(<WidgetState>{WidgetState.disabled}),
            evoColors.grayBorders);

        // Test background color is transparent
        expect(style.backgroundColor?.resolve(<WidgetState>{}), evoColors.transparent);
      });

      test('should apply zero padding when hasPadding is false', () {
        final ButtonStyle style = evoButtonStyles.secondary(ButtonSize.medium, hasPadding: false);
        expect(style.padding?.resolve(<WidgetState>{}), EdgeInsets.zero);
      });
    });

    group('tertiary()', () {
      test('should return correct styles for small button', () {
        final ButtonStyle style = evoButtonStyles.tertiary(ButtonSize.small);

        // Test overlay color
        expect(style.overlayColor?.resolve(<WidgetState>{}), evoColors.transparent);

        // Test minimum size
        expect(style.minimumSize?.resolve(<WidgetState>{}), Size(0, 38));

        // Test foreground (text) color states
        expect(style.foregroundColor?.resolve(<WidgetState>{}), evoColors.primaryBase);
        expect(style.foregroundColor?.resolve(<WidgetState>{WidgetState.pressed}),
            evoColors.primaryDark);
        expect(style.foregroundColor?.resolve(<WidgetState>{WidgetState.disabled}),
            evoColors.grayBorders);

        // Test background color is transparent
        expect(style.backgroundColor?.resolve(<WidgetState>{}), evoColors.transparent);
      });

      test('should apply zero padding when hasPadding is false', () {
        final ButtonStyle style = evoButtonStyles.tertiary(ButtonSize.medium, hasPadding: false);
        expect(style.padding?.resolve(<WidgetState>{}), EdgeInsets.zero);
      });
    });

    test('Deprecated code', () async {
      expect(evoButtonStyles.utility(ButtonSize.small), isA<ButtonStyle>());
      expect(evoButtonStyles.popup(ButtonSize.small), isA<ButtonStyle>());
    });
  });
}
