import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  setUpAll(() {
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
  });

  group('test TextSize', () {
    test('xs has correct properties', () {
      expect(TextSize.xs.fontSize, 11);
      expect(TextSize.xs.lineHeight, 14);
      expect(TextSize.xs.height, 14 / 11);
    });

    test('s has correct properties', () {
      expect(TextSize.s.fontSize, 12);
      expect(TextSize.s.lineHeight, 14);
      expect(TextSize.s.height, 14 / 12);
    });

    test('base has correct properties', () {
      expect(TextSize.base.fontSize, 14);
      expect(TextSize.base.lineHeight, 20);
      expect(TextSize.base.height, 20 / 14);
    });

    test('lg has correct properties', () {
      expect(TextSize.lg.fontSize, 16);
      expect(TextSize.lg.lineHeight, 24);
      expect(TextSize.lg.height, 24 / 16);
    });

    test('h5 has correct properties', () {
      expect(TextSize.h5.fontSize, 18);
      expect(TextSize.h5.lineHeight, 24);
      expect(TextSize.h5.height, 24 / 18);
    });

    test('h4 has correct properties', () {
      expect(TextSize.h4.fontSize, 20);
      expect(TextSize.h4.lineHeight, 24);
      expect(TextSize.h4.height, 24 / 20);
    });

    test('h3 has correct properties', () {
      expect(TextSize.h3.fontSize, 24);
      expect(TextSize.h3.lineHeight, 28);
      expect(TextSize.h3.height, 28 / 24);
    });

    test('h2 has correct properties', () {
      expect(TextSize.h2.fontSize, 28);
      expect(TextSize.h2.lineHeight, 32);
      expect(TextSize.h2.height, 32 / 28);
    });

    test('h1 has correct properties', () {
      expect(TextSize.h1.fontSize, 32);
      expect(TextSize.h1.lineHeight, 40);
      expect(TextSize.h1.height, 40 / 32);
    });

    test('h0 has correct properties', () {
      expect(TextSize.h0.fontSize, 48);
      expect(TextSize.h0.lineHeight, 56);
      expect(TextSize.h0.height, 56 / 48);
    });

    test('deprecated xl3 has correct properties', () {
      expect(TextSize.xl3.fontSize, 31);
      expect(TextSize.xl3.lineHeight, 40);
      expect(TextSize.xl3.height, 40 / 31);
    });

    test('deprecated xl2 has correct properties', () {
      expect(TextSize.xl2.fontSize, 25);
      expect(TextSize.xl2.lineHeight, 32);
      expect(TextSize.xl2.height, 32 / 25);
    });

    test('deprecated xl has correct properties', () {
      expect(TextSize.xl.fontSize, 20);
      expect(TextSize.xl.lineHeight, 26);
      expect(TextSize.xl.height, 26 / 20);
    });

    test('deprecated sm has correct properties', () {
      expect(TextSize.sm.fontSize, 13);
      expect(TextSize.sm.lineHeight, 16);
      expect(TextSize.sm.height, 16 / 13);
    });
  });

  group('test EvoTextStyles', () {
    late EvoTextStyles evoTextStyles;

    setUp(() {
      evoTextStyles = EvoTextStyles();
    });

    test('defaultFontFamily is correct', () {
      expect(EvoTextStyles.defaultFontFamily, 'Gabarito');
    });

    test('regular TextStyle is created as expected', () {
      final TextStyle style = evoTextStyles.regular(TextSize.base);

      expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
      expect(style.color, evoTextStyles.commonColors.textActive);
      expect(style.fontSize, TextSize.base.fontSize);
      expect(style.height, TextSize.base.height);
      expect(style.fontWeight, FontWeight.w400);
    });

    test('medium TextStyle is created as expected', () {
      final TextStyle style = evoTextStyles.medium(TextSize.base);

      expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
      expect(style.color, evoTextStyles.commonColors.textActive);
      expect(style.fontSize, TextSize.base.fontSize);
      expect(style.height, TextSize.base.height);
      expect(style.fontWeight, FontWeight.w500);
    });

    test('semibold TextStyle is created as expected', () {
      final TextStyle style = evoTextStyles.semibold(TextSize.base);

      expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
      expect(style.color, evoTextStyles.commonColors.textActive);
      expect(style.fontSize, TextSize.base.fontSize);
      expect(style.height, TextSize.base.height);
      expect(style.fontWeight, FontWeight.w600);
    });

    test('bold TextStyle is created as expected', () {
      final TextStyle style = evoTextStyles.bold(TextSize.base);

      expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
      expect(style.color, evoTextStyles.commonColors.textActive);
      expect(style.fontSize, TextSize.base.fontSize);
      expect(style.height, TextSize.base.height);
      expect(style.fontWeight, FontWeight.w700);
    });

    test('regular TextStyle with custom color is created as expected', () {
      const Color customColor = Colors.red;
      final TextStyle style = evoTextStyles.regular(TextSize.sm, color: customColor);

      expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
      expect(style.color, customColor);
      expect(style.fontSize, TextSize.sm.fontSize);
      expect(style.height, TextSize.sm.height);
      expect(style.fontWeight, FontWeight.w400);
    });

    test('medium TextStyle with custom color is created as expected', () {
      const Color customColor = Colors.green;
      final TextStyle style = evoTextStyles.medium(TextSize.lg, color: customColor);

      expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
      expect(style.color, customColor);
      expect(style.fontSize, TextSize.lg.fontSize);
      expect(style.height, TextSize.lg.height);
      expect(style.fontWeight, FontWeight.w500);
    });

    test('semibold TextStyle with custom color is created as expected', () {
      const Color customColor = Colors.amber;
      final TextStyle style = evoTextStyles.semibold(TextSize.h3, color: customColor);

      expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
      expect(style.color, customColor);
      expect(style.fontSize, TextSize.h3.fontSize);
      expect(style.height, TextSize.h3.height);
      expect(style.fontWeight, FontWeight.w600);
    });

    test('bold TextStyle with custom color is created as expected', () {
      const Color customColor = Colors.blue;
      final TextStyle style = evoTextStyles.bold(TextSize.h1, color: customColor);

      expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
      expect(style.color, customColor);
      expect(style.fontSize, TextSize.h1.fontSize);
      expect(style.height, TextSize.h1.height);
      expect(style.fontWeight, FontWeight.w700);
    });

    group('EvoTextStyles.button()', () {
      test('should return correct TextStyle for ButtonSize.small', () {
        final TextStyle style = evoTextStyles.button(ButtonSize.small, Colors.white);

        expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
        expect(style.fontSize, TextSize.s.fontSize);
        expect(style.height, TextSize.s.height);
        expect(style.fontWeight, FontWeight.w600);
      });

      test('should return correct TextStyle for ButtonSize.medium', () {
        final TextStyle style = evoTextStyles.button(ButtonSize.medium, Colors.white);

        expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
        expect(style.fontSize, TextSize.base.fontSize);
        expect(style.height, TextSize.base.height);
        expect(style.fontWeight, FontWeight.w600);
      });

      test('should return correct TextStyle for ButtonSize.large', () {
        final TextStyle style = evoTextStyles.button(ButtonSize.large, Colors.white);

        expect(style.fontFamily, EvoTextStyles.defaultFontFamily);
        expect(style.fontSize, TextSize.lg.fontSize);
        expect(style.height, TextSize.lg.height);
        expect(style.fontWeight, FontWeight.w600);
      });
    });
  });
}
