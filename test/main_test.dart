import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/inactive_detector/inactive_detector_widget.dart';
import 'package:evoapp/feature/server_logging/install_source_checker_module.dart';
import 'package:evoapp/feature/splash_screen/splash_screen_cubit.dart';
import 'package:evoapp/main.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/evo_theme.dart';
import 'package:evoapp/util/navigator/evo_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// ignore: depend_on_referenced_packages
import 'package:flutter_localizations/flutter_localizations.dart';

import 'base/evo_page_state_base_test_config.dart';
import 'util/app_mock_cubit.dart';
import 'util/flutter_test_config.dart';

class MockInactiveDetectorController extends Mock implements InactiveDetectorController {}

class MockSplashScreenCubit extends AppMockCubit<SplashScreenState> implements SplashScreenCubit {}

void main() {
  late AppState appState;
  late MockInactiveDetectorController mockInactiveDetectorController;
  late MockSplashScreenCubit mockSplashScreenCubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    initConfigEvoPageStateBase();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
    setupFirebaseForTest();

    appState = getIt.get<AppState>();
    mockInactiveDetectorController = MockInactiveDetectorController();
    appState.inactiveDetectorController = mockInactiveDetectorController;

    mockSplashScreenCubit = MockSplashScreenCubit()..emit(SplashScreenInitialState());
    when(() => mockSplashScreenCubit.initData()).thenAnswer((_) async {});
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('StatusApp enum', () {
    test('should have correct enum values', () {
      expect(StatusApp.values.length, 4);
      expect(StatusApp.tutorial, StatusApp.tutorial);
      expect(StatusApp.hadLoggedIn, StatusApp.hadLoggedIn);
      expect(StatusApp.nonUser, StatusApp.nonUser);
      expect(StatusApp.insecureDevice, StatusApp.insecureDevice);
    });

    test('should have correct enum names', () {
      expect(StatusApp.tutorial.name, 'tutorial');
      expect(StatusApp.hadLoggedIn.name, 'hadLoggedIn');
      expect(StatusApp.nonUser.name, 'nonUser');
      expect(StatusApp.insecureDevice.name, 'insecureDevice');
    });
  });

  group('MyApp widget', () {
    testWidgets('should create MyApp widget', (WidgetTester tester) async {
      const MyApp myApp = MyApp();
      expect(myApp, isA<StatelessWidget>());
      expect(myApp.key, isNull);
    });

    testWidgets('should create MyApp widget with key', (WidgetTester tester) async {
      const Key key = Key('test_key');
      const MyApp myApp = MyApp(key: key);
      expect(myApp.key, key);
    });

    testWidgets('should build widget tree correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        BlocProvider<SplashScreenCubit>.value(
          value: mockSplashScreenCubit,
          child: const MyApp(),
        ),
      );

      // Verify the widget tree structure
      expect(find.byType(KeyboardDismissOnTap), findsOneWidget);
      expect(find.byType(InactiveDetectorWidget), findsOneWidget);
      expect(find.byType(MaterialApp), findsAtLeastNWidgets(1));
    });

    test('getAppBuilder should return HudLoading TransitionBuilder', () {
      const MyApp myApp = MyApp();
      final TransitionBuilder builder = myApp.getAppBuilder();
      expect(builder, isA<TransitionBuilder>());
    });

    test('getMaterialApp should return MaterialApp with correct properties', () {
      const MyApp myApp = MyApp();
      final MockContext context = MockContext();

      final MaterialApp materialApp = myApp.getMaterialApp(context);

      expect(materialApp, isA<MaterialApp>());
      expect(materialApp.title, 'EVO');
      expect(materialApp.theme, EvoTheme.themeData);
      expect(materialApp.routerConfig, evoRouter);

      // Check localization delegates
      expect(materialApp.localizationsDelegates, isNotNull);
      expect(materialApp.localizationsDelegates!.length, 3);
      expect(
        materialApp.localizationsDelegates!.any(
          (LocalizationsDelegate<dynamic> delegate) =>
              delegate == GlobalMaterialLocalizations.delegate,
        ),
        isTrue,
      );
      expect(
        materialApp.localizationsDelegates!.any(
          (LocalizationsDelegate<dynamic> delegate) =>
              delegate == GlobalWidgetsLocalizations.delegate,
        ),
        isTrue,
      );
      expect(
        materialApp.localizationsDelegates!.any(
          (LocalizationsDelegate<dynamic> delegate) =>
              delegate == GlobalCupertinoLocalizations.delegate,
        ),
        isTrue,
      );
    });
  });

  group('MyApp InstallSourceCheckerModule mixin', () {
    test('should implement InstallSourceCheckerModule', () {
      const MyApp myApp = MyApp();
      expect(myApp, isA<InstallSourceCheckerModule>());
    });
  });

  group('MyApp build method behavior', () {
    testWidgets('should pass correct controller to InactiveDetectorWidget',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        BlocProvider<SplashScreenCubit>.value(
          value: mockSplashScreenCubit,
          child: const MyApp(),
        ),
      );

      final InactiveDetectorWidget inactiveDetectorWidget = tester.widget<InactiveDetectorWidget>(
        find.byType(InactiveDetectorWidget),
      );

      expect(inactiveDetectorWidget.controller, mockInactiveDetectorController);
    });
  });
}
