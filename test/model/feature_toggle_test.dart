import 'package:evoapp/feature/feature_toggle.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('constructor works correctly', () {
    test('create FeatureToggle instance', () {
      final FeatureToggle featureToggle = FeatureToggle();

      expect(featureToggle.enableEventTrackingFeature, true);
    });
  });

  group('mapFromRemoteConfigEntity() method works correctly', () {
    test('RemoteConfigFeatureToggleEntity instance with default value', () {
      final FeatureToggle featureToggle = FeatureToggle();

      expect(featureToggle.enableEventTrackingFeature, true);
    });
  });
}
