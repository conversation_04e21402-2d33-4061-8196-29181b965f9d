import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EvoDialogId', () {
    test('should have unique ids', () {
      final Set<dynamic> ids = EvoDialogId.values.map((EvoDialogId e) => e.id).toSet();

      expect(ids.length, equals(EvoDialogId.values.length));
    });

    test('should have correct string ids', () {
      /// Bottom Sheet
      expect(EvoDialogId.newAppVersionBottomSheet.id, 'new_app_version_bottom_sheet');
      expect(EvoDialogId.unAuthorizationSessionBottomSheet.id,
          'un_authorization_session_bottom_sheet');
      expect(EvoDialogId.requestEnableActiveBiometricBottomSheet.id,
          'request_enable_active_biometric_bottom_sheet');
      expect(EvoDialogId.biometricChangedWarningBottomSheet.id,
          'biometric_changed_warning_bottom_sheet');

      /// Dialog
      expect(EvoDialogId.common.id, 'common_dialog');
      expect(EvoDialogId.signInSessionTokenExpiredErrorDialog.id,
          'sign_in_session_token_expired_error_dialog');
      expect(EvoDialogId.resetPinSessionTokenExpiredErrorDialog.id,
          'reset_pin_session_token_expired_error_dialog');
      expect(EvoDialogId.activateAccountSessionTokenExpiredErrorDialog.id,
          'activate_account_session_token_expired_error_dialog');
      expect(EvoDialogId.blockInsecureDeviceDialog.id, 'block_insecure_device_dialog');
      expect(EvoDialogId.openDeviceSecuritySettingDialog.id, 'open_device_security_setting_dialog');
      expect(EvoDialogId.loginLimitedExceededDialog.id, 'login_limited_exceeded_dialog');
      expect(EvoDialogId.defaultErrorDialog.id, 'default_error_dialog');
      expect(EvoDialogId.loginOnNewDeviceDialog.id, 'login_on_new_device_dialog');
      expect(EvoDialogId.confirmLogOutDialog.id, 'confirm_logout_dialog');
      expect(EvoDialogId.confirmSwitchAccountDialog.id, 'confirm_switch_account_dialog');
      expect(EvoDialogId.warningIdleAWhileDialog.id, 'warning_idle_a_while_dialog');
      expect(EvoDialogId.warningInActiveDialog.id, 'warning_inactive_dialog');
      expect(EvoDialogId.changeMPINLockedResourceDialog.id, 'change_mpin_locked_resource_dialog');
      expect(EvoDialogId.forceLogoutDialog.id, 'force_logout_dialog');
      expect(EvoDialogId.activeAccountErrorApplicationNotFoundDialog.id,
          'active_account_error_application_not_found_dialog');
      expect(EvoDialogId.activeAccountErrorApplicationRejectedDialog.id,
          'active_account_error_application_rejected_dialog');
      expect(EvoDialogId.activeAccountErrorApplicationPendingDialog.id,
          'active_account_error_application_pending_dialog');
      expect(EvoDialogId.activateAccountErrorLimitExceededDialog.id,
          'activate_account_error_limit_exceeded_dialog');
      expect(EvoDialogId.signInErrorLimitExceededDialog.id, 'sign_in_error_limit_exceeded_dialog');
      expect(
          EvoDialogId.resetPinErrorLimitExceededDialog.id, 'reset_pin_error_limit_exceeded_dialog');
      expect(EvoDialogId.welcomeNewUserDialog.id, 'welcome_new_user_dialog');
      expect(EvoDialogId.proceedLoginOnNewDeviceDialog.id, 'proceed_login_on_new_device_dialog');
    });
  });
}
