// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/model/challenge_success_model.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ChallengeSuccessModel', () {
    test('verify constants value', () {
      expect(ChallengeSuccessModel.usernameKey, 'username');
    });

    test('should create instance with null values', () {
      final ChallengeSuccessModel model = ChallengeSuccessModel();

      expect(model.entity, isNull);
      expect(model.resendData, isNull);
      expect(model.isCardActivated, isNull);
    });

    test('should create instance with provided values', () {
      final BaseEntity entity = BaseEntity();
      final ResendDataModel resendData = ResendDataModel(
        contactInfo: '<EMAIL>',
        sessionToken: 'token123',
      );
      final bool isCardActivated = true;

      final ChallengeSuccessModel model = ChallengeSuccessModel(
        entity: entity,
        resendData: resendData,
        isCardActivated: isCardActivated,
      );

      expect(model.entity, equals(entity));
      expect(model.resendData, equals(resendData));
      expect(model.isCardActivated, isCardActivated);
    });
  });

  group('ResendDataModel', () {
    test('should create instance with null values', () {
      final ResendDataModel model = ResendDataModel();

      expect(model.contactInfo, isNull);
      expect(model.sessionToken, isNull);
    });

    test('should create instance with provided values', () {
      final ResendDataModel model = ResendDataModel(
        contactInfo: '<EMAIL>',
        sessionToken: 'token123',
      );

      expect(model.contactInfo, equals('<EMAIL>'));
      expect(model.sessionToken, equals('token123'));
    });

    test('should create instance with email contact info', () {
      final ResendDataModel model = ResendDataModel(
        contactInfo: '<EMAIL>',
        sessionToken: 'abc123',
      );

      expect(model.contactInfo, equals('<EMAIL>'));
      expect(model.sessionToken, equals('abc123'));
    });

    test('should create instance with phone number contact info', () {
      final ResendDataModel model = ResendDataModel(
        contactInfo: '+1234567890',
        sessionToken: null,
      );

      expect(model.contactInfo, equals('+1234567890'));
      expect(model.sessionToken, isNull);
    });
  });
}
