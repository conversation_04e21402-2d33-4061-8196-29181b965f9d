import 'package:evoapp/feature/logging/evo_logging_event.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EvoEventType', () {
    test('EvoEventType constants should have correct string values', () {
      expect(EvoEventType.ekyc.name, 'ekyc');
      expect(EvoEventType.firebaseRemoteConfig.name, 'firebase_remote_config');
      expect(EvoEventType.biometrics.name, 'biometrics');
      expect(EvoEventType.downloadFile.name, 'download_file');
      expect(EvoEventType.commonPlatformException.name, 'common_platform_exception');
      expect(EvoEventType.loadPdfFromUrl.name, 'load_pdf_from_url');
      expect(EvoEventType.launchUrl.name, 'launch_url');
      expect(EvoEventType.qrCodeScanner.name, 'qr_code_scanner');
      expect(EvoEventType.sharingFeature.name, 'sharing_feature');
      expect(EvoEventType.cancelableFutureFeature.name, 'cancelable_future_feature');
      expect(EvoEventType.appsflyer.name, 'appsflyer');
      expect(EvoEventType.deepLink.name, 'deep_link');
      expect(EvoEventType.dopRecaptcha.name, 'dop_recaptcha');
      expect(EvoEventType.dopNFC.name, 'dop_nfc');
    });
  });
}
