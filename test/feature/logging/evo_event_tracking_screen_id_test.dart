import 'package:evoapp/feature/logging/evo_event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EvoEventTrackingScreenId', () {
    test('predefined constants should have correct string values', () {
      expect(EventTrackingScreenId.undefined.name, 'null');

      expect(EvoEventTrackingScreenId.special.name, '0000');
      expect(EvoEventTrackingScreenId.special.name.length, 4);

    });

    test('should allow creation of new instances with specific names', () {
      const String customName = 'custom_screen';
      const EvoEventTrackingScreenId customScreenId = EvoEventTrackingScreenId(customName);

      expect(customScreenId.name, customName);
    });
  });
}
