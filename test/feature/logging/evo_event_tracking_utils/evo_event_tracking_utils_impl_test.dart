import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/logging/event_tracking_shared_data.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_screen_id.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_utils/evo_event_tracking_utils_impl.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEventTrackingUtils extends Mock implements EventTrackingUtils {}

class MockEventTrackingSharedData extends Mock implements EventTrackingSharedData {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockAppState extends Mock implements AppState {}

class MockEvoEventTrackingUtils extends Mock implements EvoEventTrackingUtils {}

void main() {
  const String fakeEventId = 'fake_event_id';
  const String fakeKey = 'fake_key';
  const String fakeValue = 'fake_value';
  const String previousUrl = '/previous';
  const String currentUrl = '/current';

  late EventTrackingUtils mockEventTrackingUtils;
  late FeatureToggle mockFeatureToggle;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerSingleton<EventTrackingUtils>(MockEventTrackingUtils());
    getIt.registerSingleton<FeatureToggle>(MockFeatureToggle());
    mockFeatureToggle = getIt.get<FeatureToggle>();

    mockEventTrackingUtils = getIt.get<EventTrackingUtils>();
  });

  setUp(() {
    when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);

    when(() => mockEventTrackingUtils.sendUserActionEvent(
          eventId: any(named: 'eventId'),
          metaData: any(named: 'metaData'),
        )).thenAnswer((_) async {
      return Future<void>.value();
    });
  });

  group('verify sendEvoUserEvent()', () {
    late AppState appState;
    late final EvoEventTrackingUtilsImpl evoEventTrackingUtilsImpl = EvoEventTrackingUtilsImpl();

    setUp(() {
      getIt.registerSingleton<AppState>(AppState());
      appState = getIt<AppState>();
    });

    tearDown(() {
      getIt.unregister<AppState>();
      appState.eventTrackingSharedData.clear();
    });

    test('sendEvoUserEvent should construct correct eventId and call sendEvoActionEvent', () async {
      // Set screenNumberId
      const EvoEventTrackingScreenId fakeScreenId = EvoEventTrackingScreenId.special;
      appState.eventTrackingSharedData.currentScreenId = fakeScreenId;

      const String eventActionId = 'action01';
      final Map<String, dynamic> additionalData = <String, dynamic>{'key': 'value'};
      final String expectedEventId = '01.$fakeScreenId.$eventActionId';

      evoEventTrackingUtilsImpl.sendEvoUserEvent(
        eventActionId: eventActionId,
        metaData: additionalData,
      );

      verify(() => mockEventTrackingUtils.sendUserActionEvent(
            eventId: expectedEventId,
            metaData: additionalData,
          )).called(1);
    });
  });

  group('verify sendEvoSpecialEvent()', () {
    late AppState appState;
    late final EvoEventTrackingUtilsImpl evoEventTrackingUtilsImpl = EvoEventTrackingUtilsImpl();

    setUp(() {
      getIt.registerSingleton<AppState>(AppState());
      appState = getIt<AppState>();
    });

    tearDown(() {
      getIt.unregister<AppState>();
      appState.eventTrackingSharedData.clear();
    });

    test('sendEvoSpecialEvent should construct correct eventId and call sendEvoActionEvent',
        () async {
      const String eventActionId = 'action01';
      final Map<String, dynamic> metadata = <String, dynamic>{'key': 'value'};
      final String specialScreenId = EvoEventTrackingScreenId.special.name;
      final String expectedEventId = '01.$specialScreenId.$eventActionId';

      evoEventTrackingUtilsImpl.sendEvoSpecialEvent(
        eventActionId: eventActionId,
        metaData: metadata,
      );

      verify(() => mockEventTrackingUtils.sendUserActionEvent(
            eventId: expectedEventId,
            metaData: metadata,
          )).called(1);
    });
  });

  group('verify sendEvoActionEvent()', () {
    late AppState appState;
    late final EvoEventTrackingUtilsImpl evoEventTrackingUtilsImpl = EvoEventTrackingUtilsImpl();

    setUp(() {
      getIt.registerSingleton<AppState>(AppState());
      appState = getIt<AppState>();
    });

    tearDown(() {
      getIt.unregister<AppState>();
      appState.eventTrackingSharedData.clear();
    });

    test('sendEvoActionEvent should call sendUserActionEvent with correct parameters', () async {
      final Map<String, dynamic> metadata = <String, dynamic>{
        fakeKey: fakeValue,
      };

      appState.eventTrackingSharedData.previousUrlPath = previousUrl;
      appState.eventTrackingSharedData.urlPath = currentUrl;

      await evoEventTrackingUtilsImpl.sendEvoActionEvent(
        eventId: fakeEventId,
        metaData: metadata,
      );

      expect(
        verify(() => mockEventTrackingUtils.sendUserActionEvent(
              eventId: fakeEventId,
              metaData: captureAny(named: 'metaData'),
            )).captured.first,
        <String, dynamic>{
          fakeKey: fakeValue,
        },
      );
    });

    test('sendEvoActionEvent should call sendUserActionEvent with path screen is null', () async {
      final Map<String, dynamic> metaData = <String, dynamic>{
        fakeKey: fakeValue,
      };

      expect(appState.eventTrackingSharedData.previousUrlPath, null);
      expect(appState.eventTrackingSharedData.urlPath, null);

      await evoEventTrackingUtilsImpl.sendEvoActionEvent(
        eventId: fakeEventId,
        metaData: metaData,
      );

      verify(() => mockEventTrackingUtils.sendUserActionEvent(
            eventId: fakeEventId,
            metaData: metaData,
          )).called(1);
    });

    test('sendEvoActionEvent should not call sendUserActionEvent', () async {
      when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(false);

      await evoEventTrackingUtilsImpl.sendEvoActionEvent(
        eventId: fakeEventId,
      );

      verifyNever(() => mockEventTrackingUtils.sendUserActionEvent(
            eventId: any(named: 'eventId'),
            metaData: any(named: 'metaData'),
          ));
    });
  });

  group('verify prepareMetaData', () {
    late AppState mockAppState;
    late EvoEventTrackingUtilsImpl evoEventTrackingUtilsImpl;

    setUp(() {
      getIt.registerSingleton<AppState>(MockAppState());
      mockAppState = getIt<AppState>();

      evoEventTrackingUtilsImpl = EvoEventTrackingUtilsImpl();
    });

    tearDown(() {
      getIt.unregister<AppState>();
    });

    test('should return correct map with metadata is null', () {
      final Map<String, dynamic>? result = evoEventTrackingUtilsImpl.prepareMetaData();
      expect(result, null);
    });

    test('should return correct map with metadata is not null', () {
      final Map<String, dynamic> metadata = <String, dynamic>{
        fakeKey: fakeValue,
      };

      final Map<String, dynamic>? result =
          evoEventTrackingUtilsImpl.prepareMetaData(metaData: metadata);

      expect(result, metadata);
    });
  });

  test('evoEventTrackingUtils should return registered instance of EvoEventTrackingUtils',
      () async {
    final EvoEventTrackingUtils utils = MockEvoEventTrackingUtils();
    getIt.registerSingleton<EvoEventTrackingUtils>(utils);
    addTearDown(() => getIt.unregister(instance: utils));

    expect(identical(evoEventTrackingUtils, utils), isTrue);
  });
}
