import 'package:evoapp/feature/logging/event_tracking_shared_data.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const EventTrackingScreenId screenId = EventTrackingScreenId.undefined;
  const String previousUrlPath = '/previous_url_path';
  const String currentUrlPath = '/current_url_path';

  void verifyDataIsNull(EventTrackingSharedData sharedData) {
    expect(sharedData.currentScreenId, null);
    expect(sharedData.previousUrlPath, null);
    expect(sharedData.urlPath, null);
  }

  group('EventTrackingSharedData', () {
    test('should allow assignment and retrieval of currentScreenId', () {
      final EventTrackingSharedData sharedData = EventTrackingSharedData();

      verifyDataIsNull(sharedData);

      // Set value
      sharedData.currentScreenId = screenId;
      sharedData.previousUrlPath = previousUrlPath;
      sharedData.urlPath = currentUrlPath;

      expect(sharedData.currentScreenId, screenId);
      expect(sharedData.previousUrlPath, previousUrlPath);
      expect(sharedData.urlPath, currentUrlPath);

      // verify clear()
      sharedData.clear();

      verifyDataIsNull(sharedData);
    });
  });
}
