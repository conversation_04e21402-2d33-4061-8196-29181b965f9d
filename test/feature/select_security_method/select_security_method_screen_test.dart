// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/select_security_method/security_method.dart';
import 'package:evoapp/feature/select_security_method/select_security_method_cubit.dart';
import 'package:evoapp/feature/select_security_method/select_security_method_screen.dart';
import 'package:evoapp/feature/select_security_method/select_security_method_state.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/widget/action_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test_config.dart';
import '../../util/app_mock_cubit.dart';

class MockOnSuccess extends Mock {
  void call(ChallengeSuccessModel model);
}

class MockSelectSecurityMethodCubit extends AppMockCubit<SelectSecurityMethodState>
    implements SelectSecurityMethodCubit {}

void main() {
  late MockOnSuccess mockOnSuccess;
  late SelectSecurityMethodCubit mockCubit;
  const String mockSessionToken = 'session-token';
  final List<SecurityMethod> mockMethods = <SecurityMethod>[
    SecurityMethod.otp,
    SecurityMethod.emailOtp,
  ];

  setUpAll(() {
    initConfigEvoPageStateBase();
    registerFallbackValue(ChallengeSuccessModel());
    registerFallbackValue(SessionDialogType.resetPin);
    registerFallbackValue(SecurityMethod.otp);
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    mockOnSuccess = MockOnSuccess();
    mockCubit = MockSelectSecurityMethodCubit()..emit(SelectSecurityMethodInitial());

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});

    when(() => mockDialogFunction.showDialogSessionTokenExpired(type: any(named: 'type')))
        .thenAnswer((_) async {});

    when(() => mockDialogFunction.showDialogErrorLimitExceeded(
          type: any(named: 'type'),
          content: any(named: 'content'),
        )).thenAnswer((_) async {});

    when(() => mockCubit.select(any())).thenAnswer((_) async {});
  });

  Future<void> buildWidget(
    WidgetTester tester, {
    SelectSecurityMethodFlow flow = SelectSecurityMethodFlow.resetPin,
  }) async {
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider<SelectSecurityMethodCubit>.value(
          value: mockCubit,
          child: SelectSecurityMethodScreen(
            flow: flow,
            methods: mockMethods,
            sessionToken: mockSessionToken,
            onSuccess: mockOnSuccess.call,
          ),
        ),
      ),
    );
  }

  test('should self navigate', () {
    SelectSecurityMethodScreen.pushNamed(
      flow: SelectSecurityMethodFlow.resetPin,
      methods: mockMethods,
      sessionToken: mockSessionToken,
      onSuccess: mockOnSuccess.call,
    );

    final SelectSecurityMethodArg arg = verify(() => mockNavigatorContext.pushNamed(
          Screen.selectSecurityMethodScreen.name,
          extra: captureAny(named: 'extra'),
        )).captured.first as SelectSecurityMethodArg;

    expect(arg.flow, SelectSecurityMethodFlow.resetPin);
    expect(arg.methods, mockMethods);
    expect(arg.sessionToken, mockSessionToken);
    expect(arg.onSuccess, mockOnSuccess.call);
  });

  testWidgets('should render title, description and security method options initially',
      (WidgetTester tester) async {
    await buildWidget(tester);

    expect(find.text(EvoStrings.selectSecurityMethodTitle), findsOneWidget);
    expect(find.text(EvoStrings.selectSecurityMethodDesc), findsOneWidget);
    expect(find.text(EvoStrings.selectSecurityMethodOtp), findsOneWidget);
    expect(find.text(EvoStrings.selectSecurityMethodEmailOtp), findsOneWidget);
    expect(find.byType(ActionButtonWidget), findsNWidgets(2));
  });

  testWidgets('should render only available security methods', (WidgetTester tester) async {
    final List<SecurityMethod> singleMethod = <SecurityMethod>[SecurityMethod.otp];

    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider<SelectSecurityMethodCubit>.value(
          value: mockCubit,
          child: SelectSecurityMethodScreen(
            flow: SelectSecurityMethodFlow.resetPin,
            methods: singleMethod,
            sessionToken: mockSessionToken,
            onSuccess: mockOnSuccess.call,
          ),
        ),
      ),
    );

    expect(find.byType(ActionButtonWidget), findsOneWidget);
    expect(find.text(EvoStrings.selectSecurityMethodOtp), findsOneWidget);
    expect(find.text(EvoStrings.selectSecurityMethodEmailOtp), findsNothing);
  });

  testWidgets('should show loading HUD on loading state', (WidgetTester tester) async {
    await buildWidget(tester);

    await mockCubit.emit(SelectSecurityMethodLoading());
    await tester.pump();

    verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
  });

  testWidgets('should hide loading HUD on non-loading state', (WidgetTester tester) async {
    await buildWidget(tester);

    // First emit loading to show HUD
    await mockCubit.emit(SelectSecurityMethodLoading());
    await tester.pump();

    // Then emit success to hide HUD
    await mockCubit.emit(SelectSecurityMethodSuccess(entity: BaseEntity()));
    await tester.pump();

    verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
  });

  testWidgets('should call onSuccess callback on success state', (WidgetTester tester) async {
    final BaseEntity entity = BaseEntity();
    await buildWidget(tester);

    await mockCubit.emit(SelectSecurityMethodSuccess(entity: entity));
    await tester.pump();

    final ChallengeSuccessModel capturedModel =
        verify(() => mockOnSuccess.call(captureAny())).captured.single as ChallengeSuccessModel;
    expect(identical(capturedModel.entity, entity), isTrue);
  });

  testWidgets('should show session expired dialog on session expired error for reset pin flow',
      (WidgetTester tester) async {
    await buildWidget(tester, flow: SelectSecurityMethodFlow.resetPin);

    await mockCubit.emit(SelectSecurityMethodFailureSessionExpired(error: ErrorUIModel()));
    await tester.pump();

    verify(() => mockDialogFunction.showDialogSessionTokenExpired(
          type: SessionDialogType.resetPin,
        )).called(1);
  });

  testWidgets('should show limit exceeded dialog on limit exceeded error for reset pin flow',
      (WidgetTester tester) async {
    const String errorMessage = 'Security method selection limit exceeded';
    final ErrorUIModel error = ErrorUIModel(userMessage: errorMessage);

    await buildWidget(tester, flow: SelectSecurityMethodFlow.resetPin);

    await mockCubit.emit(SelectSecurityMethodFailureLimitExceeded(error: error));
    await tester.pump();

    verify(() => mockDialogFunction.showDialogErrorLimitExceeded(
          type: SessionDialogType.resetPin,
          content: errorMessage,
        )).called(1);
  });

  testWidgets('should call cubit select method when OTP option is tapped',
      (WidgetTester tester) async {
    await buildWidget(tester);

    await tester.tap(find.text(EvoStrings.selectSecurityMethodOtp));

    verify(() => mockCubit.select(SecurityMethod.otp)).called(1);
  });

  testWidgets('should call cubit select method when Email OTP option is tapped',
      (WidgetTester tester) async {
    await buildWidget(tester);

    await tester.tap(find.text(EvoStrings.selectSecurityMethodEmailOtp));

    verify(() => mockCubit.select(SecurityMethod.emailOtp)).called(1);
  });
}
