// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/select_security_method/security_method.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SecurityMethod', () {
    group('enum values', () {
      test('should have correct value for otp', () {
        expect(SecurityMethod.otp.value, equals('otp'));
      });

      test('should have correct value for emailOtp', () {
        expect(SecurityMethod.emailOtp.value, equals('email_otp'));
      });
    });

    group('constants', () {
      test('should have correct otpValue constant', () {
        expect(SecurityMethod.otpValue, equals('otp'));
      });

      test('should have correct emailOtpValue constant', () {
        expect(SecurityMethod.emailOtpValue, equals('email_otp'));
      });
    });

    group('fromValue', () {
      test('should return otp when value is otpValue', () {
        final SecurityMethod result = SecurityMethod.fromValue(SecurityMethod.otpValue);
        expect(result, equals(SecurityMethod.otp));
      });

      test('should return emailOtp when value is emailOtpValue', () {
        final SecurityMethod result = SecurityMethod.fromValue(SecurityMethod.emailOtpValue);
        expect(result, equals(SecurityMethod.emailOtp));
      });

      test('should return otp when value is "otp"', () {
        final SecurityMethod result = SecurityMethod.fromValue('otp');
        expect(result, equals(SecurityMethod.otp));
      });

      test('should return emailOtp when value is "email_otp"', () {
        final SecurityMethod result = SecurityMethod.fromValue('email_otp');
        expect(result, equals(SecurityMethod.emailOtp));
      });

      test('should throw UnimplementedError for invalid value', () {
        expect(
          () => SecurityMethod.fromValue('invalid'),
          throwsA(isA<UnimplementedError>()),
        );
      });

      test('should throw UnimplementedError for empty string', () {
        expect(
          () => SecurityMethod.fromValue(''),
          throwsA(isA<UnimplementedError>()),
        );
      });
    });

    group('enum properties', () {
      test('should have two enum values', () {
        expect(SecurityMethod.values.length, equals(2));
      });

      test('should contain otp and emailOtp values', () {
        final List<SecurityMethod> values = SecurityMethod.values;
        expect(values, contains(SecurityMethod.otp));
        expect(values, contains(SecurityMethod.emailOtp));
      });
    });
  });
}
