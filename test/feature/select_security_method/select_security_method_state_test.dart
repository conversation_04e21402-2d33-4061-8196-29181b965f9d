// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/select_security_method/select_security_method_state.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SelectSecurityMethodState', () {
    test('SelectSecurityMethodInitial should extend SelectSecurityMethodState', () {
      final SelectSecurityMethodInitial state = SelectSecurityMethodInitial();

      expect(state, isA<SelectSecurityMethodState>());
    });

    test('SelectSecurityMethodLoading should extend SelectSecurityMethodState', () {
      final SelectSecurityMethodLoading state = SelectSecurityMethodLoading();

      expect(state, isA<SelectSecurityMethodState>());
    });

    test(
        'SelectSecurityMethodSuccess should extend SelectSecurityMethodState and have correct entity property',
        () {
      final BaseEntity mockEntity = BaseEntity();
      final SelectSecurityMethodSuccess state = SelectSecurityMethodSuccess(
        entity: mockEntity,
      );

      expect(state, isA<SelectSecurityMethodState>());
      expect(state.entity, equals(mockEntity));
    });

    test(
        'SelectSecurityMethodFailure should extend SelectSecurityMethodState and have correct error property',
        () {
      final ErrorUIModel mockError = ErrorUIModel(userMessage: 'Test error message');
      final SelectSecurityMethodFailure state = SelectSecurityMethodFailure(
        error: mockError,
      );

      expect(state, isA<SelectSecurityMethodState>());
      expect(state.error, equals(mockError));
    });

    test(
        'SelectSecurityMethodFailureSessionExpired should extend SelectSecurityMethodFailure and have correct error property',
        () {
      final ErrorUIModel mockError = ErrorUIModel(userMessage: 'Session expired error');
      final SelectSecurityMethodFailureSessionExpired state =
          SelectSecurityMethodFailureSessionExpired(error: mockError);

      expect(state, isA<SelectSecurityMethodFailure>());
      expect(state.error, equals(mockError));
    });

    test(
        'SelectSecurityMethodFailureLimitExceeded should extend SelectSecurityMethodFailure and have correct error property',
        () {
      final ErrorUIModel mockError = ErrorUIModel(userMessage: 'Limit exceeded error');
      final SelectSecurityMethodFailureLimitExceeded state =
          SelectSecurityMethodFailureLimitExceeded(error: mockError);

      expect(state, isA<SelectSecurityMethodFailure>());
      expect(state.error, equals(mockError));
    });
  });
}
