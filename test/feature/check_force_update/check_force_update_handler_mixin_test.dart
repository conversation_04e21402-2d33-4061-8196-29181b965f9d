import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/repository/common_repo.dart';
import 'package:evoapp/data/response/force_update_entity.dart';
import 'package:evoapp/feature/check_force_update/check_force_update_handler_mixin.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';
import '../../util/test_util.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockCommonRepo extends Mock implements CommonRepo {}

class MockAppState extends Mock implements AppState {}

class TestCheckForceUpdateHandler with CheckForceUpdateHandlerMixin {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockEvoPlatformWrapper extends Mock implements EvoFlutterWrapper {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockEvoImageProvider extends Mock implements CommonImageProvider {}

class SpyTestCheckForceUpdateHandler extends TestCheckForceUpdateHandler {
  int handleOpenStoreCallCount = 0;
  int handleIgnoreUpdateCallCount = 0;

  @override
  Future<void> handleOpenStore() async {
    handleOpenStoreCallCount += 1;
  }

  @override
  Future<void> handleIgnoreUpdate(String? version) async {
    handleIgnoreUpdateCallCount += 1;
  }
}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late TestCheckForceUpdateHandler checkForceUpdateHandler;
  late EvoLocalStorageHelper localStorageHelper;
  late CommonRepo commonRepo;
  late AppState appState;
  late CommonNavigator commonNavigator;
  late EvoFlutterWrapper evoPlatformWrapper;
  late EvoUtilFunction evoUtilFunction;
  late CommonImageProvider commonImageProvider;
  late CommonUtilFunction commonUtilFunction;
  late BuildContext mockNavigatorContext;

  setUpAll(() {
    localStorageHelper = MockEvoLocalStorageHelper();
    commonRepo = MockCommonRepo();
    appState = MockAppState();
    commonNavigator = MockCommonNavigator();
    evoPlatformWrapper = MockEvoPlatformWrapper();
    evoUtilFunction = MockEvoUtilFunction();
    commonImageProvider = MockEvoImageProvider();
    commonUtilFunction = MockCommonUtilFunction();

    /// Inject the mocked dependencies
    getIt.registerSingleton<EvoLocalStorageHelper>(localStorageHelper);
    getIt.registerSingleton<CommonRepo>(commonRepo);
    getIt.registerSingleton<AppState>(appState);
    getIt.registerSingleton<CommonNavigator>(commonNavigator);
    getIt.registerSingleton<EvoFlutterWrapper>(evoPlatformWrapper);
    getIt.registerSingleton<EvoUtilFunction>(evoUtilFunction);
    getIt.registerSingleton<CommonImageProvider>(commonImageProvider);
    getIt.registerSingleton<CommonUtilFunction>(commonUtilFunction);

    /// Register a fallback value
    registerFallbackValue(MockBuildContext());
    registerFallbackValue(EvoDialogId.newAppVersionBottomSheet);
    registerFallbackValue(CommonLaunchUrlMode.externalApplication);

    checkForceUpdateHandler = TestCheckForceUpdateHandler();

    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);
  });

  void stubShowDialogBottomSheet() {
    when(() => evoUtilFunction.showDialogBottomSheet(
          dialogId: any(named: 'dialogId'),
          header: any(named: 'header'),
          title: any(named: 'title'),
          isDismissible: any(named: 'isDismissible'),
          textNegative: any(named: 'textNegative'),
          textPositive: any(named: 'textPositive'),
          content: any(named: 'content'),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        )).thenAnswer((_) => Future<void>.value());

    when(() => commonImageProvider.asset(any(), width: any(named: 'width'), fit: any(named: 'fit')))
        .thenReturn(const SizedBox());
  }

  group('test AppleStoreUrl & GooglePlayStoreUrl', () {
    test('test AppleStoreUrl', () {
      expect(CheckForceUpdateHandlerMixin.urlStoreIos, 'https://apps.apple.com/app/id/**********');
    });

    test('test GooglePlayStoreUrl', () {
      expect(CheckForceUpdateHandlerMixin.urlStoreAndroid,
          'https://play.google.com/store/apps/details?id=vn.goevo.evo');
    });
  });

  group('test checkForceUpdate() ', () {
    setUp(() {
      stubShowDialogBottomSheet();

      when(() => appState.appVersion).thenReturn('1.0.0');
    });

    tearDown(() {
      reset(commonImageProvider);
      reset(evoUtilFunction);
    });

    test(
        'delete EvoSecureStorageHelperImpl.latestVersionIgnore in localStorage'
        'if forceUpdateEntity.hasNewerVersion == FALSE', () async {
      /// Arrange
      final Map<String, dynamic> responseData =
          await TestUtil.getResponseMock('check_force_update_has_new_version_false.json');

      when(() => commonRepo.getForceUpdate(mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async {
        return ForceUpdateEntity.fromBaseResponse(
            BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
      });

      when(() => localStorageHelper.delete(key: any(named: 'key')))
          .thenAnswer((_) => Future<void>.value());

      /// Act
      await checkForceUpdateHandler.checkForceUpdate();

      /// Assert
      verify(() => localStorageHelper.delete(key: EvoSecureStorageHelperImpl.latestVersionIgnore))
          .called(1);
    });

    testWidgets(
        'show dialog to force update if forceUpdateEntity.hasNewerVersion == TRUE'
        ' && forceUpdateEntity.forceToUpdate == FALSE', (WidgetTester widgetTester) async {
      /// Arrange
      /// mock API return version 1.0.1 & hasForceUpdate = true
      final Map<String, dynamic> responseData =
          await TestUtil.getResponseMock('check_force_update_is_true.json');

      when(() => commonRepo.getForceUpdate(mockConfig: any(named: 'mockConfig')))
          .thenAnswer((_) async {
        return ForceUpdateEntity.fromBaseResponse(
            BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData));
      });

      /// Act
      await checkForceUpdateHandler.checkForceUpdate();

      /// Assert
      verify(() => evoUtilFunction.showDialogBottomSheet(
            dialogId: EvoDialogId.newAppVersionBottomSheet,
            header: any(named: 'header'),
            title: EvoStrings.forceUpdateSubDesc,
            isDismissible: false,
            textPositive: EvoStrings.forceUpdateAgree,
            content: EvoStrings.forceUpdateDescription,
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });
  });

  group('test handleIgnoreUpdate method', () {
    test('should call handleIgnoreUpdate correctly', () async {
      /// Arrange
      when(() => localStorageHelper.setLatestVersionIgnore(any()))
          .thenAnswer((_) => Future<void>.value());

      when(() => commonNavigator.pop(mockNavigatorContext, result: any(named: 'result')))
          .thenAnswer((_) {});

      const String version = '1.0.0';

      /// Act
      await checkForceUpdateHandler.handleIgnoreUpdate(version);

      /// Assert
      verify(() => localStorageHelper.setLatestVersionIgnore(version)).called(1);
      verify(() => mockNavigatorContext.pop()).called(1);
    });
  });

  group('test handleOpenStore method', () {
    test('handleOpenStore should open iOS store', () async {
      /// Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(true);
      when(() => evoPlatformWrapper.isAndroid()).thenReturn(false);
      when(() => commonUtilFunction.commonLaunchUrlString(any(), mode: any(named: 'mode')))
          .thenAnswer((_) => Future<void>.value());

      /// Act
      await checkForceUpdateHandler.handleOpenStore();

      /// Assert
      verify(() => commonUtilFunction.commonLaunchUrlString(
          CheckForceUpdateHandlerMixin.urlStoreIos,
          mode: CommonLaunchUrlMode.externalApplication)).called(1);
      verifyNever(() => commonUtilFunction.commonLaunchUrlString(
          CheckForceUpdateHandlerMixin.urlStoreAndroid,
          mode: any(named: 'mode')));
    });

    test('handleOpenStore should open Android store', () async {
      /// Arrange
      when(() => evoPlatformWrapper.isIOS()).thenReturn(false);
      when(() => evoPlatformWrapper.isAndroid()).thenReturn(true);
      when(() => commonUtilFunction.commonLaunchUrlString(any()))
          .thenAnswer((_) => Future<void>.value());

      /// Act
      await checkForceUpdateHandler.handleOpenStore();

      /// Assert
      verify(() => commonUtilFunction.commonLaunchUrlString(
          CheckForceUpdateHandlerMixin.urlStoreAndroid,
          mode: CommonLaunchUrlMode.externalApplication)).called(1);
      verifyNever(() => commonUtilFunction.commonLaunchUrlString(
          CheckForceUpdateHandlerMixin.urlStoreIos,
          mode: any(named: 'mode')));
    });
  });

  group('test showRequestUpdateDialog method', () {
    setUp(() {
      stubShowDialogBottomSheet();
    });

    testWidgets('should show dialog correctly', (WidgetTester widgetTester) async {
      /// Act
      await checkForceUpdateHandler.showRequestUpdateDialog(isForceUpdate: true);

      /// Assert
      verify(() => commonImageProvider.asset(
            EvoImages.bgAppUpdate,
            fit: BoxFit.fitWidth,
          )).called(1);

      verify(() => evoUtilFunction.showDialogBottomSheet(
            dialogId: EvoDialogId.newAppVersionBottomSheet,
            header: any(named: 'header'),
            title: EvoStrings.forceUpdateSubDesc,
            isDismissible: false,
            textPositive: EvoStrings.forceUpdateAgree,
            content: EvoStrings.forceUpdateDescription,
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    test('onClickNegative should call callback onNegativeClick', () async {
      bool isCalled = false;

      await checkForceUpdateHandler.showRequestUpdateDialog(onNegativeClick: () => isCalled = true);
      final List<dynamic> captured = verify(() => evoUtilFunction.showDialogBottomSheet(
            dialogId: EvoDialogId.newAppVersionBottomSheet,
            header: any(named: 'header'),
            title: EvoStrings.forceUpdateSubDesc,
            isDismissible: true,
            textNegative: EvoStrings.forceUpdateSkip,
            textPositive: EvoStrings.forceUpdateAgree,
            content: EvoStrings.forceUpdateDescription,
            onClickNegative: captureAny(named: 'onClickNegative'),
            onClickPositive: any(named: 'onClickPositive'),
          )).captured;
      final VoidCallback onClickNegative = captured[0];

      onClickNegative();

      expect(isCalled, true);
    });

    test('onClickPositive should call handleOpenStore()', () async {
      final SpyTestCheckForceUpdateHandler handler = SpyTestCheckForceUpdateHandler();

      await handler.showRequestUpdateDialog();
      final List<dynamic> captured = verify(() => evoUtilFunction.showDialogBottomSheet(
            dialogId: EvoDialogId.newAppVersionBottomSheet,
            header: any(named: 'header'),
            title: EvoStrings.forceUpdateSubDesc,
            isDismissible: true,
            textNegative: EvoStrings.forceUpdateSkip,
            textPositive: EvoStrings.forceUpdateAgree,
            content: EvoStrings.forceUpdateDescription,
            onClickNegative: any(named: 'onClickNegative'),
            onClickPositive: captureAny(named: 'onClickPositive'),
          )).captured;
      final VoidCallback onClickPositive = captured[0];

      onClickPositive();

      expect(handler.handleOpenStoreCallCount, 1);
    });
  });

  group('test handleAppUpdate method', () {
    setUp(() {
      stubShowDialogBottomSheet();
    });

    testWidgets('do nothing if currentVersion and ForceUpdateEntity.latestVersion is same',
        (WidgetTester widgetTester) async {
      /// Arrange
      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: true,
        latestVersion: '2.0.0',
      );

      when(() => appState.appVersion).thenReturn('2.0.0');

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      //Assert
      verifyNever(
        () => evoUtilFunction.showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: any(named: 'title'),
          isDismissible: any(named: 'isDismissible'),
          textNegative: any(named: 'textNegative'),
          textPositive: any(named: 'textPositive'),
          content: any(named: 'content'),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      );
    });

    testWidgets('should show dialog if ForceUpdateEntity.isForceUpdate is true',
        (WidgetTester widgetTester) async {
      /// Arrange
      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: true,
        latestVersion: '2.0.0',
      );
      when(() => appState.appVersion).thenReturn('1.0.0');

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      //Assert
      verify(
        () => evoUtilFunction.showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: EvoStrings.forceUpdateSubDesc,
          isDismissible: false,
          textPositive: EvoStrings.forceUpdateAgree,
          content: EvoStrings.forceUpdateDescription,
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      ).called(1);
    });

    testWidgets(
        'should show dialog if ForceUpdateEntity.isForceUpdate is FALSE and ForceUpdateEntity.latestVersion is NOT IGNORED',
        (WidgetTester widgetTester) async {
      /// Arrange
      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: false,
        latestVersion: '2.0.0',
      );

      when(() => appState.appVersion).thenReturn('1.0.0');

      when(() => localStorageHelper.getLatestVersionIgnore()).thenAnswer(
        (_) async {
          return '1.0.1';
        },
      );

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      //Assert
      verify(
        () => evoUtilFunction.showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: EvoStrings.forceUpdateSubDesc,
          textNegative: EvoStrings.forceUpdateSkip,
          textPositive: EvoStrings.forceUpdateAgree,
          content: EvoStrings.forceUpdateDescription,
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      ).called(1);
    });

    testWidgets(
        'do nothing if ForceUpdateEntity.isForceUpdate is FALSE and ForceUpdateEntity.latestVersion is IGNORED',
        (WidgetTester widgetTester) async {
      /// Arrange
      final ForceUpdateEntity forceUpdateEntity = ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: false,
        latestVersion: '2.0.0',
      );

      when(() => appState.appVersion).thenReturn('1.0.0');

      when(() => localStorageHelper.getLatestVersionIgnore()).thenAnswer(
        (_) async {
          return '2.0.0';
        },
      );

      /// Act
      await checkForceUpdateHandler.handleAppUpdate(forceUpdateEntity);

      //Assert
      verifyNever(
        () => evoUtilFunction.showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: any(named: 'title'),
          isDismissible: any(named: 'isDismissible'),
          textNegative: any(named: 'textNegative'),
          textPositive: any(named: 'textPositive'),
          content: any(named: 'content'),
          onClickNegative: any(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      );
    });

    test('onNegativeClick passed into showRequestUpdateDialog() should call handleIgnoreUpdate()',
        () async {
      when(() => appState.appVersion).thenReturn('1.0.0');
      when(() => localStorageHelper.getLatestVersionIgnore()).thenAnswer((_) async => '1.0.1');

      final SpyTestCheckForceUpdateHandler handler = SpyTestCheckForceUpdateHandler();
      await handler.handleAppUpdate(ForceUpdateEntity(
        hasNewerVersion: true,
        forceToUpdate: false,
        latestVersion: '2.0.0',
      ));
      final List<dynamic> captured = verify(
        () => evoUtilFunction.showDialogBottomSheet(
          dialogId: EvoDialogId.newAppVersionBottomSheet,
          header: any(named: 'header'),
          title: EvoStrings.forceUpdateSubDesc,
          textNegative: EvoStrings.forceUpdateSkip,
          textPositive: EvoStrings.forceUpdateAgree,
          content: EvoStrings.forceUpdateDescription,
          onClickNegative: captureAny(named: 'onClickNegative'),
          onClickPositive: any(named: 'onClickPositive'),
        ),
      ).captured;
      final VoidCallback onClickNegative = captured[0];

      onClickNegative();

      expect(handler.handleIgnoreUpdateCallCount, 1);
    });
  });
}
