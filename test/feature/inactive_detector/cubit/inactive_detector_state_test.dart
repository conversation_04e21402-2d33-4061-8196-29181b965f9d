import 'package:evoapp/feature/inactive_detector/cubit/inactive_detector_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  // State tests
  test('InactiveDetectorInitialize state is correctly instantiated', () {
    final InactiveDetectorState state = InactiveDetectorInitialize();
    expect(state, isA<InactiveDetectorInitialize>());
  });

  test('SignOutSuccess state is correctly instantiated', () {
    final SignOutSuccess state = SignOutSuccess();
    expect(state, isA<SignOutSuccess>());
  });
}