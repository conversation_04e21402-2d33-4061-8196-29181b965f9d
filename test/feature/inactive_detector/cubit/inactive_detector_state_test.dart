import 'package:evoapp/feature/inactive_detector/cubit/inactive_detector_state.dart';
import 'package:flutter_common_package/base/bloc_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('InactiveDetectorState', () {
    test('InactiveDetectorInitializeState should be correctly instantiated', () {
      final InactiveDetectorState state = InactiveDetectorInitializeState();
      expect(state, isA<InactiveDetectorInitializeState>());
      expect(state, isA<InactiveDetectorState>());
      expect(state, isA<BlocState>());
    });

    test('InactiveDetectorDetectedState should be correctly instantiated', () {
      final InactiveDetectorState state = InactiveDetectorDetectedState();
      expect(state, isA<InactiveDetectorDetectedState>());
      expect(state, isA<InactiveDetectorState>());
      expect(state, isA<BlocState>());
    });

    test('InactiveDetectorLogoutSuccessState should be correctly instantiated', () {
      final InactiveDetectorState state = InactiveDetectorLogoutSuccessState();
      expect(state, isA<InactiveDetectorLogoutSuccessState>());
      expect(state, isA<InactiveDetectorState>());
      expect(state, isA<BlocState>());
    });

    test('all states should be different instances', () {
      final initState = InactiveDetectorInitializeState();
      final detectedState = InactiveDetectorDetectedState();
      final logoutState = InactiveDetectorLogoutSuccessState();

      expect(initState.runtimeType, isNot(equals(detectedState.runtimeType)));
      expect(initState.runtimeType, isNot(equals(logoutState.runtimeType)));
      expect(detectedState.runtimeType, isNot(equals(logoutState.runtimeType)));
    });

    test('states should have proper inheritance hierarchy', () {
      final states = [
        InactiveDetectorInitializeState(),
        InactiveDetectorDetectedState(),
        InactiveDetectorLogoutSuccessState(),
      ];

      for (final state in states) {
        expect(state, isA<InactiveDetectorState>());
        expect(state, isA<BlocState>());
      }
    });
  });
}