import 'dart:async';

import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/constants.dart';
import 'package:evoapp/feature/inactive_detector/cubit/inactive_detector_cubit.dart';
import 'package:evoapp/feature/inactive_detector/cubit/inactive_detector_state.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  group('InactiveDetectorCubit', () {
    late InactiveDetectorCubit cubit;
    late MockEvoUtilFunction mockEvoUtilFunction;

    setUp(() {
      mockEvoUtilFunction = MockEvoUtilFunction();
      cubit = InactiveDetectorCubit(
        evoUtilFunction: mockEvoUtilFunction,
      );
    });

    tearDown(() {
      cubit.close();
    });

    group('constructor', () {
      test('should initialize with correct default values', () {
        expect(cubit.state, isA<InactiveDetectorInitializeState>());
        expect(cubit.foregroundInactiveDurationInSec,
            DurationConstants.defaultForegroundInactiveDurationInSec);
        expect(cubit.backgroundInactiveDurationInSec,
            DurationConstants.defaultBackgroundInactiveDurationInSec);
        expect(cubit.isOnForeground, true);
        expect(cubit.isEnabled, false);
        expect(cubit.lastEnterBackgroundDateTime, null);
        expect(cubit.timer, null);
      });

      test('should initialize with custom duration values', () {
        const int customForegroundDuration = 600; // 10 minutes
        const int customBackgroundDuration = 900; // 15 minutes

        final InactiveDetectorCubit customCubit = InactiveDetectorCubit(
          evoUtilFunction: mockEvoUtilFunction,
          foregroundInactiveDurationInSec: customForegroundDuration,
          backgroundInactiveDurationInSec: customBackgroundDuration,
        );

        expect(customCubit.foregroundInactiveDurationInSec, customForegroundDuration);
        expect(customCubit.backgroundInactiveDurationInSec, customBackgroundDuration);

        customCubit.close();
      });
    });

    group('startDetection', () {
      test('should enable detector and start timer', () {
        cubit.startDetection();

        expect(cubit.isEnabled, true);
        expect(cubit.timer, isNotNull);
        expect(cubit.timer!.isActive, true);
      });

      test('should cancel existing timer before starting new one', () {
        cubit.startDetection();
        final Timer? firstTimer = cubit.timer;

        cubit.startDetection();
        final Timer? secondTimer = cubit.timer;

        expect(firstTimer, isNot(equals(secondTimer)));
        expect(cubit.timer!.isActive, true);
      });
    });

    group('stopDetection', () {
      test('should disable detector and cancel timer', () {
        cubit.startDetection();
        expect(cubit.isEnabled, true);
        expect(cubit.timer, isNotNull);

        cubit.stopDetection();

        expect(cubit.isEnabled, false);
        expect(cubit.timer, null);
      });

      test('should handle being called when already stopped', () {
        expect(cubit.isEnabled, false);
        expect(cubit.timer, null);

        // Should not throw
        cubit.stopDetection();

        expect(cubit.isEnabled, false);
        expect(cubit.timer, null);
      });
    });

    group('onUserAction', () {
      test('should reset detector when enabled', () {
        cubit.startDetection();
        final Timer? originalTimer = cubit.timer;

        cubit.onUserAction();

        expect(cubit.timer, isNotNull);
        expect(cubit.timer, isNot(equals(originalTimer)));
        expect(cubit.timer!.isActive, true);
      });

      test('should do nothing when disabled', () {
        expect(cubit.isEnabled, false);
        expect(cubit.timer, null);

        cubit.onUserAction();

        expect(cubit.isEnabled, false);
        expect(cubit.timer, null);
      });
    });

    group('onAppGoForeground', () {
      test('should do nothing when disabled', () {
        cubit.isOnForeground = false;
        expect(cubit.isEnabled, false);

        cubit.onAppGoForeground();

        expect(cubit.isOnForeground, false);
      });

      test('should do nothing when already on foreground', () {
        cubit.startDetection();
        expect(cubit.isOnForeground, true);
        final Timer? originalTimer = cubit.timer;

        cubit.onAppGoForeground();

        expect(cubit.isOnForeground, true);
        expect(cubit.timer, equals(originalTimer));
      });

      test('should reset detector when returning from background within time limit', () {
        cubit.startDetection();
        cubit.isOnForeground = false;
        cubit.lastEnterBackgroundDateTime = DateTime.now().subtract(const Duration(seconds: 60));

        cubit.onAppGoForeground();

        expect(cubit.isOnForeground, true);
        expect(cubit.timer, isNotNull);
        expect(cubit.timer!.isActive, true);
      });
    });

    group('onAppGoBackground', () {
      test('should do nothing when disabled', () {
        expect(cubit.isEnabled, false);
        expect(cubit.isOnForeground, true);

        cubit.onAppGoBackground();

        expect(cubit.isOnForeground, true);
        expect(cubit.lastEnterBackgroundDateTime, null);
      });

      test('should do nothing when already on background', () {
        cubit.startDetection();
        cubit.isOnForeground = false;
        final DateTime? originalDateTime = cubit.lastEnterBackgroundDateTime;

        cubit.onAppGoBackground();

        expect(cubit.isOnForeground, false);
        expect(cubit.lastEnterBackgroundDateTime, equals(originalDateTime));
      });

      test('should record background timestamp when going to background', () {
        cubit.startDetection();
        expect(cubit.isOnForeground, true);
        expect(cubit.lastEnterBackgroundDateTime, null);

        cubit.onAppGoBackground();

        expect(cubit.isOnForeground, false);
        expect(cubit.lastEnterBackgroundDateTime, isNotNull);
      });

      test('should cancel timer but keep detector enabled when going to background', () {
        cubit.startDetection();
        expect(cubit.isEnabled, true);
        expect(cubit.timer, isNotNull);
        expect(cubit.timer!.isActive, true);

        cubit.onAppGoBackground();

        expect(cubit.isEnabled, true); // Should remain enabled
        expect(cubit.timer, null); // Timer should be cancelled
        expect(cubit.isOnForeground, false);
      });
    });

    group('isReachedBackgroundInactiveDuration', () {
      test('should return false when lastEnterBackgroundDateTime is null', () {
        expect(cubit.lastEnterBackgroundDateTime, null);
        expect(cubit.isReachedBackgroundInactiveDuration(), false);
      });

      test('should return false when background duration not exceeded', () {
        cubit.lastEnterBackgroundDateTime = DateTime.now().subtract(const Duration(seconds: 60));
        expect(cubit.isReachedBackgroundInactiveDuration(), false);
      });

      test('should return true when background duration exceeded', () {
        cubit.lastEnterBackgroundDateTime =
            DateTime.now().subtract(Duration(seconds: cubit.backgroundInactiveDurationInSec + 10));
        expect(cubit.isReachedBackgroundInactiveDuration(), true);
      });
    });

    group('logout', () {
      blocTest<InactiveDetectorCubit, InactiveDetectorState>(
        'should stop detection, clear user info and emit logout state',
        build: () => cubit,
        setUp: () {
          when(() => mockEvoUtilFunction.clearUserInfoAppState()).thenReturn(null);
          cubit.startDetection();
        },
        act: (InactiveDetectorCubit cubit) => cubit.logout(),
        expect: () => <TypeMatcher<InactiveDetectorLogoutSuccessState>>[
          isA<InactiveDetectorLogoutSuccessState>(),
        ],
        verify: (_) {
          expect(cubit.isEnabled, false);
          expect(cubit.timer, null);
          verify(() => mockEvoUtilFunction.clearUserInfoAppState()).called(1);
        },
      );
    });

    group('onInactiveTimerCompleted', () {
      blocTest<InactiveDetectorCubit, InactiveDetectorState>(
        'should emit detected state and stop detection when on foreground',
        build: () => cubit,
        setUp: () {
          cubit.startDetection();
          expect(cubit.isOnForeground, true);
        },
        act: (InactiveDetectorCubit cubit) => cubit.onInactiveTimerCompleted(),
        expect: () => <TypeMatcher<InactiveDetectorDetectedState>>[
          isA<InactiveDetectorDetectedState>(),
        ],
        verify: (_) {
          expect(cubit.isEnabled, false);
          expect(cubit.timer, null);
        },
      );

      test('should do nothing when on background', () {
        cubit.startDetection();
        cubit.isOnForeground = false;
        final Timer? originalTimer = cubit.timer;

        cubit.onInactiveTimerCompleted();

        expect(cubit.isEnabled, true);
        expect(cubit.timer, equals(originalTimer));
      });
    });

    group('resetDetector', () {
      test('should cancel current timer and create new one', () {
        cubit.startDetection();
        final Timer? originalTimer = cubit.timer;

        cubit.resetDetector();

        expect(cubit.timer, isNotNull);
        expect(cubit.timer, isNot(equals(originalTimer)));
        expect(cubit.timer!.isActive, true);
      });
    });

    group('close', () {
      test('should stop detection when closed', () async {
        cubit.startDetection();
        expect(cubit.isEnabled, true);
        expect(cubit.timer, isNotNull);

        await cubit.close();

        expect(cubit.isEnabled, false);
        expect(cubit.timer, null);
      });
    });

    group('complex scenarios', () {
      blocTest<InactiveDetectorCubit, InactiveDetectorState>(
        'should logout when returning from background after timeout',
        build: () => cubit,
        setUp: () {
          when(() => mockEvoUtilFunction.clearUserInfoAppState()).thenReturn(null);
          cubit.startDetection();
          cubit.isOnForeground = false;
          cubit.lastEnterBackgroundDateTime = DateTime.now()
              .subtract(Duration(seconds: cubit.backgroundInactiveDurationInSec + 10));
        },
        act: (InactiveDetectorCubit cubit) => cubit.onAppGoForeground(),
        expect: () => <TypeMatcher<InactiveDetectorLogoutSuccessState>>[
          isA<InactiveDetectorLogoutSuccessState>(),
        ],
        verify: (_) {
          expect(cubit.isEnabled, false);
          verify(() => mockEvoUtilFunction.clearUserInfoAppState()).called(1);
        },
      );

      test('should handle rapid foreground/background transitions', () {
        cubit.startDetection();

        // Rapid transitions
        cubit.onAppGoBackground();
        expect(cubit.isOnForeground, false);
        expect(cubit.lastEnterBackgroundDateTime, isNotNull);

        cubit.onAppGoForeground();
        expect(cubit.isOnForeground, true);
        expect(cubit.timer, isNotNull);

        cubit.onAppGoBackground();
        expect(cubit.isOnForeground, false);

        cubit.onAppGoForeground();
        expect(cubit.isOnForeground, true);
      });

      test('should handle timer completion during background state', () {
        cubit.startDetection();
        cubit.isOnForeground = false;

        // Timer completion while in background should not emit detected state
        cubit.onInactiveTimerCompleted();
        expect(cubit.isEnabled, true); // Should still be enabled
      });

      test('should handle multiple start/stop cycles', () {
        // First cycle
        cubit.startDetection();
        expect(cubit.isEnabled, true);
        cubit.stopDetection();
        expect(cubit.isEnabled, false);

        // Second cycle
        cubit.startDetection();
        expect(cubit.isEnabled, true);
        cubit.stopDetection();
        expect(cubit.isEnabled, false);

        // Third cycle
        cubit.startDetection();
        expect(cubit.isEnabled, true);
        expect(cubit.timer, isNotNull);
      });
    });

    group('edge cases', () {
      test('should handle null timer gracefully in stopDetection', () {
        expect(cubit.timer, null);
        // Should not throw
        cubit.stopDetection();
        expect(cubit.timer, null);
      });

      test('should handle background timestamp edge case', () {
        // Set timestamp to exactly the threshold
        cubit.lastEnterBackgroundDateTime =
            DateTime.now().subtract(Duration(seconds: cubit.backgroundInactiveDurationInSec));
        expect(cubit.isReachedBackgroundInactiveDuration(), true);
      });

      test('should handle very recent background timestamp', () {
        cubit.lastEnterBackgroundDateTime = DateTime.now();
        expect(cubit.isReachedBackgroundInactiveDuration(), false);
      });

      test('should handle future background timestamp', () {
        // Edge case: timestamp in future (should not happen in practice)
        cubit.lastEnterBackgroundDateTime = DateTime.now().add(const Duration(seconds: 10));
        expect(cubit.isReachedBackgroundInactiveDuration(), false);
      });
    });

    group('integration scenarios', () {
      test('should simulate complete user session with inactivity', () async {
        // Start session
        cubit.startDetection();
        expect(cubit.isEnabled, true);

        // User is active
        cubit.onUserAction();
        expect(cubit.timer, isNotNull);

        // App goes to background
        cubit.onAppGoBackground();
        expect(cubit.isOnForeground, false);

        // App returns to foreground quickly
        cubit.onAppGoForeground();
        expect(cubit.isOnForeground, true);

        // More user activity
        cubit.onUserAction();
        expect(cubit.timer, isNotNull);

        // End session
        cubit.stopDetection();
        expect(cubit.isEnabled, false);
      });
    });
  });
}
