import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/inactive_detector/cubit/inactive_detector_cubit.dart';
import 'package:evoapp/feature/inactive_detector/cubit/inactive_detector_state.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late MockAuthenticationRepo mockAuthenticationRepo;
  late MockEvoUtilFunction mockEvoUtilFunction;
  late InactiveDetectorCubit inactiveDetectorCubit;
  late AppState appState;
  setUp(() {
    mockAuthenticationRepo = MockAuthenticationRepo();
    mockEvoUtilFunction = MockEvoUtilFunction();
    appState = AppState();

    inactiveDetectorCubit = InactiveDetectorCubit(
      authenticationRepo: mockAuthenticationRepo,
      evoUtilFunction: mockEvoUtilFunction,
      appState: appState,
    );
  });

  setUp(() {
    inactiveDetectorCubit = InactiveDetectorCubit(
      authenticationRepo: mockAuthenticationRepo,
      evoUtilFunction: mockEvoUtilFunction,
      appState: appState,
    );
  });

  tearDown(() {
    inactiveDetectorCubit.close();
    appState.userToken = null;
  });

  blocTest<InactiveDetectorCubit, InactiveDetectorState>(
    'emits [SignOutSuccess] when signOut is called',
    build: () => inactiveDetectorCubit,
    act: (InactiveDetectorCubit cubit) => cubit.signOut(),
    expect: () => <dynamic>[isA<SignOutSuccess>()],
    verify: (_) {
      verify(() => mockEvoUtilFunction.clearUserInfoAppState()).called(1);
    },
  );
}
