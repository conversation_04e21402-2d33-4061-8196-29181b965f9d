import 'package:evoapp/feature/inactive_detector/inactive_detector_timer.dart';
import 'package:evoapp/feature/inactive_detector/inactive_detector_timer_impl.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockVoidCallback extends Mock {
  void call();
}

void main() {
  late InActiveDetectorTimerImpl inActiveDetectorTimer;
  late MockVoidCallback mockOnInactive;
  late MockVoidCallback mockOnIdleAWhile;

  setUp(() {
    mockOnInactive = MockVoidCallback();
    mockOnIdleAWhile = MockVoidCallback();
    inActiveDetectorTimer = InActiveDetectorTimerImpl(
      timeOutInSec: 2, // Set a short timeout for testing
      onInactive: mockOnInactive.call,
      onIdleAWhile: mockOnIdleAWhile.call,
    );
  });

  tearDown(() {
    inActiveDetectorTimer.stop();
  });

  test('verify default defaultTimeOutInSec is 60', () {
    expect(InActiveDetectorTimerImpl.defaultTimeOutInSec, 60);
  });

  test('Initial state should be initial', () {
    expect(inActiveDetectorTimer.state, InactiveDetectorTimerState.initial);

    inActiveDetectorTimer.reset();

    verifyNever(() => mockOnIdleAWhile.call());
    verifyNever(() => mockOnInactive.call());
  });

  test('start method should set state to active and start the timer', () {
    inActiveDetectorTimer.start();
    expect(inActiveDetectorTimer.state, InactiveDetectorTimerState.active);
  });

  test('reset method should restart the timer if state is active', () {
    inActiveDetectorTimer.start();
    inActiveDetectorTimer.reset();
    expect(inActiveDetectorTimer.state, InactiveDetectorTimerState.active);
  });

  test('stop method should set state to initial and cancel the timer', () {
    inActiveDetectorTimer.start();
    inActiveDetectorTimer.stop();
    expect(inActiveDetectorTimer.state, InactiveDetectorTimerState.initial);
  });

  test('onTimerCompleted should call onIdleAWhile when state is active', () async {
    inActiveDetectorTimer.start();
    await Future<void>.delayed(const Duration(seconds: 2)); // Wait for the timer to complete
    verify(() => mockOnIdleAWhile.call()).called(1);
    verifyNever(() => mockOnInactive.call());
    expect(inActiveDetectorTimer.state, InactiveDetectorTimerState.idleAWhile);
  });

  test('onTimerCompleted should call onInactive when state is idleAWhile', () async {
    inActiveDetectorTimer.start();
    await Future<void>.delayed(const Duration(seconds: 2)); // Wait for the timer to complete
    /// verify state is idle
    verify(() => mockOnIdleAWhile.call()).called(1);
    verifyNever(() => mockOnInactive.call());
    expect(inActiveDetectorTimer.state, InactiveDetectorTimerState.idleAWhile);

    await Future<void>.delayed(const Duration(seconds: 2)); // Wait for the timer to complete again
    verifyNever(() => mockOnIdleAWhile.call());
    verify(() => mockOnInactive.call()).called(1);
    expect(inActiveDetectorTimer.state, InactiveDetectorTimerState.inActive);
  });

  group('verify when state is onInactive', () {
    Future<void> setupInActiveStateForTesting() async {
      inActiveDetectorTimer.start();
      await Future<void>.delayed(const Duration(seconds: 2)); // Wait for the timer to complete
      verify(() => mockOnIdleAWhile.call()).called(1);
      verifyNever(() => mockOnInactive.call());

      await Future<void>.delayed(const Duration(seconds: 2)); // Wait for the timer to complete again
      verifyNever(() => mockOnIdleAWhile.call());
      verify(() => mockOnInactive.call()).called(1);
      expect(inActiveDetectorTimer.state, InactiveDetectorTimerState.inActive);
    }

    test('call start() func, verify do nothing', () async {
      await setupInActiveStateForTesting();

      inActiveDetectorTimer.start();
      await Future<void>.delayed(const Duration(seconds: 2)); // Wait for the timer to complete again
      verifyNever(() => mockOnIdleAWhile.call());
      verifyNever(() => mockOnInactive.call());
    });

    test('call reset() func, verify do nothing', () async {
      await setupInActiveStateForTesting();

      inActiveDetectorTimer.reset();
      await Future<void>.delayed(const Duration(seconds: 2)); // Wait for the timer to complete again
      verifyNever(() => mockOnIdleAWhile.call());
      verifyNever(() => mockOnInactive.call());
    });
  });

  test('when state is Initial & call reset() function, do nothing', () {
    expect(inActiveDetectorTimer.state, InactiveDetectorTimerState.initial);

    inActiveDetectorTimer.reset();

    verifyNever(() => mockOnIdleAWhile.call());
    verifyNever(() => mockOnInactive.call());
  });


}