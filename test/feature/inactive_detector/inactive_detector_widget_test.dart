// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:async';

import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/constants.dart';
import 'package:evoapp/feature/inactive_detector/cubit/inactive_detector_cubit.dart';
import 'package:evoapp/feature/inactive_detector/cubit/inactive_detector_state.dart';
import 'package:evoapp/feature/inactive_detector/inactive_detector_widget.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/extension.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/widget/countdown/onetime_countdown_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

// Mock classes
class MockInactiveDetectorCubit extends MockCubit<InactiveDetectorState>
    implements InactiveDetectorCubit {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockDialogFunction extends Mock implements DialogFunction {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockGlobalKeyProvider extends Mock implements GlobalKeyProvider {}

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  group('InactiveDetectorWidget', () {
    late MockInactiveDetectorCubit mockCubit;
    late MockEvoUtilFunction mockEvoUtilFunction;
    late MockDialogFunction mockDialogFunction;
    late MockCommonNavigator mockCommonNavigator;
    late MockGlobalKeyProvider mockGlobalKeyProvider;
    late MockBuildContext mockNavigatorContext;
    late InactiveDetectorController controller;

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(InactiveDetectorInitializeState());
      registerFallbackValue(EvoDialogId.inactiveDialog);
      registerFallbackValue(MockBuildContext());

      // Setup GetIt dependencies
      getItRegisterColor();
      getItRegisterTextStyle();
      getItRegisterButtonStyle();
    });

    setUp(() {
      // Create fresh mocks for each test
      mockCubit = MockInactiveDetectorCubit();
      mockEvoUtilFunction = MockEvoUtilFunction();
      mockDialogFunction = MockDialogFunction();
      mockCommonNavigator = MockCommonNavigator();
      mockGlobalKeyProvider = MockGlobalKeyProvider();
      mockNavigatorContext = MockBuildContext();
      controller = InactiveDetectorController();

      // Setup mock cubit initial state
      when(() => mockCubit.state).thenReturn(InactiveDetectorInitializeState());
      when(() => mockCubit.stream).thenAnswer(
          (_) => Stream<InactiveDetectorState>.value(InactiveDetectorInitializeState()));

      // Setup GetIt mocks
      getIt.registerSingleton<DialogFunction>(mockDialogFunction);
      getIt.registerSingleton<CommonNavigator>(mockCommonNavigator);
      getIt.registerSingleton<GlobalKeyProvider>(mockGlobalKeyProvider);
      getIt.registerSingleton<EvoUtilFunction>(mockEvoUtilFunction);

      // Setup global key provider mock
      when(() => mockGlobalKeyProvider.navigatorContext).thenReturn(mockNavigatorContext);

      // Setup dialog function mocks
      when(
        () => mockDialogFunction.showDialogConfirm(
          isDismissible: any(named: 'isDismissible'),
          dialogId: any(named: 'dialogId'),
          title: any(named: 'title'),
          content: any(named: 'content'),
          textPositive: any(named: 'textPositive'),
          textNegative: any(named: 'textNegative'),
          footer: any(named: 'footer'),
          onClickPositive: any(named: 'onClickPositive'),
          onClickNegative: any(named: 'onClickNegative'),
          autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
        ),
      ).thenAnswer((_) async {});
    });

    tearDown(() {
      getIt.unregister<DialogFunction>();
      getIt.unregister<CommonNavigator>();
      getIt.unregister<GlobalKeyProvider>();
      getIt.unregister<EvoUtilFunction>();
    });

    tearDownAll(() {
      getIt.reset();
    });

    group('Widget Creation and Initialization', () {
      testWidgets('should create widget with required child parameter',
          (WidgetTester tester) async {
        const Widget testChild = Text('Test Child');

        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: testChild,
            ),
          ),
        );

        expect(find.text('Test Child'), findsOneWidget);
        expect(find.byType(InactiveDetectorWidget), findsOneWidget);
      });

      testWidgets('should wire controller functions to cubit methods', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              controller: controller,
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        // Verify controller functions are wired
        expect(controller.enable, isNotNull);
        expect(controller.disable, isNotNull);

        // Test enable function
        controller.enable?.call();
        verify(() => mockCubit.startDetection()).called(1);

        // Test disable function
        controller.disable?.call();
        verify(() => mockCubit.stopDetection()).called(1);
      });

      testWidgets('should create default cubit when none provided', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              child: const SizedBox(),
            ),
          ),
        );

        // Widget should still render successfully with default cubit
        expect(find.byType(InactiveDetectorWidget), findsOneWidget);
      });

      testWidgets('should create widget with controller but no cubit', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              controller: controller,
              child: const SizedBox(),
            ),
          ),
        );

        // Widget should render successfully and controller should be wired
        expect(find.byType(InactiveDetectorWidget), findsOneWidget);
        expect(controller.enable, isNotNull);
        expect(controller.disable, isNotNull);
      });
    });

    group('User Interaction Detection', () {
      testWidgets('should call cubit.onUserAction on pointer down events',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: InactiveDetectorWidget(
                cubit: mockCubit,
                child: Container(
                  width: 200,
                  height: 200,
                  color: Colors.blue,
                  child: const Text('Tap me'),
                ),
              ),
            ),
          ),
        );

        // Simulate pointer down event by tapping on the container
        await tester.tap(find.text('Tap me'), warnIfMissed: false);

        verify(() => mockCubit.onUserAction()).called(1);
      });

      testWidgets('should use translucent hit test behavior', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        // Find all Listener widgets and check if any has translucent behavior
        final Finder listenerFinder = find.byType(Listener);
        expect(listenerFinder, findsWidgets);

        // Check if any Listener has translucent behavior (our InactiveDetectorWidget's Listener)
        bool foundTranslucentListener = false;
        for (int i = 0; i < listenerFinder.evaluate().length; i++) {
          final Listener listener = tester.widget(listenerFinder.at(i));
          if (listener.behavior == HitTestBehavior.translucent) {
            foundTranslucentListener = true;
            break;
          }
        }
        expect(
          foundTranslucentListener,
          isTrue,
          reason: 'Should find a Listener with translucent behavior',
        );
      });

      testWidgets('should allow child widgets to receive events', (WidgetTester tester) async {
        bool childTapped = false;

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: InactiveDetectorWidget(
                cubit: mockCubit,
                child: GestureDetector(
                  onTap: () => childTapped = true,
                  child: Container(
                    width: 200,
                    height: 200,
                    color: Colors.red,
                    child: const Text('Child Widget'),
                  ),
                ),
              ),
            ),
          ),
        );

        await tester.tap(find.text('Child Widget'), warnIfMissed: false);

        // Both parent and child should receive the event
        verify(() => mockCubit.onUserAction()).called(1);
        expect(childTapped, isTrue);
      });
    });

    group('App Lifecycle Management', () {
      testWidgets('should call cubit.onAppGoForeground when app resumes',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Simulate app lifecycle change to resumed
        state.didChangeAppLifecycleState(AppLifecycleState.resumed);

        verify(() => mockCubit.onAppGoForeground()).called(1);
      });

      testWidgets('should call cubit.onAppGoBackground when app pauses',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Simulate app lifecycle change to paused
        state.didChangeAppLifecycleState(AppLifecycleState.paused);

        verify(() => mockCubit.onAppGoBackground()).called(1);
      });

      testWidgets('should not call cubit methods for other lifecycle states',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Test inactive state
        state.didChangeAppLifecycleState(AppLifecycleState.inactive);
        verifyNever(() => mockCubit.onAppGoForeground());
        verifyNever(() => mockCubit.onAppGoBackground());

        // Test detached state
        state.didChangeAppLifecycleState(AppLifecycleState.detached);
        verifyNever(() => mockCubit.onAppGoForeground());
        verifyNever(() => mockCubit.onAppGoBackground());

        // Test hidden state
        state.didChangeAppLifecycleState(AppLifecycleState.hidden);
        verifyNever(() => mockCubit.onAppGoForeground());
        verifyNever(() => mockCubit.onAppGoBackground());
      });

      testWidgets('should handle widget disposal gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        // Verify widget is present
        expect(find.byType(InactiveDetectorWidget), findsOneWidget);

        // Remove the widget - should not throw any errors
        await tester.pumpWidget(const MaterialApp(home: SizedBox()));

        // Verify widget is removed
        expect(find.byType(InactiveDetectorWidget), findsNothing);
      });
    });

    group('State Change Handling', () {
      testWidgets('should show inactive dialog when InactiveDetectorDetectedState is emitted',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Trigger detected state
        state.handleOnStateChanged(InactiveDetectorDetectedState());

        // Verify dialog function was called with correct parameters
        verify(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: false,
            dialogId: EvoDialogId.inactiveDialog,
            title: EvoStrings.inactiveDialogTitle,
            textPositive: EvoStrings.inactiveDialogPositiveButton,
            textNegative: EvoStrings.inactiveDialogNegativeButton,
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: true,
          ),
        ).called(1);
      });

      testWidgets(
          'should navigate to login screen when InactiveDetectorSignOutSuccessState is emitted',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Trigger sign out success state
        state.handleOnStateChanged(InactiveDetectorLogoutSuccessState());

        verify(() => mockNavigatorContext.goNamed(Screen.previousLogInScreen.name)).called(1);
      });

      testWidgets('should handle unknown states gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Trigger initialize state (should do nothing)
        state.handleOnStateChanged(InactiveDetectorInitializeState());

        // Verify no dialog was shown
        verifyNever(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: any(named: 'isDismissible'),
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
          ),
        );
      });
    });

    group('Dialog Functionality', () {
      testWidgets('should show inactive dialog with correct content', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Call showInactiveDialog directly
        state.showInactiveDialog();

        // Verify dialog was called with correct parameters
        verify(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: false,
            dialogId: EvoDialogId.inactiveDialog,
            title: EvoStrings.inactiveDialogTitle,
            textPositive: EvoStrings.inactiveDialogPositiveButton,
            textNegative: EvoStrings.inactiveDialogNegativeButton,
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: true,
          ),
        ).called(1);
      });

      testWidgets('should create countdown timer with correct properties',
          (WidgetTester tester) async {
        Widget? capturedFooter;

        // Capture the footer widget when showDialogConfirm is called
        when(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: any(named: 'isDismissible'),
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            footer: captureAny(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
          ),
        ).thenAnswer((Invocation invocation) {
          capturedFooter = invocation.namedArguments[#footer];
          return Future<void>.value();
        });

        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Show the dialog to capture the footer
        state.showInactiveDialog();

        // Verify footer is captured and is OnetimeCountdownBuilder
        expect(capturedFooter, isA<OnetimeCountdownBuilder>());

        final OnetimeCountdownBuilder countdownBuilder = capturedFooter! as OnetimeCountdownBuilder;

        // Verify the countdown duration matches the constant
        expect(countdownBuilder.durationInSec,
            equals(DurationConstants.defaultDurationLogoutAfterInactiveInSec));

        // Verify builder function is not null
        expect(countdownBuilder.builder, isNotNull);

        // Verify onFinish callback is not null
        expect(countdownBuilder.onFinish, isNotNull);
      });

      testWidgets('should test countdown timer onFinish callback execution',
          (WidgetTester tester) async {
        Widget? capturedFooter;

        // Capture the footer widget when showDialogConfirm is called
        when(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: any(named: 'isDismissible'),
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            footer: captureAny(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
          ),
        ).thenAnswer((Invocation invocation) {
          capturedFooter = invocation.namedArguments[#footer];
          return Future<void>.value();
        });

        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Show the dialog to capture the footer
        state.showInactiveDialog();

        // Verify footer is captured and is OnetimeCountdownBuilder
        expect(capturedFooter, isA<OnetimeCountdownBuilder>());

        final OnetimeCountdownBuilder countdownBuilder = capturedFooter! as OnetimeCountdownBuilder;

        // Verify onFinish callback is not null
        expect(countdownBuilder.onFinish, isNotNull);

        // Execute the onFinish callback to test the uncovered lines
        countdownBuilder.onFinish!();

        // Verify that navigatorContext?.pop() was called
        verify(() => mockNavigatorContext.pop()).called(1);

        // Verify that showLogoutDialog was called (second dialog)
        verify(
          () => mockDialogFunction.showDialogConfirm(
            dialogId: EvoDialogId.inactiveLogoutDialog,
            textPositive: EvoStrings.ctaLogInAgain,
            content: EvoStrings.inactiveLogoutDialogDescription,
            title: EvoStrings.inactiveLogoutDialogTitle,
            isDismissible: false,
            autoClosePopupWhenClickCTA: true,
            onClickPositive: any(named: 'onClickPositive'),
          ),
        ).called(1);
      });

      testWidgets('should test countdown timer builder callback execution through widget rendering',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Show the dialog which will create the countdown builder
        state.showInactiveDialog();

        // Verify dialog was called with footer parameter
        verify(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: false,
            dialogId: EvoDialogId.inactiveDialog,
            title: EvoStrings.inactiveDialogTitle,
            textPositive: EvoStrings.inactiveDialogPositiveButton,
            textNegative: EvoStrings.inactiveDialogNegativeButton,
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: true,
          ),
        ).called(1);
      });

      testWidgets('should test countdown timer onFinish callback through direct execution',
          (WidgetTester tester) async {
        Widget? capturedFooter;

        when(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: any(named: 'isDismissible'),
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            footer: captureAny(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
          ),
        ).thenAnswer((Invocation invocation) {
          capturedFooter = invocation.namedArguments[#footer];
          return Future<void>.value();
        });

        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));
        state.showInactiveDialog();

        // Verify footer is captured
        expect(capturedFooter, isNotNull);
        expect(capturedFooter, isA<OnetimeCountdownBuilder>());

        final OnetimeCountdownBuilder countdownBuilder = capturedFooter! as OnetimeCountdownBuilder;

        // Test the onFinish callback directly to cover the uncovered lines
        expect(countdownBuilder.onFinish, isNotNull);

        // Execute onFinish to test the uncovered lines (189-192)
        countdownBuilder.onFinish!();

        // Verify the sequence: pop dialog, then show logout dialog
        verify(() => mockNavigatorContext.pop()).called(1);
        verify(
          () => mockDialogFunction.showDialogConfirm(
            dialogId: EvoDialogId.inactiveLogoutDialog,
            textPositive: EvoStrings.ctaLogInAgain,
            content: EvoStrings.inactiveLogoutDialogDescription,
            title: EvoStrings.inactiveLogoutDialogTitle,
            isDismissible: false,
            autoClosePopupWhenClickCTA: true,
            onClickPositive: any(named: 'onClickPositive'),
          ),
        ).called(1);
      });

      test('should test pluralization logic used in countdown builder', () {
        // Test the pluralization logic that's used in the builder callback (lines 202-205)
        // This tests the same logic without needing GetIt dependencies

        // Test singular form (1 second)
        const int singularSeconds = 1;
        final String singularResult = singularSeconds.pluralize(
          pluralForm: EvoStrings.pluralSecond,
          singularForm: EvoStrings.singularSecond,
        );
        expect(singularResult, equals('1 second'));

        // Test plural form (multiple seconds)
        const int pluralSeconds = 30;
        final String pluralResult = pluralSeconds.pluralize(
          pluralForm: EvoStrings.pluralSecond,
          singularForm: EvoStrings.singularSecond,
        );
        expect(pluralResult, equals('30 seconds'));

        // Test zero seconds (plural)
        const int zeroSeconds = 0;
        final String zeroResult = zeroSeconds.pluralize(
          pluralForm: EvoStrings.pluralSecond,
          singularForm: EvoStrings.singularSecond,
        );
        expect(zeroResult, equals('0 seconds'));

        // Test the exact duration used in the countdown
        const int countdownDuration = DurationConstants.defaultDurationLogoutAfterInactiveInSec;
        final String countdownResult = countdownDuration.pluralize(
          pluralForm: EvoStrings.pluralSecond,
          singularForm: EvoStrings.singularSecond,
        );
        expect(countdownResult, equals('60 seconds'));
      });

      testWidgets('should verify countdown builder properties and structure',
          (WidgetTester tester) async {
        Widget? capturedFooter;

        when(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: any(named: 'isDismissible'),
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            footer: captureAny(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
          ),
        ).thenAnswer((Invocation invocation) {
          capturedFooter = invocation.namedArguments[#footer];
          return Future<void>.value();
        });

        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));
        state.showInactiveDialog();

        final OnetimeCountdownBuilder countdownBuilder = capturedFooter! as OnetimeCountdownBuilder;

        // Verify all properties are set correctly
        expect(countdownBuilder.durationInSec,
            equals(DurationConstants.defaultDurationLogoutAfterInactiveInSec));
        expect(countdownBuilder.onFinish, isNotNull);
        expect(countdownBuilder.builder, isNotNull);

        // Verify the builder function signature by checking it can be called
        // (We can't call it due to GetIt dependencies, but we can verify it exists)
        expect(countdownBuilder.builder, isA<Widget Function(int)>());
      });

      testWidgets('should execute countdown builder callback to test RichText creation',
          (WidgetTester tester) async {
        Widget? capturedFooter;

        when(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: any(named: 'isDismissible'),
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            footer: captureAny(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
          ),
        ).thenAnswer((Invocation invocation) {
          capturedFooter = invocation.namedArguments[#footer];
          return Future<void>.value();
        });

        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));
        state.showInactiveDialog();

        final OnetimeCountdownBuilder countdownBuilder = capturedFooter! as OnetimeCountdownBuilder;

        // Now execute the builder callback directly to cover lines 194-206
        // Test with different values to ensure all code paths are covered

        // Test with 1 second (singular form)
        final Widget widget1 = countdownBuilder.builder(1);
        expect(widget1, isA<RichText>());

        // Test with multiple seconds (plural form)
        final Widget widget30 = countdownBuilder.builder(30);
        expect(widget30, isA<RichText>());

        // Test with 0 seconds (plural form)
        final Widget widget0 = countdownBuilder.builder(0);
        expect(widget0, isA<RichText>());

        // Test with the default duration
        final Widget widgetDefault =
            countdownBuilder.builder(DurationConstants.defaultDurationLogoutAfterInactiveInSec);
        expect(widgetDefault, isA<RichText>());

        // Verify the structure of the created RichText
        final RichText richText = widget30 as RichText;
        expect(richText.textAlign, equals(TextAlign.center));

        final TextSpan rootSpan = richText.text as TextSpan;
        expect(rootSpan.children, isNotNull);
        expect(rootSpan.children!.length, equals(2));

        // Verify the first child contains the static content
        final TextSpan firstSpan = rootSpan.children![0] as TextSpan;
        expect(firstSpan.text, equals(EvoStrings.inactiveDialogContent));

        // Verify the second child contains the pluralized countdown text
        final TextSpan secondSpan = rootSpan.children![1] as TextSpan;
        expect(secondSpan.text, contains('30 seconds'));
      });

      testWidgets('should show sign out dialog with correct content', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Call showSignOutDialog directly
        state.showLogoutDialog();

        // Verify dialog was called with correct parameters
        verify(
          () => mockDialogFunction.showDialogConfirm(
            dialogId: EvoDialogId.inactiveLogoutDialog,
            textPositive: EvoStrings.ctaLogInAgain,
            content: EvoStrings.inactiveLogoutDialogDescription,
            title: EvoStrings.inactiveLogoutDialogTitle,
            isDismissible: false,
            autoClosePopupWhenClickCTA: true,
            onClickPositive: any(named: 'onClickPositive'),
          ),
        ).called(1);
      });

      testWidgets('should handle positive button click in inactive dialog',
          (WidgetTester tester) async {
        VoidCallback? capturedPositiveCallback;

        // Capture the callback when showDialogConfirm is called
        when(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: any(named: 'isDismissible'),
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            footer: any(named: 'footer'),
            onClickPositive: captureAny(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
          ),
        ).thenAnswer((Invocation invocation) {
          capturedPositiveCallback = invocation.namedArguments[#onClickPositive];
          return Future<void>.value();
        });

        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Show the dialog
        state.showInactiveDialog();

        // Simulate positive button click
        capturedPositiveCallback?.call();

        // Verify cubit.startDetection was called
        verify(() => mockCubit.startDetection()).called(1);
      });

      testWidgets('should handle negative button click in inactive dialog',
          (WidgetTester tester) async {
        VoidCallback? capturedNegativeCallback;

        // Capture the callback when showDialogConfirm is called
        when(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: any(named: 'isDismissible'),
            dialogId: any(named: 'dialogId'),
            title: any(named: 'title'),
            textPositive: any(named: 'textPositive'),
            textNegative: any(named: 'textNegative'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: captureAny(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
          ),
        ).thenAnswer((Invocation invocation) {
          capturedNegativeCallback = invocation.namedArguments[#onClickNegative];
          return Future<void>.value();
        });

        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Show the dialog
        state.showInactiveDialog();

        // Simulate negative button click
        capturedNegativeCallback?.call();

        // Verify cubit.signOut was called
        verify(() => mockCubit.logout()).called(1);
      });

      testWidgets('should handle positive button click in sign out dialog',
          (WidgetTester tester) async {
        VoidCallback? capturedPositiveCallback;

        // Capture the callback when showDialogConfirm is called
        when(
          () => mockDialogFunction.showDialogConfirm(
            dialogId: any(named: 'dialogId'),
            textPositive: any(named: 'textPositive'),
            content: any(named: 'content'),
            title: any(named: 'title'),
            isDismissible: any(named: 'isDismissible'),
            autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
            onClickPositive: captureAny(named: 'onClickPositive'),
          ),
        ).thenAnswer((Invocation invocation) {
          capturedPositiveCallback = invocation.namedArguments[#onClickPositive];
          return Future<void>.value();
        });

        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Show the dialog
        state.showLogoutDialog();

        // Simulate positive button click
        capturedPositiveCallback?.call();

        // Verify cubit.signOut was called
        verify(() => mockCubit.logout()).called(1);
      });
    });

    group('BLoC Integration', () {
      testWidgets('should provide cubit through BlocProvider', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: Builder(
                builder: (BuildContext context) {
                  final InactiveDetectorCubit cubit = context.read<InactiveDetectorCubit>();
                  return Text('Cubit: ${cubit.runtimeType}');
                },
              ),
            ),
          ),
        );

        expect(find.textContaining('MockInactiveDetectorCubit'), findsOneWidget);
      });

      testWidgets('should respond to cubit state changes through BlocListener',
          (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        // Find the state instance and manually trigger state change
        final InactiveDetectorWidgetState state = tester.state(find.byType(InactiveDetectorWidget));

        // Manually call the state change handler
        state.handleOnStateChanged(InactiveDetectorDetectedState());

        // Verify dialog was shown
        verify(
          () => mockDialogFunction.showDialogConfirm(
            isDismissible: false,
            dialogId: EvoDialogId.inactiveDialog,
            title: EvoStrings.inactiveDialogTitle,
            textPositive: EvoStrings.inactiveDialogPositiveButton,
            textNegative: EvoStrings.inactiveDialogNegativeButton,
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            autoClosePopupWhenClickCTA: true,
          ),
        ).called(1);
      });
    });

    group('Controller Tests', () {
      test('InactiveDetectorController should initialize with null functions', () {
        final InactiveDetectorController controller = InactiveDetectorController();
        expect(controller.enable, isNull);
        expect(controller.disable, isNull);
      });

      testWidgets('should wire controller functions correctly', (WidgetTester tester) async {
        final InactiveDetectorController controller = InactiveDetectorController();

        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              controller: controller,
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        // Verify functions are wired
        expect(controller.enable, isNotNull);
        expect(controller.disable, isNotNull);

        // Test that functions call the correct cubit methods
        controller.enable?.call();
        verify(() => mockCubit.startDetection()).called(1);

        controller.disable?.call();
        verify(() => mockCubit.stopDetection()).called(1);
      });

      testWidgets('should work without controller', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: InactiveDetectorWidget(
              cubit: mockCubit,
              child: const SizedBox(),
            ),
          ),
        );

        // Widget should render without issues
        expect(find.byType(InactiveDetectorWidget), findsOneWidget);
      });
    });
  });
}
