import 'package:evoapp/feature/welcome/welcome_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test_config.dart';
import '../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider imageProvider;
  late WidgetTester tester;

  const Size defaultSize = Size(360, 800);

  setUpAll(() {
    initConfigEvoPageStateBase();
    imageProvider = getIt<CommonImageProvider>();
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  group('WelcomeScreen', () {
    Widget buildWidgetInTest() {
      return const MaterialApp(
        home: WelcomeScreen(),
      );
    }

    testWidgets('should show correct UI', (WidgetTester widgetTester) async {
      tester = widgetTester;
      initConfigChangeScreenSize(tester, size: defaultSize);

      await tester.pumpWidget(buildWidgetInTest());
      await tester.pumpAndSettle();

      //Verify Scaffold exists exactly once in the widget tree
      final Finder scaffoldFinder = find.byType(Scaffold);
      expect(scaffoldFinder, findsOneWidget);

      // Verify SafeArea is direct child of Scaffold
      final Scaffold scaffoldWidget = tester.widget<Scaffold>(scaffoldFinder);
      expect(
        scaffoldWidget.body,
        isA<SafeArea>(),
      );

      // Verify logo
      verify(() => imageProvider.asset(
            EvoImages.imgBrandName,
            fit: BoxFit.fill,
            height: 64.w,
            width: 163.w,
            color: evoColors.primaryBase,
          )).called(1);

      // Verify welcome image
      verify(() => imageProvider.asset(
            EvoImages.imgWelcome,
            fit: any(named: 'fit'),
          )).called(1);

      // Verify buttons
      expect(
        find.descendant(
          of: find.byType(CommonButton),
          matching: find.text(EvoStrings.activateAccountMessage),
        ),
        findsOneWidget,
      );

      expect(
        find.textContaining(EvoStrings.alreadyHaveAccountMessage, findRichText: true),
        findsOneWidget,
      );
      expect(
        find.textContaining(EvoStrings.login, findRichText: true),
        findsOneWidget,
      );

      resetConfigChangeScreenSize(tester);
    });

    testWidgets('activate account button tap should navigate to mobile number check screen',
        (WidgetTester widgetTester) async {
      tester = widgetTester;
      initConfigChangeScreenSize(tester, size: defaultSize);

      await tester.pumpWidget(buildWidgetInTest());
      await tester.pumpAndSettle();

      await tester.tap(find.descendant(
        of: find.byType(CommonButton),
        matching: find.text(EvoStrings.activateAccountMessage),
      ));
      await tester.pump();

      verify(() => mockNavigatorContext.pushNamed(
            Screen.mobileNumberCheckScreen.name,
            extra: any(named: 'extra'),
          )).called(1);

      resetConfigChangeScreenSize(tester);
    });

    testWidgets('login button tap should navigate to verify username screen',
        (WidgetTester widgetTester) async {
      tester = widgetTester;
      initConfigChangeScreenSize(tester, size: defaultSize);

      await tester.pumpWidget(buildWidgetInTest());
      await tester.pumpAndSettle();

      await tester.tap(find.textContaining(EvoStrings.login, findRichText: true));
      await tester.pump();

      verify(() => mockNavigatorContext.pushNamed(
            Screen.verifyUsernameScreen.name,
            extra: any(named: 'extra'),
          )).called(1);

      resetConfigChangeScreenSize(tester);
    });

    testWidgets('RichText should have correct padding', (WidgetTester widgetTester) async {
      tester = widgetTester;
      initConfigChangeScreenSize(tester, size: defaultSize);

      await tester.pumpWidget(buildWidgetInTest());
      await tester.pumpAndSettle();

      // Find the RichText widget containing the login text
      final Finder richTextFinder = find.textContaining(EvoStrings.login, findRichText: true);
      expect(richTextFinder, findsOneWidget);

      // Find the Padding widget that wraps the RichText
      final Finder paddingFinder = find.ancestor(
        of: richTextFinder,
        matching: find.byType(Padding),
      );

      // Get the Padding widget and verify its padding values
      final Padding paddingWidget = tester.widget<Padding>(paddingFinder.first);
      expect(paddingWidget.padding, EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.w));

      resetConfigChangeScreenSize(tester);
    });

    test('goNamed should navigate to welcome screen', () {
      WelcomeScreen.goNamed();

      verify(() => mockNavigatorContext.goNamed(
            Screen.welcomeScreen.name,
          )).called(1);
    });
  });
}
