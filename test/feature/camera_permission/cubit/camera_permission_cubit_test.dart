import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/camera_permission/cubit/camera_permission_cubit.dart';
import 'package:evoapp/feature/camera_permission/cubit/camera_permission_state.dart';
import 'package:flutter_common_package/util/permission/device_permission.dart';
import 'package:flutter_common_package/util/permission/permission_handler_mixin.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class _MockCameraPermissionHandler extends Mock implements CameraPermissionHandler {}

void main() {
  setUpAll(() {
    registerFallbackValue(TsDevicePermission.unknown);
  });

  group('CameraPermissionHandler', () {
    test('should be PermissionHandlerMixin', () {
      const CameraPermissionHandler handler = CameraPermissionHandler();
      expect(handler, isA<PermissionHandlerMixin>());
    });
  });

  group('CameraPermissionCubit', () {
    late CameraPermissionHandler handler;
    late CameraPermissionCubit cubit;
    late PermissionHandlerCallback callback;

    setUp(() {
      handler = _MockCameraPermissionHandler();
      cubit = CameraPermissionCubit(handler: handler);
      callback = cubit;
    });

    Future<void> requestOnInit() {
      return handler.requestPermissionWhenPageIsInit(
        devicePermission: any(named: 'devicePermission'),
        callback: any(named: 'callback'),
      );
    }

    test('should request permission for camera and register handler callback', () {
      when(requestOnInit).thenAnswer((_) async {});

      cubit.requestPermission();

      final List<dynamic> captured = verify(() => handler.requestPermissionWhenPageIsInit(
            devicePermission: captureAny(named: 'devicePermission'),
            callback: captureAny(named: 'callback'),
          )).captured;
      expect(captured[0], TsDevicePermission.camera);
      expect(captured[1], callback);
    });

    test('should request permission on resume when state is CameraPermissionInitial', () {
      when(requestOnInit).thenAnswer((_) async {});
      cubit.emit(CameraPermissionInitial());
      cubit.requestPermissionOnResume();
      verify(requestOnInit).called(1);
    });

    test('should NOT request permission on resume when state is NOT CameraPermissionInitial', () {
      when(requestOnInit).thenAnswer((_) async {});
      cubit.emit(CameraPermissionGranted());
      cubit.requestPermissionOnResume();
      verifyNever(requestOnInit);

      cubit.emit(CameraPermissionDenied());
      cubit.requestPermissionOnResume();
      verifyNever(requestOnInit);
    });

    blocTest<CameraPermissionCubit, CameraPermissionState>(
      'should not emit the same state in a row',
      build: () => cubit,
      seed: () => CameraPermissionDenied(),
      act: (CameraPermissionCubit cubit) => cubit.emit(CameraPermissionDenied()),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <CameraPermissionState>[],
    );

    blocTest<CameraPermissionCubit, CameraPermissionState>(
      'should emit CameraPermissionGranted when permission is granted',
      setUp: () {
        when(requestOnInit).thenAnswer((_) async {
          callback.onGrantedPermission(TsDevicePermission.camera);
        });
      },
      build: () => cubit,
      act: (CameraPermissionCubit cubit) => cubit.requestPermission(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <CameraPermissionState>[
        CameraPermissionGranted(),
      ],
    );

    blocTest<CameraPermissionCubit, CameraPermissionState>(
      'should emit CameraPermissionDenied when permission is denied',
      setUp: () {
        when(requestOnInit).thenAnswer((_) async {
          callback.onDeniedPermission(TsDevicePermission.camera);
        });
      },
      build: () => cubit,
      act: (CameraPermissionCubit cubit) => cubit.requestPermission(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <CameraPermissionState>[
        CameraPermissionDenied(),
      ],
    );

    blocTest<CameraPermissionCubit, CameraPermissionState>(
      'should emit CameraPermissionInitial when permission already denied',
      build: () => cubit,
      seed: () => CameraPermissionDenied(),
      act: (CameraPermissionCubit cubit) => cubit.resetIfPermissionDenied(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <CameraPermissionState>[
        CameraPermissionInitial(),
      ],
    );

    blocTest<CameraPermissionCubit, CameraPermissionState>(
      'should not emit any states when permission is not denied',
      build: () => cubit,
      act: (CameraPermissionCubit cubit) => cubit.resetIfPermissionDenied(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <CameraPermissionState>[],
    );
  });
}
