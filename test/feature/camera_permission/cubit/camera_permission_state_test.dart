import 'package:evoapp/feature/camera_permission/cubit/camera_permission_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CameraPermissionState', () {
    test('Subtypes should have right type', () {
      expect(CameraPermissionInitial(), isA<CameraPermissionState>());
      expect(CameraPermissionGranted(), isA<CameraPermissionState>());
      expect(CameraPermissionDenied(), isA<CameraPermissionState>());
    });

    test('should be equatable', () {
      expect(CameraPermissionInitial(), equals(CameraPermissionInitial()));
      expect(CameraPermissionGranted(), equals(CameraPermissionGranted()));
      expect(CameraPermissionDenied(), equals(CameraPermissionDenied()));
    });
  });
}
