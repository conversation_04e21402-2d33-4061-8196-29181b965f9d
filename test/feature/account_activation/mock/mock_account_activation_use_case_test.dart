import 'package:evoapp/feature/account_activation/mock/mock_account_activation_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify getMockAccountActivationFileNameByCase should return corrected file names', () {
    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifyOtpChallengeType),
        'get_account_activation_verify_otp_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifySelfieChallengeType),
        'get_account_activation_verify_selfie_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getCreateUsernameChallengeType),
        'get_account_activation_create_username_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getCreateUsernameSuccess),
        'get_account_activation_create_user_name_success.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getCreateUsernameBadRequest),
        'get_account_activation_create_user_name_bad_request.json');
    expect(getMockAccountActivationFileNameByCase(MockAccountActivationUseCase.getCreatePinSuccess),
        'get_account_activation_create_pin_success.json');
    expect(
        getMockAccountActivationFileNameByCase(MockAccountActivationUseCase.getCreatePinBadRequest),
        'get_account_activation_create_pin_bad_request.json');
    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getEnableBiometricSuccess),
        'get_account_activation_enable_biometric_success.json');
    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getSkipEnableBiometricSuccess),
        'get_account_activation_skip_enable_biometric_success.json');
    expect(
        getMockAccountActivationFileNameByCase(MockAccountActivationUseCase.getNoneChallengeType),
        'get_none_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.verifySelfieChallengeTypeSuccessWithError),
        'verify_selfie_challenge_type_success_with_error.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.inputMobileNumberErrorLimitExceededForVerifySelfie),
        'input_mobile_number_error_limit_exceeded_for_verify_selfie.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getBiometricTokenChallengeType),
        'get_account_activation_biometric_token_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifyCardOtpChallengeType),
        'get_account_activation_verify_card_otp_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getActivateCardChallengeType),
        'get_account_activation_activate_card_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifyEmailChallengeType),
        'get_account_activation_verify_email_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifyEmailChallengeTypeEmailExists),
        'get_account_activation_verify_email_challenge_type_email_exists.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifyEmailErrorDuplicate),
        'get_account_activation_verify_email_error_duplicate.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getVerifyEmailOtpChallengeType),
        'get_account_activation_verify_email_otp_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getActivateCardOtpChallengeType),
        'get_account_activation_activate_card_otp_challenge_type.json');

    expect(
        getMockAccountActivationFileNameByCase(
            MockAccountActivationUseCase.getActivateCardOtpThirdPartyError),
        'get_account_activation_activate_card_otp_third_party_error.json');
  });
}
