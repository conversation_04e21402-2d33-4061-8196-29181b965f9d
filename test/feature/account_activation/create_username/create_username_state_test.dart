import 'package:evoapp/feature/account_activation/create_username/create_username_cubit.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Verify CreateUsernameState', () {
    test('should be a subclass of CreateUsernameState', () {
      final CreateUsernameInitial state = CreateUsernameInitial();
      expect(state, isA<CreateUsernameState>());
    });

    test('should be a subclass of CreateUsernameState', () {
      final CreateUsernameStateLoading state = CreateUsernameStateLoading();
      expect(state, isA<CreateUsernameState>());
    });

    test('should be a subclass of CreateUsernameState', () {
      final BaseEntity entity = BaseEntity();
      final CreateUsernameStateSuccess state = CreateUsernameStateSuccess(
        entity,
      );
      expect(state, isA<CreateUsernameState>());
    });

    test('should be a subclass of CreateUsernameState', () {
      final ErrorUIModel errorModel = ErrorUIModel();
      final CreateUsernameInvalidUsername state = CreateUsernameInvalidUsername(errorModel);
      expect(state, isA<CreateUsernameState>());
      expect(state.error, errorModel);
    });

    test('should be a subclass of CreateUsernameState', () {
      const String username = 'test-username';
      final ChangeUsernameState state = ChangeUsernameState(username);
      expect(state, isA<CreateUsernameState>());
      expect(state.username, username);
    });
    test('CreateUsernameBadRequest should be a subclass of CreateUsernameState', () {
      final ErrorUIModel errorModel = ErrorUIModel(userMessage: 'Bad request error');
      final CreateUsernameBadRequest state = CreateUsernameBadRequest(errorModel);
      expect(state, isA<CreateUsernameState>());
      expect(state.error, errorModel);
    });

    test('CreateUsernameInvalidToken should be a subclass of CreateUsernameState', () {
      final CreateUsernameInvalidToken state = CreateUsernameInvalidToken();
      expect(state, isA<CreateUsernameState>());
    });

    test('CreateUsernameFailed should be a subclass of CreateUsernameState', () {
      final ErrorUIModel errorModel = ErrorUIModel(userMessage: 'General failure');
      final CreateUsernameFailed state = CreateUsernameFailed(errorModel);
      expect(state, isA<CreateUsernameState>());
      expect(state.error, errorModel);
    });
  });
}
