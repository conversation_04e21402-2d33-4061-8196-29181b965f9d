import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/create_username/create_username_cubit.dart';
import 'package:evoapp/util/validator/username_validator.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class MockUsernameValidator extends Mock implements UsernameValidator {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  late CreateUsernameCubit cubit;
  late MockUsernameValidator usernameValidator;
  late MockAuthenticationRepo mockAuthRepo;

  setUpAll(() {
    registerFallbackValue(ActivateAccountCreateUsernameRequest(
      username: '',
      sessionToken: '',
    ));
  });

  setUp(() {
    usernameValidator = MockUsernameValidator();
    mockAuthRepo = MockAuthenticationRepo();

    cubit = CreateUsernameCubit(
      authRepo: mockAuthRepo,
      usernameValidator: usernameValidator,
    );

    when(() => mockAuthRepo.activateAccount(
        request: any(named: 'request'),
        mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
      return AccountActivationEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{},
      ));
    });

    when(() => usernameValidator.validate(any())).thenReturn(null);
  });

  tearDown(() {
    cubit.close();
  });

  group('CreateUsernameCubit', () {
    test('initial state should be CreateUsernameInitial', () {
      expect(cubit.state, isA<CreateUsernameInitial>());
    });

    blocTest<CreateUsernameCubit, CreateUsernameState>(
      'should emit ChangeUsernameState with provided username',
      build: () => cubit,
      act: (CreateUsernameCubit cubit) => cubit.updateUsername('testuser'),
      expect: () => <dynamic>[
        isA<ChangeUsernameState>()
            .having((ChangeUsernameState state) => state.username, 'username', 'testuser'),
      ],
    );

    blocTest<CreateUsernameCubit, CreateUsernameState>(
      'should emit CreateUsernameInvalidUsername when validator returns error',
      setUp: () {
        when(() => usernameValidator.validate(any())).thenReturn('error');
      },
      build: () => cubit,
      act: (CreateUsernameCubit cubit) async {
        await cubit.submit(username: '!!_speci@l_characters_!!', sessionToken: '');
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CreateUsernameInvalidUsername>().having(
            (CreateUsernameInvalidUsername state) => state.error.userMessage,
            'error message',
            'error'),
      ],
    );

    blocTest<CreateUsernameCubit, CreateUsernameState>(
      'should emit CreateUsernameStateSuccess when validation passes and submission succeeds',
      build: () => cubit,
      act: (CreateUsernameCubit cubit) async {
        await cubit.submit(username: 'validUsername', sessionToken: '');
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CreateUsernameStateLoading>(),
        isA<CreateUsernameStateSuccess>(),
      ],
    );

    test('should call AuthenticationRepo.activateAccount with correct request parameters',
        () async {
      const String testUsername = 'testUsername';
      const String testSessionToken = 'testSessionToken';
      await cubit.submit(username: testUsername, sessionToken: testSessionToken);

      final ActivateAccountCreateUsernameRequest capturedRequest =
          verify(() => mockAuthRepo.activateAccount(
                request: captureAny(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).captured.first as ActivateAccountCreateUsernameRequest;

      expect(capturedRequest.username, testUsername);
      expect(capturedRequest.sessionToken, testSessionToken);
    });

    blocTest<CreateUsernameCubit, CreateUsernameState>(
      'should emit CreateUsernameInvalidToken when server returns INVALID_TOKEN',
      setUp: () {
        when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
          return AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.INVALID_TOKEN,
            response: <String, dynamic>{},
          ));
        });
      },
      build: () => cubit,
      act: (CreateUsernameCubit cubit) async {
        await cubit.submit(username: 'validUsername', sessionToken: '');
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CreateUsernameStateLoading>(),
        isA<CreateUsernameInvalidToken>(),
      ],
    );

    blocTest<CreateUsernameCubit, CreateUsernameState>(
      'should emit CreateUsernameBadRequest when server returns BAD_REQUEST',
      setUp: () {
        when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
          return AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: <String, dynamic>{
              'data': {'user_message': 'error_message_400'}
            },
          ));
        });
      },
      build: () => cubit,
      act: (CreateUsernameCubit cubit) async {
        await cubit.submit(username: 'validUsername', sessionToken: '');
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CreateUsernameStateLoading>(),
        isA<CreateUsernameBadRequest>().having(
            (CreateUsernameBadRequest state) => state.error.userMessage,
            'verify user_message',
            'error_message_400'),
      ],
    );

    blocTest<CreateUsernameCubit, CreateUsernameState>(
      'should emit CreateUsernameFailed when server returns other error',
      setUp: () {
        when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
          return AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.UNKNOWN_ERRORS,
            response: <String, dynamic>{'message': 'Server error'},
          ));
        });
      },
      build: () => cubit,
      act: (CreateUsernameCubit cubit) async {
        await cubit.submit(username: 'validUsername', sessionToken: '');
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<CreateUsernameStateLoading>(),
        isA<CreateUsernameFailed>(),
      ],
    );
  });
}
