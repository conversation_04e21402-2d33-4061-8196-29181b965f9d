import 'package:evoapp/feature/account_activation/create_username/create_username_cubit.dart';
import 'package:evoapp/feature/account_activation/create_username/create_username_screen.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/widget/appbar/evo_appbar.dart';
import 'package:evoapp/widget/banners/info_banner_widget.dart';
import 'package:evoapp/widget/evo_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockOnPopSuccess extends Mock {
  void call(ChallengeSuccessModel model);
}

class MockErrorCallback extends Mock {
  void call(ErrorUIModel? errorUIModel);
}

class MockCreateUsernameCubit extends AppMockCubit<CreateUsernameState>
    implements CreateUsernameCubit {}

class MockCreateUsernameScreen extends CreateUsernameScreen {
  final MockErrorCallback? mockErrorCb;

  const MockCreateUsernameScreen({
    required super.sessionToken,
    required super.onPopSuccess,
    this.mockErrorCb,
    super.key,
  });

  @override
  State<CreateUsernameScreen> createState() => MockCreateUsernameScreenState();
}

class MockCreateUsernameScreenState extends CreateUsernameScreenState {
  @override
  Future<void> handleEvoApiError(ErrorUIModel? errorUIModel) async {
    (widget as MockCreateUsernameScreen).mockErrorCb?.call(errorUIModel);
  }
}

void main() {
  late MockOnPopSuccess mockCb;
  late CreateUsernameCubit mockCubit;
  final String mockSessionToken = 'mock-session-token';
  late MockErrorCallback mockErrorCb;

  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();

    registerFallbackValue(ChallengeSuccessModel());
  });

  setUp(() {
    mockCb = MockOnPopSuccess();
    mockErrorCb = MockErrorCallback();
    mockCubit = MockCreateUsernameCubit()..emit(CreateUsernameInitial());
    registerFallbackValue(BaseEntity());
    registerFallbackValue(SessionDialogType.activateAccount);

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});
    when(() => mockDialogFunction.showDialogSessionTokenExpired(
          type: any(named: 'type'),
          onClickPositive: any(named: 'onClickPositive'),
        )).thenAnswer((_) async {});
  });
  test('CreateUsernameScreenArgs', () {
    final ChallengeSuccessModel model = ChallengeSuccessModel();
    final CreateUsernameScreenArgs args = CreateUsernameScreenArgs(
      onPopSuccess: mockCb.call,
      sessionToken: mockSessionToken,
    );

    args.onPopSuccess(model);

    verify(() => mockCb.call(model)).called(1);
  });

  test('pushNamed', () {
    CreateUsernameScreen.pushNamed(
      onPopSuccess: mockCb.call,
      sessionToken: mockSessionToken,
    );

    verify(() => mockNavigatorContext.pushNamed(
          Screen.createUsernameScreen.name,
          extra: any(named: 'extra'),
        )).called(1);
  });

  buildWidget(WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider<CreateUsernameCubit>.value(
            value: mockCubit,
            child: MockCreateUsernameScreen(
              onPopSuccess: mockCb.call,
              mockErrorCb: mockErrorCb,
              sessionToken: mockSessionToken,
            )),
      ),
    );
  }

  group('verify build widget', () {
    testWidgets('should initially render correctly', (WidgetTester tester) async {
      await buildWidget(tester);

      /// verify title
      expect(find.byType(EvoAppBar), findsOneWidget);
      expect(find.text(EvoStrings.activeAccountCreateUsernameTitle), findsOneWidget);
      expect(find.text(EvoStrings.activeAccountCreateUsernameDesc), findsOneWidget);

      expect(
          find.byWidgetPredicate(
            (Widget widget) =>
                widget is EvoTextField && widget.hintText == EvoStrings.usernameLabel,
          ),
          findsOneWidget);

      expect(
          find.byWidgetPredicate((Widget widget) =>
              widget is InfoBannerWidget &&
              widget.info.contains(
                EvoStrings.usernameGuide1,
              ) &&
              widget.info.contains(
                EvoStrings.usernameGuide2,
              ) &&
              widget.info.contains(
                EvoStrings.usernameGuide3,
              ) &&
              widget.info.contains(
                EvoStrings.usernameGuide4,
              )),
          findsOneWidget);

      expect(find.text(EvoStrings.ctaSubmit), findsOneWidget);
    });

    testWidgets('should show hud loading when emit [CreateUsernameStateLoading]',
        (WidgetTester tester) async {
      await buildWidget(tester);

      mockCubit.emit(CreateUsernameStateLoading());
      await tester.pump();

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
    });

    testWidgets('should show error message when emit [CreateUsernameStateFailed]',
        (WidgetTester tester) async {
      final String mockUserMessage = 'mock-user-message';
      final ErrorUIModel errorMessage = ErrorUIModel(userMessage: mockUserMessage);

      await buildWidget(tester);

      mockCubit.emit(CreateUsernameInvalidUsername(errorMessage));
      await tester.pump();

      expect(
          find.byWidgetPredicate(
              (Widget widget) => widget is EvoTextField && widget.errMessage == mockUserMessage),
          findsOneWidget);
    });

    testWidgets('should show error message when emit [CreateUsernameBadRequest]',
        (WidgetTester tester) async {
      final String mockUserMessage = 'mock-user-message';
      final ErrorUIModel errorMessage = ErrorUIModel(userMessage: mockUserMessage);

      await buildWidget(tester);

      mockCubit.emit(CreateUsernameRequestError(errorMessage));
      await tester.pump();

      expect(
          find.byWidgetPredicate(
              (Widget widget) => widget is EvoTextField && widget.errMessage == mockUserMessage),
          findsOneWidget);
    });

    testWidgets(
        'should call onPopSuccess with correct entity when emit [CreateUsernameStateSuccess]',
        (WidgetTester tester) async {
      // Create a specific entity to test with
      final BaseEntity mockEntity = BaseEntity();

      await buildWidget(tester);

      mockCubit.emit(CreateUsernameStateSuccess(mockEntity));
      await tester.pump();

      // Verify onPopSuccess is called with the correct model
      final ChallengeSuccessModel model =
          verify(() => mockCb.call(captureAny())).captured.single as ChallengeSuccessModel;
      expect(identical(model.entity, mockEntity), isTrue);
    });

    testWidgets('should show session token expired dialog when emit [CreateUsernameInvalidToken]',
        (WidgetTester tester) async {
      await buildWidget(tester);

      mockCubit.emit(CreateUsernameInvalidToken());
      await tester.pump();

      // Verify the dialog is shown with the correct parameters
      verify(() => mockDialogFunction.showDialogSessionTokenExpired(
            type: SessionDialogType.activateAccount,
          )).called(1);
    });

    testWidgets('should handle API error when emit [CreateUsernameFailed]',
        (WidgetTester tester) async {
      final ErrorUIModel mockError = ErrorUIModel(userMessage: 'mock-error-message');

      // Set up mock for handleEvoApiError
      when(() => mockErrorCb.call(any())).thenAnswer((_) {});

      await buildWidget(tester);

      mockCubit.emit(CreateUsernameFailed(mockError));
      await tester.pump();

      // Verify handleEvoApiError is called with the correct error
      verify(() => mockErrorCb.call(mockError)).called(1);
    });

    testWidgets('should handle onTapSubmit correctly', (WidgetTester tester) async {
      final String mockInputText = 'mock-input-text';
      when(() => mockCubit.submit(
            username: any(named: 'username'),
            sessionToken: any(named: 'sessionToken'),
          )).thenAnswer((_) async {});

      await buildWidget(tester);

      // enable submit button
      mockCubit.emit(ChangeUsernameState(mockInputText));
      await tester.pump();

      // enter text field
      await tester.enterText(find.byType(EvoTextField), mockInputText);

      // tap submit button
      await tester.tap(find.text(EvoStrings.ctaSubmit));
      await tester.pump();

      verify(() => mockCubit.submit(username: mockInputText, sessionToken: mockSessionToken))
          .called(1);
    });
  });
}
