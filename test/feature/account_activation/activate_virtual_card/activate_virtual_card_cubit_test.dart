// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/activate_virtual_card/cubit/activate_virtual_card_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  late MockAuthenticationRepo mockAuthRepo;
  late ActivateVirtualCardCubit cubit;

  setUpAll(() {
    mockAuthRepo = MockAuthenticationRepo();

    registerFallbackValue(ActivateVirtualCardInitial());
    registerFallbackValue(ActivateAccountActivateCardRequest(skip: true, sessionToken: ''));
  });

  setUp(() {
    cubit = ActivateVirtualCardCubit(authRepo: mockAuthRepo);

    when(
      () => mockAuthRepo.activateAccount(
        request: any(named: 'request'),
        mockConfig: any(named: 'mockConfig'),
      ),
    ).thenAnswer(
      (_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
        response: <String, dynamic>{},
        statusCode: CommonHttpClient.SUCCESS,
      )),
    );
  });

  tearDown(() {
    cubit.close();
  });

  group('initial state', () {
    test('should be ActivateVirtualCardInitial', () {
      expect(cubit.state, isA<ActivateVirtualCardInitial>());
    });
  });

  group('activate', () {
    const String sessionToken = 'test-session-token';
    const bool skipFlag = false;

    setUpAll(() {
      registerFallbackValue(ActivateAccountActivateCardRequest(
        skip: skipFlag,
        sessionToken: sessionToken,
      ));
    });

    void verifyActivateAccountRequest(
        MockAuthenticationRepo mockAuthRepo, String? sessionToken, bool skip) {
      verify(() => mockAuthRepo.activateAccount(
            request: any(
              named: 'request',
              that: isA<ActivateAccountActivateCardRequest>().having(
                (ActivateAccountActivateCardRequest req) => (req.sessionToken, req.skip),
                'verify request parameters',
                (sessionToken, skip),
              ),
            ),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    }

    blocTest<ActivateVirtualCardCubit, ActivateVirtualCardState>(
      'should emit ActivateVirtualCardLoading and ActivateVirtualCardSuccess when activation is successful',
      setUp: () {
        when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
              response: <String, dynamic>{},
              statusCode: CommonHttpClient.SUCCESS,
            )));
      },
      build: () => cubit,
      act: (ActivateVirtualCardCubit cubit) =>
          cubit.activate(sessionToken: sessionToken, skip: skipFlag),
      expect: () => <TypeMatcher<ActivateVirtualCardState>>[
        isA<ActivateVirtualCardLoading>(),
        isA<ActivateVirtualCardSuccess>(),
      ],
      wait: TestConstant.blocEmitStateDelayDuration,
      verify: (_) => verifyActivateAccountRequest(mockAuthRepo, sessionToken, skipFlag),
    );

    blocTest<ActivateVirtualCardCubit, ActivateVirtualCardState>(
      'should emit ActivateVirtualCardLoading and ActivateVirtualCardFailureTokenExpired when status code is INVALID_TOKEN',
      setUp: () {
        when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
              response: <String, dynamic>{},
              statusCode: CommonHttpClient.INVALID_TOKEN,
            )));
      },
      build: () => cubit,
      act: (ActivateVirtualCardCubit cubit) =>
          cubit.activate(sessionToken: sessionToken, skip: skipFlag),
      expect: () => <TypeMatcher<ActivateVirtualCardState>>[
        isA<ActivateVirtualCardLoading>(),
        isA<ActivateVirtualCardFailureTokenExpired>(),
      ],
      wait: TestConstant.blocEmitStateDelayDuration,
      verify: (_) => verifyActivateAccountRequest(mockAuthRepo, sessionToken, skipFlag),
    );

    blocTest<ActivateVirtualCardCubit, ActivateVirtualCardState>(
      'should emit ActivateVirtualCardLoading and ActivateVirtualCardFailureLimitExceeded when status code is LIMIT_EXCEEDED',
      setUp: () {
        when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
              response: <String, dynamic>{},
              statusCode: CommonHttpClient.LIMIT_EXCEEDED,
            )));
      },
      build: () => cubit,
      act: (ActivateVirtualCardCubit cubit) =>
          cubit.activate(sessionToken: sessionToken, skip: skipFlag),
      expect: () => <TypeMatcher<ActivateVirtualCardState>>[
        isA<ActivateVirtualCardLoading>(),
        isA<ActivateVirtualCardFailureLimitExceeded>(),
      ],
      wait: TestConstant.blocEmitStateDelayDuration,
      verify: (_) => verifyActivateAccountRequest(mockAuthRepo, sessionToken, skipFlag),
    );

    blocTest<ActivateVirtualCardCubit, ActivateVirtualCardState>(
      'should emit ActivateVirtualCardLoading and ActivateVirtualCardFailureLimitExceeded when status code is LOCKED_RESOURCE',
      setUp: () {
        when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
              response: <String, dynamic>{},
              statusCode: CommonHttpClient.LOCKED_RESOURCE,
            )));
      },
      build: () => cubit,
      act: (ActivateVirtualCardCubit cubit) =>
          cubit.activate(sessionToken: sessionToken, skip: skipFlag),
      expect: () => <TypeMatcher<ActivateVirtualCardState>>[
        isA<ActivateVirtualCardLoading>(),
        isA<ActivateVirtualCardFailureLimitExceeded>(),
      ],
      wait: TestConstant.blocEmitStateDelayDuration,
      verify: (_) => verifyActivateAccountRequest(mockAuthRepo, sessionToken, skipFlag),
    );

    blocTest<ActivateVirtualCardCubit, ActivateVirtualCardState>(
      'should emit ActivateVirtualCardLoading and ActivateVirtualCardFailure for other error status codes',
      setUp: () {
        when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
              response: <String, dynamic>{},
              statusCode: 500,
            )));
      },
      build: () => cubit,
      act: (ActivateVirtualCardCubit cubit) =>
          cubit.activate(sessionToken: sessionToken, skip: skipFlag),
      expect: () => <TypeMatcher<ActivateVirtualCardState>>[
        isA<ActivateVirtualCardLoading>(),
        isA<ActivateVirtualCardFailure>(),
      ],
      wait: TestConstant.blocEmitStateDelayDuration,
      verify: (_) => verifyActivateAccountRequest(mockAuthRepo, sessionToken, skipFlag),
    );

    blocTest<ActivateVirtualCardCubit, ActivateVirtualCardState>(
      'should pass skip=true parameter correctly',
      build: () => cubit,
      act: (ActivateVirtualCardCubit cubit) =>
          cubit.activate(sessionToken: sessionToken, skip: true),
      expect: () => <TypeMatcher<ActivateVirtualCardState>>[
        isA<ActivateVirtualCardLoading>(),
        isA<ActivateVirtualCardSuccess>(),
      ],
      wait: TestConstant.blocEmitStateDelayDuration,
      verify: (_) => verifyActivateAccountRequest(mockAuthRepo, sessionToken, true),
    );
  });
}
