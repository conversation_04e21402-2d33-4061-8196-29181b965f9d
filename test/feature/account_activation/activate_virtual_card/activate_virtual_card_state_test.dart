import 'package:evoapp/feature/account_activation/activate_virtual_card/cubit/activate_virtual_card_cubit.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ActivateVirtualCardState', () {
    test('basic states should be instances of ActivateVirtualCardState', () {
      expect(ActivateVirtualCardInitial(), isA<ActivateVirtualCardState>());
      expect(ActivateVirtualCardLoading(), isA<ActivateVirtualCardState>());
      expect(ActivateVirtualCardFailureTokenExpired(), isA<ActivateVirtualCardState>());
    });

    test('ActivateVirtualCardSuccess should have correct properties', () {
      final BaseEntity entity = BaseEntity();
      final ActivateVirtualCardSuccess state =
          ActivateVirtualCardSuccess(entity: entity, isCardActivated: true);

      expect(state, isA<ActivateVirtualCardState>());
      expect(state.entity, equals(entity));
      expect(state.isCardActivated, true);
    });

    test('ActivateVirtualCardFailure should have correct properties', () {
      final ErrorUIModel error = ErrorUIModel();
      final ActivateVirtualCardFailure state = ActivateVirtualCardFailure(error: error);

      expect(state, isA<ActivateVirtualCardState>());
      expect(state.error, equals(error));
    });

    test('ActivateVirtualCardFailureLimitExceeded should have correct properties', () {
      final ErrorUIModel error = ErrorUIModel();
      final ActivateVirtualCardFailureLimitExceeded state =
          ActivateVirtualCardFailureLimitExceeded(error: error);

      expect(state, isA<ActivateVirtualCardState>());
      expect(state, isA<ActivateVirtualCardFailure>());
      expect(state.error, equals(error));
    });
  });
}
