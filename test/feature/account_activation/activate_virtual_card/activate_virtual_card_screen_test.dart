import 'package:evoapp/feature/account_activation/activate_virtual_card/activate_virtual_card_screen.dart';
import 'package:evoapp/feature/account_activation/activate_virtual_card/cubit/activate_virtual_card_cubit.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:evoapp/widget/no_app_bar_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockActivateVirtualCardCubit extends AppMockCubit<ActivateVirtualCardState>
    implements ActivateVirtualCardCubit {}

class MockOnPopSuccess extends Mock {
  void call(ChallengeSuccessModel model);
}

void main() {
  late ActivateVirtualCardCubit cubit;
  final MockOnPopSuccess onPopSuccess = MockOnPopSuccess();
  const String sessionToken = 'session_token';

  setUpAll(() {
    initConfigEvoPageStateBase();

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});

    registerFallbackValue(ChallengeSuccessModel());
    registerFallbackValue(SessionDialogType.activateAccount);

    when(() => mockDialogFunction.showDialogErrorLimitExceeded(
          type: any(named: 'type'),
          content: any(named: 'content'),
        )).thenAnswer((_) async {});
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    cubit = MockActivateVirtualCardCubit()..emit(ActivateVirtualCardInitial());
    reset(onPopSuccess);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('ActivateVirtualCardScreen', () {
    Widget buildWidgetInTest() {
      return MaterialApp(
        home: BlocProvider<ActivateVirtualCardCubit>(
          create: (_) => cubit,
          child: ActivateVirtualCardScreen(
            sessionToken: sessionToken,
            onPopSuccess: onPopSuccess.call,
          ),
        ),
      );
    }

    testWidgets('should have needed widgets', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      expect(find.byType(NoAppBarWrapper), findsOneWidget);
      expect(find.byType(PopScope), findsOneWidget);

      expect(find.text(EvoStrings.activateVirtualCardTitle), findsOneWidget);
      expect(find.text(EvoStrings.activateVirtualCardDesc), findsOneWidget);

      verify(() => evoImageProvider.asset(
            EvoImages.imgActivateVirtualCard,
            height: any(named: 'height'),
            fit: any(named: 'fit'),
          )).called(1);

      expect(find.widgetWithText(PrimaryButton, EvoStrings.ctaActivateVirtualCard), findsOneWidget);
      expect(find.widgetWithText(TertiaryButton, EvoStrings.ctaActivateLater), findsOneWidget);
    });

    test('should self navigate on push', () {
      ActivateVirtualCardScreen.pushNamed(
        sessionToken: sessionToken,
        onPopSuccess: (_) {},
      );
      verify(() => mockNavigatorContext.pushNamed(
            Screen.activateVirtualCardScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should call cubit.activate and skip is false when activate button is tapped',
        (WidgetTester tester) async {
      when(() => cubit.activate(
            sessionToken: any(named: 'sessionToken'),
            skip: any(named: 'skip'),
          )).thenAnswer((_) async {});

      await tester.pumpWidget(buildWidgetInTest());

      await tester.tap(find.text(EvoStrings.ctaActivateVirtualCard));

      verify(() => cubit.activate(sessionToken: sessionToken, skip: false)).called(1);
    });

    testWidgets('should call cubit.activate and skip is true when activate button is tapped',
        (WidgetTester tester) async {
      when(() => cubit.activate(
            sessionToken: any(named: 'sessionToken'),
            skip: any(named: 'skip'),
          )).thenAnswer((_) async {});

      await tester.pumpWidget(buildWidgetInTest());

      await tester.tap(find.text(EvoStrings.ctaActivateLater));

      verify(() => cubit.activate(sessionToken: sessionToken, skip: true)).called(1);
    });

    testWidgets('should showHudLoading when state is ActivateVirtualCardLoading',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      await cubit.emit(ActivateVirtualCardLoading());
      await tester.pump();

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
    });

    testWidgets('should hideHudLoading when state is NOT ActivateVirtualCardLoading',
        (WidgetTester tester) async {
      final BaseEntity entity = BaseEntity();

      await tester.pumpWidget(buildWidgetInTest());

      await cubit.emit(ActivateVirtualCardFailureTokenExpired());
      await tester.pump();

      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
    });

    testWidgets(
        'should call onPopSuccess when state is ActivateVirtualCardSuccess and skip is true',
        (WidgetTester tester) async {
      final BaseEntity entity = BaseEntity();

      await tester.pumpWidget(buildWidgetInTest());

      await cubit.emit(ActivateVirtualCardSuccess(entity: entity, isCardActivated: true));
      await tester.pump();

      verify(() => onPopSuccess(any(
            that: isA<ChallengeSuccessModel>().having(
              (ChallengeSuccessModel m) =>
                  (m.entity, m.isCardActivated, m.resendData?.sessionToken),
              'should have entity, isCardActivated, and resendData with sessionToken',
              (entity, true, sessionToken),
            ),
          ))).called(1);
    });

    testWidgets(
        'should call onPopSuccess when state is ActivateVirtualCardSuccess and skip is false',
        (WidgetTester tester) async {
      final BaseEntity entity = BaseEntity();

      await tester.pumpWidget(buildWidgetInTest());

      await cubit.emit(ActivateVirtualCardSuccess(entity: entity, isCardActivated: false));
      await tester.pump();

      verify(() => onPopSuccess(any(
            that: isA<ChallengeSuccessModel>().having(
              (ChallengeSuccessModel m) => (m.entity, m.isCardActivated),
              'should have entity',
              (entity, false),
            ),
          ))).called(1);
    });

    testWidgets(
        'should show token expired dialog when state is ActivateVirtualCardFailureTokenExpired',
        (WidgetTester tester) async {
      when(() => mockDialogFunction.showDialogSessionTokenExpired(
            onClickPositive: any(named: 'onClickPositive'),
            type: any(named: 'type'),
          )).thenAnswer((_) async {});

      await tester.pumpWidget(buildWidgetInTest());

      await cubit.emit(ActivateVirtualCardFailureTokenExpired());
      await tester.pump();

      final VoidCallback onClickPositive = verify(
        () => mockDialogFunction.showDialogSessionTokenExpired(
          onClickPositive: captureAny(named: 'onClickPositive'),
          type: any(named: 'type', that: equals(SessionDialogType.activateAccount)),
        ),
      ).captured.first as VoidCallback;

      onClickPositive.call();

      verify(() => mockNavigatorContext.popUntilNamed(Screen.mobileNumberCheckScreen.name))
          .called(1);
    });

    testWidgets(
        'should call showDialogConfirm when state is ActivateVirtualCardFailureLimitExceeded',
        (WidgetTester tester) async {
      final String userMessage = 'Try again later';
      final ErrorUIModel error = ErrorUIModel(userMessage: userMessage);

      await tester.pumpWidget(buildWidgetInTest());

      await cubit.emit(ActivateVirtualCardFailureLimitExceeded(error: error));
      await tester.pump();

      verify(() => mockDialogFunction.showDialogErrorLimitExceeded(
            type: SessionDialogType.activateAccount,
            content: userMessage,
          )).called(1);
    });
  });
}
