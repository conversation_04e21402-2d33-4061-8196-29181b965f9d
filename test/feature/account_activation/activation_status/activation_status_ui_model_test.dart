import 'package:evoapp/feature/account_activation/activation_status/activation_status_screen.dart';
import 'package:evoapp/feature/account_activation/activation_status/activation_status_ui_model.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavor_manager.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/url_launcher_uri_wrapper.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/url_launcher.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class MockUrlLauncherWrapper extends Mock implements UrlLauncherWrapper {}

late MockUrlLauncherWrapper mockUrlLauncherWrapper;

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();

    // Setup URL launcher wrapper mock
    mockUrlLauncherWrapper = MockUrlLauncherWrapper();
    getIt.registerSingleton<UrlLauncherWrapper>(mockUrlLauncherWrapper);

    // Register fallback values for URL launcher
    registerFallbackValue(Uri.parse('https://example.com'));
    registerFallbackValue(LaunchMode.externalApplication);
  });

  group('ActivationStatusUiModel', () {
    group('create', () {
      test('creates RejectedStatusUiModel for rejected status', () {
        final ActivationStatusUiModel model =
            ActivationStatusUiModel.create(ActivationStatus.rejected);
        expect(model, isA<RejectedStatusUiModel>());
      });

      test('creates ProcessingStatusUiModel for processing status', () {
        final ActivationStatusUiModel model =
            ActivationStatusUiModel.create(ActivationStatus.processing);
        expect(model, isA<ProcessingStatusUiModel>());
      });

      test('creates NoneStatusUiModel for none status', () {
        final ActivationStatusUiModel model = ActivationStatusUiModel.create(ActivationStatus.none);
        expect(model, isA<NoneStatusUiModel>());
      });

      test('creates ExistStatusUiModel for exist status', () {
        final ActivationStatusUiModel model =
            ActivationStatusUiModel.create(ActivationStatus.exist);
        expect(model, isA<ExistStatusUiModel>());
      });

      test('creates ExistStatusUiModel for cancelled status', () {
        final ActivationStatusUiModel model =
            ActivationStatusUiModel.create(ActivationStatus.cancelled);
        expect(model, isA<CancelledStatusUiModel>());
      });
    });

    group('RejectedStatusUiModel', () {
      late RejectedStatusUiModel model;

      setUp(() {
        model = RejectedStatusUiModel();
      });

      test('has correct title', () {
        expect(model.title, equals(EvoStrings.activationRejectedTitle));
      });

      test('has correct description', () {
        expect(model.description, equals(EvoStrings.activationRejectedDesc));
      });

      test('has correct icon asset', () {
        expect(model.iconAsset, equals(EvoImages.imgActivationStatusReject));
      });

      testWidgets('verify button', (tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Column(
            children: model.actions,
          ),
        ));

        await tester.tap(find.text(EvoStrings.backToHomePage));

        verify(() => mockNavigatorContext.goNamed(
              Screen.welcomeScreen.name,
              extra: any(named: 'extra'),
            )).called(1);
      });
    });

    group('ProcessingStatusUiModel', () {
      late ProcessingStatusUiModel model;

      setUp(() {
        model = ProcessingStatusUiModel();
      });

      test('has correct title', () {
        expect(model.title, equals(EvoStrings.activationProcessingTitle));
      });

      test('has correct description', () {
        expect(model.description, equals(EvoStrings.activationProcessingDesc));
      });

      test('has correct icon asset', () {
        expect(model.iconAsset, equals(EvoImages.imgActivationStatusProcessing));
      });

      test('has one action button', () {
        expect(model.actions.length, equals(1));
        expect(model.actions[0], isA<PrimaryButton>());
      });

      testWidgets('verify button', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Column(
            children: model.actions,
          ),
        ));

        await tester.tap(find.text(EvoStrings.backToHomePage));

        verify(() => mockNavigatorContext.goNamed(
              Screen.welcomeScreen.name,
              extra: any(named: 'extra'),
            )).called(1);
      });
    });

    group('NoneStatusUiModel', () {
      late NoneStatusUiModel model;

      setUp(() {
        model = NoneStatusUiModel();
      });

      test('has correct title', () {
        expect(model.title, equals(EvoStrings.activationNotFoundTitle));
      });

      test('has correct description', () {
        expect(model.description, equals(EvoStrings.activationNotFoundDesc));
      });

      test('has correct icon asset', () {
        expect(model.iconAsset, equals(EvoImages.imgActivationStatusNone));
      });

      test('has two action buttons', () {
        expect(model.actions.length, equals(2));
        expect(model.actions[0], isA<PrimaryButton>());
        expect(model.actions[1], isA<SecondaryButton>());
      });

      testWidgets('verify back to home button navigation', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Column(
            children: model.actions,
          ),
        ));

        await tester.tap(find.text(EvoStrings.backToHomePage));

        verify(() => mockNavigatorContext.goNamed(
              Screen.welcomeScreen.name,
              extra: any(named: 'extra'),
            )).called(1);
      });

      group('ctaApplyNow button DOP link functionality', () {
        setUp(() {
          // Setup default mock behavior for URL launcher
          when(() => mockUrlLauncherWrapper.launchUrl(
                any(),
                mode: any(named: 'mode'),
              )).thenAnswer((_) async => true);
        });

        tearDown(() {
          reset(mockUrlLauncherWrapper);
        });

        testWidgets('should launch staging DOP link when flavor is stag',
            (WidgetTester tester) async {
          // Arrange - Set up staging flavor
          FlavorConfig(
            flavor: FlavorType.stag.name,
            values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
          );

          await tester.pumpWidget(MaterialApp(
            home: Column(
              children: model.actions,
            ),
          ));

          // Act
          await tester.tap(find.text(EvoStrings.ctaApplyNow));

          // Assert - Verify URL launcher was called with staging DOP link
          verify(() => mockUrlLauncherWrapper.launchUrl(any(),
              mode: any(
                named: 'mode',
                that: equals(LaunchMode.externalApplication),
              ))).called(1);
        });

        testWidgets('should launch UAT DOP link when flavor is uat', (WidgetTester tester) async {
          // Arrange - Set up UAT flavor
          FlavorConfig(
            flavor: FlavorType.uat.name,
            values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
          );

          await tester.pumpWidget(MaterialApp(
            home: Column(
              children: model.actions,
            ),
          ));

          // Act
          await tester.tap(find.text(EvoStrings.ctaApplyNow));

          // Assert - Verify URL launcher was called with UAT DOP link
          verify(() => mockUrlLauncherWrapper.launchUrl(any(),
              mode: any(
                named: 'mode',
                that: equals(LaunchMode.externalApplication),
              ))).called(1);
        });

        test('should throw UnimplementedError when trying to get prod DOP link', () {
          // Arrange - Set up prod flavor
          FlavorConfig(
            flavor: FlavorType.prod.name,
            values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
          );

          // Act & Assert - Production flavor should throw UnimplementedError
          expect(() => FlavorManager.getDOPLink(), throwsUnimplementedError);

          // Verify that the button exists but we can't test the tap due to the exception
          expect(model.actions.length, equals(2));
          expect(model.actions[0], isA<PrimaryButton>());
        });
      });

      test('should have correct button text in actions', () {
        // Check that the actions contain buttons with correct text
        expect(model.actions.length, equals(2));
        expect(model.actions[0], isA<PrimaryButton>());
        expect(model.actions[1], isA<SecondaryButton>());
      });
    });

    group('ExistStatusUiModel', () {
      late ExistStatusUiModel model;

      setUp(() {
        model = ExistStatusUiModel();
      });

      test('has correct title', () {
        expect(model.title, equals(EvoStrings.activationExistTitle));
      });

      test('has correct description', () {
        expect(model.description, equals(EvoStrings.activationExistDesc));
      });

      test('has correct icon asset', () {
        expect(model.iconAsset, equals(EvoImages.imgActivationStatusExisting));
      });

      test('has two action buttons', () {
        expect(model.actions.length, equals(2));
        expect(model.actions[0], isA<PrimaryButton>());
        expect(model.actions[1], isA<SecondaryButton>());
      });

      testWidgets('verify button', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Column(
            children: model.actions,
          ),
        ));

        await tester.tap(find.text(EvoStrings.backToHomePage));

        verify(() => mockNavigatorContext.goNamed(
              Screen.welcomeScreen.name,
              extra: any(named: 'extra'),
            )).called(1);

        await tester.tap(find.text(EvoStrings.login));

        verify(() => mockNavigatorContext.goNamed(
              Screen.verifyUsernameScreen.name,
              extra: any(named: 'extra'),
            )).called(1);
      });
    });

    group('CancelledStatusUiModel', () {
      late CancelledStatusUiModel model;

      setUp(() {
        model = CancelledStatusUiModel();
      });

      test('has correct title', () {
        expect(model.title, equals(EvoStrings.activationCancelledTitle));
      });

      test('has correct description', () {
        expect(model.description, equals(EvoStrings.activationCancelledDesc));
      });

      test('has correct icon asset', () {
        expect(model.iconAsset, equals(EvoImages.imgActivationStatusCancelled));
      });

      test('has one action button', () {
        expect(model.actions.length, equals(1));
        expect(model.actions[0], isA<PrimaryButton>());
      });

      testWidgets('verify button', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(
          home: Column(
            children: model.actions,
          ),
        ));

        await tester.tap(find.text(EvoStrings.backToHomePage));

        verify(() => mockNavigatorContext.goNamed(
              Screen.welcomeScreen.name,
              extra: any(named: 'extra'),
            )).called(1);
      });
    });
  });
}
