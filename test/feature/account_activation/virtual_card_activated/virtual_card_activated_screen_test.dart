import 'package:evoapp/feature/account_activation/virtual_card_activated/virtual_card_activated_screen.dart';
import 'package:evoapp/feature/account_activation/virtual_card_activated/widgets/card_activated_intro_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/widget/appbar/evo_support_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class MockImageProvider extends Mock implements CommonImageProvider {}

void main() {
  late CommonImageProvider mockImageProvider;
  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
  });

  setUp(() {
    mockImageProvider = getIt.get<CommonImageProvider>();

    when(() => mockImageProvider.asset(any(),
        width: any(named: 'width'),
        height: any(named: 'height'),
        fit: any(named: 'fit'),
        color: any(named: 'color'))).thenReturn(const SizedBox.shrink());
  });

  test('should self navigate on pushNamed', () async {
    VirtualCardActivatedScreen.pushNamed(username: '');

    verify(() => mockNavigatorContext.pushNamed(Screen.virtualCardActivatedScreen.name,
        extra: any(
          named: 'extra',
        ))).called(1);
  });

  group('VirtualCardActivatedScreen', () {
    const String testUsername = 'Test User';

    Future<void> buildWidget(WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const VirtualCardActivatedScreen(
            username: testUsername,
          ),
        ),
      );
    }

    testWidgets('should render initial UI correctly', (WidgetTester tester) async {
      await buildWidget(tester);

      expect(find.byType(EvoSupportAppbar), findsOneWidget);
      expect(
          find.text(
              EvoStrings.virtualCardActivatedTitle.replaceVariableByValue(<String>[testUsername])),
          findsOneWidget);
      expect(find.byType(CardActivatedIntroWidget), findsOneWidget);
      expect(find.text(EvoStrings.ctaGoToHome), findsOneWidget);
      verify(() => mockImageProvider.asset(
            EvoImages.virtualCardActivated,
            fit: any(named: 'fit'),
            width: any(named: 'width'),
          )).called(1);
    });

    testWidgets('test widget cannot be popped', (WidgetTester tester) async {
      await buildWidget(tester);

      bool didPop = true;
      tester.binding.addPostFrameCallback((_) {
        didPop = Navigator.of(tester.element(find.byType(PopScope))).canPop();
      });

      await tester.pump();
      expect(didPop, isFalse);
    });
  });

  group('VirtualCardActivatedArg', () {
    const String testUsername = 'Test User';

    test('should create an instance with required username', () {
      final VirtualCardActivatedArg arg = VirtualCardActivatedArg(
        userName: testUsername,
      );

      expect(arg.userName, equals(testUsername));
    });
  });
}
