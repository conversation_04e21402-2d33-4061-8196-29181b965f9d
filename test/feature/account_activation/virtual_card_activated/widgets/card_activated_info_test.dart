import 'package:evoapp/feature/account_activation/virtual_card_activated/widgets/card_activated_intro_widget.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/evo_list_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  setUpAll((){
    getItRegisterColor();
    getItRegisterTextStyle();
  });

  test('CardActivatedIntroWidget order of descriptions verification', () {
    expect(CardActivatedIntroWidget.descriptions, <String>[
      EvoStrings.virtualCardActivatedIntroItem1,
      EvoStrings.virtualCardActivatedIntroItem2,
      EvoStrings.virtualCardActivatedIntroItem3
    ]);
  });


  testWidgets('CardActivatedIntroWidget layout verification', (WidgetTester tester) async {
    // Build the widget
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: CardActivatedIntroWidget(),
        ),
      ),
    );

    // Verify the title is displayed
    expect(find.text(EvoStrings.virtualCardActivatedIntroTitle), findsOneWidget);

    // Verify the leading widgets
    for (int i = 0; i < CardActivatedIntroWidget.descriptions.length; i++) {
      final String index = (i + 1).toString();
      final Finder leadingWidgetFinder = find.descendant(
        of: find.byType(EvoListTileWidget).at(i),
        matching: find.byWidgetPredicate(
              (Widget widget) =>
          widget is Container &&
              widget.decoration is BoxDecoration &&
              (widget.decoration as BoxDecoration).shape == BoxShape.circle &&
              (widget.decoration as BoxDecoration).color == evoColors.accent70 &&
              widget.child is Text &&
              (widget.child as Text).data == index &&
              (widget.child as Text).style?.color == evoColors.accent90,
        ),
      );

      expect(leadingWidgetFinder, findsOneWidget);
    }
  });
}