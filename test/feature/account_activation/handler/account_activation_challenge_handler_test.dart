// Mock classes
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_challenge_handler.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_ui_handler.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAccountActivationUiHandler extends Mock implements AccountActivationUiHandler {}

class MockErrorCallback extends Mock {
  void call(ErrorUIModel? uiModel);
}

void main() {
  late MockAccountActivationUiHandler mockUiHandler;
  late AccountActivationChallengeHandler challengeHandler;
  late MockErrorCallback mockErrorCallback;

  setUpAll(() {
    registerFallbackValue(AccountActivationEntity.unserializable());
    registerFallbackValue(({required AccountActivationEntity entity}) {});
  });

  setUp(() {
    mockErrorCallback = MockErrorCallback();
    mockUiHandler = MockAccountActivationUiHandler();
    challengeHandler = AccountActivationChallengeHandler(
      onError: mockErrorCallback.call,
    )..uiHandler = mockUiHandler;
  });

  group('nextChallenge', () {
    setUp(() {
      when(() => mockUiHandler.verifyOtp(
            resendData: any(named: 'resendData'),
            entity: any(named: 'entity'),
          )).thenAnswer((_) {});

      when(() => mockUiHandler.createUsername(entity: any(named: 'entity'))).thenAnswer((_) {});
      when(() => mockUiHandler.createPin(
            entity: any(named: 'entity'),
          )).thenAnswer((_) {});
      when(() => mockUiHandler.activateAccountSuccess()).thenAnswer((_) {});
      when(() => mockUiHandler.enableBiometric(entity: any(named: 'entity'))).thenAnswer((_) {});
    });

    final ResendDataModel resendData = ResendDataModel(contactInfo: '**********');
    test('should call verifyOtp for verify_otp challenge type', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'verify_otp', sessionToken: 'token');

      final ChallengeSuccessModel challengeSuccessModel =
          ChallengeSuccessModel(entity: entity, resendData: resendData);

      when(() => mockUiHandler.verifyOtp(
            entity: entity,
            resendData: resendData,
          )).thenAnswer((_) {});

      challengeHandler.nextChallenge(challengeSuccessModel);

      verify(() => mockUiHandler.verifyOtp(
            resendData: resendData,
            entity: entity,
          )).called(1);
    });

    test('should call onError for unknown challenge type', () {
      when(() => mockUiHandler.onError).thenReturn(mockErrorCallback.call);
      when(() => mockErrorCallback.call(any())).thenReturn(null);

      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'unknown-challenge', sessionToken: 'token');
      challengeHandler.nextChallenge(ChallengeSuccessModel(entity: entity));

      verify(() => mockErrorCallback.call(any())).called(1);
    });

    test('should call verifySelfie for face_auth challenge type', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'face_auth', sessionToken: 'token');

      challengeHandler.nextChallenge(ChallengeSuccessModel(entity: entity, resendData: resendData));

      verify(() => mockUiHandler.verifySelfie(
            entity: entity,
          )).called(1);
    });

    test('should call createUsername for challenge type create_user_name', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'create_user_name', sessionToken: 'token');

      challengeHandler.nextChallenge(ChallengeSuccessModel(entity: entity, resendData: resendData));

      verify(() => mockUiHandler.createUsername(entity: any(named: 'entity'))).called(1);
    });

    test('should call createPin for challenge type create_pin', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'create_pin', sessionToken: 'token');

      challengeHandler.nextChallenge(ChallengeSuccessModel(entity: entity));

      verify(() => mockUiHandler.createPin(entity: any(named: 'entity'))).called(1);
    });

    test('should call verifyEmail for challenge type verify_email', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'verify_email', sessionToken: 'token');

      challengeHandler.nextChallenge(ChallengeSuccessModel(entity: entity));

      verify(() => mockUiHandler.verifyEmail(entity: entity)).called(1);
    });

    test('should call verifyOtpEmail for challenge type verify_email_otp', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'verify_email_otp', sessionToken: 'token');

      challengeHandler.nextChallenge(ChallengeSuccessModel(entity: entity, resendData: resendData));

      verify(() => mockUiHandler.verifyEmailOtp(entity: entity, resendData: resendData)).called(1);
    });

    test('should call enableBiometric for challenge type biometric_token', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'biometric_token', sessionToken: 'token');

      challengeHandler.nextChallenge(ChallengeSuccessModel(entity: entity));

      verify(() => mockUiHandler.enableBiometric(entity: entity)).called(1);
    });

    test('should call activateAccountSuccess for challenge type none', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'none', sessionToken: 'token');
      final bool isCardActivated = true;

      challengeHandler
          .nextChallenge(ChallengeSuccessModel(entity: entity, isCardActivated: isCardActivated));

      verify(() => mockUiHandler.activateAccountSuccess(isCardActivated: isCardActivated))
          .called(1);
    });

    test('should call activateCard for challenge type activate_card', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'activate_card', sessionToken: 'token');

      challengeHandler.nextChallenge(ChallengeSuccessModel(entity: entity));

      verify(() => mockUiHandler.activateCard(entity: entity)).called(1);
    });

    test('should call activateCardVerifyOtp for challenge type activate_card_verify_otp', () {
      final AccountActivationEntity entity =
          AccountActivationEntity(challengeType: 'activate_card_verify_otp', sessionToken: 'token');

      challengeHandler.nextChallenge(ChallengeSuccessModel(entity: entity, resendData: resendData));

      verify(() => mockUiHandler.activateCardVerifyOtp(entity: entity, resendData: resendData))
          .called(1);
    });

    test('should call onError when entity is not AccountActivationEntity', () {
      final AccountActivationChallengeHandler handler = AccountActivationChallengeHandler(
        onError: mockErrorCallback.call,
      );
      handler.nextChallenge(ChallengeSuccessModel(entity: BaseEntity()));

      verify(() => handler.uiHandler.onError(any())).called(1);
    });
  });
}
