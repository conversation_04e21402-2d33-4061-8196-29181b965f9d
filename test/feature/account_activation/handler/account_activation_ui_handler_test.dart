import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/verify_email/input_email_screen.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_page.dart';
import 'package:evoapp/feature/account_activation/activate_virtual_card/activate_virtual_card_screen.dart';
import 'package:evoapp/feature/account_activation/activation_status/activation_status_screen.dart';
import 'package:evoapp/feature/account_activation/create_username/create_username_screen.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_ui_handler.dart';
import 'package:evoapp/feature/account_activation/verify_email/verify_email_screen.dart';
import 'package:evoapp/feature/ekyc/intro/face_capture_check_screen.dart';
import 'package:evoapp/feature/error_screen/common_error_screen.dart';
import 'package:evoapp/feature/main_screen/card_page/activate_card_success_screen.dart';
import 'package:evoapp/feature/main_screen/main_screen.dart';
import 'package:evoapp/feature/pin/change_pin/change_pin_arg.dart';
import 'package:evoapp/feature/verify_otp/cubit/otp_success_model.dart';
import 'package:evoapp/feature/verify_otp/cubit/verify_otp_cubit.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/flutter_test_config.dart';

class MockOnErrorCallback extends Mock {
  void call(ErrorUIModel? error);
}

class MockOnSuccessCallback extends Mock {
  void call(ChallengeSuccessModel model);
}

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  setUpAll(() {
    registerFallbackValue(ChallengeSuccessModel());
  });

  group('AccountActivationType', () {
    test('should return verifyOTP for "verify_otp"', () {
      final AccountActivationType result = AccountActivationType.fromString('verify_otp');

      expect(result, AccountActivationType.verifyOTP);
    });

    test('should return verifySelfie for "face_auth"', () {
      final AccountActivationType result = AccountActivationType.fromString('face_auth');

      expect(result, AccountActivationType.verifySelfie);
    });

    test('should return createUsername for "create_user_name"', () {
      final AccountActivationType result = AccountActivationType.fromString('create_user_name');

      expect(result, AccountActivationType.createUsername);
    });

    test('should return createPin for "create_pin"', () {
      final AccountActivationType result = AccountActivationType.fromString('create_pin');

      expect(result, AccountActivationType.createPin);
    });

    test('should return verifyEmail for "verify_email"', () {
      final AccountActivationType result = AccountActivationType.fromString('verify_email');

      expect(result, AccountActivationType.verifyEmail);
    });

    test('should return verifyEmailOtp for "verify_email_otp"', () {
      final AccountActivationType result = AccountActivationType.fromString('verify_email_otp');

      expect(result, AccountActivationType.verifyEmailOtp);
    });

    test('should return activateCardVerifyOtp for "activate_card_verify_otp"', () {
      final AccountActivationType result =
          AccountActivationType.fromString('activate_card_verify_otp');

      expect(result, AccountActivationType.activateCardVerifyOtp);
    });

    test('should return biometricToken for "biometric_token"', () {
      final AccountActivationType result = AccountActivationType.fromString('biometric_token');

      expect(result, AccountActivationType.biometricToken);
    });

    test('should return none for "none"', () {
      final AccountActivationType result = AccountActivationType.fromString('none');

      expect(result, AccountActivationType.none);
    });

    test('should return unknown for null', () {
      final result = AccountActivationType.fromString(null);

      expect(result, AccountActivationType.unknown);
    });

    test('should return unknown for unrecognized value', () {
      final result = AccountActivationType.fromString('unrecognized_value');

      expect(result, AccountActivationType.unknown);
    });

    test('should return unknown for empty string', () {
      final result = AccountActivationType.fromString('');

      expect(result, AccountActivationType.unknown);
    });

    test('value property should match the string constant', () {
      expect(AccountActivationType.verifyOTP.value, AccountActivationType.verifyOTPValue);
      expect(AccountActivationType.verifySelfie.value, AccountActivationType.verifySelfieValue);
      expect(AccountActivationType.createUsername.value, AccountActivationType.createUsernameValue);
      expect(AccountActivationType.createPin.value, AccountActivationType.createPinValue);
      expect(AccountActivationType.verifyEmail.value, AccountActivationType.verifyEmailValue);
      expect(AccountActivationType.verifyEmailOtp.value, AccountActivationType.verifyEmailOtpValue);
      expect(AccountActivationType.biometricToken.value, AccountActivationType.biometricTokenValue);
      expect(AccountActivationType.none.value, AccountActivationType.noneValue);
      expect(AccountActivationType.unknown.value, AccountActivationType.unknownValue);
      expect(AccountActivationType.activateCardVerifyOtp.value,
          AccountActivationType.activateCardVerifyOtpValue);
    });
  });

  group('AccountActivationUiHandler', () {
    late MockOnSuccessCallback mockOnSuccess;
    late MockOnErrorCallback mockOnError;
    final AccountActivationEntity mockEntity = AccountActivationEntity(
      challengeType: 'challenge-type',
      sessionToken: 'session-token',
      otpResendSecs: 30,
      otpValiditySecs: 120,
      email: '<EMAIL>',
    );
    late final BuildContext mockNavigatorContext;
    late AccountActivationUiHandler handler;

    setUpAll(() {
      mockNavigatorContext = MockBuildContext();
      setUpMockGlobalKeyProvider(mockNavigatorContext);

      getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());

      registerFallbackValue(AccountActivationEntity());
    });

    setUp(() {
      mockOnSuccess = MockOnSuccessCallback();
      mockOnError = MockOnErrorCallback();
      handler = AccountActivationUiHandler(
        onError: mockOnError.call,
        onSuccess: mockOnSuccess.call,
      );
    });

    group('verifyOtp', () {
      final ResendDataModel resendData = ResendDataModel(contactInfo: '**********');

      test('should navigate Screen.verifyOtpScreen with correct args', () {
        handler.verifyOtp(
          entity: mockEntity,
          resendData: resendData,
        );

        final dynamic captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;

        expect(
            captured,
            isA<VerifyOtpPageArg>().having(
                (VerifyOtpPageArg args) => (
                      args.contactInfo,
                      args.otpResendSecs,
                      args.otpValiditySecs,
                      args.otpValiditySecs,
                      args.verifyOtpType,
                      args.sessionToken,
                    ),
                'verify VerifyOtpPageArg',
                (
                  resendData.contactInfo,
                  mockEntity.otpResendSecs,
                  mockEntity.otpValiditySecs,
                  mockEntity.otpValiditySecs,
                  VerifyOtpType.activateAccount,
                  mockEntity.sessionToken,
                )));
      });

      test(
          'should called onSuccess when called onPopSuccess(state), which state is VerifyOtpSuccess',
          () {
        handler.verifyOtp(
          entity: mockEntity,
          resendData: resendData,
        );

        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;
        captured.onPopSuccess?.call(VerifyOtpSuccess(OtpSuccessModel(
          challengeType: 'challenge-type',
          sessionToken: 'session-token',
        )));
        final dynamic onSuccessArgs = verify(() => mockOnSuccess.call(captureAny())).captured.first;
        expect(onSuccessArgs, isA<ChallengeSuccessModel>());
        onSuccessArgs as ChallengeSuccessModel;

        expect(
            onSuccessArgs.entity,
            isA<AccountActivationEntity>().having(
              (AccountActivationEntity args) => (
                args.challengeType,
                args.sessionToken,
              ),
              'verify onSuccess args',
              ('challenge-type', 'session-token'),
            ));
      });

      test('should navigate to ActivationStatusScreen when status verdict is provided', () {
        handler.verifyOtp(entity: mockEntity, resendData: null);

        final VerifyOtpPageArg verifyOtpPageArg = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;

        final String statusVerdict = AccountActivationEntity.verdictStatusRejected;
        verifyOtpPageArg.onPopSuccess
            ?.call(VerifyOtpSuccess(OtpSuccessModel(verdict: statusVerdict)));

        final ActivationStatusScreenArg activationStatusScreenArg =
            verify(() => mockNavigatorContext.pushNamed(
                  Screen.activationStatusScreen.name,
                  extra: captureAny(named: 'extra'),
                )).captured.first as ActivationStatusScreenArg;

        expect(activationStatusScreenArg.status, ActivationStatus.fromVerdict(statusVerdict));
      });

      test('should called onError when called onPopSuccess(state), which state is VerifyOtpFailed',
          () {
        final ErrorUIModel mockError = ErrorUIModel();

        handler.verifyOtp(
          entity: mockEntity,
          resendData: resendData,
        );
        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;
        captured.onPopSuccess?.call(VerifyOtpFailed(error: mockError));
        final dynamic onErrorArgs = verify(() => mockOnError.call(captureAny())).captured.first;

        expect(onErrorArgs, mockError);
      });
    });

    group('verifySelfie', () {
      test('should navigate Screen.verifyOtpScreen with correct args', () {
        handler.verifySelfie(
          entity: mockEntity,
        );

        final dynamic captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.faceCaptureCheckScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;

        expect(
            captured,
            isA<FaceCaptureCheckScreenArgs>().having(
                (FaceCaptureCheckScreenArgs args) => (args.sessionToken,),
                'verify FaceCaptureCheckScreenArgs',
                (mockEntity.sessionToken,)));
      });

      test('should called onSuccess when called onPopSuccess(state) ', () {
        handler.verifySelfie(
          entity: mockEntity,
        );

        final FaceCaptureCheckScreenArgs captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.faceCaptureCheckScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as FaceCaptureCheckScreenArgs;
        final ChallengeSuccessModel model = ChallengeSuccessModel();

        captured.onPopSuccess.call(model);
        verify(() => mockOnSuccess.call(model)).called(1);
      });
    });

    group('createUsername', () {
      test('should navigate Screen.createUsernameScreen', () {
        final AccountActivationEntity entity = AccountActivationEntity(
          sessionToken: 'mock-session-token',
        );
        handler.createUsername(
          entity: entity,
        );

        final dynamic captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.createUsernameScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;

        expect(
            captured,
            isA<CreateUsernameScreenArgs>().having(
                (CreateUsernameScreenArgs args) => (args.sessionToken,),
                'verify CreateUsernameScreenArgs',
                (entity.sessionToken,)));
      });

      test('should called onSuccess when called onPopSuccess(state)', () {
        handler.createUsername(entity: AccountActivationEntity());

        final CreateUsernameScreenArgs captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.createUsernameScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as CreateUsernameScreenArgs;
        final ChallengeSuccessModel model = ChallengeSuccessModel();

        captured.onPopSuccess.call(model);
        verify(() => mockOnSuccess.call(model)).called(1);
      });
    });

    group('create_pin', () {
      test('should navigate Screen.createNewPinScreen', () {
        final AccountActivationEntity entity = AccountActivationEntity();
        handler.createPin(entity: entity);

        verify(
          () => mockNavigatorContext.pushNamed(
            Screen.createNewPinScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;
      });

      test('should called onSuccess when called onPopSuccess(state)', () {
        handler.createPin(entity: AccountActivationEntity());

        final CreateNewPinArgs captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.createNewPinScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as CreateNewPinArgs;

        final ChallengeSuccessModel model = ChallengeSuccessModel();
        captured.onSuccess.call(model);
        verify(() => mockOnSuccess.call(model)).called(1);
      });
    });

    group('verify_email', () {
      group('when email is duplicate', () {
        final AccountActivationEntity entity =
            AccountActivationEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'verdict': AccountActivationEntity.verdictSuccessEmailExists,
            'data': <String, dynamic>{
              'user_message': 'user_message',
              'email': 'email',
              'session_token': 'session_token',
            },
          },
        ));

        test('should navigate Screen.inputEmailScreen', () {
          handler.verifyEmail(entity: entity);

          verify(
            () => mockNavigatorContext.pushNamed(
              Screen.inputEmailScreen.name,
              extra: any(named: 'extra'),
            ),
          ).called(1);
        });

        test('should pass correct extra data on navigation', () {
          handler.verifyEmail(entity: entity);

          final InputEmailArg captured = verify(
            () => mockNavigatorContext.pushNamed(
              Screen.inputEmailScreen.name,
              extra: captureAny(named: 'extra'),
            ),
          ).captured.first as InputEmailArg;

          expect(captured.isDuplicate, true);
          expect(captured.email, entity.email);
          expect(captured.duplicateErrorMessage, entity.userMessage);
          expect(captured.sessionToken, entity.sessionToken);

          final ChallengeSuccessModel model = ChallengeSuccessModel();
          captured.onPopSuccess.call(model);
          verify(() => mockOnSuccess.call(model)).called(1);
        });
      });

      group('when email is not duplicate', () {
        final AccountActivationEntity entity =
            AccountActivationEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'verdict': BaseEntity.verdictSuccess,
            'data': <String, dynamic>{
              'email': 'email',
              'session_token': 'session_token',
            },
          },
        ));

        test('should navigate Screen.verifyEmailScreen', () {
          handler.verifyEmail(entity: entity);

          verify(
            () => mockNavigatorContext.pushNamed(
              Screen.verifyEmailScreen.name,
              extra: any(named: 'extra'),
            ),
          ).called(1);
        });

        test('should pass correct extra data on navigation', () {
          handler.verifyEmail(entity: entity);

          final VerifyEmailArg captured = verify(
            () => mockNavigatorContext.pushNamed(
              Screen.verifyEmailScreen.name,
              extra: captureAny(named: 'extra'),
            ),
          ).captured.first as VerifyEmailArg;

          expect(captured.email, entity.email);
          expect(captured.sessionToken, entity.sessionToken);

          final ChallengeSuccessModel model = ChallengeSuccessModel();
          captured.onPopSuccess.call(model);
          verify(() => mockOnSuccess.call(model)).called(1);
        });
      });
    });

    group('verify_email_otp', () {
      test('should navigate Screen.verifyOtpScreen with correct extra data', () {
        final AccountActivationEntity entity = AccountActivationEntity(
          challengeType: 'challenge-type',
          sessionToken: 'session-token',
          otpResendSecs: 30,
          otpValiditySecs: 120,
        );
        final ResendDataModel resendData = ResendDataModel(
          contactInfo: 'contact-info',
          sessionToken: 'session-token',
        );

        handler.verifyEmailOtp(entity: entity, resendData: resendData);

        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;

        expect(captured.contactInfo, resendData.contactInfo);
        expect(captured.resendSessionToken, resendData.sessionToken);
        expect(captured.sessionToken, entity.sessionToken);
        expect(captured.verifyOtpType, VerifyOtpType.email);
        expect(captured.otpResendSecs, entity.otpResendSecs);
        expect(captured.otpValiditySecs, entity.otpValiditySecs);
      });

      test('should called onSuccess when called onPopSuccess(state), state is VerifyOtpSuccess',
          () {
        handler.verifyEmailOtp(entity: mockEntity, resendData: null);

        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;
        captured.onPopSuccess?.call(VerifyOtpSuccess(OtpSuccessModel(
          challengeType: 'challenge-type',
          sessionToken: 'session-token',
        )));
        final dynamic model = verify(() => mockOnSuccess.call(captureAny())).captured.first;
        expect(model, isA<ChallengeSuccessModel>());

        model as ChallengeSuccessModel;
        expect(
            model.entity,
            isA<AccountActivationEntity>().having(
              (AccountActivationEntity args) => (
                args.challengeType,
                args.sessionToken,
              ),
              'verify onSuccess args',
              ('challenge-type', 'session-token'),
            ));
      });

      test('should navigate to InputEmailScreen when state is VerifyEmailOtpDuplicateEmailError',
          () {
        handler.verifyEmailOtp(entity: mockEntity, resendData: null);

        final VerifyOtpPageArg verifyOtpPageArg = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;

        final String errorMessage = 'message';
        final String duplicateEmail = 'email';
        final String resendSessionToken = 'token';

        verifyOtpPageArg.onPopSuccess?.call(VerifyEmailOtpDuplicateEmailError(
          ErrorUIModel(userMessage: errorMessage),
          email: duplicateEmail,
          sessionToken: resendSessionToken,
        ));

        verifyNever(() => mockOnSuccess.call(any()));

        final InputEmailArg captured = verify(
          () => mockNavigatorContext.pushReplacementNamed(
            Screen.inputEmailScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as InputEmailArg;

        expect(captured.isDuplicate, true);
        expect(captured.email, duplicateEmail);
        expect(captured.duplicateErrorMessage, errorMessage);
        expect(captured.sessionToken, resendSessionToken);

        final ChallengeSuccessModel model = ChallengeSuccessModel();
        captured.onPopSuccess.call(model);
        verify(() => mockOnSuccess.call(model)).called(1);
      });

      test('should called onError when called onPopSuccess(state), which state is VerifyOtpFailed',
          () {
        final ErrorUIModel mockError = ErrorUIModel();

        handler.verifyEmailOtp(entity: mockEntity, resendData: null);

        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;
        captured.onPopSuccess?.call(VerifyOtpFailed(error: mockError));
        final dynamic onErrorArgs = verify(() => mockOnError.call(captureAny())).captured.first;

        expect(onErrorArgs, mockError);
      });
    });

    group('activate_card', () {
      test('should navigate Screen.activateVirtualCardScreen with correct extra data', () {
        final AccountActivationEntity entity = AccountActivationEntity(
          challengeType: 'challenge-type',
          sessionToken: 'session-token',
        );

        handler.activateCard(entity: entity);

        final ActivateVirtualCardArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.activateVirtualCardScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as ActivateVirtualCardArg;

        expect(captured.sessionToken, entity.sessionToken);
      });

      test('should called onSuccess when called onPopSuccess(model)', () {
        handler.activateCard(entity: mockEntity);

        final ActivateVirtualCardArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.activateVirtualCardScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as ActivateVirtualCardArg;

        captured.onPopSuccess.call(ChallengeSuccessModel(entity: mockEntity));
        final dynamic model = verify(() => mockOnSuccess.call(captureAny())).captured.first;
        expect(model, isA<ChallengeSuccessModel>());

        model as ChallengeSuccessModel;
        expect(
            model.entity,
            isA<AccountActivationEntity>().having(
              (AccountActivationEntity args) => (args.challengeType, args.sessionToken),
              'verify onSuccess args',
              ('challenge-type', 'session-token'),
            ));
      });
    });

    group('activateAccountSuccess', () {
      test('should goNamed Screen.mainScreen with correct parameters', () {
        final bool isCardActivated = true;
        handler.activateAccountSuccess(isCardActivated: isCardActivated);

        final MainScreenArg extra = verify(
          () => mockNavigatorContext.goNamed(
            Screen.mainScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as MainScreenArg;

        expect(extra.isCardActivated, isCardActivated);
      });
    });

    group('enableBiometric', () {
      test('should navigate to ActiveBiometricScreen with correct args', () {
        handler.enableBiometric(entity: mockEntity);

        final dynamic captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.activateBiometricScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;

        expect(
          captured,
          isA<ActivateBiometricScreenArg>().having(
            (ActivateBiometricScreenArg args) => args.sessionToken,
            'sessionToken',
            mockEntity.sessionToken,
          ),
        );
      });

      test('should call onSuccess when biometric activation succeeds', () {
        handler.enableBiometric(entity: mockEntity);

        final ActivateBiometricScreenArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.activateBiometricScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as ActivateBiometricScreenArg;

        final ChallengeSuccessModel model = ChallengeSuccessModel(entity: mockEntity);
        captured.onSuccess(model);

        verify(() => mockOnSuccess.call(model)).called(1);
      });
    });

    group('activateCardVerifyOtp', () {
      final ResendDataModel resendData = ResendDataModel(sessionToken: 'resend-session-token');

      test('should navigate to verify OTP screen with correct args', () {
        handler.activateCardVerifyOtp(
          entity: mockEntity,
          resendData: resendData,
        );

        final dynamic captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first;

        expect(
          captured,
          isA<VerifyOtpPageArg>().having(
            (VerifyOtpPageArg args) => (
              args.contactInfo,
              args.resendSessionToken,
              args.verifyOtpType,
              args.otpValiditySecs,
              args.otpResendSecs,
              args.sessionToken,
            ),
            'verify VerifyOtpPageArg',
            (
              null,
              resendData.sessionToken,
              VerifyOtpType.activateCard,
              mockEntity.otpValiditySecs,
              mockEntity.otpResendSecs,
              mockEntity.sessionToken,
            ),
          ),
        );
      });

      test('should navigate to success screen and call onSuccess when OTP verification succeeds',
          () {
        handler.activateCardVerifyOtp(
          entity: mockEntity,
          resendData: resendData,
        );

        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;

        captured.onPopSuccess?.call(VerifyOtpSuccess(OtpSuccessModel(
          challengeType: 'challenge-type',
          sessionToken: 'session-token',
        )));

        final ActivateCardSuccessArg capturedArg = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.activateCardSuccessScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as ActivateCardSuccessArg;

        capturedArg.onPopSuccess?.call();

        verify(() => mockOnSuccess.call(captureAny(
                that: isA<ChallengeSuccessModel>().having(
              (ChallengeSuccessModel model) => model.isCardActivated,
              'verify isCardActivated',
              true,
            )))).called(1);
      });

      test('should navigate to error screen when OTP verification has third party error', () {
        handler.activateCardVerifyOtp(
          entity: mockEntity,
          resendData: resendData,
        );

        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;

        captured.onPopSuccess?.call(VerifyOtpThirdPartyError(ErrorUIModel()));

        final CommonErrorScreenArg capturedArg = verify(
          () => mockNavigatorContext.pushReplacementNamed(
            Screen.commonErrorScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as CommonErrorScreenArg;

        capturedArg.onTap.call();

        verify(() => mockOnSuccess.call(captureAny(
                that: isA<ChallengeSuccessModel>().having(
              (ChallengeSuccessModel model) => model.isCardActivated,
              'verify isCardActivated',
              false,
            )))).called(1);
      });

      test('should call onError when OTP verification fails', () {
        final ErrorUIModel mockError = ErrorUIModel();

        handler.activateCardVerifyOtp(
          entity: mockEntity,
          resendData: resendData,
        );

        final VerifyOtpPageArg captured = verify(
          () => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          ),
        ).captured.first as VerifyOtpPageArg;

        captured.onPopSuccess?.call(VerifyOtpFailed(error: mockError));

        final dynamic onErrorArgs = verify(() => mockOnError.call(captureAny())).captured.first;
        expect(onErrorArgs, mockError);
      });
    });
  });
}
