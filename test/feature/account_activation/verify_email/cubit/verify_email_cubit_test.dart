// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_cubit.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_state.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  late AuthenticationRepo authRepo;
  late VerifyEmailCubit cubit;

  setUpAll(() {
    registerFallbackValue(ActivateAccountVerifyEmailRequest(email: null, sessionToken: null));
  });

  setUp(() {
    authRepo = MockAuthenticationRepo();
    cubit = VerifyEmailCubit(authRepo: authRepo);

    when(
      () => authRepo.activateAccount(
          request: any(named: 'request'), mockConfig: any(named: 'mockConfig')),
    ).thenAnswer((_) async {
      return AccountActivationEntity.fromBaseResponse(BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{},
      ));
    });
  });

  group('VerifyEmailCubit', () {
    test('initial state is VerifyEmailInitial', () {
      expect(cubit.state, isA<VerifyEmailInitial>());
    });

    test('request should be ActivateAccountVerifyEmailRequest', () async {
      await cubit.verify(email: 'email', token: 'token');
      ActivateAccountVerifyEmailRequest entity = verify(() => authRepo.activateAccount(
            request: captureAny(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).captured.first as ActivateAccountVerifyEmailRequest;
      expect(entity.email, 'email');
      expect(entity.sessionToken, 'token');

      await cubit.verify(email: 'email', token: 'token');
      entity = verify(() => authRepo.activateAccount(
            request: captureAny(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).captured.first as ActivateAccountVerifyEmailRequest;
      expect(entity.email, 'email');
      expect(entity.sessionToken, 'token');
    });

    blocTest<VerifyEmailCubit, VerifyEmailState>(
      'should emit [VerifyEmailLoading, VerifyEmailSuccess] when statusCode is 200',
      setUp: () {
        when(
          () => authRepo.activateAccount(
              request: any(named: 'request'), mockConfig: any(named: 'mockConfig')),
        ).thenAnswer((_) async {
          return AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: <String, dynamic>{},
          ));
        });
      },
      build: () => cubit,
      act: (VerifyEmailCubit cubit) => cubit.verify(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<VerifyEmailLoading>(),
        isA<VerifyEmailSuccess>(),
      ],
    );

    blocTest<VerifyEmailCubit, VerifyEmailState>(
      'should emit [VerifyEmailLoading, VerifyEmailFailureTokenExpired] when statusCode is 401',
      setUp: () {
        when(
          () => authRepo.activateAccount(
              request: any(named: 'request'), mockConfig: any(named: 'mockConfig')),
        ).thenAnswer((_) async {
          return AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.INVALID_TOKEN,
            response: <String, dynamic>{},
          ));
        });
      },
      build: () => cubit,
      act: (VerifyEmailCubit cubit) => cubit.verify(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<VerifyEmailLoading>(),
        isA<VerifyEmailFailureTokenExpired>(),
      ],
    );

    blocTest<VerifyEmailCubit, VerifyEmailState>(
      'should emit [VerifyEmailLoading, VerifyEmailFailureLimitExceeded] when statusCode is 429',
      setUp: () {
        when(
          () => authRepo.activateAccount(
              request: any(named: 'request'), mockConfig: any(named: 'mockConfig')),
        ).thenAnswer((_) async {
          return AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.LIMIT_EXCEEDED,
            response: <String, dynamic>{},
          ));
        });
      },
      build: () => cubit,
      act: (VerifyEmailCubit cubit) => cubit.verify(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<VerifyEmailLoading>(),
        isA<VerifyEmailFailureLimitExceeded>(),
      ],
    );

    blocTest<VerifyEmailCubit, VerifyEmailState>(
      'should emit [VerifyEmailLoading, VerifyEmailFailureLimitExceeded] when statusCode is 423',
      setUp: () {
        when(
          () => authRepo.activateAccount(
              request: any(named: 'request'), mockConfig: any(named: 'mockConfig')),
        ).thenAnswer((_) async {
          return AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.LOCKED_RESOURCE,
            response: <String, dynamic>{},
          ));
        });
      },
      build: () => cubit,
      act: (VerifyEmailCubit cubit) => cubit.verify(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<VerifyEmailLoading>(),
        isA<VerifyEmailFailureLimitExceeded>(),
      ],
    );

    blocTest<VerifyEmailCubit, VerifyEmailState>(
      'should emit [VerifyEmailLoading, VerifyEmailFailureDuplicate] '
      'when statusCode is 409 and verdict is duplicate',
      setUp: () {
        when(
          () => authRepo.activateAccount(
              request: any(named: 'request'), mockConfig: any(named: 'mockConfig')),
        ).thenAnswer((_) async {
          return AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.DUPLICATE,
            response: <String, dynamic>{
              'verdict': AccountActivationEntity.verdictDuplicate,
            },
          ));
        });
      },
      build: () => cubit,
      act: (VerifyEmailCubit cubit) => cubit.verify(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<VerifyEmailLoading>(),
        isA<VerifyEmailFailureDuplicate>(),
      ],
    );

    blocTest<VerifyEmailCubit, VerifyEmailState>(
      'should emit [VerifyEmailInitial] when reset is called',
      build: () => cubit,
      seed: () => VerifyEmailLoading(),
      act: (VerifyEmailCubit cubit) => cubit.reset(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<VerifyEmailInitial>(),
      ],
    );

    blocTest<VerifyEmailCubit, VerifyEmailState>(
      'should emit [VerifyEmailFailureDuplicate] when setDuplicate is called',
      build: () => cubit,
      act: (VerifyEmailCubit cubit) => cubit.setDuplicateState(error: 'error'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<VerifyEmailFailureDuplicate>().having(
          (VerifyEmailFailureDuplicate e) => e.error.userMessage,
          'has correct error message',
          'error',
        ),
      ],
    );
  });
}
