// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/validate_email_cubit.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/validate_email_state.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../constant.dart';

void main() {
  group('ValidateEmailCubit', () {
    late ValidateEmailCubit validateEmailCubit;

    setUp(() {
      validateEmailCubit = ValidateEmailCubit();
    });

    tearDown(() {
      validateEmailCubit.close();
    });

    test('initial state is ValidateEmailInitial', () {
      expect(validateEmailCubit.state, isA<ValidateEmailInitial>());
    });
    blocTest<ValidateEmailCubit, ValidateEmailState>(
      'emits [ValidateEmailStatus] when email is valid',
      build: () => validateEmailCubit,
      act: (ValidateEmailCubit cubit) => cubit.validate('<EMAIL>'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<ValidateEmailStatus>().having((s) => s.isValid, 'valid email', true),
      ],
    );

    blocTest<ValidateEmailCubit, ValidateEmailState>(
      'emits [ValidateEmailStatus] when email is null',
      build: () => validateEmailCubit,
      act: (ValidateEmailCubit cubit) => cubit.validate(null),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<ValidateEmailStatus>().having((s) => s.isValid, 'valid email', false),
      ],
    );

    blocTest<ValidateEmailCubit, ValidateEmailState>(
      'emits [ValidateEmailStatus] when email is invalid',
      build: () => validateEmailCubit,
      act: (ValidateEmailCubit cubit) => cubit.validate('invalid-email'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<ValidateEmailStatus>().having((s) => s.isValid, 'invalid email', false),
      ],
    );
  });
}
