import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_state.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VerifyEmailState', () {
    test('Subtypes should have right type', () {
      expect(VerifyEmailInitial(), isA<VerifyEmailState>());
      expect(VerifyEmailLoading(), isA<VerifyEmailState>());
      expect(VerifyEmailSuccess(AccountActivationEntity()), isA<VerifyEmailState>());
      expect(VerifyEmailFailure(error: ErrorUIModel()), isA<VerifyEmailState>());
      expect(VerifyEmailFailureTokenExpired(), isA<VerifyEmailState>());
      expect(VerifyEmailFailureLimitExceeded(error: ErrorUIModel()), isA<VerifyEmailState>());
    });

    test('VerifyEmailSuccess error property should return correct value', () {
      final AccountActivationEntity entity = AccountActivationEntity();
      final VerifyEmailSuccess state = VerifyEmailSuccess(entity);
      expect(state.entity, entity);
    });

    test('VerifyEmailFailureLimitExceeded error property should return correct value', () {
      final ErrorUIModel error = ErrorUIModel();
      final VerifyEmailFailureLimitExceeded state = VerifyEmailFailureLimitExceeded(error: error);
      expect(state.error, equals(error));
    });

    test('VerifyEmailFailure error property should return correct value', () {
      final ErrorUIModel error = ErrorUIModel();
      final VerifyEmailFailure state = VerifyEmailFailure(error: error);
      expect(state.error, equals(error));
    });

    test('VerifyEmailFailureDuplicate error property should return correct value', () {
      final ErrorUIModel error = ErrorUIModel();
      final VerifyEmailFailure state = VerifyEmailFailureDuplicate(error: error);
      expect(state, isA<VerifyEmailState>());
      expect(state.error, equals(error));
    });
  });
}
