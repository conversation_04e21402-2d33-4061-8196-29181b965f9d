// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/account_activation/verify_email/cubit/validate_email_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ValidateEmailState', () {
    test('ValidateEmailInitial should be a subclass of ValidateEmailState', () {
      final ValidateEmailInitial state = ValidateEmailInitial();
      expect(state, isA<ValidateEmailState>());
    });

    test('ValidateEmailStatus should have correct parameter', () {
      final ValidateEmailStatus state = ValidateEmailStatus(true);

      expect(state, isA<ValidateEmailState>());
      expect(state.isValid, true);
    });
  });
}
