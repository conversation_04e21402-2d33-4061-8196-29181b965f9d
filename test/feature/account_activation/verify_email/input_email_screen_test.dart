// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/validate_email_cubit.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/validate_email_state.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_cubit.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_state.dart';
import 'package:evoapp/feature/account_activation/verify_email/input_email_screen.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/widget/appbar/evo_appbar.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:evoapp/widget/evo_text_field.dart';
import 'package:evoapp/widget/no_app_bar_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockValidateEmailCubit extends AppMockCubit<ValidateEmailState>
    implements ValidateEmailCubit {}

class MockVerifyEmailCubit extends AppMockCubit<VerifyEmailState> implements VerifyEmailCubit {}

class MockOnPopSuccess extends Mock {
  void call(ChallengeSuccessModel model);
}

void main() {
  late MockValidateEmailCubit validateEmailCubit;
  late MockVerifyEmailCubit verifyEmailCubit;

  const String testEmail = '<EMAIL>';

  setUpAll(() {
    initConfigEvoPageStateBase();
    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});

    registerFallbackValue(ChallengeSuccessModel());
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    validateEmailCubit = MockValidateEmailCubit()..emit(ValidateEmailInitial());
    verifyEmailCubit = MockVerifyEmailCubit()..emit(VerifyEmailInitial());

    when(() => validateEmailCubit.validate(any())).thenReturn(null);
    when(() => verifyEmailCubit.verify(
          email: any(named: 'email'),
          token: any(named: 'token'),
        )).thenAnswer((_) async {});
  });

  Widget buildWidgetInTest({
    String? email,
    String? sessionToken,
    ChallengeSuccessCallback? onPopSuccess,
    bool? isDuplicate,
    String? duplicateErrorMessage,
  }) {
    return MaterialApp(
      home: MultiBlocProvider(
        providers: <BlocProvider<dynamic>>[
          BlocProvider<ValidateEmailCubit>.value(value: validateEmailCubit),
          BlocProvider<VerifyEmailCubit>.value(value: verifyEmailCubit),
        ],
        child: InputEmailScreen(
          email: email,
          sessionToken: sessionToken,
          onPopSuccess: onPopSuccess ?? (_) {},
          isDuplicate: isDuplicate,
          duplicateErrorMessage: duplicateErrorMessage,
        ),
      ),
    );
  }

  group('InputEmailScreen', () {
    test('should self navigate', () {
      InputEmailScreen.pushNamed(
        email: testEmail,
        onPopSuccess: (_) {},
      );

      verify(() => mockNavigatorContext.pushNamed(
            Screen.inputEmailScreen.name,
            extra: any(named: 'extra'),
          )).called(1);

      InputEmailScreen.pushReplacementNamed(
        email: testEmail,
        onPopSuccess: (_) {},
      );
      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.inputEmailScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should display correct UI when DOP email is duplicate',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(
        isDuplicate: true,
      ));

      expect(find.byType(NoAppBarWrapper), findsOneWidget);

      final PopScope popScope = tester.widget(find.byType(PopScope));
      expect(popScope.canPop, false);

      expect(find.text(EvoStrings.inputDuplicateEmailTitle), findsOneWidget);
      expect(find.text(EvoStrings.inputDuplicateEmailDescription), findsOneWidget);
      expect(find.textContaining(EvoStrings.inputDuplicateEmailContactSupport1, findRichText: true),
          findsOneWidget);
      expect(find.textContaining(EvoStrings.inputDuplicateEmailContactSupport2, findRichText: true),
          findsOneWidget);

      expect(find.byType(EvoTextField), findsOneWidget);

      expect(find.widgetWithText(PrimaryButton, EvoStrings.sendEmailCodeBtn2), findsOneWidget);
    });

    testWidgets('should display correct UI when DOP email is NOT duplicate',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(
        isDuplicate: false,
      ));

      expect(find.byType(EvoAppBar), findsOneWidget);
      expect(find.text(EvoStrings.inputEmailTitle), findsOneWidget);
      expect(find.byType(EvoTextField), findsOneWidget);
      expect(find.widgetWithText(PrimaryButton, EvoStrings.sendEmailCodeBtn2), findsOneWidget);

      expect(find.text(EvoStrings.inputDuplicateEmailDescription), findsNothing);
      expect(find.textContaining(EvoStrings.inputDuplicateEmailContactSupport1, findRichText: true),
          findsNothing);
      expect(find.textContaining(EvoStrings.inputDuplicateEmailContactSupport2, findRichText: true),
          findsNothing);
    });

    testWidgets('should pre-fill email if provided', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(email: testEmail));

      final TextField textField = tester.widget<TextField>(
        find.descendant(
          of: find.byType(EvoTextField),
          matching: find.byType(TextField),
        ),
      );
      expect(textField.controller?.text, testEmail);
    });

    testWidgets('should disable send-code button when email is NOT valid',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(email: testEmail));

      PrimaryButton button() =>
          tester.widget(find.widgetWithText(PrimaryButton, EvoStrings.sendEmailCodeBtn2));

      await validateEmailCubit.emit(ValidateEmailStatus(false));
      await tester.pump();
      expect(button().onTap, isNull);

      await validateEmailCubit.emit(ValidateEmailStatus(true));
      await tester.pump();
      expect(button().onTap, isNotNull);
    });

    testWidgets('should call verify email when tapping an enabled send-code button',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(email: testEmail));

      await validateEmailCubit.emit(ValidateEmailStatus(true));
      await tester.pump();

      await tester.tap(find.text(EvoStrings.sendEmailCodeBtn2));
      await tester.pump();

      verify(() => verifyEmailCubit.verify(
            email: testEmail,
            token: any(named: 'token'),
          )).called(1);
    });

    testWidgets('should call verify email when text field is submitted and email is valid',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(email: testEmail));

      await validateEmailCubit.emit(ValidateEmailStatus(true));
      await tester.pump();

      final TextField textField = tester.widget(find.byType(TextField));
      textField.onSubmitted?.call('');

      verify(() => verifyEmailCubit.verify(
            email: testEmail,
            token: any(named: 'token'),
          )).called(1);
    });

    testWidgets('should call onPopSuccess when email verification succeeds',
        (WidgetTester tester) async {
      final String testEmail = 'test_email';
      final String testToken = 'test_token';
      final MockOnPopSuccess onPopSuccess = MockOnPopSuccess();
      await tester.pumpWidget(buildWidgetInTest(
        email: testEmail,
        sessionToken: testToken,
        onPopSuccess: onPopSuccess.call,
      ));

      final AccountActivationEntity entity = AccountActivationEntity();
      await verifyEmailCubit.emit(VerifyEmailSuccess(entity));
      await tester.pump();

      verify(() => onPopSuccess.call(any(
            that: isA<ChallengeSuccessModel>().having(
              (ChallengeSuccessModel m) => (m.resendData?.contactInfo, m.resendData?.sessionToken),
              'should set resend data',
              (testEmail, testToken),
            ),
          ))).called(1);
    });

    testWidgets('should show error message when email is duplicate', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(email: testEmail));

      await verifyEmailCubit.emit(
        VerifyEmailFailureDuplicate(error: ErrorUIModel(userMessage: 'error_message')),
      );
      await tester.pump();

      expect(find.textContaining('error_message'), findsOneWidget);
    });

    testWidgets(
        'should call VerifyEmailCubit.cubit and ValidateEmailCubit.validate on text changed',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest(email: testEmail));

      final String newEmail = '<EMAIL>';
      await tester.enterText(find.byType(TextField), newEmail);
      await tester.pumpAndSettle();

      verify(() => verifyEmailCubit.reset()).called(1);
      verify(() => validateEmailCubit.validate(newEmail)).called(1);
    });
  });
}
