// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_cubit.dart';
import 'package:evoapp/feature/account_activation/verify_email/cubit/verify_email_state.dart';
import 'package:evoapp/feature/account_activation/verify_email/input_email_screen.dart';
import 'package:evoapp/feature/account_activation/verify_email/verify_email_screen.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockVerifyEmailCubit extends AppMockCubit<VerifyEmailState> implements VerifyEmailCubit {}

class MockOnPopSuccess extends Mock {
  void call(ChallengeSuccessModel model);
}

void main() {
  final String testEmail = 'test.email.com';
  final String testToken = 'test-token';

  late MockVerifyEmailCubit mockCubit;

  group('VerifyEmailScreen', () {
    setUpAll(() {
      initConfigEvoPageStateBase();

      registerFallbackValue(ChallengeSuccessModel());
    });

    setUp(() {
      setUpMockConfigEvoPageStateBase();

      mockCubit = MockVerifyEmailCubit()..emit(VerifyEmailInitial());

      when(() => mockCubit.verify(
            email: any(named: 'email'),
            token: any(named: 'token'),
          )).thenAnswer((_) async {});

      when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});
    });

    Future<void> pumpScreen(
      WidgetTester tester, {
      ChallengeSuccessCallback? onPopSuccess,
    }) async {
      await tester.pumpWidget(
        BlocProvider<VerifyEmailCubit>.value(
          value: mockCubit,
          child: MaterialApp(
            home: Material(
              child: VerifyEmailScreen(
                email: testEmail,
                sessionToken: testToken,
                onPopSuccess: onPopSuccess ?? (_) {},
              ),
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();
    }

    testWidgets('should display screen with correct elements', (WidgetTester tester) async {
      await pumpScreen(tester);

      // Verify title and description texts
      expect(find.text(EvoStrings.verifyEmailTitle), findsOneWidget);
      expect(find.text(EvoStrings.verifyEmailDesc), findsOneWidget);
      expect(find.text(EvoStrings.verifyEmailSubtitle), findsOneWidget);

      // Verify email
      expect(find.textContaining(testEmail), findsOneWidget);

      // Verify buttons
      expect(find.widgetWithText(PrimaryButton, EvoStrings.sendEmailCodeBtn), findsOneWidget);
      expect(find.widgetWithText(TertiaryButton, EvoStrings.changeEmailBtn), findsOneWidget);
    });

    test('should self navigate on pushNamed', () {
      VerifyEmailScreen.pushNamed(email: testEmail, onPopSuccess: (_) {});

      verify(() => mockNavigatorContext.pushNamed(
            Screen.verifyEmailScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should verify with null email on tapping send-code button',
        (WidgetTester tester) async {
      await pumpScreen(tester);

      await tester.tap(find.text(EvoStrings.sendEmailCodeBtn));
      await tester.pumpAndSettle();

      verify(() => mockCubit.verify(
            email: testEmail,
            token: any(named: 'token'),
          )).called(1);
    });

    testWidgets('should navigate to input email screen on tapping change-email button',
        (WidgetTester tester) async {
      await pumpScreen(tester);

      await tester.tap(find.text(EvoStrings.changeEmailBtn));
      await tester.pumpAndSettle();

      verify(() => mockNavigatorContext.pushNamed(
            Screen.inputEmailScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should call onPopSuccess when email verification succeeds',
        (WidgetTester tester) async {
      final MockOnPopSuccess onPopSuccess = MockOnPopSuccess();
      await pumpScreen(
        tester,
        onPopSuccess: onPopSuccess.call,
      );

      final AccountActivationEntity entity = AccountActivationEntity();
      mockCubit.emit(VerifyEmailSuccess(entity));
      await tester.pump();

      verify(() => onPopSuccess.call(any(
            that: isA<ChallengeSuccessModel>().having(
              (ChallengeSuccessModel m) => (m.resendData?.contactInfo, m.resendData?.sessionToken),
              'should set resend data',
              (testEmail, testToken),
            ),
          ))).called(1);
    });

    testWidgets('should navigate to input email screen on duplicate email error',
        (WidgetTester tester) async {
      await pumpScreen(tester);

      await mockCubit.emit(VerifyEmailFailureDuplicate(error: ErrorUIModel(userMessage: 'error')));
      await tester.pump();

      final InputEmailArg arg = verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.inputEmailScreen.name,
            extra: captureAny(named: 'extra'),
          )).captured.first as InputEmailArg;

      expect(arg.email, testEmail);
      expect(arg.sessionToken, testToken);
      expect(arg.duplicateErrorMessage, 'error');
      expect(arg.isDuplicate, true);
    });
  });
}
