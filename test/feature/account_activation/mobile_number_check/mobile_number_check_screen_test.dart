import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_ui_handler.dart';
import 'package:evoapp/feature/account_activation/mobile_number_check/mobile_number_check_cubit.dart';
import 'package:evoapp/feature/account_activation/mobile_number_check/mobile_number_check_screen.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:evoapp/widget/evo_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockMobileNumberCheckCubit extends AppMockCubit<MobileNumberCheckState>
    implements MobileNumberCheckCubit {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

void main() {
  late MockMobileNumberCheckCubit checkCubit;
  late EvoSnackBar evoSnackBar;

  setUpAll(() {
    registerFallbackValue(SnackBarType.neutral);

    initConfigEvoPageStateBase();

    evoSnackBar = getIt.registerSingleton<EvoSnackBar>(MockEvoSnackBar());
    when(() => evoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInMilliSec: any(named: 'durationInMilliSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).thenAnswer((_) async => true);

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() async {
    setUpMockConfigEvoPageStateBase();

    checkCubit = MockMobileNumberCheckCubit()..emit(MobileNumberCheckInitial());
  });

  group('MobileNumberCheckScreen', () {
    Widget buildWidgetInTest() {
      return MaterialApp(
        home: BlocProvider<MobileNumberCheckCubit>.value(
          value: checkCubit,
          child: MobileNumberCheckScreen(),
        ),
      );
    }

    test('should self navigate', () {
      MobileNumberCheckScreen.pushNamed();

      verify(() => mockNavigatorContext.pushNamed(Screen.mobileNumberCheckScreen.name,
          extra: any(named: 'extra'))).called(1);
    });

    testWidgets('should show correct UI', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      expect(find.text(EvoStrings.mobileNumberCheckTitle), findsOneWidget);
      expect(find.text(EvoStrings.mobileNumberCheckDesc), findsOneWidget);
      expect(find.byType(EvoTextField), findsOneWidget);
      expect(
        find.descendant(
          of: find.byType(CommonButton),
          matching: find.text(EvoStrings.activeAccountText),
        ),
        findsOneWidget,
      );
    });

    testWidgets('login button on tap should call cubit checkMobileNumber',
        (WidgetTester tester) async {
      when(() => checkCubit.checkMobileNumber(phoneNumber: any(named: 'phoneNumber')))
          .thenAnswer((_) async {});

      await tester.pumpWidget(buildWidgetInTest());

      await tester.enterText(find.byType(EvoTextField), '**********');
      await tester.pumpAndSettle();

      await tester.tap(find.widgetWithText(PrimaryButton, EvoStrings.activeAccountText));
      await tester.pumpAndSettle();

      verify(() => checkCubit.checkMobileNumber(phoneNumber: any(named: 'phoneNumber'))).called(1);
    });

    testWidgets('should disable the activate account button when phone number is empty',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      // clear text field
      await tester.enterText(find.byType(EvoTextField), '');
      await tester.pumpAndSettle();

      await tester.tap(find.widgetWithText(PrimaryButton, EvoStrings.activeAccountText));
      await tester.pumpAndSettle();

      verifyNever(() => checkCubit.checkMobileNumber(phoneNumber: any(named: 'phoneNumber')));
    });

    testWidgets('should show error in text field, not in dialog for bad request errors',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      final String userMessage = 'user message';

      checkCubit.emit(MobileNumberCheckFailedBadRequest(ErrorUIModel(userMessage: userMessage)));
      await tester.pump();

      expect(find.widgetWithText(EvoTextField, userMessage), findsOneWidget);

      verifyNever(() => evoDialogFunction.showDialogConfirm(
            textPositive: any(named: 'textPositive'),
            dialogId: any(named: 'dialogId'),
          ));
      verifyNever(() => evoSnackBar.show(any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInMilliSec: any(named: 'durationInMilliSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio')));
    });

    testWidgets('should clear error in text field when next state is NOT bad request error state',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      final String userMessage = 'user message';

      checkCubit.emit(MobileNumberCheckFailedBadRequest(ErrorUIModel(userMessage: userMessage)));
      await tester.pump();

      expect(find.widgetWithText(EvoTextField, userMessage), findsOneWidget);

      checkCubit.emit(MobileNumberCheckLoading());
      await tester.pump();
      expect(find.widgetWithText(EvoTextField, userMessage), findsNothing);
    });

    testWidgets('should NOT show error in text field when state is NOT bad request error',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      final String userMessage = 'user message';

      checkCubit
          .emit(MobileNumberCheckFailed(ErrorUIModel(userMessage: userMessage, statusCode: 404)));
      await tester.pump();

      expect(find.widgetWithText(EvoTextField, userMessage), findsNothing);
    });

    testWidgets('should show error limit exceeded dialog with strings from state',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      final String userMessage = 'user message';
      final String userMessageTitle = 'user message title';

      checkCubit.emit(MobileNumberCheckFailedLimitExceeded(ErrorUIModel(
        userMessage: userMessage,
        userMessageTitle: userMessageTitle,
      )));
      await tester.pump();

      verify(() => evoDialogFunction.showDialogConfirm(
            dialogId: EvoDialogId.activateAccountErrorLimitExceededDialog,
            title: userMessageTitle,
            content: userMessage,
            textPositive: EvoStrings.backToHomePage,
            alertType: DialogAlertType.error,
            autoClosePopupWhenClickCTA: true,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });
    testWidgets('should show error limit exceeded dialog with default strings',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      checkCubit.emit(MobileNumberCheckFailedLimitExceeded(ErrorUIModel()));
      await tester.pump();

      verify(() => evoDialogFunction.showDialogConfirm(
            dialogId: EvoDialogId.activateAccountErrorLimitExceededDialog,
            title: EvoStrings.maxTriesReached,
            content: EvoStrings.tryAgainLater,
            textPositive: EvoStrings.backToHomePage,
            alertType: DialogAlertType.error,
            autoClosePopupWhenClickCTA: true,
            onClickPositive: any(named: 'onClickPositive'),
          )).called(1);
    });

    testWidgets('should keep entered text field value when navigating to another screen',
        (WidgetTester tester) async {
      final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
      await tester.pumpWidget(
        MaterialApp(
          navigatorKey: navigatorKey,
          home: BlocProvider<MobileNumberCheckCubit>.value(
            value: checkCubit,
            child: MobileNumberCheckScreen(),
          ),
        ),
      );
      final NavigatorState nav = navigatorKey.currentState!;

      final String number = '**********';
      await tester.enterText(find.byType(EvoTextField), number);
      expect(find.text('************'), findsOneWidget);

      nav.push(MaterialPageRoute<dynamic>(
        builder: (_) => SizedBox(),
      ));
      await tester.pumpAndSettle();
      expect(find.text('************'), findsNothing);

      nav.pop();
      await tester.pumpAndSettle();
      expect(find.text('************'), findsOneWidget);
    });

    testWidgets('should navigate to verify otp screen on success', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      final String number = '*********';
      await tester.enterText(find.byType(EvoTextField), number);
      await tester.pumpAndSettle();

      checkCubit.emit(MobileNumberCheckSuccess(
          entity: AccountActivationEntity(challengeType: AccountActivationType.verifyOTPValue)));
      await tester.pumpAndSettle();

      final VerifyOtpPageArg args = verify(() => mockNavigatorContext.pushNamed(
            Screen.verifyOtpScreen.name,
            extra: captureAny(named: 'extra'),
          )).captured.first as VerifyOtpPageArg;

      expect(args.contactInfo, '63$number');
    });
  });
}
