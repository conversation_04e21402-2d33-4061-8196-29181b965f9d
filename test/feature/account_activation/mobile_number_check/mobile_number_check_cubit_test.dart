import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/mobile_number_check/mobile_number_check_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  late AuthenticationRepo mockAuthRepo;
  late MobileNumberCheckCubit cubit;

  setUpAll(() {
    registerFallbackValue(ActivateAccountVerifyPhoneNumberRequest());
  });

  setUp(() {
    mockAuthRepo = MockAuthenticationRepo();
    cubit = MobileNumberCheckCubit(authRepo: mockAuthRepo);

    when(() => mockAuthRepo.activateAccount(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{'data': <String, dynamic>{}},
        )));
  });

  blocTest<MobileNumberCheckCubit, MobileNumberCheckState>(
    'emits [MobileNumberCheckLoading, MobileNumberCheckSuccess] when checkMobileNumber succeeds',
    build: () => cubit,
    wait: TestConstant.blocEmitStateDelayDuration,
    act: (MobileNumberCheckCubit cubit) => cubit.checkMobileNumber(phoneNumber: '**********'),
    expect: () => <TypeMatcher<MobileNumberCheckState>>[
      isA<MobileNumberCheckLoading>(),
      isA<MobileNumberCheckSuccess>(),
    ],
  );

  blocTest<MobileNumberCheckCubit, MobileNumberCheckState>(
    'emits [MobileNumberCheckLoading, MobileNumberCheckFailed] when checkMobileNumber fails',
    setUp: () {
      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.NOT_FOUND,
            response: <String, dynamic>{},
          )));
    },
    wait: TestConstant.blocEmitStateDelayDuration,
    build: () => cubit,
    act: (MobileNumberCheckCubit cubit) => cubit.checkMobileNumber(phoneNumber: '**********'),
    expect: () => <TypeMatcher<MobileNumberCheckState>>[
      isA<MobileNumberCheckLoading>(),
      isA<MobileNumberCheckFailed>().having(
          (MobileNumberCheckFailed state) => state.errorUIModel.statusCode,
          'verify statusCode',
          CommonHttpClient.NOT_FOUND),
    ],
  );

  blocTest<MobileNumberCheckCubit, MobileNumberCheckState>(
    'emits [MobileNumberCheckLoading, MobileNumberCheckFailedBadRequest] when error is BAD_REQUEST',
    setUp: () {
      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: null,
          )));
    },
    wait: TestConstant.blocEmitStateDelayDuration,
    build: () => cubit,
    act: (MobileNumberCheckCubit cubit) => cubit.checkMobileNumber(phoneNumber: '**********'),
    expect: () => <TypeMatcher<dynamic>>[
      isA<MobileNumberCheckLoading>(),
      isA<MobileNumberCheckFailedBadRequest>(),
    ],
  );

  blocTest<MobileNumberCheckCubit, MobileNumberCheckState>(
    'emits [MobileNumberCheckLoading, MobileNumberCheckFailedLimitExceeded] when error is LIMIT_EXCEEDED',
    setUp: () {
      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.LIMIT_EXCEEDED,
            response: null,
          )));
    },
    wait: TestConstant.blocEmitStateDelayDuration,
    build: () => cubit,
    act: (MobileNumberCheckCubit cubit) => cubit.checkMobileNumber(phoneNumber: '**********'),
    expect: () => <TypeMatcher<dynamic>>[
      isA<MobileNumberCheckLoading>(),
      isA<MobileNumberCheckFailedLimitExceeded>(),
    ],
  );

  blocTest<MobileNumberCheckCubit, MobileNumberCheckState>(
    'emits [MobileNumberCheckLoading, MobileNumberCheckFailedLimitExceeded] when error is LOCKED_RESOURCE',
    setUp: () {
      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => AccountActivationEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.LOCKED_RESOURCE,
            response: null,
          )));
    },
    wait: TestConstant.blocEmitStateDelayDuration,
    build: () => cubit,
    act: (MobileNumberCheckCubit cubit) => cubit.checkMobileNumber(phoneNumber: '**********'),
    expect: () => <TypeMatcher<dynamic>>[
      isA<MobileNumberCheckLoading>(),
      isA<MobileNumberCheckFailedLimitExceeded>(),
    ],
  );

  test('should send an ActivateAccountVerifyPhoneNumberRequest when activating account', () async {
    when(() => mockAuthRepo.activateAccount(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async => AccountActivationEntity());

    final String phoneNumber = '**********';
    await cubit.checkMobileNumber(phoneNumber: phoneNumber);

    final ActivateAccountRequest request = verify(() => mockAuthRepo.activateAccount(
        request: captureAny(named: 'request'),
        mockConfig: any(named: 'mockConfig'))).captured.first as ActivateAccountRequest;

    expect(request, isA<ActivateAccountVerifyPhoneNumberRequest>());
    expect((request as ActivateAccountVerifyPhoneNumberRequest).phoneNumber, phoneNumber);
  });
}
