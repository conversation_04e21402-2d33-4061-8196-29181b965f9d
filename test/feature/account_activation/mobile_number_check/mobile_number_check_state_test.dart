import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/mobile_number_check/mobile_number_check_cubit.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Verify MobileNumberCheckState', () {
    test('should be a subclass of MobileNumberCheckState', () {
      final MobileNumberCheckInitial state = MobileNumberCheckInitial();
      expect(state, isA<MobileNumberCheckState>());
    });

    test('should be a subclass of MobileNumberCheckState', () {
      final MobileNumberCheckLoading state = MobileNumberCheckLoading();
      expect(state, isA<MobileNumberCheckState>());
    });

    test('should be a subclass of MobileNumberCheckState', () {
      final BaseEntity entity = BaseEntity();
      final MobileNumberCheckSuccess state = MobileNumberCheckSuccess(entity: entity);
      expect(state, isA<MobileNumberCheckState>());
      expect(state.entity, entity);
    });

    test('should be a subclass of MobileNumberCheckState', () {
      final ErrorUIModel errorModel = ErrorUIModel();
      final MobileNumberCheckFailed state = MobileNumberCheckFailed(errorModel);
      expect(state, isA<MobileNumberCheckState>());
      expect(state.errorUIModel, errorModel);
    });

    test('MobileNumberCheckFailedBadRequest should be a subclass of MobileNumberCheckFailed', () {
      final ErrorUIModel errorModel = ErrorUIModel();
      final MobileNumberCheckFailedBadRequest state = MobileNumberCheckFailedBadRequest(errorModel);
      expect(state, isA<MobileNumberCheckFailed>());
      expect(state.errorUIModel, errorModel);
    });

    test('MobileNumberCheckFailedLimitExceeded should be a subclass of MobileNumberCheckFailed',
        () {
      final ErrorUIModel errorModel = ErrorUIModel();
      final MobileNumberCheckFailedLimitExceeded state =
          MobileNumberCheckFailedLimitExceeded(errorModel);
      expect(state, isA<MobileNumberCheckFailed>());
      expect(state.errorUIModel, errorModel);
    });
  });
}
