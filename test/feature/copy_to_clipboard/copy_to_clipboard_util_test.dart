import 'package:evoapp/feature/copy_to_clipboard/copy_to_clipboard_util.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockDeviceInfoPluginWrapper extends Mock implements DeviceInfoPluginWrapper {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

void main() {
  late MockDeviceInfoPluginWrapper mockDeviceInfoPluginWrapper;
  late MockEvoSnackBar mockEvoSnackBar;
  late CopyToClipboardUtil copyToClipboardUtil;

  setUp(() {
    mockDeviceInfoPluginWrapper = MockDeviceInfoPluginWrapper();
    mockEvoSnackBar = MockEvoSnackBar();
    copyToClipboardUtil = CopyToClipboardUtil(
      deviceInfoPluginWrapper: mockDeviceInfoPluginWrapper,
      evoSnackBar: mockEvoSnackBar,
    );
  });

  void stubDeviceInfoPluginWrapper({required bool isAndroidDevice}) {
    final DevicePlatform mockDevicePlatform = MockDevicePlatform();
    when(() => mockDeviceInfoPluginWrapper.platform).thenReturn(mockDevicePlatform);
    when(() => mockDevicePlatform.isAndroid()).thenReturn(isAndroidDevice);
  }

  group('isAndroid13OrAbove', () {
    test('should return true if Android version is 13 or above', () {
      stubDeviceInfoPluginWrapper(isAndroidDevice: true);
      when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenReturn('33');

      final bool result = copyToClipboardUtil.isAndroid13OrAbove();

      expect(result, true);
    });

    test('should return false if Android version is below 13', () {
      stubDeviceInfoPluginWrapper(isAndroidDevice: true);
      when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenReturn('32');

      final bool result = copyToClipboardUtil.isAndroid13OrAbove();

      expect(result, false);
    });

    test('should return false if platform is not Android', () {
      stubDeviceInfoPluginWrapper(isAndroidDevice: false);

      final bool result = copyToClipboardUtil.isAndroid13OrAbove();

      expect(result, false);
    });
  });

  group('showFeedback', () {
    test('should not show feedback if Android version is 13 or above', () {
      stubDeviceInfoPluginWrapper(isAndroidDevice: true);
      when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenReturn('33');

      copyToClipboardUtil.showFeedback(toastMsg: 'Test message');

      verifyNever(
          () => mockEvoSnackBar.show(any(), durationInMilliSec: any(named: 'durationInMilliSec')));
    });

    test('should show feedback if Android version is below 13', () {
      stubDeviceInfoPluginWrapper(isAndroidDevice: true);
      when(() => mockDeviceInfoPluginWrapper.getOSVersion()).thenReturn('32');
      when(() => mockEvoSnackBar.show(any(), durationInMilliSec: any(named: 'durationInMilliSec')))
          .thenAnswer((_) => Future<bool?>.value(true));

      copyToClipboardUtil.showFeedback(toastMsg: 'Test message');

      verify(() => mockEvoSnackBar.show('Test message',
          durationInMilliSec: SnackBarDuration.short.value)).called(1);
    });

    test('should show feedback if platform is not Android', () {
      stubDeviceInfoPluginWrapper(isAndroidDevice: false);
      when(() => mockEvoSnackBar.show(any(), durationInMilliSec: any(named: 'durationInMilliSec')))
          .thenAnswer((_) => Future<bool?>.value(true));

      copyToClipboardUtil.showFeedback(toastMsg: 'Test message');

      verify(() => mockEvoSnackBar.show('Test message',
          durationInMilliSec: SnackBarDuration.short.value)).called(1);
    });
  });
}
