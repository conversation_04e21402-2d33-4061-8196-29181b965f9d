import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/copy_to_clipboard/copy_to_clipboard_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

void main() {
  const String mockData = 'mock_data';
  late CommonUtilFunction mockCommonUtilFunction;
  late CopyToClipboardCubit copyToClipboardCubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerSingleton<CommonUtilFunction>(MockCommonUtilFunction());
    mockCommonUtilFunction = getIt<CommonUtilFunction>();
  });

  setUp(() {
    copyToClipboardCubit = CopyToClipboardCubit();
  });

  tearDownAll(() {
    getIt.reset();
  });

  test('create constructor correctly', () {
    expect(copyToClipboardCubit.state, isA<CopyToClipBoardState>());
  });

  blocTest<CopyToClipboardCubit, CopyToClipBoardState>(
    'emits [CopiedSuccess] when mockCommonUtilFunction.copyToClipboard is called',
    setUp: () {
      when(() => mockCommonUtilFunction.copyToClipboard(any()))
          .thenAnswer((_) => Future<void>.value());
    },
    build: () => copyToClipboardCubit,
    act: (CopyToClipboardCubit cubit) => cubit.copyToClipboard(mockData),
    expect: () => <dynamic>[
      isA<CopiedSuccess>(),
    ],
    verify: (_) {
      verify(() => mockCommonUtilFunction.copyToClipboard(mockData)).called(1);
    },
  );
}
