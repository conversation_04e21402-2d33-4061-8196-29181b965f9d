import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/feature/copy_to_clipboard/copy_to_clipboard_cubit.dart';

void main() {
  group('CopyToClipBoardState', () {
    test('CopiedToClipboardInitial should be instantiated', () {
      final CopiedToClipboardInitial state = CopiedToClipboardInitial();
      expect(state, isA<CopiedToClipboardInitial>());
    });

    test('CopiedSuccess should be instantiated', () {
      final CopiedSuccess state = CopiedSuccess();
      expect(state, isA<CopiedSuccess>());
    });
  });
}