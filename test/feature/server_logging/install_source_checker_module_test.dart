import 'package:evoapp/feature/server_logging/install_source_checker_module.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

class TestInstallSourceCheckerModule with InstallSourceCheckerModule {}

void main() {
  late TestInstallSourceCheckerModule installSourceCheckerModule;
  late LoggingRepo loggingRepo;

  // Get the method channel used by the StoreChecker plugin
  const MethodChannel methodChannel = MethodChannel('store_checker');
  const String requestGetSourceMethod = 'getSource';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    installSourceCheckerModule = TestInstallSourceCheckerModule();

    loggingRepo = MockLoggingRepo();
    getIt.registerLazySingleton<LoggingRepo>(() => loggingRepo);
  });

  group('verify logInstallationSource()', () {
    setUp(() {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(methodChannel, (MethodCall methodCall) async {
        if (methodCall.method == requestGetSourceMethod) {
          return 'unknown';
        }
        return null;
      });
    });

    tearDown(() {
      // Reset the method call handler before each test
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(methodChannel, null);
    });

    test('logInstallationSource should log the correct installation source', () async {
      /// stub out method
      when(() => loggingRepo.logEvent(eventType: EventType.installSource, data: any(named: 'data')))
          .thenAnswer((_) async => Future<void>.value());

      // Act
      await installSourceCheckerModule.logInstallationSource();

      // Assert
      verify(() => loggingRepo.logEvent(
            eventType: EventType.installSource,
            data: <String, dynamic>{'source': 'unknown_source'},
          )).called(1);
    });
  });

  group('verify getInstallSourceByLibrary()', () {
    tearDown(() {
      // Reset the method call handler before each test
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(methodChannel, null);
    });

    /// due to can not simulate Platform.isAndroid and Platform.isIOS form 3rd library
    /// so we just stub-out the test-case with unknown_source
    test('value is correct if StoreChecker.getSource is invoked', () async {
      // Set the mock method call handler to return a valid Source
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(methodChannel, (MethodCall methodCall) async {
        if (methodCall.method == requestGetSourceMethod) {
          return 'unknown';
        }
        return null;
      });

      final Source installationSource =
          await installSourceCheckerModule.getInstallSourceByLibrary();
      expect(installationSource, Source.UNKNOWN);
    });

    test('value is Source.UNKNOWN if StoreChecker.getSource is invoked and EXCEPTION is thrown',
        () async {
      /// stub out method
      when(() => loggingRepo.logErrorEvent(
          errorType: EventType.installSource.name,
          args: any(named: 'args'))).thenAnswer((_) async => Future<void>.value());

      // Set the mock method call handler to throw a PlatformException
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(methodChannel, (MethodCall methodCall) async {
        if (methodCall.method == requestGetSourceMethod) {
          throw PlatformException(code: 'ERROR');
        }
        return null;
      });

      final Source installationSource =
          await installSourceCheckerModule.getInstallSourceByLibrary();
      expect(installationSource, Source.UNKNOWN);

      verify(() => loggingRepo.logErrorEvent(
            errorType: EventType.installSource.name,
            args: captureAny(named: 'args'),
          )).called(1);
    });

    test('value is Source.UNKNOWN if StoreChecker.getSource is invoked and ERROR is thrown',
        () async {
      /// stub out method
      when(() => loggingRepo.logErrorEvent(
          errorType: EventType.installSource.name,
          args: any(named: 'args'))).thenAnswer((_) async => Future<void>.value());

      // Set the mock method call handler to throw a PlatformException
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(methodChannel, (MethodCall methodCall) async {
        if (methodCall.method == requestGetSourceMethod) {
          throw Error();
        }
        return null;
      });

      final Source installationSource =
          await installSourceCheckerModule.getInstallSourceByLibrary();
      expect(installationSource, Source.UNKNOWN);

      verify(() => loggingRepo.logErrorEvent(
            errorType: EventType.installSource.name,
            args: captureAny(named: 'args'),
          )).called(1);
    });
  });

  group('verify mapInstallSourceToString()', () {
    test('return play_store if source = Source.IS_INSTALLED_FROM_PLAY_STORE', () async {
      // Act
      final String installationSource =
          installSourceCheckerModule.mapInstallSourceToString(Source.IS_INSTALLED_FROM_PLAY_STORE);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.playStoreSource);
    });

    test('return local_source if source = Source.IS_INSTALLED_FROM_LOCAL_SOURCE', () async {
      // Act
      final String installationSource = installSourceCheckerModule
          .mapInstallSourceToString(Source.IS_INSTALLED_FROM_LOCAL_SOURCE);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.localSourceSource);
    });

    test('return amazon_store if source = Source.IS_INSTALLED_FROM_AMAZON_APP_STORE', () async {
      // Act
      final String installationSource = installSourceCheckerModule
          .mapInstallSourceToString(Source.IS_INSTALLED_FROM_AMAZON_APP_STORE);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.amazonStoreSource);
    });

    test('return huawei_app_gallery if source = Source.IS_INSTALLED_FROM_HUAWEI_APP_GALLERY',
        () async {
      // Act
      final String installationSource = installSourceCheckerModule
          .mapInstallSourceToString(Source.IS_INSTALLED_FROM_HUAWEI_APP_GALLERY);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.huaweiAppGallerySource);
    });

    test('return samsung_galaxy_store if source = Source.IS_INSTALLED_FROM_SAMSUNG_GALAXY_STORE',
        () async {
      // Act
      final String installationSource = installSourceCheckerModule
          .mapInstallSourceToString(Source.IS_INSTALLED_FROM_SAMSUNG_GALAXY_STORE);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.samsungGalaxyStoreSource);
    });

    test(
        'return samsung_smart_switch_mobile if source = Source.IS_INSTALLED_FROM_SAMSUNG_SMART_SWITCH_MOBILE',
        () async {
      // Act
      final String installationSource = installSourceCheckerModule
          .mapInstallSourceToString(Source.IS_INSTALLED_FROM_SAMSUNG_SMART_SWITCH_MOBILE);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.samsungSmartSwitchSource);
    });

    test('return xiaomi_get_apps if source = Source.IS_INSTALLED_FROM_XIAOMI_GET_APPS', () async {
      // Act
      final String installationSource = installSourceCheckerModule
          .mapInstallSourceToString(Source.IS_INSTALLED_FROM_XIAOMI_GET_APPS);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.xiaomiAppStoreSource);
    });

    test('return oppo_app_market if source = Source.IS_INSTALLED_FROM_OPPO_APP_MARKET', () async {
      // Act
      final String installationSource = installSourceCheckerModule
          .mapInstallSourceToString(Source.IS_INSTALLED_FROM_OPPO_APP_MARKET);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.oppoAppMarketSource);
    });

    test('return vivo_app_store if source = Source.IS_INSTALLED_FROM_VIVO_APP_STORE', () async {
      // Act
      final String installationSource = installSourceCheckerModule
          .mapInstallSourceToString(Source.IS_INSTALLED_FROM_VIVO_APP_STORE);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.vivoAppStoreSource);
    });

    test('return other_source if source = Source.IS_INSTALLED_FROM_OTHER_SOURCE', () async {
      // Act
      final String installationSource = installSourceCheckerModule
          .mapInstallSourceToString(Source.IS_INSTALLED_FROM_OTHER_SOURCE);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.otherSource);
    });

    test('return app_store if source = Source.IS_INSTALLED_FROM_APP_STORE', () async {
      // Act
      final String installationSource =
          installSourceCheckerModule.mapInstallSourceToString(Source.IS_INSTALLED_FROM_APP_STORE);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.appStoreSource);
    });

    test('return test_flight if source = Source.IS_INSTALLED_FROM_TEST_FLIGHT', () async {
      // Act
      final String installationSource =
          installSourceCheckerModule.mapInstallSourceToString(Source.IS_INSTALLED_FROM_TEST_FLIGHT);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.testflightSource);
    });

    test('return unknown_source if source = Source.UNKNOWN', () async {
      // Act
      final String installationSource =
          installSourceCheckerModule.mapInstallSourceToString(Source.UNKNOWN);
      // Assert
      expect(installationSource, InstallSourceCheckerModule.unknownSource);
    });
  });
}
