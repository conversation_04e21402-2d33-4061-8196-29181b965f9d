import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/transaction_details/transaction_details_cubit.dart';
import 'package:evoapp/feature/transaction_details/transaction_details_state.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../constant.dart';

void main() {
  group('TransactionDetailsCubit', () {
    test('init with TransactionDetailsInitial', () {
      expect(TransactionDetailsCubit().state, isA<TransactionDetailsInitial>());
    });

    blocTest<TransactionDetailsCubit, TransactionDetailsState>(
      'should emit TransactionDetailsSuccess when request is successful',
      build: () => TransactionDetailsCubit(),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (TransactionDetailsCubit cubit) => cubit.getTransactionDetails('id'),
      expect: () => <dynamic>[
        isA<TransactionDetailsLoading>(),
        isA<TransactionDetailsSuccess>(),
      ],
    );
  });
}
