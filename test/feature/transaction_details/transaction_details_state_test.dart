import 'package:evoapp/data/response/transaction_details_entity.dart';
import 'package:evoapp/feature/transaction_details/transaction_details_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TransactionDetailsState', () {
    test('Subclasses should extend TransactionDetailsState', () async {
      expect(TransactionDetailsInitial(), isA<TransactionDetailsState>());
      expect(TransactionDetailsLoading(), isA<TransactionDetailsState>());
      expect(TransactionDetailsSuccess(details: TransactionDetailsEntity()),
          isA<TransactionDetailsState>());
      expect(TransactionDetailsError(), isA<TransactionDetailsState>());
    });

    test('TransactionDetailsSuccessState should have correct properties', () async {
      final TransactionDetailsEntity details = TransactionDetailsEntity();
      final TransactionDetailsSuccess state = TransactionDetailsSuccess(details: details);
      expect(state.details, details);
    });
  });
}
