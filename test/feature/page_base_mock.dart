import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';

class PageBaseMock extends PageBase {
  @override
  final RouteSettings routeSettings;

  const PageBaseMock({required this.routeSettings, super.key});

  @override
  State<StatefulWidget> createState() => _PageBaseMockState();

  @override
  EventTrackingScreenId get eventTrackingScreenId => EventTrackingScreenId.undefined;
}

class _PageBaseMockState extends State<PageBaseMock> {
  @override
  Widget build(BuildContext context) {
    throw UnimplementedError();
  }
}
