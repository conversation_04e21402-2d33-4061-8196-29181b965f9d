import 'dart:async';

import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockStreamController<UnauthorizedSessionState> extends Mock
    implements StreamController<UnauthorizedSessionState> {}

void main() {
  late AuthorizationSessionExpiredHandlerImpl sessionExpiredHandler;
  late EvoUtilFunction evoUtilFunction;
  setUpAll(() async {
    getIt.registerLazySingleton<AppState>(() => AppState());

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    evoUtilFunction = getIt.get<EvoUtilFunction>();
  });

  setUp(() {
    sessionExpiredHandler = AuthorizationSessionExpiredHandlerImpl();
    when(() => evoUtilFunction.clearAllUserData()).thenAnswer((_) async => Future<void>.value());
    when(() => evoUtilFunction.clearDataOnTokenInvalid())
        .thenAnswer((_) async => Future<void>.value());
  });

  tearDown(() {
    reset(evoUtilFunction);
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  test(
    'test can emit UnauthorizedSessionState.invalidToken',
    () async {
      getIt.get<AppState>().isUserLogIn = true;

      final Stream<UnauthorizedSessionState> stream = sessionExpiredHandler.getStreamSubscription();

      expectLater(stream, emits(UnauthorizedSessionState.invalidToken));

      sessionExpiredHandler.emitUnauthorized(UnauthorizedSessionState.invalidToken);

      expect(getIt.get<AppState>().isUserLogIn, false);
      verify(() => evoUtilFunction.clearDataOnTokenInvalid()).called(1);
      verify(() => evoUtilFunction.clearUserInfoAppState()).called(1);
      verifyNever(() => evoUtilFunction.clearAllUserData());
    },
  );

  test(
    'test can emit UnauthorizedSessionState.unknown',
    () {
      getIt.get<AppState>().isUserLogIn = true;

      final Stream<UnauthorizedSessionState> stream = sessionExpiredHandler.getStreamSubscription();

      expectLater(stream, emits(UnauthorizedSessionState.unknown));

      sessionExpiredHandler.emitUnauthorized(UnauthorizedSessionState.unknown);

      expect(getIt.get<AppState>().isUserLogIn, false);
      verify(() => evoUtilFunction.clearDataOnTokenInvalid()).called(1);
      verify(() => evoUtilFunction.clearUserInfoAppState()).called(1);
      verifyNever(() => evoUtilFunction.clearAllUserData());
    },
  );

  test(
    'test can emit UnauthorizedSessionState.forcedLogout',
    () {
      getIt.get<AppState>().isUserLogIn = true;

      final Stream<UnauthorizedSessionState> stream = sessionExpiredHandler.getStreamSubscription();

      expectLater(stream, emits(UnauthorizedSessionState.forcedLogout));

      sessionExpiredHandler.emitUnauthorized(UnauthorizedSessionState.forcedLogout);

      expect(getIt.get<AppState>().isUserLogIn, false);
      verify(() => evoUtilFunction.clearAllUserData()).called(1);
      verifyNever(() => evoUtilFunction.clearDataOnTokenInvalid());
      verifyNever(() => evoUtilFunction.clearUserInfoAppState());
    },
  );

  test(
    'should not emit any state if user is not logged in',
    () async {
      getIt.get<AppState>().isUserLogIn = false;

      // Create a subscription to the stream
      final StreamSubscription<UnauthorizedSessionState> subscription =
          sessionExpiredHandler.getStreamSubscription().listen(
                expectAsync1(
                  (UnauthorizedSessionState event) {
                    fail('No events should be emitted');
                  },
                  count: 0, // Expect 0 events
                ),
              );

      // Call the method
      await sessionExpiredHandler.emitUnauthorized(UnauthorizedSessionState.forcedLogout);

      // Allow some time for potential emissions
      await Future<void>.delayed(const Duration(milliseconds: 100));

      // Cancel the subscription
      await subscription.cancel();

      // Verify that no data clearing functions are called
      verifyNever(() => evoUtilFunction.clearAllUserData());
      verifyNever(() => evoUtilFunction.clearDataOnTokenInvalid());
      verifyNever(() => evoUtilFunction.clearUserInfoAppState());
    },
  );

  test('close() should close the inner stream', () async {
    sessionExpiredHandler.close();

    expect(sessionExpiredHandler.authorizationSessionExpireController.isClosed, isTrue);
  });
}
