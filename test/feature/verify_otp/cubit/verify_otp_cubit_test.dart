import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/verify_otp/cubit/otp_success_model.dart';
import 'package:evoapp/feature/verify_otp/cubit/verify_otp_cubit.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_otp_handler.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class MockVerifyOtpHandler extends Mock implements VerifyOtpHandler {}

void main() {
  late VerifyOtpCubit cubit;
  late MockVerifyOtpHandler mockHandler;

  setUp(() {
    mockHandler = MockVerifyOtpHandler();
    cubit = VerifyOtpCubit(verifyHandler: mockHandler);
  });

  tearDown(() {
    cubit.close();
    reset(mockHandler);
  });

  group('verifyOtp', () {
    const String otp = '123456';
    const String sessionToken = 'sessionToken';
    const String mockChallengeType = 'mock_challenge_type';
    const String mockSessionToken = 'mock_session_token';

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, VerifyOtpSuccess] when verifyOtp is successful',
      setUp: () {
        when(() => mockHandler.verifyOtp(any(), any())).thenAnswer(
          (_) async => VerifyOtpModel.success(
            successModel:
                OtpSuccessModel(challengeType: mockChallengeType, sessionToken: mockSessionToken),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.verifyOtp(otp, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<VerifyOtpSuccess>().having(
            (VerifyOtpSuccess state) => (state.uiModel.challengeType, state.uiModel.sessionToken),
            'verify success model',
            (mockChallengeType, mockSessionToken)),
      ],
      verify: (_) {
        verify(() => mockHandler.verifyOtp(otp, <String, String>{'sessionToken': sessionToken}))
            .called(1);
      },
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, VerifyOtpSessionExpired] when errorType is expired session',
      setUp: () {
        when(() => mockHandler.verifyOtp(any(), any())).thenAnswer((_) async =>
            VerifyOtpModel.error(
                errorType: OtpErrorType.sessionExpired, errorUIModel: ErrorUIModel()));
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.verifyOtp(otp, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<VerifyOtpSessionExpired>(),
      ],
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, LimitExceedOtp] when errorType is limit exceeded',
      setUp: () {
        when(() => mockHandler.verifyOtp(any(), any())).thenAnswer(
          (_) async => VerifyOtpModel.error(
            errorType: OtpErrorType.limitExceeded,
            errorUIModel: ErrorUIModel(userMessage: 'Limit exceeded'),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.verifyOtp(otp, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<LimitExceedOtp>().having(
            (LimitExceedOtp state) => state.errorText, 'verify error text', 'Limit exceeded'),
      ],
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, VerifyOtpFailed] when errorType is invalid param',
      setUp: () {
        when(() => mockHandler.verifyOtp(any(), any())).thenAnswer(
          (_) async => VerifyOtpModel.error(
            errorType: OtpErrorType.invalidParams,
            errorUIModel: ErrorUIModel(userMessage: 'Invalid params'),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.verifyOtp(otp, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<VerifyOtpFailed>().having(
            (VerifyOtpFailed state) => (state.error.userMessage, state.unknownError),
            'verify userMessage & unknownError',
            ('Invalid params', false)),
      ],
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, VerifyOtpFailed] when errorType is unknown',
      setUp: () {
        when(() => mockHandler.verifyOtp(any(), any())).thenAnswer(
          (_) async => VerifyOtpModel.error(
            errorType: OtpErrorType.unknown,
            errorUIModel: ErrorUIModel(userMessage: 'unknown message'),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.verifyOtp(otp, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<VerifyOtpFailed>().having(
            (VerifyOtpFailed state) => (state.error.userMessage, state.unknownError),
            'verify userMessage & unknownError',
            ('unknown message', true)),
      ],
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, VerifyOtpThirdPartyError] when errorType is thirdParty',
      setUp: () {
        when(() => mockHandler.verifyOtp(any(), any())).thenAnswer(
          (_) async => VerifyOtpModel.error(
            errorType: OtpErrorType.thirdParty,
            errorUIModel: ErrorUIModel(userMessage: 'Third party error'),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.verifyOtp(otp, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<VerifyOtpThirdPartyError>().having(
          (VerifyOtpThirdPartyError state) => state.error.userMessage,
          'verify error message',
          'Third party error',
        ),
      ],
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, VerifyEmailOtpDuplicateEmailError] when errorType is duplicateEmail',
      setUp: () {
        when(() => mockHandler.verifyOtp(any(), any())).thenAnswer(
          (_) async => VerifyOtpModel.error(
            errorType: OtpErrorType.duplicateEmail,
            errorUIModel: ErrorUIModel(userMessage: 'duplicate error'),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.verifyOtp(otp, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<VerifyEmailOtpDuplicateEmailError>().having(
          (VerifyEmailOtpDuplicateEmailError state) => state.error.userMessage,
          'verify userMessage',
          'duplicate error',
        ),
      ],
    );
  });

  group('resendOtp', () {
    const String phoneNumber = '035508290';
    const String sessionToken = 'sessionToken';

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, ResendOtpSuccess] when resendOtp is successful',
      setUp: () {
        when(() => mockHandler.resendOtp(any(), any())).thenAnswer(
          (_) async => ResendOtpModel.success(
            resendOtpSuccess: ResendOtpSuccess(
              otpResendSecs: 30,
              sessionToken: sessionToken,
              otpValiditySecs: 60,
            ),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.resendOtp(phoneNumber, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<ResendOtpSuccess>().having(
            (ResendOtpSuccess state) => (
                  state.otpResendSecs,
                  state.sessionToken,
                  state.otpValiditySecs,
                ),
            'verify ui model',
            (30, sessionToken, 60)),
      ],
      verify: (_) {
        verify(() =>
                mockHandler.resendOtp(phoneNumber, <String, String>{'sessionToken': sessionToken}))
            .called(1);
      },
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, VerifyOtpSessionExpired] when errorType is expired session',
      setUp: () {
        when(() => mockHandler.resendOtp(any(), any())).thenAnswer(
          (_) async => ResendOtpModel.error(
              errorType: OtpErrorType.sessionExpired, errorUIModel: ErrorUIModel()),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.resendOtp(phoneNumber, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<VerifyOtpSessionExpired>(),
      ],
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, LimitExceedOtp] when errorType is limit exceeded',
      setUp: () {
        when(() => mockHandler.resendOtp(any(), any())).thenAnswer(
          (_) async => ResendOtpModel.error(
            errorType: OtpErrorType.limitExceeded,
            errorUIModel: ErrorUIModel(userMessage: 'Limit exceeded'),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.resendOtp(phoneNumber, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<LimitExceedOtp>().having((LimitExceedOtp state) => (state.errorText, state.isResent),
            'verify data', ('Limit exceeded', true)),
      ],
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, VerifyOtpFailed] when errorType is invalidParams',
      setUp: () {
        when(() => mockHandler.resendOtp(any(), any())).thenAnswer(
          (_) async => ResendOtpModel.error(
            errorType: OtpErrorType.invalidParams,
            errorUIModel: ErrorUIModel(userMessage: 'One last try'),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.resendOtp(phoneNumber, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<VerifyOtpFailed>().having(
            (VerifyOtpFailed state) => (state.error.userMessage, state.unknownError),
            'verify data',
            ('One last try', false)),
      ],
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, VerifyEmailOtpDuplicateEmailError] when errorType is duplicateEmail',
      setUp: () {
        when(() => mockHandler.resendOtp(any(), any())).thenAnswer(
          (_) async => ResendOtpModel.error(
            errorType: OtpErrorType.duplicateEmail,
            errorUIModel: ErrorUIModel(userMessage: 'duplicate error'),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.resendOtp(phoneNumber, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<VerifyEmailOtpDuplicateEmailError>().having(
          (VerifyEmailOtpDuplicateEmailError state) => state.error.userMessage,
          'verify userMessage',
          'duplicate error',
        ),
      ],
    );

    blocTest<VerifyOtpCubit, VerifyOtpState>(
      'emits [VerifyOtpLoading, VerifyOtpFailed] when errorType is unknown',
      setUp: () {
        when(() => mockHandler.resendOtp(any(), any())).thenAnswer(
          (_) async => ResendOtpModel.error(
            errorType: OtpErrorType.unknown,
            errorUIModel: ErrorUIModel(userMessage: 'Unknown error'),
          ),
        );
      },
      build: () => cubit,
      act: (VerifyOtpCubit cubit) => cubit.resendOtp(phoneNumber, sessionToken),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<VerifyOtpLoading>(),
        isA<VerifyOtpFailed>().having(
            (VerifyOtpFailed state) => (state.error.userMessage, state.unknownError),
            'verify userMessage & unknownError',
            ('Unknown error', true)),
      ],
    );
  });
}
