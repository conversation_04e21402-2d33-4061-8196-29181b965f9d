import 'package:evoapp/feature/verify_otp/cubit/otp_success_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('OtpSuccessModel', () {
    test('should create an instance with null values when no parameters are provided', () {
      final OtpSuccessModel model = OtpSuccessModel();
      
      expect(model.challengeType, null);
      expect(model.sessionToken, null);
      expect(model.ekycClientSettings, null);
      expect(model.verdict, null);
    });

    test('should create an instance with provided values', () {
      final Map<String?, dynamic> mockSettings = <String?, dynamic>{
        'key1': 'value1',
        'key2': 123,
      };
      
      final OtpSuccessModel model = OtpSuccessModel(
        challengeType: 'test_challenge_type',
        sessionToken: 'test_session_token',
        ekycClientSettings: mockSettings,
        verdict: 'test_verdict',
      );
      
      expect(model.challengeType, 'test_challenge_type');
      expect(model.sessionToken, 'test_session_token');
      expect(model.ekycClientSettings, mockSettings);
      expect(model.verdict, 'test_verdict');
    });

    test('should create an instance with partial values', () {
      final OtpSuccessModel model = OtpSuccessModel(
        challengeType: 'test_challenge_type',
        sessionToken: 'test_session_token',
      );
      
      expect(model.challengeType, 'test_challenge_type');
      expect(model.sessionToken, 'test_session_token');
      expect(model.ekycClientSettings, null);
      expect(model.verdict, null);
    });
  });
}
