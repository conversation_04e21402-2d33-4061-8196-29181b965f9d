import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/feature/verify_otp/cubit/otp_success_model.dart';
import 'package:evoapp/feature/verify_otp/cubit/verify_otp_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VerifyOtpState', () {
    test('VerifyOtpInitial can be instantiated', () {
      final VerifyOtpInitial state = VerifyOtpInitial();
      expect(state, isA<VerifyOtpInitial>());
    });

    test('VerifyOtpLoading can be instantiated', () {
      final VerifyOtpLoading state = VerifyOtpLoading();
      expect(state, isA<VerifyOtpLoading>());
    });

    test('LimitExceedOtp can be instantiated with and without errorText', () {
      final LimitExceedOtp stateWithError = LimitExceedOtp(errorText: 'Error occurred');
      final LimitExceedOtp stateWithoutError = LimitExceedOtp(
        errorText: null,
        isResent: true,
      );

      expect(stateWithError, isA<LimitExceedOtp>());
      expect(stateWithError.errorText, 'Error occurred');
      expect(stateWithoutError,
          isA<LimitExceedOtp>().having((state) => state.isResent, 'verify isResent', isTrue));
      expect(stateWithoutError.errorText, isNull);
    });

    test('ResendOtpSuccess can be instantiated with required parameters', () {
      final ResendOtpSuccess state =
          ResendOtpSuccess(otpResendSecs: 30, sessionToken: 'token123', otpValiditySecs: 21);

      expect(state, isA<ResendOtpSuccess>());
      expect(state.otpResendSecs, 30);
      expect(state.sessionToken, 'token123');
      expect(state.otpValiditySecs, 21);
    });

    test('VerifyOtpFailed can be instantiated with required parameters', () {
      final ErrorUIModel error = ErrorUIModel(); // Assuming ErrorUIModel has a default constructor
      final VerifyOtpFailed state = VerifyOtpFailed(error: error, unknownError: false);

      expect(state, isA<VerifyOtpFailed>());
      expect(state.error, error);
      expect(state.unknownError, isFalse);
    });

    test('VerifyOtpFailed unknownError defaults to true', () {
      final ErrorUIModel error = ErrorUIModel(); // Assuming ErrorUIModel has a default constructor
      final VerifyOtpFailed state = VerifyOtpFailed(error: error);

      expect(state.unknownError, isTrue);
    });

    test('VerifyOtpSessionExpired can be instantiated', () {
      final VerifyOtpSessionExpired state = VerifyOtpSessionExpired();
      expect(state, isA<VerifyOtpSessionExpired>());
    });

    test('VerifyOtpSuccess can be instantiated with required parameters', () {
      final OtpSuccessModel uiModel = OtpSuccessModel(
        sessionToken: 'mock_session',
        challengeType: AuthChallengeType.none.value,
      );
      final VerifyOtpSuccess state = VerifyOtpSuccess(uiModel);

      expect(state, isA<VerifyOtpSuccess>());
      expect(state.uiModel, uiModel);
    });

    test('VerifyOtpThirdPartyError can be instantiated with required parameters', () {
      final ErrorUIModel error = ErrorUIModel();
      final VerifyOtpThirdPartyError state = VerifyOtpThirdPartyError(error);

      expect(state, isA<VerifyOtpThirdPartyError>());
      expect(state.error, error);
    });

    group('VerifyEmailOtpDuplicateEmailError', () {
      test('should be instantiated with required parameters', () async {
        final ErrorUIModel error = ErrorUIModel();
        final VerifyEmailOtpDuplicateEmailError state = VerifyEmailOtpDuplicateEmailError(
          error,
          email: 'email',
          sessionToken: 'token',
        );

        expect(state, isA<VerifyOtpState>());
        expect(state.error, error);
        expect(state.email, 'email');
        expect(state.sessionToken, 'token');
      });

      test('should copy with email and session token', () async {
        final ErrorUIModel error = ErrorUIModel();
        final VerifyEmailOtpDuplicateEmailError state = VerifyEmailOtpDuplicateEmailError(
          error,
          email: 'email',
          sessionToken: 'token',
        );

        final VerifyEmailOtpDuplicateEmailError copiedState = state.copyWith(
          email: 'new_email',
          sessionToken: 'new_token',
        );

        expect(copiedState.email, 'new_email');
        expect(copiedState.sessionToken, 'new_token');
      });
    });
  });
}
