// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_ui_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_email_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_otp_handler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockActivateAccountRequest extends ActivateAccountRequest {
  @override
  AccountActivationType get type => AccountActivationType.none;
}

void main() {
  const String fakeUserMessage = 'fake_user_message';
  const String mockOtp = '12345';
  const String mockEmail = '<EMAIL>';
  const String mockSessionToken = 'mock_session_token';
  const String mockVerdict = 'mock_verdict';
  const String mockChallengeType = 'email';
  const Map<String, String> mockData = <String, String>{'sessionToken': mockSessionToken};

  late MockAuthenticationRepo mockAuthRepo;
  late VerifyEmailOtpHandler handler;

  setUpAll(() {
    WidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockActivateAccountRequest());
    registerFallbackValue(MockConfig(enable: false, fileName: ''));
  });

  setUp(() {
    mockAuthRepo = MockAuthenticationRepo();
    handler = VerifyEmailOtpHandler(authRepo: mockAuthRepo);
  });

  tearDown(() {
    reset(mockAuthRepo);
  });

  AccountActivationEntity generateStubEntity({
    required int statusCode,
    String? userMsg,
    String? sessionToken,
    String? challengeType,
    String? verdict,
    int? otpResendSecs,
    int? otpValiditySecs,
  }) {
    final Map<String, dynamic> data = <String, dynamic>{};

    if (userMsg != null) {
      data['user_message'] = userMsg;
    }
    if (sessionToken != null) {
      data['session_token'] = sessionToken;
    }
    if (challengeType != null) {
      data['challenge_type'] = challengeType;
    }
    if (otpResendSecs != null) {
      data['otp_resend_secs'] = otpResendSecs;
    }
    if (otpValiditySecs != null) {
      data['otp_validity_secs'] = otpValiditySecs;
    }

    final Map<String, dynamic> response = <String, dynamic>{
      'data': data,
    };

    if (verdict != null) {
      response['verdict'] = verdict;
    }

    return AccountActivationEntity.fromBaseResponse(BaseResponse(
      statusCode: statusCode,
      response: response,
    ));
  }

  group('VerifyEmailOtpHandler - verifyOtp', () {
    test('should return success model when API call is successful', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.SUCCESS,
        sessionToken: mockSessionToken,
        challengeType: mockChallengeType,
        verdict: mockVerdict,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, true);
      expect(result.successModel?.sessionToken, mockSessionToken);
      expect(result.successModel?.challengeType, mockChallengeType);
      expect(result.successModel?.verdict, mockVerdict);
      expect(result.errorUIModel, isNull);

      verify(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should use sessionToken from data', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.SUCCESS,
        sessionToken: mockSessionToken,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      await handler.verifyOtp(mockOtp, mockData);

      final ActivateAccountVerifyEmailOTPRequest capturedRequest =
          verify(() => mockAuthRepo.activateAccount(
                request: captureAny(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).captured.first as ActivateAccountVerifyEmailOTPRequest;

      expect(capturedRequest.otp, mockOtp);
      expect(capturedRequest.sessionToken, mockSessionToken);
    });

    test('should handle null data by using null sessionToken', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.SUCCESS,
        sessionToken: mockSessionToken,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      await handler.verifyOtp(mockOtp, null);

      final ActivateAccountVerifyEmailOTPRequest capturedRequest =
          verify(() => mockAuthRepo.activateAccount(
                request: captureAny(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).captured.first as ActivateAccountVerifyEmailOTPRequest;

      expect(capturedRequest.otp, mockOtp);
      expect(capturedRequest.sessionToken, null);
    });

    test('should return error model when status code is INVALID_TOKEN', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, OtpErrorType.sessionExpired);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    });

    test('should return error model when status code is LIMIT_EXCEEDED', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, OtpErrorType.limitExceeded);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    });

    test('should return error model when status code is BAD_REQUEST', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.BAD_REQUEST,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, OtpErrorType.invalidParams);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    });

    test('should return error model with unknown type for unhandled status codes', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, OtpErrorType.unknown);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    });
  });

  group('VerifyEmailOtpHandler - resendOtp', () {
    test('should return success model when API call is successful', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.SUCCESS,
        sessionToken: mockSessionToken,
        otpResendSecs: 30,
        otpValiditySecs: 120,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(mockEmail, mockData);

      expect(result.isSuccess, true);
      expect(result.resendOtpSuccess?.sessionToken, mockSessionToken);
      expect(result.resendOtpSuccess?.otpResendSecs, 30);
      expect(result.resendOtpSuccess?.otpValiditySecs, 120);
      expect(result.errorUIModel, isNull);

      verify(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should use email and sessionToken from parameters', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.SUCCESS,
        sessionToken: mockSessionToken,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      await handler.resendOtp(mockEmail, mockData);

      final ActivateAccountVerifyEmailRequest capturedRequest =
          verify(() => mockAuthRepo.activateAccount(
                request: captureAny(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).captured.first as ActivateAccountVerifyEmailRequest;

      expect(capturedRequest.email, mockEmail);
      expect(capturedRequest.sessionToken, mockSessionToken);
    });

    test('should return error model when status code is INVALID_TOKEN', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(mockEmail, mockData);

      expect(result.isSuccess, false);
      expect(result.resendOtpSuccess, isNull);
      expect(result.errorType, OtpErrorType.sessionExpired);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    });

    test('should return error model when status code is LIMIT_EXCEEDED', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(mockEmail, mockData);

      expect(result.isSuccess, false);
      expect(result.resendOtpSuccess, isNull);
      expect(result.errorType, OtpErrorType.limitExceeded);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    });

    test('should return error model when status code is BAD_REQUEST', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.BAD_REQUEST,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(mockEmail, mockData);

      expect(result.isSuccess, false);
      expect(result.resendOtpSuccess, isNull);
      expect(result.errorType, OtpErrorType.invalidParams);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    });
  });

  group('VerifyEmailOtpHandler - getOtpModelByEntity', () {
    test('should call onSuccess when status code is SUCCESS', () {
      bool onSuccessCalled = false;
      bool onErrorCalled = false;

      handler.getOtpModelByEntity<bool>(
        generateStubEntity(statusCode: CommonHttpClient.SUCCESS),
        onSuccess: () {
          onSuccessCalled = true;
          return true;
        },
        onError: (_) {
          onErrorCalled = true;
          return false;
        },
      );

      expect(onSuccessCalled, true);
      expect(onErrorCalled, false);
    });

    test('should call onError with sessionExpired when status code is INVALID_TOKEN', () {
      OtpErrorType? receivedErrorType;

      handler.getOtpModelByEntity<bool>(
        generateStubEntity(statusCode: CommonHttpClient.INVALID_TOKEN),
        onSuccess: () => true,
        onError: (OtpErrorType errorType) {
          receivedErrorType = errorType;
          return false;
        },
      );

      expect(receivedErrorType, OtpErrorType.sessionExpired);
    });

    test('should call onError with limitExceeded when status code is LIMIT_EXCEEDED', () {
      OtpErrorType? receivedErrorType;

      handler.getOtpModelByEntity<bool>(
        generateStubEntity(statusCode: CommonHttpClient.LIMIT_EXCEEDED),
        onSuccess: () => true,
        onError: (OtpErrorType errorType) {
          receivedErrorType = errorType;
          return false;
        },
      );

      expect(receivedErrorType, OtpErrorType.limitExceeded);
    });

    test('should call onError with invalidParams when status code is BAD_REQUEST', () {
      OtpErrorType? receivedErrorType;

      handler.getOtpModelByEntity<bool>(
        generateStubEntity(statusCode: CommonHttpClient.BAD_REQUEST),
        onSuccess: () => true,
        onError: (OtpErrorType errorType) {
          receivedErrorType = errorType;
          return false;
        },
      );

      expect(receivedErrorType, OtpErrorType.invalidParams);
    });

    test('should call onError with unknown for other status codes', () {
      OtpErrorType? receivedErrorType;

      handler.getOtpModelByEntity<bool>(
        generateStubEntity(statusCode: 999), // Unknown code
        onSuccess: () => true,
        onError: (OtpErrorType errorType) {
          receivedErrorType = errorType;
          return false;
        },
      );

      expect(receivedErrorType, OtpErrorType.unknown);
    });

    test('should call onError with limitExceeded when status code is LOCKED_RESOURCE', () {
      OtpErrorType? receivedErrorType;

      handler.getOtpModelByEntity<bool>(
        generateStubEntity(statusCode: CommonHttpClient.LOCKED_RESOURCE),
        onSuccess: () => true,
        onError: (OtpErrorType errorType) {
          receivedErrorType = errorType;
          return false;
        },
      );

      expect(receivedErrorType, OtpErrorType.limitExceeded);
    });

    test(
        'should call onError with limitExceeded '
        'when status code is DUPLICATE and verdict is duplicate', () {
      OtpErrorType? receivedErrorType;

      handler.getOtpModelByEntity<bool>(
        generateStubEntity(
          statusCode: CommonHttpClient.DUPLICATE,
          verdict: AccountActivationEntity.verdictDuplicate,
        ),
        onSuccess: () => true,
        onError: (OtpErrorType errorType) {
          receivedErrorType = errorType;
          return false;
        },
      );

      expect(receivedErrorType, OtpErrorType.duplicateEmail);
    });
  });
}
