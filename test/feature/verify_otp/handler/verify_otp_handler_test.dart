import 'package:evoapp/feature/verify_otp/cubit/otp_success_model.dart';
import 'package:evoapp/feature/verify_otp/cubit/verify_otp_cubit.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_otp_handler.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VerifyOtpModel', () {
    test('data setup properly when successModel is not null', () {
      final VerifyOtpModel model = VerifyOtpModel.success(
          successModel: OtpSuccessModel(sessionToken: 'token', challengeType: 'challenge_type'));
      expect(model.isSuccess, true);
      expect(model.successModel, isNotNull);
      expect(model.successModel?.challengeType, 'challenge_type');
      expect(model.successModel?.sessionToken, 'token');

      expect(model.errorUIModel, null);
      expect(model.errorType, null);
    });

    test('data setup properly when errorUIModel & errorType are not null', () {
      final VerifyOtpModel model = VerifyOtpModel.error(
          errorType: OtpErrorType.unknown, errorUIModel: ErrorUIModel(userMessage: 'error'));
      expect(model.isSuccess, false);
      expect(model.successModel, isNull);
      expect(model.errorType, OtpErrorType.unknown);
      expect(model.errorUIModel?.userMessage, 'error');
    });
  });

  group('ResendOtpModel', () {
    test('should return true when resendOtpSuccess is not null', () {
      final ResendOtpModel model = ResendOtpModel.success(
          resendOtpSuccess:
              ResendOtpSuccess(sessionToken: 'token', otpResendSecs: 60, otpValiditySecs: 300));
      expect(model.isSuccess, true);
      expect(model.resendOtpSuccess, isNotNull);
      expect(model.resendOtpSuccess?.sessionToken, 'token');
      expect(model.resendOtpSuccess?.otpResendSecs, 60);
      expect(model.resendOtpSuccess?.otpValiditySecs, 300);

      expect(model.errorUIModel, null);
      expect(model.errorType, null);
    });

    test('should return false when resendOtpSuccess is null', () {
      final ResendOtpModel model = ResendOtpModel.error(
          errorType: OtpErrorType.sessionExpired, errorUIModel: ErrorUIModel(userMessage: 'error'));
      expect(model.isSuccess, false);
      expect(model.resendOtpSuccess, isNull);

      expect(model.errorType, OtpErrorType.sessionExpired);
      expect(model.errorUIModel?.userMessage, 'error');
    });
  });
}
