import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/reset_pin_request.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/feature/verify_otp/handler/reset_pin_verify_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_otp_handler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/test_util.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  const String phoneNumber = '1234567890';
  const String fakeUserMessage = 'fake_user_message';

  late MockAuthenticationRepo mockAuthRepo;
  late ResetPinVerifyOtpHandler handler;

  setUpAll(() {
    registerFallbackValue(InitializeResetPinRequest(phoneNumber: ''));
    registerFallbackValue(ResetPinVerifyOTPRequest(otp: '', sessionToken: ''));

    WidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    mockAuthRepo = MockAuthenticationRepo();
    handler = ResetPinVerifyOtpHandler(authRepo: mockAuthRepo);
  });

  tearDown(() {
    reset(mockAuthRepo);
  });

  ResetPinEntity generateStubResetPinEntity(
      {required int statusCode, required String verdict, required String userMsg}) {
    return ResetPinEntity.fromBaseResponse(BaseResponse(
      statusCode: statusCode,
      response: <String, dynamic>{
        'verdict': verdict,
        'data': <String, String>{
          'user_message': userMsg,
        }
      },
    ));
  }

  void assertErrorModel({required ResendOtpModel result, required OtpErrorType errorType}) {
    expect(result.isSuccess, false);
    expect(result.resendOtpSuccess, isNull);
    expect(result.errorType, errorType);
    expect(result.errorUIModel, isNotNull);
    expect(result.errorUIModel?.userMessage, fakeUserMessage);
  }

  group('verify resendOtp function', () {
    test('resendOtp should return success model when API call is successful', () async {
      /// Arrange
      final Map<String, dynamic> responseData =
          await TestUtil.getResponseMock('reset_pin_request_otp_success.json');
      final BaseResponse mockResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseData,
      );

      final ResetPinEntity entity = ResetPinEntity.fromBaseResponse(mockResponse);

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => entity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      expect(result.isSuccess, true);
      expect(result.resendOtpSuccess?.sessionToken, 'mock_session_token');
      expect(result.resendOtpSuccess?.otpResendSecs, 60);
      expect(result.resendOtpSuccess?.otpValiditySecs, 10);
      expect(result.errorUIModel, isNull);

      final ResetPinRequest request = verify(() => mockAuthRepo.resetPin(
          request: captureAny(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).captured.single;
      expect(request, isA<InitializeResetPinRequest>());
      expect((request as InitializeResetPinRequest).phoneNumber, phoneNumber);
    });

    test('resendOtp should return error model when statusCode is invalid token', () async {
      /// Arrange
      final ResetPinEntity stubEntity = generateStubResetPinEntity(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        verdict: 'invalid_token',
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.sessionExpired);

      verify(() => mockAuthRepo.resetPin(
          request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('resendOtp should return error model when statusCode is bad request', () async {
      /// Arrange
      final ResetPinEntity stubEntity = generateStubResetPinEntity(
        statusCode: CommonHttpClient.BAD_REQUEST,
        verdict: ResetPinEntity.verdictInvalidParameters,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.unknown);
      verify(() => mockAuthRepo.resetPin(
          request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('resendOtp should return error model when statusCode is limit exceeded', () async {
      /// Arrange
      final ResetPinEntity stubEntity = generateStubResetPinEntity(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        verdict: ResetPinEntity.verdictLimitExceeded,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.limitExceeded);
      verify(() => mockAuthRepo.resetPin(
          request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('resendOtp should return error model when statusCode is locked resource', () async {
      /// Arrange
      final ResetPinEntity stubEntity = generateStubResetPinEntity(
        statusCode: CommonHttpClient.LOCKED_RESOURCE,
        verdict: ResetPinEntity.verdictLockedResource,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.limitExceeded);
      verify(() => mockAuthRepo.resetPin(
          request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('resendOtp should return error model when statusCode is unknown', () async {
      /// Arrange
      final ResetPinEntity stubEntity = generateStubResetPinEntity(
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        verdict: 'unknown_verdict',
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.unknown);
      verify(() => mockAuthRepo.resetPin(
          request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);
    });
  });

  group('verify verifyOtp function', () {
    const String mockOtp = '12345';
    const String mockSessionToken = 'mock_session_token';
    const Map<String, String> mockData = <String, String>{'sessionToken': mockSessionToken};

    void assertErrorModel({required VerifyOtpModel result, required OtpErrorType errorType}) {
      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, errorType);
      expect(result.errorUIModel, isNotNull);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    }

    test('verifyOtp should return success model when API call is successful', () async {
      /// Arrange
      final Map<String, dynamic> responseData =
          await TestUtil.getResponseMock('reset_pin_face_auth_challenge.json');
      final BaseResponse mockResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseData,
      );

      final ResetPinEntity entity = ResetPinEntity.fromBaseResponse(mockResponse);

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => entity);

      /// Action
      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      /// Assert
      expect(result.isSuccess, true);
      expect(result.successModel?.sessionToken, 'mock_session_token');
      expect(result.successModel?.challengeType, 'face_auth');
      expect(result.errorUIModel, isNull);
      expect(result.successModel?.ekycClientSettings, isNotNull);

      final ResetPinRequest request = verify(() => mockAuthRepo.resetPin(
          request: captureAny(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).captured.single;
      expect(request, isA<ResetPinVerifyOTPRequest>());

      final ResetPinVerifyOTPRequest verifyOtpRequest = request as ResetPinVerifyOTPRequest;
      expect(verifyOtpRequest.sessionToken, mockSessionToken);
      expect(verifyOtpRequest.otp, mockOtp);
    });

    test('verifyOtp should return error model when statusCode is invalid token', () async {
      /// Arrange
      final ResetPinEntity stubEntity = generateStubResetPinEntity(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        verdict: ResetPinEntity.verdictInvalidResetPinSession,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.sessionExpired);

      verify(() => mockAuthRepo.resetPin(
          request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('verifyOtp should return error model when StatusCode is bad Request', () async {
      /// Arrange
      final ResetPinEntity stubEntity = generateStubResetPinEntity(
        statusCode: CommonHttpClient.BAD_REQUEST,
        verdict: ResetPinEntity.verdictIncorrectOtp,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.invalidParams);

      verify(() => mockAuthRepo.resetPin(
          request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('verifyOtp should return error model when statusCode is limit exceed', () async {
      /// Arrange
      final ResetPinEntity stubEntity = generateStubResetPinEntity(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        verdict: ResetPinEntity.verdictLimitExceeded,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.limitExceeded);

      verify(() => mockAuthRepo.resetPin(
          request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('verifyOtp should return error model when statusCode is unknown', () async {
      /// Arrange
      final ResetPinEntity stubEntity = generateStubResetPinEntity(
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        verdict: 'unknown_verdict',
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.unknown);

      verify(() => mockAuthRepo.resetPin(
          request: any(named: 'request'), mockConfig: any(named: 'mockConfig'))).called(1);
    });
  });
}
