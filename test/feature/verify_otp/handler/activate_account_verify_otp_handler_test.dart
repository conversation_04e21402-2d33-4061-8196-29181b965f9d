import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_ui_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/activate_account_verify_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_otp_handler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockActivateAccountRequest extends ActivateAccountRequest {
  @override
  AccountActivationType get type => AccountActivationType.none;
}

void main() {
  const String fakeUserMessage = 'fake_user_message';
  const String mockOtp = '12345';
  const String mockSessionToken = 'mock_session_token';
  const String mockVerdict = 'mock_verdict';
  const Map<String, String> mockData = <String, String>{'sessionToken': mockSessionToken};

  late MockAuthenticationRepo mockAuthRepo;
  late ActivateAccountVerifyOtpHandler handler;

  setUpAll(() {
    WidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockActivateAccountRequest());
  });

  setUp(() {
    mockAuthRepo = MockAuthenticationRepo();
    handler = ActivateAccountVerifyOtpHandler(authRepo: mockAuthRepo);
  });

  tearDown(() {
    reset(mockAuthRepo);
  });

  AccountActivationEntity generateStubEntity({required int statusCode, required String userMsg}) {
    return AccountActivationEntity.fromBaseResponse(BaseResponse(
      statusCode: statusCode,
      response: <String, dynamic>{
        'data': <String, dynamic>{
          'user_message': userMsg,
        }
      },
    ));
  }

  group('verify verifyOtp function', () {
    void assertErrorModel({required VerifyOtpModel result, required OtpErrorType errorType}) {
      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, errorType);
      expect(result.errorUIModel, isNotNull);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    }

    test('should return success model when API call is successful', () async {
      final AccountActivationEntity entity = AccountActivationEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'challenge_type': 'challenge_type',
              'session_token': mockSessionToken,
            }
          },
        ),
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, true);
      expect(result.successModel?.sessionToken, mockSessionToken);
      expect(result.successModel?.challengeType, 'challenge_type');
      expect(result.errorUIModel, isNull);

      verify(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should extract sessionToken from data if present', () async {
      final AccountActivationEntity entity = AccountActivationEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'challenge_type': 'challenge_type',
              'session_token': mockSessionToken,
            },
          },
        ),
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, true);

      final ActivateAccountVerifyOTPRequest capturedRequest =
          verify(() => mockAuthRepo.activateAccount(
                request: captureAny(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).captured.first as ActivateAccountVerifyOTPRequest;

      expect(capturedRequest.otp, mockOtp);
      expect(capturedRequest.sessionToken, mockSessionToken);
    });

    test('should return error model when statusCode is not SUCCESS', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      assertErrorModel(result: result, errorType: OtpErrorType.sessionExpired);
    });

    test('should use empty string as sessionToken if not provided in data', () async {
      final AccountActivationEntity entity = AccountActivationEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'challenge_type': 'challenge_type',
              'session_token': mockSessionToken,
            }
          },
        ),
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      await handler.verifyOtp(mockOtp, null);

      final ActivateAccountVerifyOTPRequest capturedRequest =
          verify(() => mockAuthRepo.activateAccount(
                request: captureAny(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).captured.first as ActivateAccountVerifyOTPRequest;

      expect(capturedRequest.otp, mockOtp);
      expect(capturedRequest.sessionToken, '');
    });
  });

  group('verify generateVerifyOtpModelFromEntity method', () {
    test('should return success model when status code is success', () {
      final AccountActivationEntity entity = AccountActivationEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'challenge_type': 'challenge_type',
              'session_token': mockSessionToken,
            },
            'verdict': mockVerdict,
          },
        ),
      );

      final VerifyOtpModel result = handler.generateVerifyOtpModelFromEntity(entity);

      expect(result.isSuccess, true);
      expect(result.successModel?.sessionToken, mockSessionToken);
      expect(result.successModel?.challengeType, 'challenge_type');
      expect(result.successModel?.verdict, mockVerdict);
      expect(result.errorUIModel, isNull);
    });

    test('should return error model when status code is INVALID_TOKEN', () {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        userMsg: fakeUserMessage,
      );

      final VerifyOtpModel result = handler.generateVerifyOtpModelFromEntity(entity);

      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, OtpErrorType.sessionExpired);
    });

    test('should return error model when status code is LIMIT_EXCEEDED', () {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        userMsg: fakeUserMessage,
      );

      final VerifyOtpModel result = handler.generateVerifyOtpModelFromEntity(entity);

      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, OtpErrorType.limitExceeded);
    });

    test('should return error model when status code is LOCKED_RESOURCE', () {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.LOCKED_RESOURCE,
        userMsg: fakeUserMessage,
      );

      final VerifyOtpModel result = handler.generateVerifyOtpModelFromEntity(entity);

      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, OtpErrorType.limitExceeded);
    });

    test('should return error model when status code is BAD_REQUEST', () {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.BAD_REQUEST,
        userMsg: fakeUserMessage,
      );

      final VerifyOtpModel result = handler.generateVerifyOtpModelFromEntity(entity);

      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, OtpErrorType.invalidParams);
    });

    test('should return error model with unknown type for unhandled status codes', () {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        userMsg: fakeUserMessage,
      );

      final VerifyOtpModel result = handler.generateVerifyOtpModelFromEntity(entity);

      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, OtpErrorType.unknown);
    });
  });

  group('verify resendOtp function', () {
    test('resendOtp should call authRepo.activateAccount with proper request', () async {
      const String phoneNumber = '**********';
      final AccountActivationEntity entity = AccountActivationEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'session_token': mockSessionToken,
              'otp_resend_secs': 30,
              'otp_validity_secs': 120,
            }
          },
        ),
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      expect(result.isSuccess, true);
      expect(result.resendOtpSuccess?.sessionToken, mockSessionToken);
      expect(result.resendOtpSuccess?.otpResendSecs, 30);
      expect(result.resendOtpSuccess?.otpValiditySecs, 120);

      verify(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });
  });
}
