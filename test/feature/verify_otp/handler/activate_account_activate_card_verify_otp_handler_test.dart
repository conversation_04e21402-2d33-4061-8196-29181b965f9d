import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_ui_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/activate_account_activate_card_verify_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_otp_handler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockActivateAccountRequest extends ActivateAccountRequest {
  @override
  AccountActivationType get type => AccountActivationType.none;
}

void main() {
  const String mockSessionToken = 'mock_session_token';
  const String mockContactInfo = '<EMAIL>';
  const String mockOtp = '123456';
  const Map<String, String> mockData = <String, String>{'sessionToken': mockSessionToken};

  late MockAuthenticationRepo mockAuthRepo;
  late ActivateAccountActivateCardVerifyOtpHandler handler;

  setUpAll(() {
    WidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(MockActivateAccountRequest());
  });

  setUp(() {
    mockAuthRepo = MockAuthenticationRepo();
    handler = ActivateAccountActivateCardVerifyOtpHandler(authRepo: mockAuthRepo);
  });

  tearDown(() {
    reset(mockAuthRepo);
  });

  AccountActivationEntity generateStubEntity({
    required int statusCode,
    String? sessionToken,
    int? otpResendSecs,
    int? otpValiditySecs,
    String? challengeType,
    String? verdict,
  }) {
    final Map<String, dynamic> data = <String, dynamic>{
      'session_token': sessionToken,
      'otp_resend_secs': otpResendSecs,
      'otp_validity_secs': otpValiditySecs,
      'challenge_type': challengeType,
    };

    return AccountActivationEntity.fromBaseResponse(BaseResponse(
      statusCode: statusCode,
      response: <String, dynamic>{
        'data': data,
        'verdict': verdict,
        'status_code': statusCode,
      },
    ));
  }

  group('resendOtp', () {
    test('should return success model when statusCode is SUCCESS', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.SUCCESS,
        sessionToken: mockSessionToken,
        otpResendSecs: 30,
        otpValiditySecs: 120,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(mockContactInfo, mockData);

      expect(result.isSuccess, true);
      expect(result.resendOtpSuccess?.sessionToken, mockSessionToken);
      expect(result.resendOtpSuccess?.otpResendSecs, 30);
      expect(result.resendOtpSuccess?.otpValiditySecs, 120);
    });

    test('should use sessionToken from data if present', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.SUCCESS,
        sessionToken: mockSessionToken,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      await handler.resendOtp(mockContactInfo, mockData);

      final ActivateAccountActivateCardRequest capturedRequest =
          verify(() => mockAuthRepo.activateAccount(
                request: captureAny(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).captured.first as ActivateAccountActivateCardRequest;

      expect(capturedRequest.sessionToken, mockSessionToken);
      expect(capturedRequest.skip, false);
    });

    test('should return error model when statusCode is BAD_REQUEST', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.BAD_REQUEST,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(mockContactInfo, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.invalidParams);
    });

    test('should return error model when statusCode is INVALID_TOKEN', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.INVALID_TOKEN,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(mockContactInfo, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.sessionExpired);
    });

    test('should return error model when statusCode is LIMIT_EXCEEDED', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(mockContactInfo, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.limitExceeded);
    });

    test('should return error model when statusCode is LOCKED_RESOURCE', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.LOCKED_RESOURCE,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(mockContactInfo, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.limitExceeded);
    });

    test('should return error model with unknown type for other status codes', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: 999, // Some unknown status code
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final ResendOtpModel result = await handler.resendOtp(mockContactInfo, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.unknown);
    });
  });

  group('verifyOtp', () {
    test('should return success model when statusCode is SUCCESS', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.SUCCESS,
        sessionToken: mockSessionToken,
        challengeType: 'challenge_type',
        verdict: 'verdict',
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, true);
      expect(result.successModel?.sessionToken, mockSessionToken);
      expect(result.successModel?.challengeType, 'challenge_type');
      expect(result.successModel?.verdict, 'verdict');
    });

    test('should use sessionToken from data if present', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.SUCCESS,
        sessionToken: mockSessionToken,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      await handler.verifyOtp(mockOtp, mockData);

      final ActivateAccountActivateCardVerifyOtpRequest capturedRequest =
          verify(() => mockAuthRepo.activateAccount(
                request: captureAny(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).captured.first as ActivateAccountActivateCardVerifyOtpRequest;

      expect(capturedRequest.sessionToken, mockSessionToken);
      expect(capturedRequest.otp, mockOtp);
    });

    test('should return error model when statusCode is BAD_REQUEST', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.BAD_REQUEST,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.invalidParams);
    });

    test('should return error model when statusCode is INVALID_TOKEN', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.INVALID_TOKEN,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.sessionExpired);
    });

    test('should return error model when statusCode is LIMIT_EXCEEDED', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.limitExceeded);
    });

    test('should return error model when statusCode is LOCKED_RESOURCE', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.LOCKED_RESOURCE,
      );

      when(() => mockAuthRepo.activateAccount(
        request: any(named: 'request'),
        mockConfig: any(named: 'mockConfig'),
      )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.limitExceeded);
    });

    test('should return third party error when verdict is invalid_entity_id', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        verdict: AccountActivationEntity.verdictInvalidEntityId,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.thirdParty);
    });

    test('should return third party error when verdict is invalid_kit_no', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        verdict: AccountActivationEntity.verdictInvalidKitNo,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.thirdParty);
    });

    test('should return third party error when verdict is kit_not_mapped_to_entity', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        verdict: AccountActivationEntity.verdictKitNotMappedToEntity,
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.thirdParty);
    });

    test('should return error model with unknown type for other status codes', () async {
      final AccountActivationEntity entity = generateStubEntity(
        statusCode: 999, // Some unknown status code
      );

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => entity);

      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      expect(result.isSuccess, false);
      expect(result.errorType, OtpErrorType.unknown);
    });
  });
}
