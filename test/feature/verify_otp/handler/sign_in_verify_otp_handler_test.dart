import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:evoapp/feature/verify_otp/handler/sign_in_verify_otp_handler.dart';
import 'package:evoapp/feature/verify_otp/handler/verify_otp_handler.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/test_util.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  const String phoneNumber = '1234567890';
  const String fakeUserMessage = 'fake_user_message';

  late MockAuthenticationRepo mockAuthRepo;
  late SignInVerifyOtpHandler handler;

  setUpAll(() {
    WidgetsFlutterBinding.ensureInitialized();
  });

  setUp(() {
    mockAuthRepo = MockAuthenticationRepo();
    handler = SignInVerifyOtpHandler(authRepo: mockAuthRepo);
  });

  tearDown(() {
    reset(mockAuthRepo);
  });

  SignInEntity generateStubSignInEntity(
      {required int statusCode, required String verdict, required String userMsg}) {
    return SignInEntity.fromBaseResponse(BaseResponse(
      statusCode: statusCode,
      response: <String, dynamic>{
        'verdict': verdict,
        'data': <String, String>{
          'user_message': userMsg,
        }
      },
    ));
  }

  group('verify resendOtp function', () {
    void assertErrorModel({required ResendOtpModel result, required OtpErrorType errorType}) {
      expect(result.isSuccess, false);
      expect(result.resendOtpSuccess, isNull);
      expect(result.errorType, errorType);
      expect(result.errorUIModel, isNotNull);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    }

    test('resendOtp should return success model when API call is successful', () async {
      /// Arrange
      final Map<String, dynamic> responseData =
          await TestUtil.getResponseMock('sign_in_otp_success.json');
      final BaseResponse mockResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseData,
      );

      final SignInEntity entity = SignInEntity.fromBaseResponse(mockResponse);

      when(() => mockAuthRepo.signIn(TypeLogin.otp,
          phoneNumber: phoneNumber,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => entity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      expect(result.isSuccess, true);
      expect(result.resendOtpSuccess?.sessionToken, 'mock_session_token');
      expect(result.resendOtpSuccess?.otpResendSecs, 10);
      expect(result.resendOtpSuccess?.otpValiditySecs, 10);
      expect(result.errorUIModel, isNull);

      verify(() => mockAuthRepo.signIn(TypeLogin.otp,
          phoneNumber: phoneNumber, mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('resendOtp should return error model when statusCode is invalid token', () async {
      /// Arrange
      final SignInEntity stubEntity = generateStubSignInEntity(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        verdict: SignInEntity.verdictInvalidToken,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.signIn(TypeLogin.otp,
          phoneNumber: phoneNumber,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.sessionExpired);

      verify(() => mockAuthRepo.signIn(TypeLogin.otp,
          phoneNumber: phoneNumber, mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('resendOtp should return error model when statusCode is invalid params', () async {
      /// Arrange
      final SignInEntity stubEntity = generateStubSignInEntity(
        statusCode: CommonHttpClient.BAD_REQUEST,
        verdict: SignInEntity.verdictOneLastTry,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.signIn(TypeLogin.otp,
          phoneNumber: phoneNumber,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.invalidParams);

      verify(() => mockAuthRepo.signIn(TypeLogin.otp,
          phoneNumber: phoneNumber, mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('resendOtp should return error model when statusCode is limit exceeded', () async {
      /// Arrange
      final SignInEntity stubEntity = generateStubSignInEntity(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        verdict: SignInEntity.verdictLimitExceeded,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.signIn(TypeLogin.otp,
          phoneNumber: phoneNumber,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.limitExceeded);

      verify(() => mockAuthRepo.signIn(TypeLogin.otp,
          phoneNumber: phoneNumber, mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('resendOtp should return error model when statusCode is unknown', () async {
      /// Arrange
      final SignInEntity stubEntity = generateStubSignInEntity(
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        verdict: 'unknown_verdict',
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.signIn(TypeLogin.otp,
          phoneNumber: phoneNumber,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final ResendOtpModel result = await handler.resendOtp(phoneNumber, null);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.unknown);

      verify(() => mockAuthRepo.signIn(TypeLogin.otp,
          phoneNumber: phoneNumber, mockConfig: any(named: 'mockConfig'))).called(1);
    });
  });

  group('verify verifyOtp function', () {
    const String mockOtp = '12345';
    const String mockSessionToken = 'mock_session_token';
    const Map<String, String> mockData = <String, String>{'sessionToken': mockSessionToken};

    void assertErrorModel({required VerifyOtpModel result, required OtpErrorType errorType}) {
      expect(result.isSuccess, false);
      expect(result.successModel, isNull);
      expect(result.errorType, errorType);
      expect(result.errorUIModel, isNotNull);
      expect(result.errorUIModel?.userMessage, fakeUserMessage);
    }

    test('verifyOtp should return success model when API call is successful', () async {
      /// Arrange
      final Map<String, dynamic> responseData =
          await TestUtil.getResponseMock('verify_sign_in_otp_success.json');
      final BaseResponse mockResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: responseData,
      );

      final SignInEntity entity = SignInEntity.fromBaseResponse(mockResponse);

      when(() => mockAuthRepo.signIn(TypeLogin.verifyOTP,
          otp: mockOtp,
          sessionToken: mockSessionToken,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => entity);

      /// Action
      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      /// Assert
      expect(result.isSuccess, true);
      expect(result.successModel?.sessionToken, mockSessionToken);
      expect(result.successModel?.challengeType, 'verify_pin');
      expect(result.successModel?.ekycClientSettings, isNull);
      expect(result.errorUIModel, isNull);

      verify(() => mockAuthRepo.signIn(TypeLogin.verifyOTP,
          otp: mockOtp,
          sessionToken: mockSessionToken,
          mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('verifyOtp should return error model when statusCode is invalid token', () async {
      /// Arrange
      final SignInEntity stubEntity = generateStubSignInEntity(
        statusCode: CommonHttpClient.INVALID_TOKEN,
        verdict: SignInEntity.verdictInvalidToken,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.signIn(TypeLogin.verifyOTP,
          otp: mockOtp,
          sessionToken: mockSessionToken,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.sessionExpired);

      verify(() => mockAuthRepo.signIn(TypeLogin.verifyOTP,
          otp: mockOtp,
          sessionToken: mockSessionToken,
          mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('verifyOtp should return error model when statusCode is bad request', () async {
      /// Arrange
      final SignInEntity stubEntity = generateStubSignInEntity(
        statusCode: CommonHttpClient.BAD_REQUEST,
        verdict: SignInEntity.verdictOneLastTry,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.signIn(TypeLogin.verifyOTP,
          otp: mockOtp,
          sessionToken: mockSessionToken,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.invalidParams);

      verify(() => mockAuthRepo.signIn(TypeLogin.verifyOTP,
          otp: mockOtp,
          sessionToken: mockSessionToken,
          mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('verifyOtp should return error model when statusCode is limit exceeded', () async {
      /// Arrange
      final SignInEntity stubEntity = generateStubSignInEntity(
        statusCode: CommonHttpClient.LIMIT_EXCEEDED,
        verdict: SignInEntity.verdictLimitExceeded,
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.signIn(TypeLogin.verifyOTP,
          otp: mockOtp,
          sessionToken: mockSessionToken,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.limitExceeded);

      verify(() => mockAuthRepo.signIn(TypeLogin.verifyOTP,
          otp: mockOtp,
          sessionToken: mockSessionToken,
          mockConfig: any(named: 'mockConfig'))).called(1);
    });

    test('verifyOtp should return error model when statusCode is unknown', () async {
      /// Arrange
      final SignInEntity stubEntity = generateStubSignInEntity(
        statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        verdict: 'unknown_verdict',
        userMsg: fakeUserMessage,
      );

      when(() => mockAuthRepo.signIn(TypeLogin.verifyOTP,
          otp: mockOtp,
          sessionToken: mockSessionToken,
          mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async => stubEntity);

      /// Action
      final VerifyOtpModel result = await handler.verifyOtp(mockOtp, mockData);

      /// Assert
      assertErrorModel(result: result, errorType: OtpErrorType.unknown);

      verify(() => mockAuthRepo.signIn(TypeLogin.verifyOTP,
          otp: mockOtp,
          sessionToken: mockSessionToken,
          mockConfig: any(named: 'mockConfig'))).called(1);
    });
  });
}
