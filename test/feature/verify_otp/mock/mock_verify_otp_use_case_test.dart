import 'package:evoapp/feature/verify_otp/mock/mock_verify_otp_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify getMockVerifyOtpFileNameByCase should return corrected file names', () {
    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getSignInOtpSuccess),
        'sign_in_otp_success.json');
    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getVerifySignInOtpSuccess),
        'verify_sign_in_otp_success.json');
    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getVerifySignInOtpIncorrectOtp),
        'verify_sign_in_otp_incorrect_otp.json');
    expect(
        getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getVerifySignInResendOtpLimitExceeded),
        'verify_sign_in_resend_otp_limit_exceeded.json');

    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getActivateAccountVerifyOtpSuccess),
        'get_activate_account_verify_otp_success.json');
    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getActivateAccountVerifyOtpRejected),
        'get_activate_account_verify_otp_rejected.json');
    expect(
        getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getActivateAccountVerifyOtpProcessing),
        'get_activate_account_verify_otp_processing.json');
    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getActivateAccountVerifyOtpNone),
        'get_activate_account_verify_otp_none.json');
    expect(getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getActivateAccountVerifyOtpExisting),
        'get_activate_account_verify_otp_existing.json');
    expect(
        getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getActivateAccountVerifyOtpCancelled),
        'get_activate_account_verify_otp_cancelled.json');
  });
}
