import 'package:evoapp/feature/verify_otp/widget/evo_otp_widget.dart';
import 'package:evoapp/feature/verify_otp/widget/evo_otp_widget_controller.dart';
import 'package:evoapp/feature/verify_otp/widget/otp_field.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class MockResendOtpCallback extends Mock {
  Future<void> call();
}

class MockOnSubmitCallback extends Mock {
  void call(String value);
}

void main() {
  late MockResendOtpCallback mockResendOtpCallback;
  late MockOnSubmitCallback mockSubmitCallback;
  const int mockOtpValiditySecs = 3;
  late OtpWidgetController otpWidgetController;

  /// Because countdown timer count to 0 so that add extra 1 second
  const Duration countDownDoneDuration = Duration(seconds: mockOtpValiditySecs + 1);
  final GlobalKey<EvoOtpWidgetState> key = GlobalKey<EvoOtpWidgetState>();

  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
  });

  setUp(() {
    mockResendOtpCallback = MockResendOtpCallback();
    mockSubmitCallback = MockOnSubmitCallback();
    otpWidgetController = OtpWidgetController();

    when(() => mockSubmitCallback.call(any())).thenAnswer((_) async {});
    when(() => mockResendOtpCallback.call()).thenAnswer((_) async {});
  });

  tearDown(() {
    reset(mockResendOtpCallback);
    reset(mockSubmitCallback);
  });

  Widget buildWidget() {
    return MaterialApp(
        home: Scaffold(
      body: EvoOtpWidget(
        key: key,
        otpValiditySecs: mockOtpValiditySecs,
        onSubmit: mockSubmitCallback.call,
        onResendOtp: mockResendOtpCallback.call,
        controller: otpWidgetController,
      ),
    ));
  }

  group('verify Resend behavior', () {
    verifyResendButton(WidgetTester widgetTester) async {
      expect(
        find.textContaining(EvoStrings.otpResendCode, findRichText: true),
        findsOne,
      );
    }

    testWidgets('should not show [Resend code]', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(buildWidget());

      expect(
        find.textContaining(EvoStrings.otpResendCode, findRichText: true),
        findsNothing,
      );
      verifyNever(() => mockResendOtpCallback.call());
    });

    testWidgets('should [Resend code] button enabled after countdown done',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(buildWidget());
      otpWidgetController.startCountdown();
      await widgetTester.pump(countDownDoneDuration);

      verifyResendButton(widgetTester);
      verifyNever(() => mockResendOtpCallback.call());
    });

    testWidgets('should call onResendOtp() when [Resend code] button is pressed',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(buildWidget());
      otpWidgetController.onCountdownDone();
      await widgetTester.pump(countDownDoneDuration);

      verifyResendButton(widgetTester);
      await widgetTester.tapOnText(
        find.textRange.ofSubstring(EvoStrings.otpResendCode),
      );
      await widgetTester.pump();

      verify(() => mockResendOtpCallback.call()).called(1);
    });

    testWidgets('verify showing countdown and clear pin text when resend otp',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(buildWidget());
      otpWidgetController.onCountdownDone();
      await widgetTester.pump(countDownDoneDuration);

      verifyResendButton(widgetTester);
      otpWidgetController.startCountdown();
      await widgetTester.pump();

      /// should restart countdown
      expect(
        find.textContaining(const Duration(seconds: mockOtpValiditySecs).remainder()),
        findsOne,
      );

      /// should clear pin text
      expect(
          find.byWidgetPredicate(
              (Widget widget) => widget is OtpField && widget.controller?.text.isEmpty == true),
          findsOne);
    });
  });

  group('verify input OTP', () {
    /// Refer to [common_pin_code_fields._textEditingControllerListener]
    /// delayed 300 ms for onCompleted be called
    const Duration delayedPinOnCompleted = Duration(milliseconds: 300);

    testWidgets('should  not call onSubmit if [OtpField] not filled',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(buildWidget());
      await widgetTester.enterText(find.byType(OtpField), '1');
      await widgetTester.pump(delayedPinOnCompleted);

      verifyNever(() => mockSubmitCallback.call(any()));
    });

    testWidgets('should call onSubmit if [OtpField] filled', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(buildWidget());
      await widgetTester.enterText(find.byType(OtpField), '123456');
      await widgetTester.pump(delayedPinOnCompleted);

      verify(() => mockSubmitCallback.call(any())).called(1);
    });
  });
}
