import 'package:evoapp/feature/verify_otp/widget/evo_otp_widget.dart';
import 'package:evoapp/feature/verify_otp/widget/evo_otp_widget_controller.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCallback extends Mock {
  void call();
}

void main() {
  late OtpWidgetController controller;

  setUp(() {
    controller = OtpWidgetController();
  });

  test('OtpWidgetController initial state', () {
    expect(controller.listenableResendOtpStatus.value, ResendOtpStatus.disabled);
  });

  test('OtpWidgetController startCountdown', () async {
    final MockCallback mockCallback = MockCallback();

    controller.countdown.start = mockCallback.call;
    controller.startCountdown();
    expect(controller.listenableResendOtpStatus.value, ResendOtpStatus.disabled);
    verify(() => mockCallback.call()).called(1);
  });

  test('OtpWidgetController onCountdownDone', () {
    controller.onCountdownDone();

    expect(controller.listenableResendOtpStatus.value, ResendOtpStatus.enabled);
  });
}
