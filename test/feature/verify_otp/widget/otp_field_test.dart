import 'package:evoapp/feature/verify_otp/widget/otp_field.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/widget/otp_listenable/otp_listenable_widget.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class SpyFocusNode extends FocusNode {
  int requestFocusCount = 0;
  int unfocusCount = 0;

  @override
  void requestFocus([FocusNode? node]) {
    requestFocusCount += 1;
    super.requestFocus(node);
  }

  @override
  void unfocus({UnfocusDisposition disposition = UnfocusDisposition.scope}) {
    unfocusCount += 1;
    super.unfocus(disposition: disposition);
  }
}

void main() {
  group('OtpField', () {
    setUpAll(() {
      initConfigEvoPageStateBase();
      setUpMockConfigEvoPageStateBase();
    });

    setUp(() {});

    tearDownAll(() {
      getIt.reset();
    });

    Future<void> pumpOtpField(
      WidgetTester tester, {
      TextEditingController? controller,
      FocusNode? focusNode,
      ValueChanged<String>? onCompleted,
      String error = '',
      bool obscureText = false,
    }) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(builder: (BuildContext context) {
              return OtpField(
                context: context,
                controller: controller,
                onCompleted: onCompleted,
                obscureText: obscureText,
                focusNode: focusNode,
                error: error,
              );
            }),
          ),
        ),
      );
    }

    testWidgets('should configure TextFormField with correct properties',
        (WidgetTester tester) async {
      final TextEditingController controller = TextEditingController();
      final FocusNode focusNode = FocusNode();

      await pumpOtpField(
        tester,
        controller: controller,
        focusNode: focusNode,
      );

      final Finder textFieldFinder = find.byType(TextField);
      expect(textFieldFinder, findsOneWidget);

      final TextField textField = tester.widget<TextField>(textFieldFinder);

      // Check all properties of TextFormField
      expect(textField.textInputAction, TextInputAction.done);
      expect(textField.controller, controller);
      expect(textField.focusNode, focusNode);
      expect(textField.enabled, true);
      expect(textField.autocorrect, false);
      expect(textField.keyboardType, TextInputType.number);
      expect(textField.enableInteractiveSelection, false);
      expect(textField.showCursor, false);

      // Verify inputFormatters
      expect(textField.inputFormatters!.length, 2);
      expect(textField.inputFormatters![0], isA<FilteringTextInputFormatter>());
      expect(textField.inputFormatters![1], isA<LengthLimitingTextInputFormatter>());

      // Verify decoration
      final InputDecoration decoration = textField.decoration!;
      expect(decoration.contentPadding, const EdgeInsets.all(0));
      expect(decoration.border, InputBorder.none);
      expect(decoration.enabledBorder, InputBorder.none);
      expect(decoration.focusedBorder, InputBorder.none);
      expect(decoration.disabledBorder, InputBorder.none);
      expect(decoration.errorBorder, InputBorder.none);
      expect(decoration.focusedErrorBorder, InputBorder.none);

      // Verify style
      final TextStyle style = textField.style!;
      expect(style.height, 0);
      expect(style.fontSize, 0);

      // Verify obscureText defaults to false when not explicitly set
      expect(textField.obscureText, false);
    });

    testWidgets('should render correctly with default values', (WidgetTester tester) async {
      final TextEditingController textController = TextEditingController();
      await pumpOtpField(tester, controller: textController);

      // Verify 6 OTP fields are rendered
      expect(find.byType(Container), findsNWidgets(7)); // 6 fields + 1 separator

      // Verify the separator dot is rendered
      final Container dotContainer = tester.widget<Container>(
        find.byType(Container).at(3),
      );
      expect(dotContainer.margin, EdgeInsets.symmetric(horizontal: 18));
      expect((dotContainer.decoration as BoxDecoration).shape, BoxShape.circle);
    });

    testWidgets('should accept numeric input', (WidgetTester tester) async {
      final TextEditingController textController = TextEditingController();
      await pumpOtpField(tester, controller: textController);

      // Find the TextFormField
      final Finder textField = find.byType(TextFormField);
      expect(textField, findsOneWidget);

      // Enter text
      await tester.enterText(textField, '123456');
      await tester.pump();

      // Verify text is displayed correctly
      expect(textController.text, '123456');

      // Verify each field has the correct value
      for (int i = 0; i < 6; i++) {
        if (i == 3) {
          continue; // Skip separator dot
        }
        expect(find.text('${i + 1}'), findsOneWidget);
      }
    });

    testWidgets('should NOT accept non-numeric input', (WidgetTester tester) async {
      final TextEditingController textController = TextEditingController();
      await pumpOtpField(tester, controller: textController);

      // Find the TextFormField
      final Finder textField = find.byType(TextFormField);
      expect(textField, findsOneWidget);

      // Enter text
      await tester.enterText(textField, '1a2b3c');
      await tester.pump();

      // Verify text is displayed correctly
      expect(textController.text, '123');

      // Verify each field has the correct value
      expect(find.text('1'), findsOneWidget);
      expect(find.text('2'), findsOneWidget);
      expect(find.text('3'), findsOneWidget);
      expect(find.text('a'), findsNothing);
      expect(find.text('b'), findsNothing);
      expect(find.text('c'), findsNothing);
    });

    testWidgets('should limit input to 6 digits', (WidgetTester tester) async {
      final TextEditingController textController = TextEditingController();
      await pumpOtpField(tester, controller: textController);

      // Find the TextFormField
      final Finder textField = find.byType(TextFormField);

      // Enter more than 6 digits
      await tester.enterText(textField, '12345678');
      await tester.pump();

      // Verify only 6 digits are kept
      expect(textController.text, '123456');
    });

    testWidgets('should call onCompleted callback when all digits entered, then unfocus',
        (WidgetTester tester) async {
      final TextEditingController textController = TextEditingController();
      final FocusNode focusNode = FocusNode();
      String? completed;
      await pumpOtpField(
        tester,
        controller: textController,
        focusNode: focusNode,
        onCompleted: (String value) => completed = value,
      );

      // Find the TextFormField
      final Finder textField = find.byType(TextFormField);

      // Enter 6 digits
      await tester.enterText(textField, '123456');
      await tester.pump(const Duration(milliseconds: 350)); // Wait for callback delay

      // Verify onCompleted was called
      expect(completed, '123456');

      // Verify focus is lost
      expect(focusNode.hasFocus, isFalse);
    });

    testWidgets('should call onCompleted callback when enough digits are set via controller',
        (WidgetTester tester) async {
      final TextEditingController textController = TextEditingController(text: '11223344');
      final FocusNode focusNode = FocusNode();
      String? completed;
      await pumpOtpField(
        tester,
        controller: textController,
        onCompleted: (String value) => completed = value,
        focusNode: focusNode,
      );
      focusNode.requestFocus();

      await tester.pump(const Duration(milliseconds: 350)); // Wait for callback delay

      // Verify onCompleted was called
      expect(completed, '112233');
    });

    testWidgets('should show error state when error is provided', (WidgetTester tester) async {
      final TextEditingController textController = TextEditingController();
      await pumpOtpField(tester, controller: textController, error: 'error');

      // Verify error styling (red border) is applied
      final Container firstFieldContainer = tester.widget<Container>(
        find.byType(Container).first,
      );
      final Border border = (firstFieldContainer.decoration as BoxDecoration).border as Border;
      expect(border.top.color, evoColors.errorBase);

      // Verify error message is displayed
      expect(find.text('error'), findsOneWidget);
    });

    testWidgets('should obscure text when obscureText is true', (WidgetTester tester) async {
      final TextEditingController textController = TextEditingController();
      await pumpOtpField(tester, controller: textController, obscureText: true);

      // Enter text
      await tester.enterText(find.byType(TextFormField), '123456');
      await tester.pump();

      // Verify text is obscured (bullet characters)
      expect(find.text('•'), findsNWidgets(6));
      expect(find.text('1'), findsNothing);
    });

    testWidgets('should focus via tapping on field when the field is not focused',
        (WidgetTester tester) async {
      final SpyFocusNode focusNode = SpyFocusNode();
      await pumpOtpField(tester, focusNode: focusNode);

      focusNode.unfocus();
      await tester.pump();
      expect(focusNode.hasFocus, isFalse);

      await tester.tap(find.byType(GestureDetector));
      expect(focusNode.requestFocusCount, 1);
      expect(focusNode.hasFocus, isTrue);
    });

    testWidgets('should clear all inputted digits on tap', (WidgetTester tester) async {
      final TextEditingController textController = TextEditingController();
      final FocusNode focusNode = FocusNode();
      await pumpOtpField(tester, controller: textController, focusNode: focusNode);

      // Find the TextFormField
      final Finder textField = find.byType(TextFormField);

      // Enter digits
      await tester.enterText(textField, '123');
      expect(textController.text, '123');

      focusNode.unfocus();
      await tester.pump();

      // Tap on the field to clear input
      await tester.tap(find.byType(GestureDetector));

      // Verify input is cleared
      expect(textController.text, isEmpty);
      expect(focusNode.hasFocus, isTrue);
    });

    testWidgets('should autofill received otp', (WidgetTester tester) async {
      final TextEditingController textController = TextEditingController();
      final String mockOtp = '123456';

      await pumpOtpField(tester, controller: textController);

      /// notify the otp auto fill
      final Finder otpListenableFinder = find.byType(OtpListenableWidget);
      final OtpListenableWidget otpListenableWidget =
          tester.widget<OtpListenableWidget>(otpListenableFinder);
      otpListenableWidget.onOtpCodeReceived?.call(mockOtp);
      await tester.pump();

      expect(textController.text, mockOtp);
    });
  });
}
