import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:evoapp/feature/login/old_device/login_on_old_device_cubit.dart';
import 'package:evoapp/feature/login/utils/login_old_device_utils.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/onesignal/onesignal.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';
import '../../../util/test_util.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockLoginOldDeviceUtils extends Mock implements LoginOldDeviceUtils {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  late LoginOnOldDeviceCubit cubit;

  final AuthenticationRepo mockAuthenticationRepo = MockAuthenticationRepo();
  final LoginOldDeviceUtils mockLoginOldDeviceUtils = MockLoginOldDeviceUtils();
  final EvoLocalStorageHelper mockLocalStorageHelper = MockEvoLocalStorageHelper();

  const String phoneNumber = '0123456789';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerSingleton<EvoUtilFunction>(MockEvoUtilFunction());
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    cubit = LoginOnOldDeviceCubit(
      authenticationRepo: mockAuthenticationRepo,
      loginOldDeviceUtils: mockLoginOldDeviceUtils,
      localStorageHelper: mockLocalStorageHelper,
    );

    registerFallbackValue(TypeLogin.verifyPin);
    when(() => mockLocalStorageHelper.getUserPhoneNumber()).thenAnswer((_) async {
      return phoneNumber;
    });
  });

  Future<void> mockSignInSucceed() async {
    final Map<String, dynamic> responseData = await TestUtil.getResponseMock('signin_success.json');

    when(() => mockAuthenticationRepo.signIn(
          any(),
          phoneNumber: any(named: 'phoneNumber'),
          pin: any(named: 'pin'),
          biometricToken: any(named: 'biometricToken'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async {
      return SignInEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        ),
      );
    });
  }

  Future<void> mockSignInFailed() async {
    final Map<String, dynamic> responseData =
        await TestUtil.getResponseMock('signin_invalid_parameters.json');

    when(() => mockAuthenticationRepo.signIn(
          any(),
          phoneNumber: any(named: 'phoneNumber'),
          pin: any(named: 'pin'),
          biometricToken: any(named: 'biometricToken'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async {
      return SignInEntity.fromBaseResponse(
        BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: responseData,
        ),
      );
    });
  }

  test('Initial state', () {
    expect(cubit.state, isA<LoginOnOldDeviceInitial>());
  });

  group('Test loginByPinCode', () {
    const String fakePinCode = '123456';

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'Login success',
      setUp: () async {
        await mockSignInSucceed();
      },
      build: () => cubit,
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByPinCode(fakePinCode),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceSuccess>(),
      ],
      verify: (_) {
        verify(() => mockLocalStorageHelper.getUserPhoneNumber()).called(1);

        verify(() => mockAuthenticationRepo.signIn(
              TypeLogin.verifyPin,
              phoneNumber: phoneNumber,
              pin: fakePinCode,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'Login failed',
      setUp: () async {
        await mockSignInFailed();
      },
      build: () => cubit,
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByPinCode('123456'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceError>()
            .having(
              (LoginOnOldDeviceError p0) => p0.error,
              'verify error',
              isNotNull,
            )
            .having(
              (LoginOnOldDeviceError p0) => p0.loginType,
              'verify loginType',
              TypeLogin.verifyPin,
            )
            .having(
              (LoginOnOldDeviceError p0) => p0.phoneNumber,
              'verify loginType',
              phoneNumber,
            ),
      ],
      verify: (_) {
        verify(() => mockLocalStorageHelper.getUserPhoneNumber()).called(1);

        verify(() => mockAuthenticationRepo.signIn(
              TypeLogin.verifyPin,
              phoneNumber: phoneNumber,
              pin: fakePinCode,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );
  });

  group('Test loginByBiometric', () {
    const String fakeBiometricToken = 'fakeBiometricToken';

    setUpAll(() {
      when(() => mockLocalStorageHelper.getBiometricToken())
          .thenAnswer((_) => Future<String>.value(fakeBiometricToken));
    });

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'Login success',
      setUp: () async {
        await mockSignInSucceed();
      },
      build: () => cubit,
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByBiometric(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceSuccess>(),
      ],
      verify: (_) {
        verifyNever(() => mockLocalStorageHelper.getUserPhoneNumber());

        verify(() => mockAuthenticationRepo.signIn(
              TypeLogin.biometricToken,
              biometricToken: fakeBiometricToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );

    blocTest<LoginOnOldDeviceCubit, LoginOnOldDeviceState>(
      'Login failed',
      setUp: () async {
        await mockSignInFailed();
      },
      build: () => cubit,
      act: (LoginOnOldDeviceCubit cubit) => cubit.loginByBiometric(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<LoginOnOldDeviceLoading>(),
        isA<LoginOnOldDeviceError>()
            .having(
              (LoginOnOldDeviceError p0) => p0.error,
              'verify error',
              isNotNull,
            )
            .having(
              (LoginOnOldDeviceError p0) => p0.loginType,
              'verify loginType',
              TypeLogin.biometricToken,
            )
            .having(
              (LoginOnOldDeviceError p0) => p0.phoneNumber,
              'verify loginType',
              phoneNumber,
            ),
      ],
      verify: (_) {
        verify(() => mockLocalStorageHelper.getUserPhoneNumber()).called(1);

        verify(() => mockAuthenticationRepo.signIn(
              TypeLogin.biometricToken,
              biometricToken: fakeBiometricToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      },
    );
  });

  test('loginWithNewAccount() should call clearAllUserData()', () async {
    when(() => evoUtilFunction.clearAllUserData(oneSignal: any(named: 'oneSignal')))
        .thenAnswer((_) async {});

    final OneSignal oneSignal = OneSignal();
    cubit.loginWithNewAccount(oneSignal: oneSignal);

    verify(() => evoUtilFunction.clearAllUserData(oneSignal: oneSignal)).called(1);
  });
}
