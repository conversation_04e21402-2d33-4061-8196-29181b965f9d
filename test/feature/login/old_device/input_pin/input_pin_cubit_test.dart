import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:evoapp/feature/login/old_device/re_auth_input_pin/re_auth_input_pin_cubit.dart';
import 'package:evoapp/feature/pin/mock/mock_pin_use_case.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/validator/evo_validator.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';
import '../../../../util/test_util.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockEvoValidator extends Mock implements EvoValidator {}

void main() {
  late ReAuthInputPinCubit cubit;
  final AuthenticationRepo authenticationRepo = MockAuthenticationRepo();
  final EvoLocalStorageHelper storageHelper = MockEvoLocalStorageHelper();

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerSingleton<EvoValidator>(MockEvoValidator());

    registerFallbackValue(TypeLogin.verifyPin);
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    cubit = ReAuthInputPinCubit(
      authenticationRepo: authenticationRepo,
      storageHelper: storageHelper,
    );
  });

  group('Test login', () {
    const String phoneNumber = 'phoneNumber';
    const String pinCode = 'pinCode';
    const String sessionToken = 'sessionToken';

    blocTest<ReAuthInputPinCubit, ReAuthPinState>(
      'Login success',
      build: () => cubit,
      setUp: () async {
        when(() => authenticationRepo.signIn(
              any(),
              phoneNumber: any(named: 'phoneNumber'),
              pin: any(named: 'pin'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => SignInEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(
                getMockPinFileNameByCase(MockPinUseCase.getVerifyPinSuccess)),
          )),
        );
        when(() => storageHelper.setUserPhoneNumber(any())).thenAnswer((_) => Future<void>.value());
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (ReAuthInputPinCubit cubit) => cubit.verifyPin(phoneNumber, pinCode, sessionToken),
      expect: () => <dynamic>[
        isA<PinLoadingState>(),
        isA<VerifyPinSuccessState>().having(
          (VerifyPinSuccessState state) => state.pinCode,
          'verify pinCode',
          pinCode,
        ),
      ],
      verify: (_) {
        verify(() => authenticationRepo.signIn(
              TypeLogin.verifyPin,
              phoneNumber: phoneNumber,
              pin: pinCode,
              sessionToken: sessionToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verify(() => storageHelper.setUserPhoneNumber(phoneNumber)).called(1);
      },
    );

    blocTest<ReAuthInputPinCubit, ReAuthPinState>(
      'Login failed limit_exceeded',
      build: () => cubit,
      setUp: () async {
        when(() => authenticationRepo.signIn(
              any(),
              phoneNumber: any(named: 'phoneNumber'),
              pin: any(named: 'pin'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => SignInEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.LIMIT_EXCEEDED,
            response: await TestUtil.getResponseMock(
                getMockPinFileNameByCase(MockPinUseCase.getVerifyPinLimitExceeded)),
          )),
        );
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (ReAuthInputPinCubit cubit) => cubit.verifyPin(phoneNumber, pinCode, sessionToken),
      expect: () => <dynamic>[
        isA<PinLoadingState>(),
        isA<PinLimitExceeded>().having(
          (PinLimitExceeded state) => state.errorMessage,
          'verify errorMessage',
          'user_message_limit_exceeded',
        ),
      ],
      verify: (_) {
        verify(() => authenticationRepo.signIn(
              TypeLogin.verifyPin,
              phoneNumber: phoneNumber,
              pin: pinCode,
              sessionToken: sessionToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verifyNever(() => storageHelper.setUserPhoneNumber(phoneNumber));
      },
    );

    blocTest<ReAuthInputPinCubit, ReAuthPinState>(
      'Login failed with statusCode is 400',
      build: () => cubit,
      setUp: () async {
        when(() => authenticationRepo.signIn(
              any(),
              phoneNumber: any(named: 'phoneNumber'),
              pin: any(named: 'pin'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => SignInEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: await TestUtil.getResponseMock(
                getMockPinFileNameByCase(MockPinUseCase.getVerifyPinInvalidCredential)),
          )),
        );
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (ReAuthInputPinCubit cubit) => cubit.verifyPin(phoneNumber, pinCode, sessionToken),
      expect: () => <dynamic>[
        isA<PinLoadingState>(),
        isA<VerifyPinBadRequest>().having(
          (VerifyPinBadRequest state) => state.errorMessage,
          'verify errorMessage',
          'verify_pin_invalid_credential',
        )
      ],
      verify: (_) {
        verify(() => authenticationRepo.signIn(
              TypeLogin.verifyPin,
              phoneNumber: phoneNumber,
              pin: pinCode,
              sessionToken: sessionToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verifyNever(() => storageHelper.setUserPhoneNumber(phoneNumber));
      },
    );

    blocTest<ReAuthInputPinCubit, ReAuthPinState>(
      'Login failed with session expired',
      build: () => cubit,
      setUp: () async {
        when(() => authenticationRepo.signIn(
              any(),
              phoneNumber: any(named: 'phoneNumber'),
              pin: any(named: 'pin'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => SignInEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.INVALID_TOKEN,
            response: <String, dynamic>{},
          )),
        );
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (ReAuthInputPinCubit cubit) => cubit.verifyPin(phoneNumber, pinCode, sessionToken),
      expect: () => <dynamic>[isA<PinLoadingState>(), isA<PinSessionExpired>()],
      verify: (_) {
        verify(() => authenticationRepo.signIn(
              TypeLogin.verifyPin,
              phoneNumber: phoneNumber,
              pin: pinCode,
              sessionToken: sessionToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verifyNever(() => storageHelper.setUserPhoneNumber(phoneNumber));
      },
    );

    blocTest<ReAuthInputPinCubit, ReAuthPinState>(
      'Login failed with default error',
      build: () => cubit,
      setUp: () async {
        when(() => authenticationRepo.signIn(
              any(),
              phoneNumber: any(named: 'phoneNumber'),
              pin: any(named: 'pin'),
              sessionToken: any(named: 'sessionToken'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => SignInEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.UNKNOWN_ERRORS,
            response: <String, dynamic>{},
          )),
        );
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (ReAuthInputPinCubit cubit) => cubit.verifyPin(phoneNumber, pinCode, sessionToken),
      expect: () => <dynamic>[
        isA<PinLoadingState>(),
        isA<PinErrorState>().having(
          (PinErrorState state) => state.errorUIModel.statusCode,
          'verify statusCode',
          CommonHttpClient.UNKNOWN_ERRORS,
        )
      ],
      verify: (_) {
        verify(() => authenticationRepo.signIn(
              TypeLogin.verifyPin,
              phoneNumber: phoneNumber,
              pin: pinCode,
              sessionToken: sessionToken,
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
        verifyNever(() => storageHelper.setUserPhoneNumber(phoneNumber));
      },
    );
  });

  group('validateLengthPin()', () {
    blocTest<ReAuthInputPinCubit, ReAuthPinState>(
      'should emit PinFullState when pin length is valid',
      setUp: () {
        when(() => evoValidator.validateMaxLengthPin(any())).thenReturn(true);
      },
      build: () => cubit,
      act: (ReAuthInputPinCubit cubit) => cubit.validateLengthPin('1234'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => [isA<PinFullState>()],
    );

    blocTest<ReAuthInputPinCubit, ReAuthPinState>(
      'should emit PinNotFullState when pin length is invalid',
      setUp: () {
        when(() => evoValidator.validateMaxLengthPin(any())).thenReturn(false);
      },
      build: () => cubit,
      act: (ReAuthInputPinCubit cubit) => cubit.validateLengthPin('123'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => [isA<PinNotFullState>()],
    );
  });
}
