import 'package:evoapp/feature/login/old_device/re_auth_input_pin/re_auth_input_pin_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PinState Tests', () {
    test('PinNotFullState should be instantiated', () {
      final ReAuthPinState state = PinNotFullState();
      expect(state, isA<PinNotFullState>());
    });

    test('PinFullState should be instantiated', () {
      final ReAuthPinState state = PinFullState();
      expect(state, isA<PinFullState>());
    });

    test('PinLoadingState should be instantiated', () {
      final ReAuthPinState state = PinLoadingState();
      expect(state, isA<PinLoadingState>());
    });

    test('PinValidatedState should be instantiated', () {
      final ReAuthPinState state = PinValidatedState();
      expect(state, isA<PinValidatedState>());
    });

    test('PinLoadedState should be instantiated with pinCode', () {
      final ReAuthPinState state = VerifyPinSuccessState('1234');
      expect(
          state,
          isA<VerifyPinSuccessState>().having(
            (VerifyPinSuccessState state) => state.pinCode,
            'verify pinCode',
            '1234',
          ));
    });

    test('PinErrorState should be instantiated with errorUIModel and unknownError', () {
      final ErrorUIModel errorUIModel = ErrorUIModel();
      final ReAuthPinState state = PinErrorState(errorUIModel: errorUIModel);
      expect(
          state,
          isA<PinErrorState>().having(
            (PinErrorState state) => state.errorUIModel,
            'verify errorUIModel',
            errorUIModel,
          ));
    });

    test('PinSessionExpired should be instantiated', () {
      final ReAuthPinState state = PinSessionExpired();
      expect(state, isA<PinSessionExpired>());
    });

    test('ResetPinSuccessState should be instantiated', () {
      final ReAuthPinState state = ResetPinSuccessState();
      expect(state, isA<ResetPinSuccessState>());
    });

    test('PinLimitExceeded should be instantiated with errorMessage', () {
      final ReAuthPinState state = PinLimitExceeded('Error');
      expect(
          state,
          isA<PinLimitExceeded>().having(
            (PinLimitExceeded state) => state.errorMessage,
            'verify errorMessage',
            'Error',
          ));
    });

    test('VerifyPinBadRequest should be instantiated with errorMessage', () {
      final ReAuthPinState state = VerifyPinBadRequest('Error');
      expect(
          state,
          isA<VerifyPinBadRequest>().having(
            (VerifyPinBadRequest state) => state.errorMessage,
            'verify errorMessage',
            'Error',
          ));
    });
  });
}
