import 'package:evoapp/feature/login/old_device/mock_file/mock_previous_login_device_usecase.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Test LoginOnPreviousDeviceUseCase', () {
    expect(MockPreviousLoginDeviceUseCase.success.value, 'previous_login_success.json');
    expect(MockPreviousLoginDeviceUseCase.invalidParameters.value, 'previous_login_invalid_parameters.json');
    expect(MockPreviousLoginDeviceUseCase.limitExceeded.value, 'previous_login_limit_exceeded.json');
  });

  test('Test getLoginPreviousDeviceMockFileName', () {
    expect(MockPreviousLoginDeviceUseCase.getMockFileNameByType(MockPreviousLoginDeviceUseCase.success), 'previous_login_success.json');
    expect(MockPreviousLoginDeviceUseCase.getMockFileNameByType(MockPreviousLoginDeviceUseCase.invalidParameters),
        'previous_login_invalid_parameters.json');
    expect(MockPreviousLoginDeviceUseCase.getMockFileNameByType(MockPreviousLoginDeviceUseCase.limitExceeded),
        'previous_login_limit_exceeded.json');
  });
}