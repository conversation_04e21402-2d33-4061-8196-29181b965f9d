import 'package:evoapp/feature/login/old_device/mock_file/mock_login_on_old_device_file_name.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('Test LoginOnOldDeviceUseCase', () {
    expect(LoginOnOldDeviceUseCase.success.value, 'signin_success.json');
    expect(LoginOnOldDeviceUseCase.invalidParameters.value, 'signin_invalid_parameters.json');
    expect(LoginOnOldDeviceUseCase.limitExceeded.value, 'signin_limit_exceeded.json');
  });

  test('Test getLoginOnOldDeviceMockFileName', () {
    expect(getLoginOnOldDeviceMockFileName(LoginOnOldDeviceUseCase.success), 'signin_success.json');
    expect(getLoginOnOldDeviceMockFileName(LoginOnOldDeviceUseCase.invalidParameters),
        'signin_invalid_parameters.json');
    expect(getLoginOnOldDeviceMockFileName(LoginOnOldDeviceUseCase.limitExceeded),
        'signin_limit_exceeded.json');
  });
}
