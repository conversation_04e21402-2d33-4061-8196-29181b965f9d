import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/login_previous_device_request.dart';
import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/feature/login/old_device/previous_login_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PreviousLogInState Tests', () {
    late ErrorUIModel mockError;

    setUp(() {
      mockError = ErrorUIModel(statusCode: 400, userMessage: 'Test error message');
    });

    test('PreviousLogInInitial is a PreviousLogInState', () {
      final PreviousLogInInitial defaultState = PreviousLogInInitial();
      expect(defaultState, isA<PreviousLogInState>());

      final PreviousLogInInitial customState = PreviousLogInInitial();
      expect(customState, isA<PreviousLogInState>());
    });

    test('PreviousLogInLoading is a PreviousLogInState', () {
      final PreviousLogInLoading state = PreviousLogInLoading();

      expect(state, isA<PreviousLogInState>());
    });

    test('PreviousLogInSuccess is a PreviousLogInState', () {
      final PreviousLogInSuccess state = PreviousLogInSuccess();

      expect(state, isA<PreviousLogInState>());
    });

    test('PreviousLogInFailure is a PreviousLogInState with correct properties', () {
      final PreviousLogInFailure state = PreviousLogInFailure(
        loginType: LoginType.mPin,
        error: mockError,
      );

      expect(state, isA<PreviousLogInState>());
      expect(state.error, equals(mockError));
      expect(state.loginType, equals(LoginType.mPin));
    });

    test('PreviousLogInBadRequest is a PreviousLogInFailure', () {
      final PreviousLogInBadRequest state = PreviousLogInBadRequest(
        loginType: LoginType.mPin,
        error: mockError,
      );

      expect(state, isA<PreviousLogInFailure>());
      expect(state, isA<PreviousLogInState>());
      expect(state.error, equals(mockError));
      expect(state.loginType, equals(LoginType.mPin));
    });

    test('PreviousLogInSessionExpired is a PreviousLogInFailure', () {
      final PreviousLogInSessionExpired state = PreviousLogInSessionExpired(
        loginType: LoginType.mPin,
        error: mockError,
      );

      expect(state, isA<PreviousLogInFailure>());
      expect(state, isA<PreviousLogInState>());
      expect(state.error, equals(mockError));
      expect(state.loginType, equals(LoginType.mPin));
    });

    test('PreviousLogInLimitedExceeded is a PreviousLogInFailure', () {
      final PreviousLogInLimitedExceeded state = PreviousLogInLimitedExceeded(
        loginType: LoginType.mPin,
        error: mockError,
      );

      expect(state, isA<PreviousLogInFailure>());
      expect(state, isA<PreviousLogInState>());
      expect(state.error, equals(mockError));
      expect(state.loginType, equals(LoginType.mPin));
    });
  });

  group('State Inheritance Tests', () {
    late ErrorUIModel mockError;

    setUp(() {
      mockError = ErrorUIModel(statusCode: 500, userMessage: 'Server error');
    });

    test('all failure states inherit from PreviousLogInFailure', () {
      final badRequestState = PreviousLogInBadRequest(
        loginType: LoginType.mPin,
        error: mockError,
      );
      final sessionExpiredState = PreviousLogInSessionExpired(
        loginType: LoginType.mPin,
        error: mockError,
      );
      final limitExceededState = PreviousLogInLimitedExceeded(
        loginType: LoginType.mPin,
        error: mockError,
      );

      expect(badRequestState, isA<PreviousLogInFailure>());
      expect(sessionExpiredState, isA<PreviousLogInFailure>());
      expect(limitExceededState, isA<PreviousLogInFailure>());
    });

    test('all states inherit from PreviousLogInState', () {
      final initialState = PreviousLogInInitial();
      final loadingState = PreviousLogInLoading();
      final successState = PreviousLogInSuccess();
      final failureState = PreviousLogInFailure(
        loginType: LoginType.mPin,
        error: mockError,
      );
      final badRequestState = PreviousLogInBadRequest(
        loginType: LoginType.mPin,
        error: mockError,
      );
      final sessionExpiredState = PreviousLogInSessionExpired(
        loginType: LoginType.mPin,
        error: mockError,
      );
      final limitExceededState = PreviousLogInLimitedExceeded(
        loginType: LoginType.mPin,
        error: mockError,
      );

      expect(initialState, isA<PreviousLogInState>());
      expect(loadingState, isA<PreviousLogInState>());
      expect(successState, isA<PreviousLogInState>());
      expect(failureState, isA<PreviousLogInState>());
      expect(badRequestState, isA<PreviousLogInState>());
      expect(sessionExpiredState, isA<PreviousLogInState>());
      expect(limitExceededState, isA<PreviousLogInState>());
    });
  });

  group('Error Handling Tests', () {
    test('failure states handle different error types', () {
      final badRequestError = ErrorUIModel(statusCode: 400, userMessage: 'Bad request');
      final unauthorizedError = ErrorUIModel(statusCode: 401, userMessage: 'Unauthorized');
      final limitExceededError = ErrorUIModel(statusCode: 429, userMessage: 'Limit exceeded');

      final badRequestState = PreviousLogInBadRequest(
        loginType: LoginType.mPin,
        error: badRequestError,
      );
      final sessionExpiredState = PreviousLogInSessionExpired(
        loginType: LoginType.mPin,
        error: unauthorizedError,
      );
      final limitExceededState = PreviousLogInLimitedExceeded(
        loginType: LoginType.mPin,
        error: limitExceededError,
      );

      expect(badRequestState.error.statusCode, equals(400));
      expect(sessionExpiredState.error.statusCode, equals(401));
      expect(limitExceededState.error.statusCode, equals(429));
    });

    test('failure states preserve error messages', () {
      const String testMessage = 'Custom error message';
      final customError = ErrorUIModel(statusCode: 400, userMessage: testMessage);

      final badRequestState = PreviousLogInBadRequest(
        loginType: LoginType.mPin,
        error: customError,
      );

      expect(badRequestState.error.userMessage, equals(testMessage));
    });
  });
}
