import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_service.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:mocktail/mocktail.dart';

class AuthenticationRepoMock extends Mock implements AuthenticationRepo {}

class EvoLocalStorageHelperMock extends Mock implements EvoLocalStorageHelper {}

class BiometricsAuthenticateMock extends Mock implements BiometricsAuthenticate {}

class JwtHelperMock extends Mock implements JwtHelper {}

class BiometricAuthenticationServiceMock extends Mock implements BiometricAuthenticationService {}
