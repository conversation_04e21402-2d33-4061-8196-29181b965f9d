import 'package:evoapp/feature/login/old_device/pincode/pin_code_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('PinCodeInitial is a PinCodeState', () {
    final PinCodeInitial state = PinCodeInitial();
    expect(state, isA<PinCodeState>());
  });

  test('PinCodeClearErrorState is a PinCodeState', () {
    final PinCodeClearErrorState state = PinCodeClearErrorState();
    expect(state, isA<PinCodeState>());
  });

  test('PinCodeErrorState is a PinCodeState', () {
    const String errorMessage = 'errorMessage';
    final PinCodeErrorState state = PinCodeErrorState(errorMessage);
    expect(state, isA<PinCodeState>());
    expect(state.error, errorMessage);
  });
}
