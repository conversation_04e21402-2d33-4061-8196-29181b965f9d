import 'package:evoapp/feature/login/old_device/pincode/validate_mpin_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('ValidateMPINInitial is a ValidateMPINState', () {
    final ValidateMPINInitial state = ValidateMPINInitial();
    expect(state, isA<ValidateMPINState>());
  });


  test('ValidateMPINFailureState is a ValidateMPINState', () {
    const String errorMessage = 'errorMessage';
    final ValidateMPINFailureState state = ValidateMPINFailureState(errorMessage);
    expect(state, isA<ValidateMPINState>());
    expect(state.errorMessage, errorMessage);
  });
}
