import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/login/old_device/pincode/validate_mpin_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late ValidateMPINCubit cubit;

  setUp(() {
    cubit = ValidateMPINCubit();
  });

  test('Initial state', () {
    expect(cubit.state, isA<ValidateMPINInitial>());
  });

  blocTest<ValidateMPINCubit, ValidateMPINState>(
    'test setMPINFailure function',
    build: () => cubit,
    act: (ValidateMPINCubit cubit) => cubit.setMPINFailure('message'),
    expect: () => <dynamic>[
      isA<ValidateMPINFailureState>().having(
        (ValidateMPINFailureState state) => state.errorMessage,
        'verify error message',
        'message',
      ),
    ],
  );
}
