import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/login/old_device/pincode/pin_code_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late PinCodeCubit cubit;

  setUp(() {
    cubit = PinCodeCubit();
  });

  test('Initial state', () {
    expect(cubit.state, isA<PinCodeInitial>());
  });

  blocTest<PinCodeCubit, PinCodeState>(
    'test clearPinCodeError function',
    build: () => cubit,
    act: (PinCodeCubit cubit) => cubit.clearPinCodeError(),
    expect: () => <dynamic>[
      isA<PinCodeClearErrorState>(),
    ],
  );

  blocTest<PinCodeCubit, PinCodeState>(
    'test setPinCodeError function',
    build: () => cubit,
    act: (PinCodeCubit cubit) => cubit.setPinCodeError('message'),
    expect: () => <dynamic>[
      isA<PinCodeErrorState>().having(
        (PinCodeErrorState state) => state.error,
        'verify error message',
        'message',
      ),
    ],
  );
}
