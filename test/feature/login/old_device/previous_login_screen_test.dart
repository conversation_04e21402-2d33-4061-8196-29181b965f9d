import 'package:evoapp/data/request/login_previous_device_request.dart';
import 'package:evoapp/feature/login/old_device/biometric/biometric_cubit.dart';
import 'package:evoapp/feature/login/old_device/components/login_biometric_button.dart';
import 'package:evoapp/feature/login/old_device/components/login_mpin_widget.dart';
import 'package:evoapp/feature/login/old_device/components/login_submit_button.dart';
import 'package:evoapp/feature/login/old_device/components/login_title_widget.dart';
import 'package:evoapp/feature/login/old_device/handlers/login_authentication_handler.dart';
import 'package:evoapp/feature/login/old_device/handlers/login_state_handler.dart';
import 'package:evoapp/feature/login/old_device/pincode/validate_mpin_cubit.dart';
import 'package:evoapp/feature/login/old_device/previous_login_cubit.dart';
import 'package:evoapp/feature/login/old_device/previous_login_screen.dart';
import 'package:evoapp/feature/login/old_device/providers/login_cubit_provider.dart';
import 'package:evoapp/feature/profile/cubit/user_profile_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockLoginPreviousLogInCubit extends AppMockCubit<PreviousLogInState>
    implements PreviousLogInCubit {}

class MockBiometricCubit extends AppMockCubit<BiometricState> implements BiometricCubit {}

class MockPinCodeCubit extends AppMockCubit<ValidateMPINState> implements ValidateMPINCubit {}

class MockUserProfileCubit extends AppMockCubit<UserProfileState> implements UserProfileCubit {}

class MockLoginAuthenticationHandler extends Mock implements LoginAuthenticationHandler {}

class MockLoginStateHandler extends Mock implements LoginStateHandler {}

class MockLoginMPINController extends Mock implements LoginMPINController {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

void main() {
  late MockLoginPreviousLogInCubit loginCubit;
  late MockBiometricCubit biometricCubit;
  late MockPinCodeCubit pinCodeCubit;
  late MockUserProfileCubit userProfileCubit;
  late EvoSnackBar mockEvoSnackBar;

  setUpAll(() {
    initConfigEvoPageStateBase();

    // register snackbar
    getIt.registerSingleton<EvoSnackBar>(MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();
    registerFallbackValue(SnackBarType.success);

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() async {
    setUpMockConfigEvoPageStateBase();

    loginCubit = MockLoginPreviousLogInCubit()..emit(PreviousLogInInitial());
    biometricCubit = MockBiometricCubit()..emit(BiometricInitial());
    pinCodeCubit = MockPinCodeCubit()..emit(ValidateMPINInitial());
    userProfileCubit = MockUserProfileCubit()..emit(UserProfileInitial());

    when(() => biometricCubit.initialize()).thenAnswer((_) async {});
    when(() => biometricCubit.authenticate(
            shouldEmitUnavailableState: any(named: 'shouldEmitUnavailableState')))
        .thenAnswer((_) async {});
    when(() => biometricCubit.authenticate()).thenAnswer((_) async {});

    when(() => userProfileCubit.getUsername()).thenAnswer((_) async {});
    when(() => loginCubit.loginWithMPIN(any())).thenAnswer((_) async {});
    when(() => loginCubit.loginWithBiometricToken()).thenAnswer((_) async {});

    /// stub show snackbar
    when(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInMilliSec: any(named: 'durationInMilliSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).thenAnswer((_) async {
      return Future<bool?>.value();
    });
  });

  tearDown(() {
    reset(mockEvoSnackBar);
  });

  group('PreviousLoginInScreen', () {
    Widget buildWidgetInTest() {
      return MaterialApp(
        home: MultiBlocProvider(
          providers: <BlocProvider<dynamic>>[
            BlocProvider<PreviousLogInCubit>.value(value: loginCubit),
            BlocProvider<BiometricCubit>.value(value: biometricCubit),
            BlocProvider<ValidateMPINCubit>.value(value: pinCodeCubit),
            BlocProvider<UserProfileCubit>.value(value: userProfileCubit),
          ],
          child: const PreviousLogInScreen(),
        ),
      );
    }

    test('should self navigate', () {
      PreviousLogInScreen.pushNamed();
      verify(() => mockNavigatorContext.pushNamed(Screen.previousLogInScreen.name,
          extra: any(named: 'extra'))).called(1);

      PreviousLogInScreen.goNamed();
      verify(() => mockNavigatorContext.goNamed(Screen.previousLogInScreen.name,
          extra: any(named: 'extra'))).called(1);
    });

    testWidgets('should display login title widget', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      expect(find.byType(LoginTitleWidget), findsOneWidget);
    });

    testWidgets('should display MPIN widget', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      expect(find.byType(LoginMPINWidget), findsOneWidget);
    });

    testWidgets('should display submit button', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      expect(find.byType(LoginSubmitButton), findsOneWidget);
    });

    testWidgets('should display biometric button', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      expect(find.byType(LoginBiometricButton), findsOneWidget);
    });

    testWidgets('should initialize handlers and setup on init', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      // Verify that initialization methods are called
      verify(() => biometricCubit.initialize()).called(1);
      verify(() => userProfileCubit.getUsername()).called(1);
    });

    testWidgets('should handle biometric authentication available state',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      // Emit biometric available state
      biometricCubit.emit(BiometricAuthenticationAvailable());
      await tester.pump();

      // Should trigger auto biometric authentication on first launch
      verify(() => biometricCubit.authenticate(shouldEmitUnavailableState: true)).called(1);
    });

    testWidgets('should handle biometric success state', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      // Emit biometric success state
      biometricCubit.emit(BiometricAuthSuccess());
      await tester.pump();

      // Should trigger login with biometric token
      verify(() => loginCubit.loginWithBiometricToken()).called(1);
    });

    testWidgets('should show hud loading when state is PreviousLogInLoading',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      loginCubit.emit(PreviousLogInLoading());
      await tester.pump();

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
    });

    testWidgets('should hide hud loading when state changes from loading',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      // First emit loading state
      loginCubit.emit(PreviousLogInLoading());
      await tester.pump();

      // Then emit non-loading state
      loginCubit.emit(PreviousLogInInitial());
      await tester.pump();

      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
    });

    testWidgets('should handle login failure with user message', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      const String errorMessage = 'Invalid MPIN';
      loginCubit.emit(PreviousLogInFailure(
          loginType: LoginType.mPin, error: ErrorUIModel(userMessage: errorMessage)));
      await tester.pump();

      // Should hide loading and handle error through state handler
      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
      verify(() => mockEvoSnackBar.show(errorMessage,
          typeSnackBar: SnackBarType.error,
          durationInMilliSec: SnackBarDuration.short.value,
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'))).called(1);
    });

    testWidgets('should prevent back navigation with PopScope', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      // Find PopScope widget
      expect(find.byType(PopScope), findsOneWidget);

      final PopScope popScope = tester.widget(find.byType(PopScope));
      expect(popScope.canPop, false);
    });

    testWidgets('should wrap content in LoginCubitProvider', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      expect(find.byType(LoginCubitProvider), findsOneWidget);
    });
  });
}
