import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/inactive_detector/inactive_detector_widget.dart';
import 'package:evoapp/feature/login/old_device/biometric/biometric_cubit.dart';
import 'package:evoapp/feature/login/old_device/components/login_mpin_widget.dart';
import 'package:evoapp/feature/login/old_device/handlers/login_authentication_handler.dart';
import 'package:evoapp/feature/login/old_device/previous_login_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockPreviousLogInCubit extends MockCubit<PreviousLogInState> implements PreviousLogInCubit {}
class MockBiometricCubit extends MockCubit<BiometricState> implements BiometricCubit {}
class MockLoginMPINController extends Mock implements LoginMPINController {}
class MockAppState extends Mock implements AppState {}
class MockInactiveDetectorController extends Mock implements InactiveDetectorController {}

void main() {
  group('LoginAuthenticationHandler Tests', () {
    late MockPreviousLogInCubit mockLoginCubit;
    late MockBiometricCubit mockBiometricCubit;
    late MockLoginMPINController mockMpinController;
    late MockAppState mockAppState;
    late MockInactiveDetectorController mockInactiveDetectorController;
    late LoginAuthenticationHandler authHandler;

    setUpAll(() {
      // Initialize Flutter binding for tests
      TestWidgetsFlutterBinding.ensureInitialized();

      // Register AppState with GetIt for dependency injection
      mockAppState = MockAppState();
      mockInactiveDetectorController = MockInactiveDetectorController();

      // Mock AppState properties
      when(() => mockAppState.isUserLogIn).thenReturn(false);
      when(() => mockAppState.inactiveDetectorController).thenReturn(mockInactiveDetectorController);

      getItRegisterNavigator(context: MockContext());

      getIt.registerSingleton<AppState>(mockAppState);
    });

    tearDownAll(() {
      // Clean up GetIt registration
      if (getIt.isRegistered<AppState>()) {
        getIt.unregister<AppState>();
      }
    });

    setUp(() {
      mockLoginCubit = MockPreviousLogInCubit();
      mockBiometricCubit = MockBiometricCubit();
      mockMpinController = MockLoginMPINController();

      // Stub mock methods to return Future<void>
      when(() => mockLoginCubit.loginWithMPIN(any())).thenAnswer((_) async {});
      when(() => mockLoginCubit.loginWithBiometricToken()).thenAnswer((_) async {});
      when(() => mockBiometricCubit.initialize()).thenAnswer((_) async {});
      when(() => mockBiometricCubit.authenticate()).thenAnswer((_) async {});
      when(() => mockBiometricCubit.authenticate(shouldEmitUnavailableState: any(named: 'shouldEmitUnavailableState'))).thenAnswer((_) async {});
      when(() => mockMpinController.requestFocus()).thenReturn(null);
      when(() => mockMpinController.text).thenReturn('');

      // Mock InactiveDetectorController functions
      when(() => mockInactiveDetectorController.enable).thenReturn(() {});
      when(() => mockInactiveDetectorController.disable).thenReturn(() {});

      // Reset AppState mock for each test
      reset(mockAppState);

      // Mock AppState properties
      when(() => mockAppState.isUserLogIn).thenReturn(false);
      when(() => mockAppState.inactiveDetectorController).thenReturn(mockInactiveDetectorController);

      authHandler = LoginAuthenticationHandler(
        loginCubit: mockLoginCubit,
        biometricCubit: mockBiometricCubit,
        mPinController: mockMpinController,
      );
    });

    group('Initialization Tests', () {
      test('should initialize correctly with all dependencies', () {
        expect(authHandler, isNotNull);
      });

      test('should schedule biometric cubit initialization when initialize is called', () {
        // Act & Assert - Should not throw
        expect(() => authHandler.initialize(), returnsNormally);

        // Note: The actual biometric cubit initialization happens in a post-frame callback
        // which is difficult to test in unit tests. The important thing is that the method
        // doesn't throw an error and schedules the callback properly.
      });
    });

    group('MPIN Login Tests', () {
      test('should call loginWithMPIN with provided MPIN', () {
        // Arrange
        const String testMpin = '1234';

        // Act
        authHandler.loginWithMPIN(testMpin);

        // Assert
        verify(() => mockLoginCubit.loginWithMPIN(testMpin)).called(1);
      });

      test('should use controller text when no MPIN provided', () {
        // Arrange
        const String controllerText = '5678';
        when(() => mockMpinController.text).thenReturn(controllerText);

        // Act
        authHandler.loginWithMPIN();

        // Assert
        verify(() => mockLoginCubit.loginWithMPIN(controllerText)).called(1);
      });

      test('should use empty string when no MPIN and no controller', () {
        // Arrange
        authHandler = LoginAuthenticationHandler(
          loginCubit: mockLoginCubit,
          biometricCubit: mockBiometricCubit,
        );

        // Act
        authHandler.loginWithMPIN();

        // Assert
        verify(() => mockLoginCubit.loginWithMPIN('')).called(1);
      });
    });

    group('Biometric Authentication Tests', () {
      test('should call biometric authenticate', () {
        // Act
        authHandler.authenticateWithBiometric();

        // Assert
        verify(() => mockBiometricCubit.authenticate()).called(1);
      });
    });

    group('Login Success Handling Tests', () {
      test('should handle login success and update user login status', () {
        // Arrange - Mock GlobalKeyProvider to avoid navigation issues
        final GlobalKeyProvider mockGlobalKeyProvider = getIt.get<GlobalKeyProvider>();
        when(() => mockGlobalKeyProvider.navigatorKey).thenReturn(GlobalKey<NavigatorState>());

        // Act & Assert - Should not throw
        expect(() => authHandler.handleLoginSuccess(), returnsNormally);
      });
    });
  });
}
