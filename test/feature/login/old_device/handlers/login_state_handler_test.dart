import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/request/login_previous_device_request.dart';
import 'package:evoapp/feature/login/old_device/biometric/biometric_cubit.dart';
import 'package:evoapp/feature/login/old_device/components/login_mpin_widget.dart';
import 'package:evoapp/feature/login/old_device/handlers/login_authentication_handler.dart';
import 'package:evoapp/feature/login/old_device/handlers/login_state_handler.dart';
import 'package:evoapp/feature/login/old_device/pincode/validate_mpin_cubit.dart';
import 'package:evoapp/feature/login/old_device/previous_login_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

// Mock classes
class MockValidateMPINCubit extends MockCubit<ValidateMPINState> implements ValidateMPINCubit {}
class MockLoginAuthenticationHandler extends Mock implements LoginAuthenticationHandler {}
class MockDialogFunction extends Mock implements DialogFunction {}
class MockPreviousLogInCubit extends MockCubit<PreviousLogInState> implements PreviousLogInCubit {}
class MockBiometricCubit extends MockCubit<BiometricState> implements BiometricCubit {}
class MockLoginMPINController extends Mock implements LoginMPINController {}

void main() {
  group('LoginStateHandler Tests', () {
    late MockPreviousLogInCubit mockLoginCubit;
    late MockBiometricCubit mockBiometricCubit;
    late MockLoginMPINController mockMpinController;
    late MockValidateMPINCubit mockPinCodeCubit;
    late MockLoginAuthenticationHandler mockAuthHandler;
    late LoginStateHandler stateHandler;
    late bool onHandleApiErrorCalled;
    late ErrorUIModel? capturedError;

    setUpAll(() {
      // Initialize Flutter binding for tests
      TestWidgetsFlutterBinding.ensureInitialized();

      // Register fallback values for mocktail
      registerFallbackValue(SessionDialogType.previousLogIn);

      // Register mock functions in GetIt
      getItRegisterMockCommonUtilFunctionAndImageProvider();

      // Register mock dialog function
      getIt.registerLazySingleton<DialogFunction>(() => MockDialogFunction());
    });

    tearDownAll(() {
      // Clean up GetIt registrations
      getItUnRegisterMockCommonUtilFunctionAndImageProvider();
      if (getIt.isRegistered<DialogFunction>()) {
        getIt.unregister<DialogFunction>();
      }
    });

    setUp(() {
      mockLoginCubit = MockPreviousLogInCubit();
      mockBiometricCubit = MockBiometricCubit();
      mockMpinController = MockLoginMPINController();

      // Stub mock methods to return Future<void>
      when(() => mockLoginCubit.loginWithMPIN(any())).thenAnswer((_) async {});
      when(() => mockLoginCubit.loginWithBiometricToken()).thenAnswer((_) async {});
      when(() => mockBiometricCubit.initialize()).thenAnswer((_) async {});
      when(() => mockBiometricCubit.authenticate()).thenAnswer((_) async {});
      when(() => mockBiometricCubit.authenticate(shouldEmitUnavailableState: any(named: 'shouldEmitUnavailableState'))).thenAnswer((_) async {});
      when(() => mockMpinController.requestFocus()).thenReturn(null);
      when(() => mockMpinController.text).thenReturn('');

      mockPinCodeCubit = MockValidateMPINCubit();
      mockAuthHandler = MockLoginAuthenticationHandler();

      // Reset callback tracking variables
      onHandleApiErrorCalled = false;
      capturedError = null;

      // Get the mock functions from GetIt and reset them
      final MockEvoUtilFunction mockEvoUtilFunction = getIt.get<EvoUtilFunction>() as MockEvoUtilFunction;
      final MockDialogFunction mockDialogFunction = getIt.get<DialogFunction>() as MockDialogFunction;

      // Reset mocks to clear any previous interactions
      reset(mockEvoUtilFunction);
      reset(mockDialogFunction);
      reset(mockPinCodeCubit);
      reset(mockAuthHandler);

      // Mock the global functions
      when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
      when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});
      when(() => mockDialogFunction.showDialogErrorLimitExceeded(
        type: any(named: 'type'),
        content: any(named: 'content'),
      )).thenAnswer((_) async {});

      when(() => mockDialogFunction.showDialogSessionTokenExpired(
        type: any(named: 'type'),
      )).thenAnswer((_) async {});

      // Mock authentication handler methods
      when(() => mockAuthHandler.handleLoginSuccess()).thenReturn(null);
      when(() => mockPinCodeCubit.setMPINFailure(any())).thenReturn(null);

      stateHandler = LoginStateHandler(
        loginCubit: mockLoginCubit,
        biometricCubit: mockBiometricCubit,
        mpinController: mockMpinController,
        pinCodeCubit: mockPinCodeCubit,
        authHandler: mockAuthHandler,
        onHandleApiError: (ErrorUIModel? error) async {
          onHandleApiErrorCalled = true;
          capturedError = error;
        },
      );
    });

    tearDown((){
      reset(mockLoginCubit);
      reset(mockBiometricCubit);
      reset(mockMpinController);
      reset(mockPinCodeCubit);
      reset(mockAuthHandler);
    });

    group('Initialization Tests', () {
      test('should initialize correctly with all dependencies', () {
        expect(stateHandler, isNotNull);
      });
    });

    group('handleLoginState Tests', () {
      test('should show loading when state is PreviousLogInLoading', () async {
        // Arrange
        final PreviousLogInLoading loadingState = PreviousLogInLoading();
        final MockEvoUtilFunction mockEvoUtilFunction = getIt.get<EvoUtilFunction>() as MockEvoUtilFunction;

        // Act
        await stateHandler.handleLoginState(loadingState);

        // Assert
        verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
        verifyNever(() => mockEvoUtilFunction.hideHudLoading());
        verifyNever(() => mockAuthHandler.handleLoginSuccess());
      });

      test('should handle success state correctly', () async {
        // Arrange
        final PreviousLogInSuccess successState = PreviousLogInSuccess();
        final MockEvoUtilFunction mockEvoUtilFunction = getIt.get<EvoUtilFunction>() as MockEvoUtilFunction;

        // Act
        await stateHandler.handleLoginState(successState);

        // Assert
        verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
        verify(() => mockAuthHandler.handleLoginSuccess()).called(1);
        verifyNever(() => mockEvoUtilFunction.showHudLoading());
      });

      test('should handle failure state correctly', () async {
        // Arrange
        final ErrorUIModel error = ErrorUIModel(userMessage: 'Test error');
        final PreviousLogInFailure failureState = PreviousLogInFailure(loginType: LoginType.mPin, error: error);
        final MockEvoUtilFunction mockEvoUtilFunction = getIt.get<EvoUtilFunction>() as MockEvoUtilFunction;

        // Act
        await stateHandler.handleLoginState(failureState);

        // Assert
        verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
        verifyNever(() => mockAuthHandler.handleLoginSuccess());
      });
    });

    group('Error Handling Tests', () {
      group('PreviousLogInLimitedExceeded Tests', () {
        test('should show limit exceeded popup for PreviousLogInLimitedExceeded', () async {
          // Arrange
          final String fakeErrorMessage = 'Limit exceeded';
          final ErrorUIModel error = ErrorUIModel(userMessage: fakeErrorMessage);
          final PreviousLogInLimitedExceeded limitExceededState = PreviousLogInLimitedExceeded(
            loginType: LoginType.mPin,
            error: error,
          );
          final MockDialogFunction mockDialogFunction = getIt.get<DialogFunction>() as MockDialogFunction;

          // Act
          await stateHandler.handleLoginState(limitExceededState);

          // Assert
          verify(() => mockDialogFunction.showDialogErrorLimitExceeded(
            type: SessionDialogType.previousLogIn,
            content: fakeErrorMessage
          )).called(1);
          verify(() => mockMpinController.clear()).called(1);

          verifyNever(() => mockPinCodeCubit.setMPINFailure(any()));
          expect(onHandleApiErrorCalled, isFalse);
        });

        test('should show limit exceeded popup even with null user message', () async {
          // Arrange
          final ErrorUIModel error = ErrorUIModel();
          final PreviousLogInLimitedExceeded limitExceededState = PreviousLogInLimitedExceeded(
            loginType: LoginType.mPin,
            error: error,
          );
          final MockDialogFunction mockDialogFunction = getIt.get<DialogFunction>() as MockDialogFunction;

          // Act
          await stateHandler.handleLoginState(limitExceededState);

          // Assert
          verify(() => mockDialogFunction.showDialogErrorLimitExceeded(
            type: SessionDialogType.previousLogIn,
          )).called(1);
          verify(() => mockMpinController.clear()).called(1);

          verifyNever(() => mockPinCodeCubit.setMPINFailure(any()));
          expect(onHandleApiErrorCalled, isFalse);
        });
      });

      group('PreviousLogInBadRequest Tests', () {
        test('should set MPIN failure for mPin login type with user message', () async {
          // Arrange
          const String errorMessage = 'Invalid MPIN';
          final ErrorUIModel error = ErrorUIModel(userMessage: errorMessage);
          final PreviousLogInBadRequest badRequestState = PreviousLogInBadRequest(
            loginType: LoginType.mPin,
            error: error,
          );

          // Act
          await stateHandler.handleLoginState(badRequestState);

          // Assert
          verify(() => mockPinCodeCubit.setMPINFailure(errorMessage)).called(1);
          expect(onHandleApiErrorCalled, isFalse);
          final MockDialogFunction mockDialogFunction = getIt.get<DialogFunction>() as MockDialogFunction;
          verifyNever(() => mockDialogFunction.showDialogErrorLimitExceeded(
            type: any(named: 'type'),
          ));
        });

        test('should use default error message when user message is null for mPin', () async {
          // Arrange
          final ErrorUIModel error = ErrorUIModel(userMessage: null);
          final PreviousLogInBadRequest badRequestState = PreviousLogInBadRequest(
            loginType: LoginType.mPin,
            error: error,
          );

          // Act
          await stateHandler.handleLoginState(badRequestState);

          // Assert
          verify(() => mockPinCodeCubit.setMPINFailure(EvoStrings.defaultInvalidMPIN)).called(1);
          expect(onHandleApiErrorCalled, isFalse);
        });

        test('should handle empty string user message for mPin', () async {
          // Arrange
          const String errorMessage = '';
          final ErrorUIModel error = ErrorUIModel(userMessage: errorMessage);
          final PreviousLogInBadRequest badRequestState = PreviousLogInBadRequest(
            loginType: LoginType.mPin,
            error: error,
          );

          // Act
          await stateHandler.handleLoginState(badRequestState);

          // Assert
          verify(() => mockPinCodeCubit.setMPINFailure(errorMessage)).called(1);
          expect(onHandleApiErrorCalled, isFalse);
        });
      });

      group('PreviousLogInSessionExpired Tests', () {
        test('should call onHandleApiError for session expired', () async {
          // Arrange
          final ErrorUIModel error = ErrorUIModel(userMessage: 'Session expired');
          final PreviousLogInSessionExpired sessionExpiredState = PreviousLogInSessionExpired(
            loginType: LoginType.mPin,
            error: error,
          );

          // Act
          await stateHandler.handleLoginState(sessionExpiredState);

          // Assert
          expect(onHandleApiErrorCalled, isFalse);
          verifyNever(() => mockPinCodeCubit.setMPINFailure(any()));
          final MockDialogFunction mockDialogFunction = getIt.get<DialogFunction>() as MockDialogFunction;
          verifyNever(() => mockDialogFunction.showDialogErrorLimitExceeded(
            type: any(named: 'type'),
          ));
        });

        test('should call onHandleApiError for session expired with null message', () async {
          // Arrange
          final ErrorUIModel error = ErrorUIModel(userMessage: null);
          final PreviousLogInSessionExpired sessionExpiredState = PreviousLogInSessionExpired(
            loginType: LoginType.mPin,
            error: error,
          );

          // Act
          await stateHandler.handleLoginState(sessionExpiredState);

          // Assert
          expect(onHandleApiErrorCalled, isFalse);
          verifyNever(() => mockPinCodeCubit.setMPINFailure(any()));
        });
      });

      group('Generic PreviousLogInFailure Tests', () {
        test('should call onHandleApiError for generic failure (default case)', () async {
          // Arrange
          final ErrorUIModel error = ErrorUIModel(userMessage: 'Generic error');
          final PreviousLogInFailure genericFailureState = PreviousLogInFailure(
            loginType: LoginType.mPin,
            error: error,
          );

          // Act
          await stateHandler.handleLoginState(genericFailureState);

          // Assert
          expect(onHandleApiErrorCalled, isTrue);
          expect(capturedError, equals(error));
          verifyNever(() => mockPinCodeCubit.setMPINFailure(any()));
          final MockDialogFunction mockDialogFunction = getIt.get<DialogFunction>() as MockDialogFunction;
          verifyNever(() => mockDialogFunction.showDialogErrorLimitExceeded(
            type: any(named: 'type'),
          ));
        });

        test('should call onHandleApiError for generic failure with null message', () async {
          // Arrange
          final ErrorUIModel error = ErrorUIModel(userMessage: null);
          final PreviousLogInFailure genericFailureState = PreviousLogInFailure(
            loginType: LoginType.mPin,
            error: error,
          );

          // Act
          await stateHandler.handleLoginState(genericFailureState);

          // Assert
          expect(onHandleApiErrorCalled, isTrue);
          expect(capturedError, equals(error));
          verifyNever(() => mockPinCodeCubit.setMPINFailure(any()));
        });
      });
    });

    group('Edge Cases Tests', () {
      test('should handle unknown state types gracefully', () async {
        // Arrange
        final PreviousLogInInitial initialState = PreviousLogInInitial();
        final MockEvoUtilFunction mockEvoUtilFunction = getIt.get<EvoUtilFunction>() as MockEvoUtilFunction;

        // Act & Assert - Should not throw
        expect(() => stateHandler.handleLoginState(initialState), returnsNormally);

        // Should hide loading for non-loading states
        verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
        verifyNever(() => mockEvoUtilFunction.showHudLoading());
        verifyNever(() => mockAuthHandler.handleLoginSuccess());
      });

      test('should handle null mpinController gracefully in biometric state', () async {
        // Arrange - Create handler without mpin controller
        final LoginStateHandler handlerWithoutMpin = LoginStateHandler(
          loginCubit: mockLoginCubit,
          biometricCubit: mockBiometricCubit,
          mpinController: null, // No mpin controller
          pinCodeCubit: mockPinCodeCubit,
          authHandler: mockAuthHandler,
          onHandleApiError: (ErrorUIModel? error) async {
            onHandleApiErrorCalled = true;
            capturedError = error;
          },
        );

        // Act & Assert - Should not throw when requesting focus
        expect(() => handlerWithoutMpin.handleBiometricState(
          BiometricAuthUserDismiss(),
          isAutoBiometricAuthAtFirstLaunch: false,
          onAutoBiometricUsed: () {},
        ), returnsNormally);
      });

    });

    group('Integration Tests', () {
      test('should handle complete login flow with loading, then success', () async {
        // Arrange
        final PreviousLogInLoading loadingState = PreviousLogInLoading();
        final PreviousLogInSuccess successState = PreviousLogInSuccess();
        final MockEvoUtilFunction mockEvoUtilFunction = getIt.get<EvoUtilFunction>() as MockEvoUtilFunction;

        // Act
        await stateHandler.handleLoginState(loadingState);
        await stateHandler.handleLoginState(successState);

        // Assert
        verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
        verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
        verify(() => mockAuthHandler.handleLoginSuccess()).called(1);
      });

      test('should handle complete login flow with loading, then bad request', () async {
        // Arrange
        final PreviousLogInLoading loadingState = PreviousLogInLoading();
        final ErrorUIModel error = ErrorUIModel(userMessage: 'Invalid MPIN');
        final PreviousLogInBadRequest badRequestState = PreviousLogInBadRequest(
          loginType: LoginType.mPin,
          error: error,
        );
        final MockEvoUtilFunction mockEvoUtilFunction = getIt.get<EvoUtilFunction>() as MockEvoUtilFunction;

        // Act
        await stateHandler.handleLoginState(loadingState);
        await stateHandler.handleLoginState(badRequestState);

        // Assert
        verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
        verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
        verify(() => mockPinCodeCubit.setMPINFailure('Invalid MPIN')).called(1);
      });

      test('should handle complete login flow with loading, then limit exceeded', () async {
        // Arrange
        final String fakeUserMessage = 'Too many attempts';
        final PreviousLogInLoading loadingState = PreviousLogInLoading();
        final ErrorUIModel error = ErrorUIModel(userMessage: fakeUserMessage);
        final PreviousLogInLimitedExceeded limitExceededState = PreviousLogInLimitedExceeded(
          loginType: LoginType.mPin,
          error: error,
        );
        final MockEvoUtilFunction mockEvoUtilFunction = getIt.get<EvoUtilFunction>() as MockEvoUtilFunction;
        final MockDialogFunction mockDialogFunction = getIt.get<DialogFunction>() as MockDialogFunction;

        // Act
        await stateHandler.handleLoginState(loadingState);
        await stateHandler.handleLoginState(limitExceededState);

        // Assert
        verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
        verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
        verify(() => mockDialogFunction.showDialogErrorLimitExceeded(
          type: SessionDialogType.previousLogIn,
          content: fakeUserMessage,
        )).called(1);
      });

      test('should handle complete login flow with loading, then session expired', () async {
        // Arrange
        final PreviousLogInLoading loadingState = PreviousLogInLoading();
        final ErrorUIModel error = ErrorUIModel(userMessage: 'Session expired');
        final PreviousLogInSessionExpired sessionExpiredState = PreviousLogInSessionExpired(
          loginType: LoginType.mPin,
          error: error,
        );
        final MockEvoUtilFunction mockEvoUtilFunction = getIt.get<EvoUtilFunction>() as MockEvoUtilFunction;

        // Act
        await stateHandler.handleLoginState(loadingState);
        await stateHandler.handleLoginState(sessionExpiredState);

        // Assert
        verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
        verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
        expect(onHandleApiErrorCalled, isFalse);
      });
    });

    group('Biometric State Handling Tests', () {
      test('should trigger auto biometric auth on first launch when available', () async {
        // Arrange
        bool onAutoBiometricUsedCalled = false;

        // Act
        await stateHandler.handleBiometricState(
          BiometricAuthenticationAvailable(),
          isAutoBiometricAuthAtFirstLaunch: true,
          onAutoBiometricUsed: () {
            onAutoBiometricUsedCalled = true;
          },
        );

        // Assert
        verify(() => mockBiometricCubit.authenticate(shouldEmitUnavailableState: true)).called(1);
        expect(onAutoBiometricUsedCalled, isTrue);
      });

      test('should not trigger auto biometric auth when not first launch', () async {
        // Arrange
        bool onAutoBiometricUsedCalled = false;

        // Act
        await stateHandler.handleBiometricState(
          BiometricAuthenticationAvailable(),
          isAutoBiometricAuthAtFirstLaunch: false,
          onAutoBiometricUsed: () {
            onAutoBiometricUsedCalled = true;
          },
        );

        // Assert
        verifyNever(() => mockBiometricCubit.authenticate(shouldEmitUnavailableState: true));
        expect(onAutoBiometricUsedCalled, isFalse);
      });

      test('should login with biometric token on success', () async {
        // Act
        await stateHandler.handleBiometricState(
          BiometricAuthSuccess(),
          isAutoBiometricAuthAtFirstLaunch: false,
          onAutoBiometricUsed: () {},
        );

        // Assert
        verify(() => mockLoginCubit.loginWithBiometricToken()).called(1);
      });

      test('should request focus on user dismiss', () async {
        // Act
        await stateHandler.handleBiometricState(
          BiometricAuthUserDismiss(),
          isAutoBiometricAuthAtFirstLaunch: false,
          onAutoBiometricUsed: () {},
        );

        // Assert
        verify(() => mockMpinController.requestFocus()).called(1);
      });

      test('should request focus if biometric is unavailable. then user enter MPIN', () async {
        // Act
        await stateHandler.handleBiometricState(
          BiometricAuthenticationUnavailable(),
          isAutoBiometricAuthAtFirstLaunch: false,
          onAutoBiometricUsed: () {},
        );

        // Assert
        verify(() => mockMpinController.requestFocus()).called(1);
      });

      test('should handle user dismiss when no mpin controller', () async {
        // Arrange
        stateHandler = LoginStateHandler(
          loginCubit: mockLoginCubit,
          biometricCubit: mockBiometricCubit,
          mpinController: mockMpinController,
          pinCodeCubit: mockPinCodeCubit,
          authHandler: mockAuthHandler,
          onHandleApiError: (ErrorUIModel? error) async {
            onHandleApiErrorCalled = true;
            capturedError = error;
          },
        );

        // Act & Assert (should not throw)
        expect(() => stateHandler.handleBiometricState(
          BiometricAuthUserDismiss(),
          isAutoBiometricAuthAtFirstLaunch: false,
          onAutoBiometricUsed: () {},
        ), returnsNormally);

      });

      test('should handle other biometric states without action', () async {
        // Test states that don't trigger specific actions
        final List<BiometricState> statesToTest = <BiometricState>[
          BiometricInitial(),
          BiometricAuthFail(),
        ];

        for (final BiometricState state in statesToTest) {
          // Act
          await stateHandler.handleBiometricState(
            state,
            isAutoBiometricAuthAtFirstLaunch: false,
            onAutoBiometricUsed: () {},
          );

          // Assert no specific actions are called
          verifyNever(() => mockBiometricCubit.authenticate(shouldEmitUnavailableState: any(named: 'shouldEmitUnavailableState')));
          verifyNever(() => mockLoginCubit.loginWithBiometricToken());
          verifyNever(() => mockMpinController.requestFocus());
        }
      });
    });
  });
}
