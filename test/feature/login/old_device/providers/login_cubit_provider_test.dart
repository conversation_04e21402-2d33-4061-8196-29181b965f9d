import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/login/old_device/biometric/biometric_cubit.dart';
import 'package:evoapp/feature/login/old_device/pincode/validate_mpin_cubit.dart';
import 'package:evoapp/feature/login/old_device/previous_login_cubit.dart';
import 'package:evoapp/feature/login/old_device/providers/login_cubit_provider.dart';
import 'package:evoapp/feature/login/utils/login_old_device_utils.dart';
import 'package:evoapp/feature/profile/cubit/user_profile_cubit.dart';
import 'package:evoapp/model/user_info_notifier.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:nested/nested.dart';

// Mock classes
class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockLoginOldDeviceUtils extends Mock implements LoginOldDeviceUtils {}

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockUserRepo extends Mock implements UserRepo {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockJwtHelper extends Mock implements JwtHelper {}

class MockAppState extends Mock implements AppState {
  @override
  UserInfoNotifier userInfo = UserInfoNotifier(null);
}

void main() {
  group('LoginCubitProvider Tests', () {
    late MockEvoLocalStorageHelper mockLocalStorageHelper;
    late MockLoginOldDeviceUtils mockLoginOldDeviceUtils;
    late MockBiometricsAuthenticate mockBiometricsAuthenticate;
    late MockAuthenticationRepo mockAuthenticationRepo;
    late MockJwtHelper mockJwtHelper;
    late MockUserRepo mockUserRepo;
    late MockAppState mockAppState;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      // Reset getIt before each test
      getIt.reset();

      // Create fresh mocks for each test
      mockLocalStorageHelper = MockEvoLocalStorageHelper();
      mockLoginOldDeviceUtils = MockLoginOldDeviceUtils();
      mockBiometricsAuthenticate = MockBiometricsAuthenticate();
      mockUserRepo = MockUserRepo();
      mockAppState = MockAppState();
      mockAuthenticationRepo = MockAuthenticationRepo();
      mockJwtHelper = MockJwtHelper();

      // Register dependencies in getIt
      getIt.registerLazySingleton<EvoLocalStorageHelper>(() => mockLocalStorageHelper);
      getIt.registerLazySingleton<LoginOldDeviceUtils>(() => mockLoginOldDeviceUtils);
      getIt.registerLazySingleton<BiometricsAuthenticate>(() => mockBiometricsAuthenticate);
      getIt.registerLazySingleton<UserRepo>(() => mockUserRepo);
      getIt.registerLazySingleton<AppState>(() => mockAppState);
      getIt.registerLazySingleton<AuthenticationRepo>(() => mockAuthenticationRepo);
      getIt.registerLazySingleton<JwtHelper>(() => mockJwtHelper);
    });

    tearDown(() {
      getIt.reset();
    });

    testWidgets('should provide all required cubits', (WidgetTester tester) async {
      // Arrange
      bool childBuilt = false;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: LoginCubitProvider(
            child: Builder(
              builder: (BuildContext context) {
                // Verify all cubits are available
                final PreviousLogInCubit loginCubit = context.read<PreviousLogInCubit>();
                final BiometricCubit biometricCubit = context.read<BiometricCubit>();
                final ValidateMPINCubit validateMPINCubit = context.read<ValidateMPINCubit>();
                final UserProfileCubit userProfileCubit = context.read<UserProfileCubit>();

                expect(loginCubit, isNotNull);
                expect(biometricCubit, isNotNull);
                expect(validateMPINCubit, isNotNull);
                expect(userProfileCubit, isNotNull);

                childBuilt = true;
                return const SizedBox();
              },
            ),
          ),
        ),
      );

      // Assert - Dependencies should have been retrieved from getIt
      // This is verified by the fact that cubits are created successfully
      // without throwing exceptions about missing dependencies
      expect(childBuilt, isTrue);
    });

    testWidgets('should render child widget', (WidgetTester tester) async {
      // Arrange
      const Key testKey = Key('test-child');

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: LoginCubitProvider(
            child: Container(key: testKey),
          ),
        ),
      );

      // Assert
      expect(find.byKey(testKey), findsOneWidget);
    });

    testWidgets('should provide cubits with correct initial states', (WidgetTester tester) async {
      // Arrange
      PreviousLogInState? loginState;
      BiometricState? biometricState;
      ValidateMPINState? mpinState;
      UserProfileState? profileState;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: LoginCubitProvider(
            child: Builder(
              builder: (BuildContext context) {
                loginState = context.read<PreviousLogInCubit>().state;
                biometricState = context.read<BiometricCubit>().state;
                mpinState = context.read<ValidateMPINCubit>().state;
                profileState = context.read<UserProfileCubit>().state;
                return const SizedBox();
              },
            ),
          ),
        ),
      );

      // Assert
      expect(loginState, isA<PreviousLogInInitial>());
      expect(biometricState, isA<BiometricInitial>());
      expect(mpinState, isA<ValidateMPINInitial>());
      expect(profileState, isA<UserProfileInitial>());
    });

    testWidgets('should reuse existing cubits when they exist in context',
        (WidgetTester tester) async {
      // Arrange
      final PreviousLogInCubit existingLoginCubit = PreviousLogInCubit(
        localStorageHelper: mockLocalStorageHelper,
        authRepo: mockAuthenticationRepo,
        jwtHelper: mockJwtHelper,
      );
      final BiometricCubit existingBiometricCubit = BiometricCubit(
        loginOldDeviceUtils: mockLoginOldDeviceUtils,
        bioAuth: mockBiometricsAuthenticate,
      );
      final ValidateMPINCubit existingValidateMPINCubit = ValidateMPINCubit();
      final UserProfileCubit existingUserProfileCubit = UserProfileCubit(
        userRepo: mockUserRepo,
        appState: mockAppState,
        localStorageHelper: mockLocalStorageHelper,
      );

      PreviousLogInCubit? retrievedLoginCubit;
      BiometricCubit? retrievedBiometricCubit;
      ValidateMPINCubit? retrievedValidateMPINCubit;
      UserProfileCubit? retrievedUserProfileCubit;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: MultiBlocProvider(
            providers: <SingleChildWidget>[
              BlocProvider<PreviousLogInCubit>.value(value: existingLoginCubit),
              BlocProvider<BiometricCubit>.value(value: existingBiometricCubit),
              BlocProvider<ValidateMPINCubit>.value(value: existingValidateMPINCubit),
              BlocProvider<UserProfileCubit>.value(value: existingUserProfileCubit),
            ],
            child: LoginCubitProvider(
              child: Builder(
                builder: (BuildContext context) {
                  retrievedLoginCubit = context.read<PreviousLogInCubit>();
                  retrievedBiometricCubit = context.read<BiometricCubit>();
                  retrievedValidateMPINCubit = context.read<ValidateMPINCubit>();
                  retrievedUserProfileCubit = context.read<UserProfileCubit>();
                  return const SizedBox();
                },
              ),
            ),
          ),
        ),
      );

      // Assert - Verify existing instances are reused
      expect(retrievedLoginCubit, same(existingLoginCubit));
      expect(retrievedBiometricCubit, same(existingBiometricCubit));
      expect(retrievedValidateMPINCubit, same(existingValidateMPINCubit));
      expect(retrievedUserProfileCubit, same(existingUserProfileCubit));
    });

    testWidgets('should maintain independent state for each cubit', (WidgetTester tester) async {
      // Arrange
      late PreviousLogInCubit loginCubit;
      late ValidateMPINCubit validateMPINCubit;
      late UserProfileCubit userProfileCubit;

      await tester.pumpWidget(
        MaterialApp(
          home: LoginCubitProvider(
            child: Builder(
              builder: (BuildContext context) {
                loginCubit = context.read<PreviousLogInCubit>();
                validateMPINCubit = context.read<ValidateMPINCubit>();
                userProfileCubit = context.read<UserProfileCubit>();
                return const SizedBox();
              },
            ),
          ),
        ),
      );

      // Verify initial states
      expect(loginCubit.state, isA<PreviousLogInInitial>());
      expect(validateMPINCubit.state, isA<ValidateMPINInitial>());
      expect(userProfileCubit.state, isA<UserProfileInitial>());

      // Act - Change state of ValidateMPINCubit to valid
      // Wait for state change. Duration is more than 10ms to ensure state change is detected.
      // because common_cubit delay 10ms to emit next state
      validateMPINCubit.updateValidState();
      await tester.pump(const Duration(milliseconds: 100));

      // Assert - Only ValidateMPINCubit state should change, others remain unchanged
      expect(loginCubit.state, isA<PreviousLogInInitial>());
      expect(validateMPINCubit.state, isA<ValidateMPINValid>());
      expect(userProfileCubit.state, isA<UserProfileInitial>());

      // Act - Change state of ValidateMPINCubit to failure
      validateMPINCubit.setMPINFailure('Invalid PIN');
      // Wait for state change. Duration is more than 10ms to ensure state change is detected.
      // because common_cubit delay 10ms to emit next state
      await tester.pump(const Duration(milliseconds: 100));

      // Assert - Only ValidateMPINCubit state should change to failure, others remain unchanged
      expect(loginCubit.state, isA<PreviousLogInInitial>());
      expect(validateMPINCubit.state, isA<ValidateMPINFailureState>());
      expect(userProfileCubit.state, isA<UserProfileInitial>());

      // Verify the error message in the failure state
      final ValidateMPINFailureState failureState =
          validateMPINCubit.state as ValidateMPINFailureState;
      expect(failureState.errorMessage, equals('Invalid PIN'));

      // Wait for state change. Duration is more than 10ms to ensure state change is detected.
      // because common_cubit delay 10ms to emit next state
      validateMPINCubit.onTextChange(null);
      await tester.pump(const Duration(milliseconds: 200));

      // Assert - ValidateMPINCubit should be back to initial, others remain unchanged
      expect(loginCubit.state, isA<PreviousLogInInitial>());
      expect(validateMPINCubit.state, isA<ValidateMPINInitial>());
      expect(userProfileCubit.state, isA<UserProfileInitial>());
    });

    testWidgets('should handle multiple nested LoginCubitProvider instances',
        (WidgetTester tester) async {
      // Arrange
      PreviousLogInCubit? outerLoginCubit;
      PreviousLogInCubit? innerLoginCubit;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: LoginCubitProvider(
            child: Builder(
              builder: (BuildContext outerContext) {
                outerLoginCubit = outerContext.read<PreviousLogInCubit>();
                return LoginCubitProvider(
                  child: Builder(
                    builder: (BuildContext innerContext) {
                      innerLoginCubit = innerContext.read<PreviousLogInCubit>();
                      return const SizedBox();
                    },
                  ),
                );
              },
            ),
          ),
        ),
      );

      // Assert - Inner provider should reuse outer cubit
      expect(outerLoginCubit, isNotNull);
      expect(innerLoginCubit, isNotNull);
      expect(innerLoginCubit, same(outerLoginCubit));
    });
  });
}
