import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/login/old_device/biometric/biometric_cubit.dart';
import 'package:evoapp/feature/login/old_device/components/login_biometric_button.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockBiometricCubit extends MockCubit<BiometricState> implements BiometricCubit {}

void main() {
  group('LoginBiometricButton Tests', () {
    late MockBiometricCubit mockBiometricCubit;
    bool onPressedCalled = false;

    setUpAll(() {
      // Register required dependencies for GetIt
      getItRegisterButtonStyle();
      getItRegisterTextStyle();
      getItRegisterColor();
    });

    tearDownAll(() {
      // Clean up registered dependencies
      getItUnRegisterButtonStyle();
      getItUnRegisterTextStyle();
      getItUnregisterColor();
    });

    setUp(() {
      mockBiometricCubit = MockBiometricCubit();
      onPressedCalled = false;
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider<BiometricCubit>(
            create: (_) => mockBiometricCubit,
            child: LoginBiometricButton(
              onPressed: () {
                onPressedCalled = true;
              },
            ),
          ),
        ),
      );
    }

    group('Visibility Tests', () {
      testWidgets('should be visible when BiometricAuthenticationAvailable', (WidgetTester tester) async {
        // Arrange
        when(() => mockBiometricCubit.state).thenReturn(BiometricAuthenticationAvailable());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(CommonButton), findsOneWidget);
        expect(find.text(EvoStrings.loginScreenLoginWithBiometric), findsOneWidget);
      });

      testWidgets('should be visible when BiometricAuthUserDismiss', (WidgetTester tester) async {
        // Arrange
        when(() => mockBiometricCubit.state).thenReturn(BiometricAuthUserDismiss());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(CommonButton), findsOneWidget);
      });

      testWidgets('should be visible when BiometricAuthSuccess', (WidgetTester tester) async {
        // Arrange
        when(() => mockBiometricCubit.state).thenReturn(BiometricAuthSuccess());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(CommonButton), findsOneWidget);
      });

      testWidgets('should be hidden when BiometricAuthenticationUnavailable', (WidgetTester tester) async {
        // Arrange
        when(() => mockBiometricCubit.state).thenReturn(BiometricAuthenticationUnavailable());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(CommonButton), findsNothing);
        expect(find.byType(SizedBox), findsOneWidget);
      });

      testWidgets('should be hidden when BiometricInitial', (WidgetTester tester) async {
        // Arrange
        when(() => mockBiometricCubit.state).thenReturn(BiometricInitial());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(CommonButton), findsNothing);
        expect(find.byType(SizedBox), findsOneWidget);
      });

      testWidgets('should be hidden when BiometricAuthFail', (WidgetTester tester) async {
        // Arrange
        when(() => mockBiometricCubit.state).thenReturn(BiometricAuthFail());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(CommonButton), findsNothing);
      });
    });

    group('Interaction Tests', () {
      testWidgets('should call onPressed when button is tapped', (WidgetTester tester) async {
        // Arrange
        when(() => mockBiometricCubit.state).thenReturn(BiometricAuthenticationAvailable());

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.tap(find.byType(CommonButton));

        // Assert
        expect(onPressedCalled, isTrue);
      });

      testWidgets('should not call onPressed when button is hidden', (WidgetTester tester) async {
        // Arrange
        when(() => mockBiometricCubit.state).thenReturn(BiometricAuthenticationUnavailable());

        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Try to tap (should not find button)
        expect(find.byType(CommonButton), findsNothing);
        expect(onPressedCalled, isFalse);
      });
    });



    group('Button Properties Tests', () {
      testWidgets('should have correct button properties when visible', (WidgetTester tester) async {
        // Arrange
        when(() => mockBiometricCubit.state).thenReturn(BiometricAuthenticationAvailable());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
        expect(button.isWrapContent, isFalse);
        expect(button.child, isA<Text>());
        
        final Text text = button.child as Text;
        expect(text.data, equals(EvoStrings.loginScreenLoginWithBiometric));
      });

      testWidgets('should use tertiary button style', (WidgetTester tester) async {
        // Arrange
        when(() => mockBiometricCubit.state).thenReturn(BiometricAuthenticationAvailable());

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
        expect(button.isWrapContent, false);
        expect(button.style, isNotNull);
      });
    });

    group('Internal State Logic Tests', () {
      testWidgets('should correctly identify available states', (WidgetTester tester) async {
        // Test the internal _isBiometricOptionAvailable logic through widget behavior
        
        final List<BiometricState> availableStates = <BiometricState>[
          BiometricAuthenticationAvailable(),
          BiometricAuthUserDismiss(),
          BiometricAuthSuccess(),
        ];

        for (final BiometricState state in availableStates) {
          when(() => mockBiometricCubit.state).thenReturn(state);
          await tester.pumpWidget(createTestWidget());
          expect(find.byType(CommonButton), findsOneWidget, 
                 reason: 'Button should be visible for state: ${state.runtimeType}');
        }
      });

      testWidgets('should correctly identify unavailable states', (WidgetTester tester) async {
        final List<BiometricState> unavailableStates = <BiometricState>[
          BiometricInitial(),
          BiometricAuthenticationUnavailable(),
          BiometricAuthFail(),
        ];

        for (final BiometricState state in unavailableStates) {
          when(() => mockBiometricCubit.state).thenReturn(state);
          await tester.pumpWidget(createTestWidget());
          expect(find.byType(CommonButton), findsNothing, 
                 reason: 'Button should be hidden for state: ${state.runtimeType}');
        }
      });
    });
  });
}
