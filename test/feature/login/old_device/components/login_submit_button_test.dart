import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/login/old_device/components/login_submit_button.dart';
import 'package:evoapp/feature/login/old_device/pincode/validate_mpin_cubit.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockValidateMPINCubit extends MockCubit<ValidateMPINState> implements ValidateMPINCubit {}

GetIt getIt = GetIt.instance;

void main() {
  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    // Register required dependencies using helper functions
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('LoginSubmitButton Tests', () {
    late MockValidateMPINCubit mockValidateMPINCubit;
    bool onPressedCalled = false;

    setUp(() {
      mockValidateMPINCubit = MockValidateMPINCubit();
      onPressedCalled = false;
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider<ValidateMPINCubit>(
            create: (_) => mockValidateMPINCubit,
            child: LoginSubmitButton(
              onPressed: () {
                onPressedCalled = true;
              },
            ),
          ),
        ),
      );
    }

    testWidgets('should render CommonButton', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINInitial());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CommonButton), findsOneWidget);
      expect(find.text(EvoStrings.login), findsOneWidget);
    });

    testWidgets('should be disabled when MPIN is not valid', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINInitial());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.onPressed, isNull);
    });

    testWidgets('should be disabled when MPIN validation fails', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINFailureState('Error'));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.onPressed, isNull);
    });

    testWidgets('should be enabled when MPIN is valid', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINValid());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.onPressed, isNotNull);
    });

    testWidgets('should call onPressed when button is tapped and enabled',
        (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINValid());

      // Act
      await tester.pumpWidget(createTestWidget());
      expect(onPressedCalled, isFalse);
      await tester.tap(find.byType(CommonButton));

      // Assert
      expect(onPressedCalled, isTrue);
    });

    testWidgets('should not call onPressed when button is disabled', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINInitial());

      // Act
      await tester.pumpWidget(createTestWidget());
      expect(onPressedCalled, isFalse);

      await tester.tap(find.byType(CommonButton));

      // Assert
      expect(onPressedCalled, isFalse);
    });

    testWidgets('should update button state when validation state changes',
        (WidgetTester tester) async {
      // Arrange
      whenListen(
        mockValidateMPINCubit,
        Stream<ValidateMPINState>.fromIterable(<ValidateMPINState>[
          ValidateMPINInitial(),
          ValidateMPINValid(),
        ]),
        initialState: ValidateMPINInitial(),
      );

      // Act
      await tester.pumpWidget(createTestWidget());

      // Initially disabled
      CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.onPressed, isNull);

      // Wait for state change
      await tester.pump();

      // Now enabled
      button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.onPressed, isNotNull);
    });

    testWidgets('should have correct button properties', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINValid());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.isWrapContent, isFalse);
      expect(button.child, isA<Text>());

      final Text text = button.child as Text;
      expect(text.data, equals(EvoStrings.login));
    });

    testWidgets('should handle rapid state changes', (WidgetTester tester) async {
      // Arrange
      whenListen(
        mockValidateMPINCubit,
        Stream<ValidateMPINState>.fromIterable(<ValidateMPINState>[
          ValidateMPINInitial(),
          ValidateMPINValid(),
          ValidateMPINFailureState('Error'),
          ValidateMPINValid(),
        ]),
        initialState: ValidateMPINInitial(),
      );

      // Act
      await tester.pumpWidget(createTestWidget());

      // Wait for all state changes
      await tester.pumpAndSettle();

      // Assert final state
      final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.onPressed, isNotNull);
    });
  });
}
