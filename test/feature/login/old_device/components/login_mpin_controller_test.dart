import 'package:evoapp/feature/login/old_device/components/login_mpin_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('LoginMPINController Tests', () {
    late LoginMPINController controller;
    late TextEditingController textController;
    late FocusNode focusNode;
    bool controllersDisposed = false;

    setUp(() {
      controller = LoginMPINController();
      textController = TextEditingController();
      focusNode = FocusNode();
      controllersDisposed = false;
    });

    tearDown(() {
      // Only dispose if they haven't been disposed by controller.detach()
      if (!controllersDisposed) {
        textController.dispose();
        focusNode.dispose();
      }
    });

    group('Initial State Tests', () {
      test('should return empty string for text when no controllers attached', () {
        // Act & Assert
        expect(controller.text, equals(''));
      });

      test('should not throw when requesting focus with no controllers attached', () {
        // Act & Assert
        expect(() => controller.requestFocus(), returnsNormally);
      });

      test('should not throw when clearing with no controllers attached', () {
        // Act & Assert
        expect(() => controller.clear(), returnsNormally);
      });
    });

    group('Attach/Detach Tests', () {
      test('should attach controllers correctly', () {
        // Arrange
        textController.text = '1234';

        // Act
        controller.attach(
          focusNode: focusNode,
          textEditingController: textController,
        );

        // Assert
        expect(controller.text, equals('1234'));
      });

      test('should handle multiple attach calls', () {
        // Arrange
        final TextEditingController secondTextController = TextEditingController(text: 'second');
        final FocusNode secondFocusNode = FocusNode();

        textController.text = 'first';

        // Act
        controller.attach(
          focusNode: focusNode,
          textEditingController: textController,
        );
        expect(controller.text, equals('first'));

        controller.attach(
          focusNode: secondFocusNode,
          textEditingController: secondTextController,
        );

        // Assert
        expect(controller.text, equals('second'));

        // Cleanup
        secondTextController.dispose();
        secondFocusNode.dispose();
      });

      test('should detach controllers and dispose resources', () {
        // Arrange
        controller.attach(
          focusNode: focusNode,
          textEditingController: textController,
        );
        textController.text = '1234';
        expect(controller.text, equals('1234'));

        // Act
        controller.detach();
        controllersDisposed = true; // Mark as disposed to avoid double disposal

        // Assert
        expect(controller.text, equals(''));
        expect(() => controller.requestFocus(), returnsNormally);
        expect(() => controller.clear(), returnsNormally);
      });
    });

    group('Text Access Tests', () {
      test('should return current text from attached controller', () {
        // Arrange
        textController.text = '5678';

        // Act
        controller.attach(
          focusNode: focusNode,
          textEditingController: textController,
        );

        // Assert
        expect(controller.text, equals('5678'));
      });

      test('should return empty string when text controller has empty text', () {
        // Arrange
        textController.text = '';

        // Act
        controller.attach(
          focusNode: focusNode,
          textEditingController: textController,
        );

        // Assert
        expect(controller.text, equals(''));
      });

      test('should handle dynamic text changes', () {
        // Arrange
        controller.attach(
          focusNode: focusNode,
          textEditingController: textController,
        );

        // Act & Assert - Simulate text changes
        textController.text = '1';
        expect(controller.text, equals('1'));

        textController.text = '12';
        expect(controller.text, equals('12'));

        textController.text = '123';
        expect(controller.text, equals('123'));

        textController.text = '1234';
        expect(controller.text, equals('1234'));
      });
    });

    group('Focus Management Tests', () {
      test('should request focus on attached focus node', () {
        // Arrange
        controller.attach(
          focusNode: focusNode,
          textEditingController: textController,
        );

        // Act
        controller.requestFocus();

        // Assert - Should not throw (focus behavior in tests is different)
        expect(() => controller.requestFocus(), returnsNormally);
      });

      test('should handle requestFocus gracefully when no controllers attached', () {
        // Act & Assert - Should not throw
        expect(() => controller.requestFocus(), returnsNormally);
      });
    });

    group('Clear Functionality Tests', () {
      test('should clear text on attached text controller', () {
        // Arrange
        textController.text = '1234';
        controller.attach(
          focusNode: focusNode,
          textEditingController: textController,
        );
        expect(controller.text, equals('1234'));

        // Act
        controller.clear();

        // Assert
        expect(textController.text, equals(''));
        expect(controller.text, equals(''));
      });

      test('should handle clear gracefully when no controllers attached', () {
        // Act & Assert - Should not throw
        expect(() => controller.clear(), returnsNormally);
      });
    });

    group('Multiple Operations Tests', () {
      test('should handle multiple operations in sequence', () {
        // Arrange
        textController.text = 'test';
        controller.attach(
          focusNode: focusNode,
          textEditingController: textController,
        );

        // Act
        controller.requestFocus();
        expect(controller.text, equals('test'));

        controller.clear();
        expect(controller.text, equals(''));

        // Simulate new text entry
        textController.text = 'new';
        final String result = controller.text;

        // Assert
        expect(result, equals('new'));
      });
    });

    group('Edge Cases Tests', () {
      test('should handle null text gracefully', () {
        // Arrange
        final TextEditingController nullTextController = TextEditingController();
        controller.attach(
          focusNode: focusNode,
          textEditingController: nullTextController,
        );

        // Act & Assert
        expect(controller.text, equals(''));

        // Cleanup
        nullTextController.dispose();
      });

      test('should handle operations after detach', () {
        // Arrange
        controller.attach(
          focusNode: focusNode,
          textEditingController: textController,
        );
        textController.text = '1234';
        expect(controller.text, equals('1234'));

        // Act
        controller.detach();
        controllersDisposed = true; // Mark as disposed to avoid double disposal

        // Assert - All operations should work gracefully
        expect(controller.text, equals(''));
        expect(() => controller.requestFocus(), returnsNormally);
        expect(() => controller.clear(), returnsNormally);
      });
    });
  });
}
