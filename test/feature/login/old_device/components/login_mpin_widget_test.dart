import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/login/old_device/components/login_mpin_widget.dart';
import 'package:evoapp/feature/login/old_device/pincode/validate_mpin_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/widget/evo_mpin_code/evo_mpin_code_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockValidateMPINCubit extends MockCubit<ValidateMPINState> implements ValidateMPINCubit {}

void main() {
  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    // Register required dependencies for GetIt
    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();
  });

  tearDownAll(() {
    // Clean up registered dependencies
    getItUnRegisterButtonStyle();
    getItUnRegisterTextStyle();
    getItUnregisterColor();
    getIt.reset();
  });


  group('LoginMPINWidget Tests', () {
    late MockValidateMPINCubit mockValidateMPINCubit;
    bool onSubmitCalled = false;
    bool onChangeCalled = false;
    String? submittedValue;
    String? changedValue;

    setUp(() {
      mockValidateMPINCubit = MockValidateMPINCubit();
      onSubmitCalled = false;
      onChangeCalled = false;
      submittedValue = null;
      changedValue = null;
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider<ValidateMPINCubit>(
            create: (_) => mockValidateMPINCubit,
            child: LoginMPINWidget(
              onSubmit: (String? value) {
                onSubmitCalled = true;
                submittedValue = value;
              },
              onChange: (String? value) {
                onChangeCalled = true;
                changedValue = value;
              },
            ),
          ),
        ),
      );
    }

    testWidgets('should render EvoMPINCodeWidget', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINInitial());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(EvoMPINCodeWidget), findsOneWidget);
    });

    testWidgets('should display error message when validation fails', (WidgetTester tester) async {
      // Arrange
      const String errorMessage = 'Invalid MPIN';
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINFailureState(errorMessage));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final EvoMPINCodeWidget mpinWidget = tester.widget<EvoMPINCodeWidget>(find.byType(EvoMPINCodeWidget));
      expect(mpinWidget.errorMessage, equals(errorMessage));
    });

    testWidgets('should not display error message when validation is successful', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINValid());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final EvoMPINCodeWidget mpinWidget = tester.widget<EvoMPINCodeWidget>(find.byType(EvoMPINCodeWidget));
      expect(mpinWidget.errorMessage, isNull);
    });

    testWidgets('should call onSubmit when MPIN is submitted', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINInitial());

      // Act
      await tester.pumpWidget(createTestWidget());
      
      final EvoMPINCodeWidget mpinWidget = tester.widget<EvoMPINCodeWidget>(find.byType(EvoMPINCodeWidget));
      mpinWidget.onSubmit?.call('1234');

      // Assert
      expect(onSubmitCalled, isTrue);
      expect(submittedValue, equals('1234'));
    });

    testWidgets('should call onChange when MPIN text changes', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINInitial());

      // Act
      await tester.pumpWidget(createTestWidget());
      
      final EvoMPINCodeWidget mpinWidget = tester.widget<EvoMPINCodeWidget>(find.byType(EvoMPINCodeWidget));
      mpinWidget.onChange?.call('12');

      // Assert
      expect(onChangeCalled, isTrue);
      expect(changedValue, equals('12'));
    });



    testWidgets('should use correct title', (WidgetTester tester) async {
      // Arrange
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINInitial());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final EvoMPINCodeWidget mpinWidget = tester.widget<EvoMPINCodeWidget>(find.byType(EvoMPINCodeWidget));
      expect(mpinWidget.title, equals(EvoStrings.verifyMPinDesc));
    });

    testWidgets('should handle state changes correctly', (WidgetTester tester) async {
      // Arrange - Test with failure state directly
      when(() => mockValidateMPINCubit.state).thenReturn(ValidateMPINFailureState('Error'));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert - Error state
      final EvoMPINCodeWidget mpinWidget = tester.widget<EvoMPINCodeWidget>(find.byType(EvoMPINCodeWidget));
      expect(mpinWidget.errorMessage, equals('Error'));
    });
  });

}
