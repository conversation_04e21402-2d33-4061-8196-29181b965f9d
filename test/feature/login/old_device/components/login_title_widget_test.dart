import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/login/old_device/components/login_title_widget.dart';
import 'package:evoapp/feature/profile/cubit/user_profile_cubit.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockUserProfileCubit extends MockCubit<UserProfileState> implements UserProfileCubit {}

GetIt getIt = GetIt.instance;

void main() {
  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    // Register required dependencies for LoginTitleWidget
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('LoginTitleWidget Tests', () {
    late MockUserProfileCubit mockUserProfileCubit;

    setUp(() {
      mockUserProfileCubit = MockUserProfileCubit();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider<UserProfileCubit>(
            create: (_) => mockUserProfileCubit,
            child: const LoginTitleWidget(),
          ),
        ),
      );
    }

    testWidgets('should display nothing when username is null', (WidgetTester tester) async {
      // Arrange
      when(() => mockUserProfileCubit.state).thenReturn(UserProfileInitial());

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(SizedBox), findsOneWidget);
      expect(find.text(EvoStrings.verifyMPinTitle), findsNothing);
    });

    testWidgets('should display title with username when GetUsernameSuccess state', (WidgetTester tester) async {
      // Arrange
      const String testUsername = 'John Doe';
      when(() => mockUserProfileCubit.state).thenReturn(GetUsernameSuccess(username: testUsername));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final String expectedTitle = EvoStrings.verifyMPinTitle.replaceVariableByValue(<String>[testUsername]);
      expect(find.text(expectedTitle), findsOneWidget);
      expect(find.byType(Text), findsOneWidget);
    });

    testWidgets('should update title when username state changes', (WidgetTester tester) async {
      // Arrange
      const String testUsername = 'Jane Smith';
      whenListen(
        mockUserProfileCubit,
        Stream<UserProfileState>.fromIterable(<UserProfileState>[
          UserProfileInitial(),
          GetUsernameSuccess(username: testUsername),
        ]),
        initialState: UserProfileInitial(),
      );

      // Act
      await tester.pumpWidget(createTestWidget());

      // Initially no title
      expect(find.byType(SizedBox), findsOneWidget);

      // Wait for state change
      await tester.pump();

      // Assert
      final String expectedTitle = EvoStrings.verifyMPinTitle.replaceVariableByValue(<String>[testUsername]);
      expect(find.text(expectedTitle), findsOneWidget);
    });

    testWidgets('should handle empty username gracefully', (WidgetTester tester) async {
      // Arrange
      when(() => mockUserProfileCubit.state).thenReturn(GetUsernameSuccess(username: ''));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final String expectedTitle = EvoStrings.verifyMPinTitle.replaceVariableByValue(<String>['']);
      expect(find.text(expectedTitle), findsOneWidget);
    });

    testWidgets('should use correct text style', (WidgetTester tester) async {
      // Arrange
      const String testUsername = 'Test User';
      when(() => mockUserProfileCubit.state).thenReturn(GetUsernameSuccess(username: testUsername));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final Text textWidget = tester.widget<Text>(find.byType(Text));
      expect(textWidget.style, isNotNull);
      expect(textWidget.style?.fontSize, TextSize.h3.fontSize);
    });
  });
}
