import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/login/old_device/biometric/biometric_cubit.dart';
import 'package:evoapp/feature/login/utils/login_old_device_utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';

class MockBiometricTypeUIModel extends Mock implements BiometricTypeUIModel {}

class MockLoginOldDeviceUtils extends Mock implements LoginOldDeviceUtils {}

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

void main() {
  final LoginOldDeviceUtils mockLoginOldDeviceUtils = MockLoginOldDeviceUtils();
  final BiometricsAuthenticate mockBiometricsAuthenticate = MockBiometricsAuthenticate();
  const String localizedReason = 'localizedReason';
  late BiometricCubit cubit;

  setUp(() {
    cubit = BiometricCubit(
      loginOldDeviceUtils: mockLoginOldDeviceUtils,
      bioAuth: mockBiometricsAuthenticate,
    );
    when(() => mockBiometricsAuthenticate.authenticate(
        localizedReason: any(named: 'localizedReason'),
        cancelButton: any(named: 'cancelButton'))).thenAnswer((_) async {
      return BioAuthResult.success();
    });
    when(
      () => mockLoginOldDeviceUtils.checkCanLoginByBiometric(),
    ).thenAnswer((_) async => true);
  });

  tearDown(() {
    reset(mockBiometricsAuthenticate);
  });

  test('Initial state', () {
    expect(cubit.state, isA<BiometricInitial>());
  });

  group('test initialize function', () {
    blocTest<BiometricCubit, BiometricState>(
      'should emit BiometricAuthenticationUnavailable if Biometric Authentication is NOT available',
      setUp: () {
        when(
          () => mockLoginOldDeviceUtils.checkCanLoginByBiometric(),
        ).thenAnswer((_) async => false);
      },
      build: () => cubit,
      act: (BiometricCubit cubit) => cubit.initialize(),
      expect: () => <dynamic>[
        isA<BiometricAuthenticationUnavailable>(),
      ],
    );

    blocTest<BiometricCubit, BiometricState>(
      'should emit BiometricAuthenticationAvailable if Biometric Authentication is available',
      setUp: () {
        when(
          () => mockLoginOldDeviceUtils.checkCanLoginByBiometric(),
        ).thenAnswer((_) async => true);
      },
      build: () => cubit,
      act: (BiometricCubit cubit) => cubit.initialize(),
      expect: () => <dynamic>[
        isA<BiometricAuthenticationAvailable>(),
      ],
    );
  });

  group('test authenticate function', () {
    blocTest<BiometricCubit, BiometricState>(
      'should emit BiometricAuthSuccess if authenticate success',
      setUp: () {
        when(() => mockBiometricsAuthenticate.authenticate(localizedReason: localizedReason))
            .thenAnswer((_) async {
          return BioAuthResult.success();
        });
      },
      build: () => cubit,
      act: (BiometricCubit cubit) => cubit.authenticate(),
      expect: () => <dynamic>[
        isA<BiometricAuthSuccess>(),
      ],
    );

    blocTest<BiometricCubit, BiometricState>(
      'should emit BiometricAuthFail if authenticate fail and Biometric is locked',
      setUp: () {
        when(() => mockBiometricsAuthenticate.authenticate(
            localizedReason: any(named: 'localizedReason'),
            cancelButton: any(named: 'cancelButton'))).thenAnswer((_) async {
          return BioAuthResult.error(BioAuthError.androidLockedOut);
        });
      },
      wait: TestConstant.blocEmitStateDelayDuration,
      build: () => cubit,
      act: (_) => cubit.authenticate(),
      expect: () => <dynamic>[
        isA<BiometricAuthFail>().having(
          (BiometricAuthFail state) => state.bioAuthError,
          'verify bioAuthError',
          BioAuthError.androidLockedOut,
        )
      ],
    );

    /// for this case, we simulate the user ignore Biometric Authentication by click MPIN button
    /// the expectation state is BiometricAuthUserDismiss
    blocTest<BiometricCubit, BiometricState>(
      'should emit BiometricAuthUserDismiss if user ignore Biometric Authentication',
      setUp: () {
        when(() => mockBiometricsAuthenticate.authenticate(
            localizedReason: any(named: 'localizedReason'),
            cancelButton: any(named: 'cancelButton'))).thenAnswer((_) async {
          return BioAuthResult.error(BioAuthError.userDismiss);
        });
      },
      build: () => cubit,
      act: (BiometricCubit cubit) => cubit.authenticate(),
      expect: () => <dynamic>[isA<BiometricAuthUserDismiss>()],
    );

    blocTest<BiometricCubit, BiometricState>(
      'should emit BiometricAuthenticationUnavailable if function execute with '
          'shouldEmitUnavailableState = TRUE',
      setUp: () {
        when(() => mockBiometricsAuthenticate.authenticate(
            localizedReason: any(named: 'localizedReason'),
            cancelButton: any(named: 'cancelButton'))).thenAnswer((_) async {
          return BioAuthResult.error(BioAuthError.permanentlyLockedOut);
        });
      },
      build: () => cubit,
      act: (BiometricCubit cubit) => cubit.authenticate(shouldEmitUnavailableState: true),
      expect: () => <dynamic>[isA<BiometricAuthenticationUnavailable>()],
    );
  });

  group('test isBiometricsIsLocked function', () {
    test('returns true for androidLockedOut', () {
      expect(cubit.isBiometricsIsLocked(BioAuthError.androidLockedOut), true);
    });

    test('returns true for notEnrolled', () {
      expect(cubit.isBiometricsIsLocked(BioAuthError.notEnrolled), true);
    });

    test('returns true for permanentlyLockedOut', () {
      expect(cubit.isBiometricsIsLocked(BioAuthError.permanentlyLockedOut), true);
    });

    test('returns false for userDismiss', () {
      expect(cubit.isBiometricsIsLocked(BioAuthError.userDismiss), false);
    });

    test('returns false for null', () {
      expect(cubit.isBiometricsIsLocked(null), false);
    });
  });
}
