import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/login/old_device/biometric/biometric_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('BiometricInitial is a BiometricState', () {
    final BiometricInitial state = BiometricInitial();
    expect(state, isA<BiometricState>());
  });

  test('BiometricAuthenticateSuccess is a BiometricState', () {
    final BiometricAuthSuccess state = BiometricAuthSuccess();
    expect(state, isA<BiometricState>());
  });

  test('BiometricAuthenticateFail is a BiometricState', () {
    final BiometricAuthFail defaultState = BiometricAuthFail();
    expect(defaultState, isA<BiometricState>());

    const BioAuthError error = BioAuthError.unknown;
    final BiometricAuthFail customValueState = BiometricAuthFail(bioAuthError: error);
    expect(customValueState, isA<BiometricState>());
    expect(customValueState.bioAuthError, error);
  });

  test('BiometricAuthenticationAvailable is a BiometricState', () {
    final BiometricAuthenticationAvailable state = BiometricAuthenticationAvailable();
    expect(state, isA<BiometricState>());
  });

  test('BiometricUnavailable is a BiometricState', () {
    final BiometricAuthenticationUnavailable state = BiometricAuthenticationUnavailable();
    expect(state, isA<BiometricState>());
  });

  test('BiometricAuthUserDismiss is a BiometricState', () {
    final BiometricAuthUserDismiss state = BiometricAuthUserDismiss();
    expect(state, isA<BiometricState>());
  });
}
