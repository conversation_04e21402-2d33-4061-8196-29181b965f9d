import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/login_previous_device_request.dart';
import 'package:evoapp/data/response/login_previous_device_entity.dart';
import 'package:evoapp/feature/login/old_device/previous_login_cubit.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockJwtHelper extends Mock implements JwtHelper {}

void main() {
  late PreviousLogInCubit cubit;

  final EvoLocalStorageHelper mockLocalStorageHelper = MockEvoLocalStorageHelper();
  final AuthenticationRepo mockAuthenticationRepo = MockAuthenticationRepo();
  final JwtHelper mockJwtHelper = MockJwtHelper();
  late EvoUtilFunction mockEvoUtilFunction;

  const String mockUsername = 'username';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerSingleton<EvoUtilFunction>(MockEvoUtilFunction());
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();

    // Register fallback values
    registerFallbackValue(TypeLogin.verifyPin);
    registerFallbackValue(LoginWithMPINRequest(pin: 'fake', deviceToken: 'fake'));
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    cubit = PreviousLogInCubit(
      localStorageHelper: mockLocalStorageHelper,
      authRepo: mockAuthenticationRepo,
      jwtHelper: mockJwtHelper,
    );

    when(() => mockLocalStorageHelper.getUsername()).thenAnswer((_) async {
      return mockUsername;
    });

    // Set up default mocks for successful login tests
    when(() => mockLocalStorageHelper.getDeviceToken())
        .thenAnswer((_) async => 'fake_device_token');
    when(() => mockJwtHelper.isCanUse(any())).thenReturn(true);

    when(() => mockEvoUtilFunction.clearAllUserData()).thenAnswer((_) async => Future<void>.value());
  });

  tearDown(() {
    reset(mockEvoUtilFunction);
  });

  test('Initial state', () {
    expect(cubit.state, isA<PreviousLogInInitial>());
  });

  group('Test loginByPinCode', () {
    const String fakePinCode = '123456';

    blocTest<PreviousLogInCubit, PreviousLogInState>(
      'Login success',
      setUp: () async {
        final LoginPreviousDeviceEntity successEntity =
            LoginPreviousDeviceEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'access_token': 'fake_access_token',
              'refresh_token': 'fake_refresh_token',
              'user_id': 123,
            },
          },
        ));

        when(() => mockAuthenticationRepo.loginPrevDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => successEntity);
      },
      build: () => cubit,
      act: (PreviousLogInCubit cubit) => cubit.loginWithMPIN(fakePinCode),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<PreviousLogInLoading>(),
        isA<PreviousLogInSuccess>(),
      ],
    );
  });

  group('Test loginByBiometric', () {
    const String fakeBiometricToken = 'fakeBiometricToken';

    setUpAll(() {
      when(() => mockLocalStorageHelper.getBiometricToken())
          .thenAnswer((_) => Future<String>.value(fakeBiometricToken));
    });

    blocTest<PreviousLogInCubit, PreviousLogInState>(
      'Login success',
      build: () => cubit,
      act: (PreviousLogInCubit cubit) => cubit.loginWithBiometricToken(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <dynamic>[
        isA<PreviousLogInLoading>(),
        isA<PreviousLogInSuccess>(),
      ],
    );
  });

  group('Test loginWithMPIN error scenarios', () {
    const String fakePinCode = '123456';
    const String fakeDeviceToken = 'fake_device_token';

    setUp(() {
      when(() => mockLocalStorageHelper.getDeviceToken())
          .thenAnswer((_) => Future<String>.value(fakeDeviceToken));
      when(() => mockJwtHelper.isCanUse(fakeDeviceToken)).thenReturn(true);
    });

    LoginPreviousDeviceEntity generateStubEntity({
      required int statusCode,
      String? userMessage,
      String? accessToken,
      String? refreshToken,
      int? userId,
    }) {
      final Map<String, dynamic> data = <String, dynamic>{};

      if (accessToken != null) data['access_token'] = accessToken;
      if (refreshToken != null) data['refresh_token'] = refreshToken;
      if (userId != null) data['user_id'] = userId;

      final Map<String, dynamic> response = <String, dynamic>{
        'data': data,
        'status_code': statusCode,
      };

      if (userMessage != null) {
        response['user_message'] = userMessage;
      }

      return LoginPreviousDeviceEntity.fromBaseResponse(BaseResponse(
        statusCode: statusCode,
        response: response,
      ));
    }

    blocTest<PreviousLogInCubit, PreviousLogInState>('Login with MPIN - Bad Request Error',
        setUp: () {
          final LoginPreviousDeviceEntity mockEntity = generateStubEntity(
            statusCode: CommonHttpClient.BAD_REQUEST,
            userMessage: 'Invalid PIN',
          );

          when(() => mockAuthenticationRepo.loginPrevDevice(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async => mockEntity);
        },
        build: () => cubit,
        act: (PreviousLogInCubit cubit) => cubit.loginWithMPIN(fakePinCode),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<PreviousLogInLoading>(),
              isA<PreviousLogInBadRequest>(),
            ],
        verify: (_) {
          // jwtHelper
          verifyNever(() => mockEvoUtilFunction.clearUserInfoAppState());
        });

    blocTest<PreviousLogInCubit, PreviousLogInState>('Login with MPIN - Session Expired Error',
        setUp: () {
          final LoginPreviousDeviceEntity mockEntity = generateStubEntity(
            statusCode: CommonHttpClient.INVALID_TOKEN,
            userMessage: 'Session expired',
          );

          when(() => mockAuthenticationRepo.loginPrevDevice(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async => mockEntity);
        },
        build: () => cubit,
        act: (PreviousLogInCubit cubit) => cubit.loginWithMPIN(fakePinCode),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<PreviousLogInLoading>(),
              isA<PreviousLogInSessionExpired>(),
            ],
        verify: (_) {
          // jwtHelper
          verify(() => mockEvoUtilFunction.clearAllUserData()).called(1);
        });

    blocTest<PreviousLogInCubit, PreviousLogInState>('Login with MPIN - Limit Exceeded Error',
        setUp: () {
          final LoginPreviousDeviceEntity mockEntity = generateStubEntity(
            statusCode: CommonHttpClient.LIMIT_EXCEEDED,
            userMessage: 'Too many attempts',
          );

          when(() => mockAuthenticationRepo.loginPrevDevice(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async => mockEntity);
        },
        build: () => cubit,
        act: (PreviousLogInCubit cubit) => cubit.loginWithMPIN(fakePinCode),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<PreviousLogInLoading>(),
              isA<PreviousLogInLimitedExceeded>(),
            ],
        verify: (_) {
          // jwtHelper
          verifyNever(() => mockEvoUtilFunction.clearUserInfoAppState());
        });

    blocTest<PreviousLogInCubit, PreviousLogInState>('Login with MPIN - Locked Resource Error',
        setUp: () {
          final LoginPreviousDeviceEntity mockEntity = generateStubEntity(
            statusCode: CommonHttpClient.LOCKED_RESOURCE,
            userMessage: 'Account locked',
          );

          when(() => mockAuthenticationRepo.loginPrevDevice(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async => mockEntity);
        },
        build: () => cubit,
        act: (PreviousLogInCubit cubit) => cubit.loginWithMPIN(fakePinCode),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<PreviousLogInLoading>(),
              isA<PreviousLogInLimitedExceeded>(),
            ],
        verify: (_) {
          // jwtHelper
          verifyNever(() => mockEvoUtilFunction.clearUserInfoAppState());
        });

    blocTest<PreviousLogInCubit, PreviousLogInState>('Login with MPIN - Generic Error',
        setUp: () {
          final LoginPreviousDeviceEntity mockEntity = generateStubEntity(
            statusCode: CommonHttpClient.UNKNOWN_ERRORS,
            userMessage: 'Server error',
          );

          when(() => mockAuthenticationRepo.loginPrevDevice(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async => mockEntity);
        },
        build: () => cubit,
        act: (PreviousLogInCubit cubit) => cubit.loginWithMPIN(fakePinCode),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<PreviousLogInLoading>(),
              isA<PreviousLogInFailure>(),
            ],
        verify: (_) {
          // jwtHelper
          verifyNever(() => mockEvoUtilFunction.clearUserInfoAppState());
        });
  });

  group('Test loginWithMPIN validation', () {
    blocTest<PreviousLogInCubit, PreviousLogInState>('Login with null MPIN',
        build: () => cubit,
        act: (PreviousLogInCubit cubit) => cubit.loginWithMPIN(null),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<PreviousLogInLoading>(),
              isA<PreviousLogInFailure>(),
            ],
        verify: (_) {
          // jwtHelper
          verifyNever(() => mockEvoUtilFunction.clearUserInfoAppState());
        });

    blocTest<PreviousLogInCubit, PreviousLogInState>('Login with empty MPIN',
        build: () => cubit,
        act: (PreviousLogInCubit cubit) => cubit.loginWithMPIN(''),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<PreviousLogInLoading>(),
              isA<PreviousLogInFailure>(),
            ],
        verify: (_) {
          // jwtHelper
          verifyNever(() => mockEvoUtilFunction.clearUserInfoAppState());
        });

    blocTest<PreviousLogInCubit, PreviousLogInState>('Login with invalid device token',
        setUp: () {
          when(() => mockLocalStorageHelper.getDeviceToken())
              .thenAnswer((_) => Future<String>.value('invalid_token'));
          when(() => mockJwtHelper.isCanUse('invalid_token')).thenReturn(false);
        },
        build: () => cubit,
        act: (PreviousLogInCubit cubit) => cubit.loginWithMPIN('123456'),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <dynamic>[
              isA<PreviousLogInLoading>(),
              isA<PreviousLogInSessionExpired>(),
            ],
        verify: (_) {
          // jwtHelper
          verifyNever(() => mockEvoUtilFunction.clearUserInfoAppState());
        });
  });
}
