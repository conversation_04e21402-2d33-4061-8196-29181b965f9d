import 'package:evoapp/feature/login/old_device/widgets/login_by_biometric_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/dimensions.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockCallback extends Mock {
  void call();
}

class MockCommonButtonStyles extends Mock implements EvoButtonStyles {}

void main() {
  const ButtonSize buttonSize = ButtonSize.medium;
  const ButtonStyle fakeButtonStyle = ButtonStyle();

  late MockCallback onClick;

  setUpAll(() {
    getIt.registerLazySingleton<CommonButtonStyles>(() => MockCommonButtonStyles());
    getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());

    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    registerFallbackValue(ButtonSize.small);
    when(() => evoButtonStyles.tertiary(any())).thenReturn(fakeButtonStyle);

    when(() => evoImageProvider.asset(
          EvoImages.icBiometrics,
          width: 24,
          height: 24,
        )).thenReturn(const SizedBox());
  });

  setUp(() {
    onClick = MockCallback();
  });

  void verifyIcon() {
    verify(() => evoImageProvider.asset(
          EvoImages.icBiometrics,
          width: 24,
          height: 24,
        )).called(1);
  }

  void verifyText() {
    expect(
      find.byWidgetPredicate((Widget widget) {
        return widget is Text &&
            widget.data == EvoStrings.loginScreenLoginWithBiometric &&
            widget.style == evoTextStyles.h200(color: evoColors.primary100) &&
            widget.overflow == TextOverflow.ellipsis;
      }),
      findsOneWidget,
    );
  }

  testWidgets('Show LoginByBiometricWidget without param', (WidgetTester tester) async {
    await tester.pumpWidget(const MaterialApp(
      home: Scaffold(
        body: LoginByBiometricWidget(),
      ),
    ));

    expect(
      find.byWidgetPredicate((Widget widget) {
        return widget is CommonButton &&
            widget.onPressed == null &&
            widget.style == fakeButtonStyle;
      }),
      findsOneWidget,
    );

    verifyIcon();

    verifyText();
  });

  testWidgets('Show LoginByBiometricWidget with param', (WidgetTester tester) async {
    final MockCallback onClick = MockCallback();
    await tester.pumpWidget(MaterialApp(
      home: Scaffold(
        body: LoginByBiometricWidget(
          onClick: onClick.call,
        ),
      ),
    ));

    final Finder ctaFinder = find.byWidgetPredicate((Widget widget) {
      return widget is CommonButton && widget.onPressed != null && widget.style == fakeButtonStyle;
    });
    expect(ctaFinder, findsOneWidget);
    final CommonButton cta = tester.widget<CommonButton>(ctaFinder);
    cta.onPressed?.call();
    verify(() => onClick.call()).called(1);
    
    verifyIcon();

    verifyText();
  });
}
