import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/feature/login/old_device/login_on_old_device_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('LoginOnOldDeviceInitial is a LoginOnOldDeviceState', () {
    final LoginOnOldDeviceInitial defaultState = LoginOnOldDeviceInitial();
    expect(defaultState, isA<LoginOnOldDeviceState>());

    final LoginOnOldDeviceInitial customState = LoginOnOldDeviceInitial();
    expect(customState, isA<LoginOnOldDeviceState>());
  });

  test('LoginOnOldDeviceLoading is a LoginOnOldDeviceState', () {
    final LoginOnOldDeviceLoading state = LoginOnOldDeviceLoading();

    expect(state, isA<LoginOnOldDeviceState>());
  });

  test('LoginOnOldDeviceError is a LoginOnOldDeviceState', () {
    final ErrorUIModel error = ErrorUIModel(statusCode: 400, userMessage: 'Error');
    const TypeLogin loginType = TypeLogin.otp;
    const String phoneNumber = '1234567890';
    final LoginOnOldDeviceError state = LoginOnOldDeviceError(error, loginType, phoneNumber);

    expect(state, isA<LoginOnOldDeviceState>());

    expect(state.error, error);
    expect(state.loginType, loginType);
    expect(state.phoneNumber, phoneNumber);
  });

  test('LoginOnOldDeviceSuccess is a LoginOnOldDeviceState', () {
    const TypeLogin type = TypeLogin.otp;
    const AuthChallengeType challengeType = AuthChallengeType.verifyPin;
    final LoginOnOldDeviceSuccess state = LoginOnOldDeviceSuccess(
      type: type,
      challengeType: challengeType,
    );

    expect(state, isA<LoginOnOldDeviceState>());
    expect(state.type, type);
    expect(state.challengeType, challengeType);
  });
}
