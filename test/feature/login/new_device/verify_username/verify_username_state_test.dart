import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/login/new_device/verify_username/verify_username_state.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('VerifyUsernameState', () {
    // Constants for reuse
    const String kTestUsername = 'testuser';
    const String kTestSessionToken = 'test-session-token';
    const String kTestChallengeType = 'verify_pin';

    LoginNewDeviceEntity createTestEntity({
      String sessionToken = kTestSessionToken,
      String challengeType = kTestChallengeType,
    }) {
      return LoginNewDeviceEntity(
        sessionToken: sessionToken,
        challengeType: challengeType,
      );
    }

    test('State classes should be instantiable', () {
      final VerifyUsernameState initialState = VerifyUsernameInitial();
      final VerifyUsernameState loadingState = VerifyUsernameLoading();

      expect(initialState, isA<VerifyUsernameState>());
      expect(loadingState, isA<VerifyUsernameState>());
    });

    test('VerifyUsernameSuccess should hold the provided parameters with verify_pin challenge', () {
      final LoginNewDeviceEntity entity = createTestEntity();

      final VerifyUsernameSuccess state = VerifyUsernameSuccess(
        username: kTestUsername,
        entity: entity,
      );

      expect(state, isA<VerifyUsernameState>());
      expect(state.username, equals(kTestUsername));
      expect(state.entity.sessionToken, equals(kTestSessionToken));
      expect(state.entity.challengeType, equals(kTestChallengeType));
    });

    test('VerifyUsernameError should be instantiable with ErrorUIModel', () {
      final ErrorUIModel errorModel = ErrorUIModel(
          statusCode: CommonHttpClient.BAD_REQUEST,
          userMessage: 'An error occurred',
          verdict: 'error');
      final VerifyUsernameError state = VerifyUsernameError(error: errorModel);

      expect(state, isA<VerifyUsernameState>());
      expect(state.error.userMessage, equals('An error occurred'));
      expect(state.error.verdict, equals('error'));
      expect(state.error.statusCode, equals(CommonHttpClient.BAD_REQUEST));
    });

    test('UsernameModifiedState should be instantiable and hold the provided username', () {
      const String username = 'testuser';
      final UsernameModifiedState state = UsernameModifiedState(username: username);

      expect(state, isA<VerifyUsernameState>());
      expect(state.username, equals(username));
    });

    test('InvalidUsernameErrorState should be instantiable and hold the provided error', () {
      const String error = 'error';
      final InvalidUsernameErrorState state = InvalidUsernameErrorState(errorMessage: error);

      expect(state, isA<InvalidUsernameErrorState>());
      expect(state.errorMessage, equals(error));
    });

    test('VerifyUsernameLockedState should be instantiable with ErrorUIModel', () {
      final ErrorUIModel errorModel = ErrorUIModel(
          statusCode: CommonHttpClient.LOCKED_RESOURCE,
          userMessage: 'Account is locked',
          verdict: 'locked');
      final VerifyUsernameLockedState state = VerifyUsernameLockedState(error: errorModel);

      expect(state, isA<VerifyUsernameState>());
      expect(state.error.userMessage, equals('Account is locked'));
      expect(state.error.verdict, equals('locked'));
      expect(state.error.statusCode, equals(CommonHttpClient.LOCKED_RESOURCE));
    });
  });
}
