import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/new_device_login_challenge_navigator.dart';
import 'package:evoapp/feature/login/new_device/verify_mpin/new_device_verify_mpin_screen.dart';
import 'package:evoapp/feature/login/new_device/verify_username/verify_username_cubit.dart';
import 'package:evoapp/feature/login/new_device/verify_username/verify_username_screen.dart';
import 'package:evoapp/feature/login/new_device/verify_username/verify_username_state.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/screen_util.dart';
import 'package:evoapp/widget/app_bar_wrapper.dart';
import 'package:evoapp/widget/appbar/evo_appbar_leading_button.dart';
import 'package:evoapp/widget/evo_text_field.dart';
import 'package:evoapp/widget/question_cta_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/app_mock_cubit.dart';

class MockVerifyUsernameCubit extends AppMockCubit<VerifyUsernameState>
    implements VerifyUsernameCubit {}

class MockNewDeviceLoginChallengeNavigator extends Mock
    implements NewDeviceLoginChallengeNavigator {}

void main() {
  late MockVerifyUsernameCubit cubit;
  late CommonUtilFunction commonUtilFunction;

  setUpAll(() {
    initConfigEvoPageStateBase();

    registerFallbackValue(NewDeviceVerifyMPinArg(
        onPopSuccess: (_) {}, sessionToken: 'fake_session', username: 'fake_username'));
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    cubit = MockVerifyUsernameCubit()..emit(VerifyUsernameInitial());
    reset(mockEvoUtilFunction);

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideKeyboard()).thenAnswer((_) async {});

    commonUtilFunction = getIt.get<CommonUtilFunction>();
    when(() => commonUtilFunction.shouldShowAlertMessage(
        enable: any(named: 'enable'),
        message: any(named: 'message'),
        latestMessage: any(named: 'latestMessage'),
        latestTimeShowMessage: any(named: 'latestTimeShowMessage'))).thenAnswer((_) => true);
  });

  tearDownAll(() {
    getIt.reset();
  });

  Widget createWidgetUnderTest() {
    return MaterialApp(
      home: VerifyUsernameScreen(cubit: cubit),
    );
  }

  group('VerifyUsernameScreen', () {
    testWidgets('should have all required widgets with correct properties',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      // Verify EvoAppBar is present with leading button
      expect(find.byType(AppBarWrapper), findsOneWidget);
      final AppBarWrapper appBarWrapper = tester.widget(find.byType(AppBarWrapper));
      expect(appBarWrapper.contentPadding, EdgeInsets.zero);

      // Find and verify the leading button has an onPressed handler
      final EvoAppBarLeadingButton leadingButton =
      tester.widget<EvoAppBarLeadingButton>(find.byType(EvoAppBarLeadingButton));
      expect(leadingButton.onPressed, isNotNull);


      expect(find.text(EvoStrings.verifyUsernameTitle), findsOneWidget);
      expect(find.byType(EvoTextField), findsOneWidget);

      // Verify username field properties
      final EvoTextField textField = tester.widget(find.byType(EvoTextField));
      expect(textField.maxLines, 1);
      expect(textField.keyboardType, TextInputType.text);
      expect(textField.onChanged, isNotNull);
      expect(textField.onSubmitted, isNotNull);
      expect(textField.errMessage, isNull);

      // Verify forgot username section
      final QuestionCtaText questionCtaText =
          tester.widget<QuestionCtaText>(find.byType(QuestionCtaText));
      expect(questionCtaText.question, EvoStrings.forgotUsernameTitle);
      expect(questionCtaText.cta, EvoStrings.ctaRecover);
      expect(questionCtaText.ctaColor, evoColors.secondaryBase);
      expect(questionCtaText.onTap, isNotNull);

      // Verify continue button
      final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
      expect(button.onPressed, isNull);
      expect(
        find.descendant(
          of: find.byType(CommonButton),
          matching: find.text(EvoStrings.ctaContinue),
        ),
        findsOneWidget,
      );
    });

    testWidgets('should have correct padding values in body widget', (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      // Using pumps to ensure all animations have completed
      await tester.pumpAndSettle();

      // Find the specific padding that contains our main body content
      final Finder mainPaddingFinder = find
          .descendant(
            of: find.byType(Scaffold),
            matching: find.byWidgetPredicate((Widget widget) {
              if (widget is Padding && widget.child is Column) {
                final Column column = widget.child as Column;
                // Check if this column contains our username title text
                return find
                    .descendant(
                      of: find.byWidget(column),
                      matching: find.text(EvoStrings.verifyUsernameTitle),
                    )
                    .evaluate()
                    .isNotEmpty;
              }
              return false;
            }),
          )
          .last; // Using last since there are nested Scaffolds

      final Padding mainPadding = tester.widget<Padding>(mainPaddingFinder);

      // Verify main padding
      expect(
          mainPadding.padding,
          EdgeInsets.only(
            bottom: EvoDimension.screenBottomPadding,
            top: 0.w,
          ));

      // Get the Column inside the main padding
      final Column column = mainPadding.child as Column;
      final List<Widget> children = column.children;

      // Find the title padding (which should be the second child after the space)
      final Padding titlePadding =
          children.whereType<Padding>().firstWhere((Padding padding) => find
              .descendant(
                of: find.byWidget(padding),
                matching: find.text(EvoStrings.verifyUsernameTitle),
              )
              .evaluate()
              .isNotEmpty);

      expect(
          titlePadding.padding,
          EdgeInsets.symmetric(
            horizontal: EvoDimension.screenHorizontalPadding,
          ));

      // Find username field padding (the third Padding in the Column)
      final Padding usernameFieldPadding =
          children.whereType<Padding>().firstWhere((Padding padding) => find
              .descendant(
                of: find.byWidget(padding),
                matching: find.byType(EvoTextField),
              )
              .evaluate()
              .isNotEmpty);

      expect(
          usernameFieldPadding.padding,
          EdgeInsets.symmetric(
            horizontal: EvoDimension.screenHorizontalPaddingWithTextField,
          ));

      // Find forgot username section padding
      final Padding forgotUsernamePadding =
          children.whereType<Padding>().firstWhere((Padding padding) => find
              .descendant(
                of: find.byWidget(padding),
                matching: find.byType(QuestionCtaText),
              )
              .evaluate()
              .isNotEmpty);

      expect(
          forgotUsernamePadding.padding,
          EdgeInsets.only(
            top: 16.w,
            right: EvoDimension.screenHorizontalPadding,
            left: EvoDimension.screenHorizontalPadding,
          ));

      // Find continue button padding (the last Padding in the Column)
      final Padding continueButtonPadding =
          children.whereType<Padding>().firstWhere((Padding padding) => find
              .descendant(
                of: find.byWidget(padding),
                matching: find.byType(CommonButton),
              )
              .evaluate()
              .isNotEmpty);

      expect(
          continueButtonPadding.padding,
          EdgeInsets.symmetric(
            horizontal: EvoDimension.screenHorizontalPadding,
          ));
    });

    testWidgets('should display toast message when there is api failure',
        (WidgetTester tester) async {
      final ErrorUIModel error = ErrorUIModel(userMessage: 'Invalid username');
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(VerifyUsernameError(error: error));
      await tester.pumpAndSettle();

      expect(find.text(error.userMessage!), findsNothing);
      verify(() => commonUtilFunction.shouldShowAlertMessage(
          enable: any(named: 'enable'),
          message: any(named: 'message'),
          latestMessage: any(named: 'latestMessage'),
          latestTimeShowMessage: any(named: 'latestTimeShowMessage'))).called(1);
    });

    testWidgets('should call verify with entered username when continue button is tapped',
        (WidgetTester tester) async {
      when(() => cubit.verify(any())).thenAnswer((_) async {});
      when(() => cubit.updateUsername(any())).thenAnswer((_) async {});

      await tester.pumpWidget(createWidgetUnderTest());

      const String username = 'validUsername';
      await tester.enterText(find.byType(EvoTextField), username);
      await tester.pumpAndSettle();
      verify(() => cubit.updateUsername(username)).called(1);

      cubit.emit(UsernameModifiedState(username: username));
      await tester.pumpAndSettle();

      await tester.tap(find.text(EvoStrings.ctaContinue));
      verify(() => cubit.verify(username)).called(1);
    });

    test('should self navigate', () {
      VerifyUsernameScreen.pushNamed();
      verify(() => mockNavigatorContext.pushNamed(Screen.verifyUsernameScreen.name)).called(1);

      VerifyUsernameScreen.pushReplacementNamed();
      verify(() => mockNavigatorContext.pushReplacementNamed(Screen.verifyUsernameScreen.name))
          .called(1);

      VerifyUsernameScreen.goNamed();
      verify(() => mockNavigatorContext.goNamed(Screen.verifyUsernameScreen.name)).called(1);
    });

    testWidgets('should call cubit.verify when the text field is submitted',
        (WidgetTester tester) async {
      when(() => cubit.verify(any())).thenAnswer((_) async {});

      await tester.pumpWidget(createWidgetUnderTest());

      final EvoTextField textField = tester.widget(find.byType(EvoTextField));
      textField.onSubmitted?.call('text');

      verify(() => cubit.verify(any())).called(1);
    });
  });

  group('Continue Button BlocBuilder', () {
    group('Initial State', () {
      testWidgets('should be disabled with empty username', (WidgetTester tester) async {
        await tester.pumpWidget(createWidgetUnderTest());

        final CommonButton button = tester.widget<CommonButton>(find.byType(CommonButton));
        expect(button.onPressed, isNull);
      });
    });

    group('Empty Username States', () {
      testWidgets('should not rebuild on same empty state', (WidgetTester tester) async {
        await tester.pumpWidget(createWidgetUnderTest());
        cubit.emit(UsernameModifiedState(username: ''));
        await tester.pumpAndSettle();

        final CommonButton initialButton = tester.widget<CommonButton>(find.byType(CommonButton));

        cubit.emit(UsernameModifiedState(username: ''));
        await tester.pumpAndSettle();

        final CommonButton updatedButton = tester.widget<CommonButton>(find.byType(CommonButton));
        expect(identical(initialButton, updatedButton), isTrue);
        expect(updatedButton.onPressed, isNull);
      });
    });

    group('State Transitions', () {
      testWidgets('should rebuild when transitioning from empty to non-empty',
          (WidgetTester tester) async {
        await tester.pumpWidget(createWidgetUnderTest());

        cubit.emit(UsernameModifiedState(username: ''));
        await tester.pumpAndSettle();
        final CommonButton initialButton = tester.widget<CommonButton>(find.byType(CommonButton));
        expect(initialButton.onPressed, isNull);

        cubit.emit(UsernameModifiedState(username: 'test'));
        await tester.pumpAndSettle();
        final CommonButton updatedButton = tester.widget<CommonButton>(find.byType(CommonButton));
        expect(updatedButton.onPressed, isNotNull);

        expect(identical(initialButton, updatedButton), isFalse);
      });

      testWidgets('should not rebuild for different non-empty values', (WidgetTester tester) async {
        await tester.pumpWidget(createWidgetUnderTest());

        cubit.emit(UsernameModifiedState(username: 'test'));
        await tester.pumpAndSettle();
        final CommonButton firstButton = tester.widget<CommonButton>(find.byType(CommonButton));

        cubit.emit(UsernameModifiedState(username: 'different'));
        await tester.pumpAndSettle();

        final CommonButton secondButton = tester.widget<CommonButton>(find.byType(CommonButton));
        expect(identical(firstButton, secondButton), isTrue);
      });
    });

    group('Error States', () {
      testWidgets('should rebuild for InvalidUsernameErrorState', (WidgetTester tester) async {
        await tester.pumpWidget(createWidgetUnderTest());

        cubit.emit(UsernameModifiedState(username: 'test'));
        await tester.pumpAndSettle();
        final CommonButton initialButton = tester.widget<CommonButton>(find.byType(CommonButton));

        cubit.emit(InvalidUsernameErrorState(errorMessage: 'Invalid username'));
        await tester.pumpAndSettle();

        final CommonButton updatedButton = tester.widget<CommonButton>(find.byType(CommonButton));
        expect(identical(initialButton, updatedButton), isFalse);
        expect(updatedButton.onPressed, isNull);
      });

      testWidgets('should rebuild for VerifyUsernameError', (WidgetTester tester) async {
        await tester.pumpWidget(createWidgetUnderTest());

        cubit.emit(UsernameModifiedState(username: 'test'));
        await tester.pumpAndSettle();
        final CommonButton initialButton = tester.widget<CommonButton>(find.byType(CommonButton));

        cubit.emit(VerifyUsernameError(error: ErrorUIModel(userMessage: 'Server error')));
        await tester.pumpAndSettle();

        final CommonButton updatedButton = tester.widget<CommonButton>(find.byType(CommonButton));
        expect(identical(initialButton, updatedButton), isFalse);
        expect(updatedButton.onPressed, isNull);
      });
    });

    group('Other States', () {
      testWidgets('should not rebuild for non-UsernameModifiedState', (WidgetTester tester) async {
        await tester.pumpWidget(createWidgetUnderTest());

        cubit.emit(UsernameModifiedState(username: 'test'));
        await tester.pumpAndSettle();
        final CommonButton initialButton = tester.widget<CommonButton>(find.byType(CommonButton));

        cubit.emit(VerifyUsernameLoading());
        await tester.pumpAndSettle();

        final CommonButton updatedButton = tester.widget<CommonButton>(find.byType(CommonButton));
        expect(identical(initialButton, updatedButton), isTrue);
      });
    });
  });

  group('Username Field', () {
    testWidgets('should not show error when in initial state', (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      final EvoTextField textField = tester.widget(find.byType(EvoTextField));
      expect(textField.errMessage, isNull);
    });

    testWidgets('should show error when state is InvalidUsernameErrorState',
        (WidgetTester tester) async {
      const String errorMessage = 'Invalid username format';
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(InvalidUsernameErrorState(errorMessage: errorMessage));
      await tester.pumpAndSettle();

      final EvoTextField textField = tester.widget(find.byType(EvoTextField));
      expect(textField.errMessage, errorMessage);
    });

    testWidgets('should no show error when state is VerifyUsernameError',
        (WidgetTester tester) async {
      final ErrorUIModel error = ErrorUIModel(userMessage: 'Username not found');
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(VerifyUsernameError(error: error));
      await tester.pumpAndSettle();

      final EvoTextField textField = tester.widget(find.byType(EvoTextField));
      expect(textField.errMessage, isNull);
    });

    testWidgets('should clear error when state changes to non-error state',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(InvalidUsernameErrorState(errorMessage: 'error'));
      await tester.pumpAndSettle();

      cubit.emit(UsernameModifiedState(username: 'username'));
      await tester.pumpAndSettle();

      final EvoTextField textField = tester.widget(find.byType(EvoTextField));
      expect(textField.errMessage, isNull);
    });
  });

  group('Screen state', () {
    testWidgets('when state is VerifyUsernameLoading, should show loading ',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(VerifyUsernameLoading());
      await tester.pumpAndSettle();

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
    });

    group('when state is VerifyUsernameSuccess', () {
      // Test constants for VerifyUsernameSuccess state
      const String kTestUsername = 'testUser';
      const String kTestSessionToken = 'test-session-token';
      const String kTestChallengeType = 'verify_pin';

      setUpAll(() {
        registerFallbackValue(ChallengeSuccessModel());
      });

      testWidgets('should show confirmation dialog with correct parameters',
          (WidgetTester tester) async {
        // Configure mock with callback capture
        when(() => mockDialogFunction.showDialogConfirm(
              dialogId: EvoDialogId.proceedLoginOnNewDeviceDialog,
              title: EvoStrings.logInOnNewDeviceTitle,
              content: EvoStrings.logInOnNewDeviceDesc,
              textPositive: EvoStrings.ctaProceed,
              textNegative: EvoStrings.ctaCancel,
              isDismissible: false,
              onClickPositive: any(named: 'onClickPositive'),
              onClickNegative: any(named: 'onClickNegative'),
            )).thenAnswer((_) async {});

        await tester.pumpWidget(createWidgetUnderTest());
        cubit.emit(VerifyUsernameSuccess(
            username: kTestUsername,
            entity: LoginNewDeviceEntity(
                sessionToken: kTestSessionToken, challengeType: kTestChallengeType)));
        await tester.pumpAndSettle();

        verify(() => mockDialogFunction.showDialogConfirm(
              dialogId: EvoDialogId.proceedLoginOnNewDeviceDialog,
              title: EvoStrings.logInOnNewDeviceTitle,
              content: EvoStrings.logInOnNewDeviceDesc,
              textPositive: EvoStrings.ctaProceed,
              textNegative: EvoStrings.ctaCancel,
              isDismissible: false,
              onClickPositive: any(named: 'onClickPositive'),
              onClickNegative: any(named: 'onClickNegative'),
            )).called(1);
      });

      testWidgets('should navigate to VerifyOtpScreen when positive button is tapped',
          (WidgetTester tester) async {
        // Create mock navigator
        final MockNewDeviceLoginChallengeNavigator mockNavigator =
            MockNewDeviceLoginChallengeNavigator();
        when(() => mockNavigator.nextChallenge(any())).thenAnswer((_) async {});

        // Create a key to access the state
        final GlobalKey<VerifyUsernameScreenState> key = GlobalKey();

        Function? positiveCallback;
        when(() => mockDialogFunction.showDialogConfirm(
              dialogId: any(named: 'dialogId'),
              title: any(named: 'title'),
              content: any(named: 'content'),
              textPositive: any(named: 'textPositive'),
              textNegative: any(named: 'textNegative'),
              isDismissible: any(named: 'isDismissible'),
              onClickPositive: any(named: 'onClickPositive'),
              onClickNegative: any(named: 'onClickNegative'),
            )).thenAnswer((Invocation invocation) {
          positiveCallback = invocation.namedArguments[const Symbol('onClickPositive')];
          return Future.value();
        });

        // Create the widget with the key
        await tester.pumpWidget(MaterialApp(
          home: VerifyUsernameScreen(
            key: key,
            cubit: cubit,
          ),
        ));

        // Replace the real navigator with the mock
        final VerifyUsernameScreenState state = key.currentState!;
        state.loginNewDeviceChallengeHandler = mockNavigator;

        cubit.emit(
          VerifyUsernameSuccess(
            username: kTestUsername,
            entity: LoginNewDeviceEntity(
              sessionToken: kTestSessionToken,
              challengeType: kTestChallengeType,
            ),
          ),
        );
        await tester.pumpAndSettle();

        positiveCallback?.call();

        verify(() => mockNavigator.nextChallenge(any(
            that: isA<ChallengeSuccessModel>()
                .having(
                    (ChallengeSuccessModel m) => (m.entity as LoginNewDeviceEntity).sessionToken,
                    'session token',
                    kTestSessionToken)
                .having((ChallengeSuccessModel m) => m.additionalData?[ChallengeSuccessModel.usernameKey], 'username',
                    kTestUsername)))).called(1);

        verify(() => mockNavigatorContext.pop()).called(1);
      });

      testWidgets('should navigate to WelcomeScreen when negative button is tapped',
          (WidgetTester tester) async {
        Function? negativeCallback;
        when(() => mockDialogFunction.showDialogConfirm(
              dialogId: any(named: 'dialogId'),
              title: any(named: 'title'),
              content: any(named: 'content'),
              textPositive: any(named: 'textPositive'),
              textNegative: any(named: 'textNegative'),
              isDismissible: any(named: 'isDismissible'),
              onClickPositive: any(named: 'onClickPositive'),
              onClickNegative: any(named: 'onClickNegative'),
            )).thenAnswer((Invocation invocation) {
          negativeCallback = invocation.namedArguments[const Symbol('onClickNegative')];
          return Future.value();
        });

        await tester.pumpWidget(createWidgetUnderTest());
        cubit.emit(
          VerifyUsernameSuccess(
            username: kTestUsername,
            entity: LoginNewDeviceEntity(
              sessionToken: kTestSessionToken,
              challengeType: kTestChallengeType,
            ),
          ),
        );
        await tester.pumpAndSettle();

        negativeCallback?.call();

        verify(() => mockNavigatorContext.pop()).called(1);
        verify(() => mockNavigatorContext.goNamed(Screen.welcomeScreen.name)).called(1);
      });
    });

    testWidgets('when state is VerifyUsernameLockedState, should show error limit exceeded dialog',
        (WidgetTester tester) async {
      // Arrange
      final ErrorUIModel mockError = ErrorUIModel(userMessage: 'Account locked');
      when(() => mockDialogFunction.showDialogErrorLimitExceeded(
            type: SessionDialogType.logIn,
            content: mockError.userMessage,
          )).thenAnswer((_) async {});

      // Act
      await tester.pumpWidget(createWidgetUnderTest());
      cubit.emit(VerifyUsernameLockedState(error: mockError));
      await tester.pumpAndSettle();

      // Assert
      verify(() => mockDialogFunction.showDialogErrorLimitExceeded(
            type: SessionDialogType.logIn,
            content: mockError.userMessage,
          )).called(1);
    });
  });
}
