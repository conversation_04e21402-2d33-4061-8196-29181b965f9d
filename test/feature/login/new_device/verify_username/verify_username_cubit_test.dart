import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/login_new_device_request.dart';
import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/login/new_device/verify_username/verify_username_cubit.dart';
import 'package:evoapp/feature/login/new_device/verify_username/verify_username_state.dart';
import 'package:evoapp/util/validator/username_validator.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';

import '../../../../constant.dart';
import '../../../../util/test_util.dart';

class MockUsernameValidator extends Mock implements UsernameValidator {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  late VerifyUsernameCubit verifyUsernameCubit;
  late UsernameValidator usernameValidator;
  late AuthenticationRepo authenticationRepo;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    usernameValidator = MockUsernameValidator();
    authenticationRepo = MockAuthenticationRepo();
    registerFallbackValue(UserNameRequest(userName: ''));
  });

  setUp(() {
    verifyUsernameCubit = VerifyUsernameCubit(
      usernameValidator: usernameValidator,
      authenticationRepo: authenticationRepo,
    );
  });

  tearDown(() {
    reset(usernameValidator);
    reset(authenticationRepo);
  });

  group('initial state', () {
    test('should be VerifyUsernameInitial', () {
      expect(verifyUsernameCubit.state, isA<VerifyUsernameInitial>());
    });
  });

  group('updateUsername', () {
    blocTest<VerifyUsernameCubit, VerifyUsernameState>(
      'should emit UsernameModifiedState with updated username',
      build: () => verifyUsernameCubit,
      act: (VerifyUsernameCubit cubit) => cubit.updateUsername('testUsername'),
      expect: () => <TypeMatcher<UsernameModifiedState>>[
        isA<UsernameModifiedState>().having(
          (UsernameModifiedState state) => state.username,
          'username',
          equals('testUsername'),
        ),
      ],
    );
  });

  group('verify', () {
    group('validation cases', () {
      blocTest<VerifyUsernameCubit, VerifyUsernameState>(
        'should emit InvalidUsernameErrorState when validation fails',
        build: () => verifyUsernameCubit,
        setUp: () {
          when(() => usernameValidator.validate(any())).thenReturn('validation error');
        },
        act: (VerifyUsernameCubit cubit) => cubit.verify('invalid'),
        expect: () => <TypeMatcher<InvalidUsernameErrorState>>[isA<InvalidUsernameErrorState>()],
        verify: (_) {
          verifyNever(() => authenticationRepo.loginNewDevice(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              ));
        },
      );

      blocTest<VerifyUsernameCubit, VerifyUsernameState>(
        'should validate empty string after trimming whitespace',
        build: () => verifyUsernameCubit,
        setUp: () {
          when(() => usernameValidator.validate('')).thenReturn('validation error');
        },
        act: (VerifyUsernameCubit cubit) => cubit.verify('   '),
        expect: () => <TypeMatcher<InvalidUsernameErrorState>>[isA<InvalidUsernameErrorState>()],
        verify: (_) {
          verify(() => usernameValidator.validate('')).called(1);
          verifyNever(() => authenticationRepo.loginNewDevice(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              ));
        },
      );
    });

    group('API responses', () {
      setUp(() {
        when(() => usernameValidator.validate(any())).thenReturn(null);
      });

      Future<void> mockLoginResponse({required int statusCode, required String fileName}) async {
        final Map<String, dynamic> apiResponse = await TestUtil.getResponseMock(fileName);
        when(() => authenticationRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer(
          (_) async => LoginNewDeviceEntity.fromBaseResponse(
            BaseResponse(
              statusCode: statusCode,
              response: apiResponse,
            ),
          ),
        );
      }

      blocTest<VerifyUsernameCubit, VerifyUsernameState>(
        'should emit Success on successful API call',
        build: () => verifyUsernameCubit,
        setUp: () async {
          await mockLoginResponse(
              statusCode: CommonHttpClient.SUCCESS,
              fileName: 'login_new_device_verify_username_success.json');
        },
        act: (VerifyUsernameCubit cubit) => cubit.verify('  validUsername  '),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<VerifyUsernameState>>[
          isA<VerifyUsernameLoading>(),
          isA<VerifyUsernameSuccess>(),
        ],
        verify: (_) {
          verify(() => usernameValidator.validate('validUsername')).called(1);
          verify(() => authenticationRepo.loginNewDevice(
                request: any(
                  named: 'request',
                  that: isA<UserNameRequest>().having(
                    (UserNameRequest req) => req.userName,
                    'userName',
                    equals('validUsername'),
                  ),
                ),
                mockConfig: any(named: 'mockConfig'),
              )).called(1);
        },
      );

      blocTest<VerifyUsernameCubit, VerifyUsernameState>(
        'should emit InvalidUsernameErrorState on NOT_FOUND',
        build: () => verifyUsernameCubit,
        setUp: () async {
          when(() => usernameValidator.validate(any())).thenReturn(null);
          await mockLoginResponse(
            statusCode: CommonHttpClient.NOT_FOUND,
            fileName: 'login_new_device_verify_username_bad_request.json',
          );
        },
        act: (VerifyUsernameCubit cubit) => cubit.verify('badUsername'),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<VerifyUsernameState>>[
          isA<VerifyUsernameLoading>(),
          isA<InvalidUsernameErrorState>().having(
              (InvalidUsernameErrorState state) => state.errorMessage,
              'verify error message',
              'invalid_parameters')
        ],
      );

      blocTest<VerifyUsernameCubit, VerifyUsernameState>(
        'should emit InvalidUsernameErrorState on BAD_REQUEST',
        build: () => verifyUsernameCubit,
        setUp: () async {
          await mockLoginResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            fileName: 'login_new_device_verify_username_bad_request.json',
          );
        },
        act: (VerifyUsernameCubit cubit) => cubit.verify('badUsername'),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<VerifyUsernameState>>[
          isA<VerifyUsernameLoading>(),
          isA<InvalidUsernameErrorState>().having(
              (InvalidUsernameErrorState state) => state.errorMessage,
              'verify error message',
              'invalid_parameters'),
        ],
      );

      blocTest<VerifyUsernameCubit, VerifyUsernameState>(
        'should emit VerifyUsernameError on other errors',
        build: () => verifyUsernameCubit,
        setUp: () async {
          await mockLoginResponse(
            statusCode: CommonHttpClient.UNKNOWN_ERRORS,
            fileName: 'login_new_device_verify_username_bad_request.json',
          );
        },
        act: (VerifyUsernameCubit cubit) => cubit.verify('username'),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<VerifyUsernameState>>[
          isA<VerifyUsernameLoading>(),
          isA<VerifyUsernameError>(),
        ],
      );

      blocTest<VerifyUsernameCubit, VerifyUsernameState>(
        'should emit VerifyUsernameLockedState on LOCKED_RESOURCE',
        build: () => verifyUsernameCubit,
        setUp: () async {
          when(() => usernameValidator.validate(any())).thenReturn(null);
          await mockLoginResponse(
            statusCode: CommonHttpClient.LOCKED_RESOURCE,
            fileName: 'login_new_device_verify_username_bad_request.json',
          );
        },
        act: (VerifyUsernameCubit cubit) => cubit.verify('lockedUsername'),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<VerifyUsernameState>>[
          isA<VerifyUsernameLoading>(),
          isA<VerifyUsernameLockedState>().having(
            (VerifyUsernameLockedState state) => state.error,
            'verify error',
            isA<ErrorUIModel>(),
          ),
        ],
      );
    });
  });
}
