import 'package:evoapp/feature/login/new_device/verify_mpin/new_device_verify_mpin_cubit.dart';
import 'package:evoapp/feature/login/new_device/verify_mpin/new_device_verify_mpin_screen.dart';
import 'package:evoapp/feature/login/new_device/verify_mpin/new_device_verify_mpin_state.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/widget/evo_mpin_code/evo_mpin_code_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';
import '../../../../util/app_mock_cubit.dart';

class MockOnPopSuccess extends Mock {
  void call(ChallengeSuccessModel model);
}

class MockNewDeviceVerifyMPinCubit extends AppMockCubit<NewDeviceVerifyMPinState>
    implements NewDeviceVerifyMPinCubit {}

void main() {
  late MockOnPopSuccess mockSuccess;
  late NewDeviceVerifyMPinCubit mockCubit;
  const String mockUsername = 'user123';
  const String mockSession = 'session-token';

  setUpAll(() {
    initConfigEvoPageStateBase();
    registerFallbackValue(ChallengeSuccessModel());
    registerFallbackValue(SessionDialogType.newDeviceLogIn);
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    mockSuccess = MockOnPopSuccess();
    mockCubit = MockNewDeviceVerifyMPinCubit()..emit(NewDeviceVerifyMPinInitial());

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});

    when(
      () => mockDialogFunction.showDialogSessionTokenExpired(
          onClickPositive: any(named: 'onClickPositive')),
    ).thenAnswer((_) async {});
    when(
      () => mockDialogFunction.showDialogErrorLimitExceeded(
        content: any(named: 'content'),
        type: any(named: 'type'),
      ),
    ).thenAnswer((_) async {});
  });

  Future<void> buildWidget(WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider<NewDeviceVerifyMPinCubit>.value(
          value: mockCubit,
          child: NewDeviceVerifyMPinScreen(
            username: mockUsername,
            sessionToken: mockSession,
            onPopSuccess: mockSuccess.call,
          ),
        ),
      ),
    );
  }

  test('should self navigate', () {
    NewDeviceVerifyMPinScreen.pushNamed(
      onPopSuccess: mockSuccess.call,
      username: mockUsername,
      sessionToken: mockSession,
    );
    verify(() => mockNavigatorContext.pushNamed(
          Screen.newDeviceVerifyMPinScreen.name,
          extra: any(named: 'extra'),
        )).called(1);
  });

  testWidgets('initially renders title and MPIN widget', (WidgetTester tester) async {
    await buildWidget(tester);
    expect(find.textContaining(mockUsername), findsOneWidget);
    expect(find.textContaining(EvoStrings.verifyMPinDesc), findsOneWidget);
    expect(find.byType(EvoMPINCodeWidget), findsOneWidget);
    expect(find.textContaining(EvoStrings.forgotMPINQuestion), findsOneWidget);
    expect(find.textContaining(EvoStrings.reset), findsOneWidget);
  });

  testWidgets('shows loading HUD on loading state', (WidgetTester tester) async {
    await buildWidget(tester);
    mockCubit.emit(NewDeviceVerifyMPinLoading());
    await tester.pump();
    verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
  });

  testWidgets('calls onPopSuccess on success state', (WidgetTester tester) async {
    final BaseEntity entity = BaseEntity();
    await buildWidget(tester);

    mockCubit.emit(NewDeviceVerifyMPinSuccess(entity));
    await tester.pump();

    final ChallengeSuccessModel capturedModel =
        verify(() => mockSuccess.call(captureAny())).captured.single as ChallengeSuccessModel;
    expect(identical(capturedModel.entity, entity), isTrue);
  });

  testWidgets('shows session expired dialog', (WidgetTester tester) async {
    await buildWidget(tester);

    mockCubit.emit(NewDeviceVerifyMPinFailureSessionExpired(error: ErrorUIModel()));
    await tester.pump();

    verify(() => mockDialogFunction.showDialogSessionTokenExpired()).called(1);
  });

  testWidgets('shows limit exceeded dialog', (WidgetTester tester) async {
    const String msg = 'limit exceeded';
    final ErrorUIModel err = ErrorUIModel(userMessage: msg);

    await buildWidget(tester);

    mockCubit.emit(NewDeviceVerifyMPinFailureLimitExceeded(error: err));
    await tester.pump();

    verify(() => mockDialogFunction.showDialogErrorLimitExceeded(
          content: msg,
          type: any(named: 'type'),
        )).called(1);
  });

  testWidgets('renders bad request error on MPIN widget', (WidgetTester tester) async {
    const String errMsg = 'bad request';
    final ErrorUIModel err = ErrorUIModel(userMessage: errMsg);

    await buildWidget(tester);

    mockCubit.emit(NewDeviceVerifyMPinFailureBadRequest(error: err));

    await tester.pump();
    expect(find.text(errMsg), findsOneWidget);
  });

  testWidgets('submits MPIN on submit action', (WidgetTester tester) async {
    final String pin = '1234';

    when(() => mockCubit.verifyPin(
          pin: any(named: 'pin'),
          sessionToken: any(named: 'sessionToken'),
        )).thenAnswer((_) async {});

    await buildWidget(tester);

    final EvoMPINCodeWidget pinField = tester.widget(find.byType(EvoMPINCodeWidget));
    pinField.textEditingController.text = pin;
    await tester.pump(const Duration(seconds: 1));

    verify(() => mockCubit.verifyPin(pin: pin, sessionToken: any(named: 'sessionToken'))).called(1);
  });
}
