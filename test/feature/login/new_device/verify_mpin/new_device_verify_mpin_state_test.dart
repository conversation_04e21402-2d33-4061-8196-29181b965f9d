import 'package:evoapp/feature/login/new_device/verify_mpin/new_device_verify_mpin_state.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('NewDeviceVerifyMPinState', () {
    test('NewDeviceVerifyMPinInitial is a BlocState', () {
      final NewDeviceVerifyMPinInitial state = NewDeviceVerifyMPinInitial();
      expect(state, isA<NewDeviceVerifyMPinState>());
    });

    test('NewDeviceVerifyMPinLoading is a BlocState', () {
      final NewDeviceVerifyMPinLoading state = NewDeviceVerifyMPinLoading();
      expect(state, isA<NewDeviceVerifyMPinState>());
    });

    test('NewDeviceVerifyMPinSuccess holds entity', () {
      final BaseEntity entity = BaseEntity();
      final NewDeviceVerifyMPinSuccess state = NewDeviceVerifyMPinSuccess(entity);
      expect(state.entity, equals(entity));
    });

    test('NewDeviceVerifyMPinFailure holds error', () {
      final ErrorUIModel error = ErrorUIModel();
      final NewDeviceVerifyMPinFailure state = NewDeviceVerifyMPinFailure(error: error);
      expect(state.error, equals(error));
    });

    test('NewDeviceVerifyMPinFailureSessionExpired holds error', () {
      final ErrorUIModel error = ErrorUIModel();
      final NewDeviceVerifyMPinFailureSessionExpired state =
          NewDeviceVerifyMPinFailureSessionExpired(error: error);
      expect(state.error, equals(error));
    });

    test('NewDeviceVerifyMPinFailureBadRequest holds error', () {
      final ErrorUIModel error = ErrorUIModel();
      final NewDeviceVerifyMPinFailureBadRequest state =
          NewDeviceVerifyMPinFailureBadRequest(error: error);
      expect(state.error, equals(error));
    });

    test('NewDeviceVerifyMPinFailureLimitExceeded holds error', () {
      final ErrorUIModel error = ErrorUIModel();
      final NewDeviceVerifyMPinFailureLimitExceeded state =
          NewDeviceVerifyMPinFailureLimitExceeded(error: error);
      expect(state.error, equals(error));
    });
  });
}
