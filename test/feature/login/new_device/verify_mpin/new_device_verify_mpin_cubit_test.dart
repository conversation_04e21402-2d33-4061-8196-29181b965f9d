// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/login_new_device_request.dart';
import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/login/mock/mock_login_new_device_file_name_use_case.dart';
import 'package:evoapp/feature/login/new_device/verify_mpin/new_device_verify_mpin_cubit.dart';
import 'package:evoapp/feature/login/new_device/verify_mpin/new_device_verify_mpin_state.dart';
import 'package:evoapp/util/validator/mpin_validator.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import '../../../../constant.dart';
import '../../../../util/test_util.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockMpinValidator extends Mock implements MpinValidator {}

void main() {
  late NewDeviceVerifyMPinCubit cubit;
  late MockAuthenticationRepo mockAuthRepo;
  late MockMpinValidator mockMpinValidator;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(const VerifyMPinRequest(
      pin: '1234',
      sessionToken: 'session-token',
    ));
  });

  setUp(() {
    mockAuthRepo = MockAuthenticationRepo();
    mockMpinValidator = MockMpinValidator();
    cubit = NewDeviceVerifyMPinCubit(
      authRepo: mockAuthRepo,
      mpinValidator: mockMpinValidator,
    );
  });

  tearDown(() {
    cubit.close();
    reset(mockAuthRepo);
    reset(mockMpinValidator);
  });

  final String validPin = '1234';
  final String validSessionToken = 'valid-session-token';

  test('initial state is NewDeviceVerifyMPinInitial', () {
    expect(cubit.state, isA<NewDeviceVerifyMPinInitial>());
  });

  // Helper function to mock login response
  Future<void> mockLoginResponse({required int statusCode, required String fileName}) async {
    final Map<String, dynamic> apiResponse = await TestUtil.getResponseMock(fileName);
    when(() => mockAuthRepo.loginNewDevice(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer(
      (_) async => LoginNewDeviceEntity.fromBaseResponse(
        BaseResponse(
          statusCode: statusCode,
          response: apiResponse,
        ),
      ),
    );
  }

  group('onChangePin', () {
    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
      'emits NewDeviceVerifyMPinInitial when pin is null',
      build: () => cubit,
      act: (NewDeviceVerifyMPinCubit cubit) => cubit.onChangePin(null),
      expect: () => <TypeMatcher<NewDeviceVerifyMPinInitial>>[isA<NewDeviceVerifyMPinInitial>()],
    );

    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
      'emits NewDeviceVerifyMPinInitial when pin is empty',
      build: () => cubit,
      act: (NewDeviceVerifyMPinCubit cubit) => cubit.onChangePin(''),
      expect: () => <TypeMatcher<NewDeviceVerifyMPinInitial>>[isA<NewDeviceVerifyMPinInitial>()],
    );
  });

  group('verifyPin', () {
    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
      'emits failure when pin validation fails',
      build: () => cubit,
      setUp: () {
        const String validationError = 'Invalid PIN format';
        when(() => mockMpinValidator.validate(any())).thenReturn(validationError);
      },
      act: (NewDeviceVerifyMPinCubit cubit) => cubit.verifyPin(
        pin: '1111',
        sessionToken: validSessionToken,
      ),
      expect: () => <TypeMatcher<NewDeviceVerifyMPinFailureBadRequest>>[
        isA<NewDeviceVerifyMPinFailureBadRequest>()
      ],
      verify: (_) {
        verifyNever(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            ));
      },
    );

    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
      'emits session expired state when session token is empty',
      build: () => cubit,
      setUp: () {
        when(() => mockMpinValidator.validate(any())).thenReturn(null);
      },
      act: (NewDeviceVerifyMPinCubit cubit) => cubit.verifyPin(pin: validPin, sessionToken: ''),
      expect: () => <TypeMatcher<NewDeviceVerifyMPinFailureSessionExpired>>[
        isA<NewDeviceVerifyMPinFailureSessionExpired>()
      ],
    );

    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
      'verifies correct parameters are passed to API when verifying PIN',
      build: () => cubit,
      setUp: () async {
        when(() => mockMpinValidator.validate(any())).thenReturn(null);
        await mockLoginResponse(
          statusCode: CommonHttpClient.SUCCESS,
          fileName: 'login_new_device_verify_mpin_success.json',
        );
      },
      act: (NewDeviceVerifyMPinCubit cubit) => cubit.verifyPin(
        pin: validPin,
        sessionToken: validSessionToken,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      verify: (_) {
        verify(() => mockMpinValidator.validate(validPin)).called(1);
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                named: 'request',
                that: isA<VerifyMPinRequest>()
                    .having(
                        (VerifyMPinRequest request) => request.pin, 'verify mpin', equals(validPin))
                    .having((VerifyMPinRequest request) => request.sessionToken,
                        'verify session token', equals(validSessionToken)),
              ),
              mockConfig: any(
                  named: 'mockConfig',
                  that: isA<MockConfig>()
                      .having((MockConfig mockConfig) => mockConfig.enable, 'verify mock enable',
                          isFalse)
                      .having(
                          (MockConfig mockConfig) => mockConfig.fileName,
                          'verify filename',
                          getMockLoginNewDeviceFileNameByCase(
                              MockLoginNewDeviceFileNameUseCase.getVerifyMPinSuccess))),
            )).called(1);
      },
    );

    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
      'emits loading and success states with correct data when API call succeeds',
      build: () => cubit,
      setUp: () async {
        when(() => mockMpinValidator.validate(any())).thenReturn(null);
        await mockLoginResponse(
          statusCode: CommonHttpClient.SUCCESS,
          fileName: 'login_new_device_verify_mpin_success.json',
        );
      },
      act: (NewDeviceVerifyMPinCubit cubit) => cubit.verifyPin(
        pin: validPin,
        sessionToken: validSessionToken,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <TypeMatcher<NewDeviceVerifyMPinState>>[
        isA<NewDeviceVerifyMPinLoading>(),
        isA<NewDeviceVerifyMPinSuccess>(),
      ],
      verify: (NewDeviceVerifyMPinCubit cubit) {
        expect(cubit.state, isA<NewDeviceVerifyMPinSuccess>());
        final NewDeviceVerifyMPinSuccess state = cubit.state as NewDeviceVerifyMPinSuccess;
        expect((state.entity as LoginNewDeviceEntity).challengeType, equals('face_auth'));
        expect((state.entity as LoginNewDeviceEntity).sessionToken, equals('session_token'));
      },
    );

    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
        'emits loading and bad request failure states when API returns bad request',
        build: () => cubit,
        setUp: () async {
          when(() => mockMpinValidator.validate(any())).thenReturn(null);
          await mockLoginResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            fileName: 'login_new_device_verify_mpin_bad_request.json',
          );
        },
        act: (NewDeviceVerifyMPinCubit cubit) => cubit.verifyPin(
              pin: validPin,
              sessionToken: validSessionToken,
            ),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<NewDeviceVerifyMPinState>>[
              isA<NewDeviceVerifyMPinLoading>(),
              isA<NewDeviceVerifyMPinFailureBadRequest>(),
            ],
        verify: (NewDeviceVerifyMPinCubit cubit) {
          expect(cubit.state, isA<NewDeviceVerifyMPinFailureBadRequest>());
          final NewDeviceVerifyMPinFailureBadRequest state =
              cubit.state as NewDeviceVerifyMPinFailureBadRequest;
          expect(state.error, isNotNull);
          expect(state.error.verdict, 'invalid_parameters');
          expect(state.error.statusCode, CommonHttpClient.BAD_REQUEST);
        });

    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
        'emits loading and limit exceeded failure states when API returns limit exceeded',
        build: () => cubit,
        setUp: () async {
          when(() => mockMpinValidator.validate(any())).thenReturn(null);
          await mockLoginResponse(
            statusCode: CommonHttpClient.LIMIT_EXCEEDED,
            fileName: 'login_new_device_verify_mpin_bad_request.json',
          );
        },
        act: (NewDeviceVerifyMPinCubit cubit) => cubit.verifyPin(
              pin: validPin,
              sessionToken: validSessionToken,
            ),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<NewDeviceVerifyMPinState>>[
              isA<NewDeviceVerifyMPinLoading>(),
              isA<NewDeviceVerifyMPinFailureLimitExceeded>(),
            ],
        verify: (NewDeviceVerifyMPinCubit cubit) {
          expect(cubit.state, isA<NewDeviceVerifyMPinFailureLimitExceeded>());
          final NewDeviceVerifyMPinFailureLimitExceeded state =
              cubit.state as NewDeviceVerifyMPinFailureLimitExceeded;
          expect(state.error, isNotNull);
          expect(state.error.statusCode, CommonHttpClient.LIMIT_EXCEEDED);
        });

    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
        'emits loading and limit exceeded failure states when API returns locked resource',
        build: () => cubit,
        setUp: () async {
          when(() => mockMpinValidator.validate(any())).thenReturn(null);
          await mockLoginResponse(
            statusCode: CommonHttpClient.LOCKED_RESOURCE,
            fileName: 'login_new_device_verify_mpin_bad_request.json',
          );
        },
        act: (NewDeviceVerifyMPinCubit cubit) => cubit.verifyPin(
              pin: validPin,
              sessionToken: validSessionToken,
            ),
        wait: TestConstant.blocEmitStateDelayDuration,
        expect: () => <TypeMatcher<NewDeviceVerifyMPinState>>[
              isA<NewDeviceVerifyMPinLoading>(),
              isA<NewDeviceVerifyMPinFailureLimitExceeded>(),
            ],
        verify: (NewDeviceVerifyMPinCubit cubit) {
          expect(cubit.state, isA<NewDeviceVerifyMPinFailureLimitExceeded>());
          final NewDeviceVerifyMPinFailureLimitExceeded state =
              cubit.state as NewDeviceVerifyMPinFailureLimitExceeded;
          expect(state.error, isNotNull);
          expect(state.error.statusCode, CommonHttpClient.LOCKED_RESOURCE);
        });

    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
      'emits loading and generic failure states when API returns an unhandled status code',
      build: () => cubit,
      setUp: () async {
        when(() => mockMpinValidator.validate(any())).thenReturn(null);
        await mockLoginResponse(
          statusCode: CommonHttpClient.UNKNOWN_ERRORS,
          // or any other unhandled status code
          fileName: 'login_new_device_verify_mpin_bad_request.json',
        );
      },
      act: (NewDeviceVerifyMPinCubit cubit) => cubit.verifyPin(
        pin: validPin,
        sessionToken: validSessionToken,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <TypeMatcher<NewDeviceVerifyMPinState>>[
        isA<NewDeviceVerifyMPinLoading>(),
        isA<NewDeviceVerifyMPinFailure>(),
      ],
      verify: (NewDeviceVerifyMPinCubit cubit) {
        expect(cubit.state, isA<NewDeviceVerifyMPinFailure>());
        final NewDeviceVerifyMPinFailure state = cubit.state as NewDeviceVerifyMPinFailure;
        expect(state.error, isNotNull);
        expect(state.error.statusCode, CommonHttpClient.UNKNOWN_ERRORS);
      },
    );

    blocTest<NewDeviceVerifyMPinCubit, NewDeviceVerifyMPinState>(
      'emits loading and session expired failure states when API returns invalid token',
      build: () => cubit,
      setUp: () async {
        when(() => mockMpinValidator.validate(any())).thenReturn(null);
        await mockLoginResponse(
          statusCode: CommonHttpClient.INVALID_TOKEN,
          fileName: 'login_new_device_verify_mpin_bad_request.json',
        );
      },
      act: (NewDeviceVerifyMPinCubit cubit) => cubit.verifyPin(
        pin: validPin,
        sessionToken: validSessionToken,
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <TypeMatcher<NewDeviceVerifyMPinState>>[
        isA<NewDeviceVerifyMPinLoading>(),
        isA<NewDeviceVerifyMPinFailureSessionExpired>(),
      ],
      verify: (NewDeviceVerifyMPinCubit cubit) {
        expect(cubit.state, isA<NewDeviceVerifyMPinFailureSessionExpired>());
        final NewDeviceVerifyMPinFailureSessionExpired state =
            cubit.state as NewDeviceVerifyMPinFailureSessionExpired;
        expect(state.error, isNotNull);
        expect(state.error.statusCode, CommonHttpClient.INVALID_TOKEN);
      },
    );
  });
}
