import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/impl/mpin_verifier.dart';
import 'package:evoapp/feature/login/new_device/verify_mpin/new_device_verify_mpin_screen.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../base/evo_page_state_base_test_config.dart';

void main() {
  late MPinVerifier verifier;

  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    verifier = MPinVerifier();

    // Register fallback values
    registerFallbackValue(ChallengeSuccessModel(entity: LoginNewDeviceEntity()));
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('MPinVerifier', () {
    test('handle should navigate to NewDeviceVerifyMPinScreen with correct parameters', () {
      // Arrange
      const String kTestSessionToken = 'test_session_token';
      const String kTestUsername = 'test_username';

      final ChallengeSuccessModel model = ChallengeSuccessModel(
        entity: LoginNewDeviceEntity(
          sessionToken: kTestSessionToken,
        ),
        additionalData: <String, dynamic>{ChallengeSuccessModel.usernameKey: kTestUsername},
      );

      void mockOnSuccess(ChallengeSuccessModel _) {}
      void mockOnError(ErrorUIModel? _) {}

      // Act
      verifier.handle(
        model: model,
        onSuccess: mockOnSuccess,
        onError: mockOnError,
      );

      // Assert
      verify(() => mockNavigatorContext.pushNamed(
            Screen.newDeviceVerifyMPinScreen.name,
            extra: any(
                named: 'extra',
                that: isA<NewDeviceVerifyMPinArg>()
                    .having((NewDeviceVerifyMPinArg arg) => arg.username, 'verify username',
                        equals(kTestUsername))
                    .having((NewDeviceVerifyMPinArg arg) => arg.sessionToken,
                        'verify session token', equals(kTestSessionToken))
                    .having((NewDeviceVerifyMPinArg arg) => arg.onPopSuccess,
                        'verify on pop success', equals(mockOnSuccess))),
          )).called(1);
    });
  });
}
