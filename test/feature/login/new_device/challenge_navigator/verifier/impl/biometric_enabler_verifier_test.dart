import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_page.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/impl/biometric_enabler_verifier.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../base/evo_page_state_base_test_config.dart';

void main() {
  late BiometricEnablerVerifier verifier;

  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    verifier = BiometricEnablerVerifier();

    // Register fallback values
    registerFallbackValue(ChallengeSuccessModel(entity: LoginNewDeviceEntity()));
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('BiometricEnablerVerifier', () {
    test('handle should navigate to ActivateBiometricScreen with correct parameters', () {
      // Arrange
      const String kTestSessionToken = 'test_session_token';

      final ChallengeSuccessModel model = ChallengeSuccessModel(
        entity: LoginNewDeviceEntity(
          sessionToken: kTestSessionToken,
        ),
      );

      void mockOnSuccess(ChallengeSuccessModel model) {}
      void mockOnError(ErrorUIModel? _) {}

      // Act
      verifier.handle(
        model: model,
        onSuccess: mockOnSuccess,
        onError: mockOnError,
      );

      // Assert
      verify(() => mockNavigatorContext.pushNamed(
            Screen.activateBiometricScreen.name,
            extra: any(
                named: 'extra',
                that: isA<ActivateBiometricScreenArg>()
                    .having((ActivateBiometricScreenArg arg) => arg.sessionToken,
                        'verify sessionToken', kTestSessionToken)
                    .having((ActivateBiometricScreenArg arg) => arg.onSuccess,
                        'verify onSuccess', mockOnSuccess)),
          )).called(1);
    });
  });
}

