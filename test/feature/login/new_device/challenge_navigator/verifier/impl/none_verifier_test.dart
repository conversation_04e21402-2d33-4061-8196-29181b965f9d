import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/impl/none_verifier.dart';
import 'package:evoapp/feature/main_screen/main_screen.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../base/evo_page_state_base_test_config.dart';

void main() {
  late NoneVerifier verifier;

  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    verifier = NoneVerifier();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('NoneVerifier', () {
    test('handle should navigate to MainScreen with isLoggedIn=true', () {
      // Arrange
      final ChallengeSuccessModel model = ChallengeSuccessModel(
          entity: LoginNewDeviceEntity(
        sessionToken: 'test_session_token',
      ));
      mockOnSuccess(ChallengeSuccessModel _) {}
      mockOnError(ErrorUIModel? _) {}

      // Act
      verifier.handle(
        model: model,
        onSuccess: mockOnSuccess,
        onError: mockOnError,
      );

      //{"isLoggedIn": true}

      // Assert
      verify(
        () => mockNavigatorContext.goNamed(
          Screen.mainScreen.name,
          extra: any(
            named: 'extra',
            that: isA<MainScreenArg>()
                .having((MainScreenArg arg) => arg.isLoggedIn, 'verify loggedIn state', isTrue),
          ),
        ),
      ).called(1);
    });
  });
}
