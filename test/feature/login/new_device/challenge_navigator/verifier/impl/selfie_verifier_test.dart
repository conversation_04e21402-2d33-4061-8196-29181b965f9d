import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_verification_screen.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/impl/selfie_verifier.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_verification_flow_type.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../../base/evo_page_state_base_test_config.dart';

void main() {
  late SelfieVerifier verifier;

  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  setUp(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    verifier = SelfieVerifier();

    // Register fallback values
    registerFallbackValue(ChallengeSuccessModel(entity: LoginNewDeviceEntity()));
    registerFallbackValue(SelfieVerificationFlowType.logIn);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('SelfieVerifier', () {
    test('handle should navigate to SelfieVerificationScreen with correct parameters', () {
      // Arrange
      const String kTestSessionToken = 'test_session_token';

      final ChallengeSuccessModel model = ChallengeSuccessModel(
        entity: LoginNewDeviceEntity(
          sessionToken: kTestSessionToken,
        ),
      );

      void mockOnSuccess(ChallengeSuccessModel model) {}
      void mockOnError(ErrorUIModel? _) {}

      // Act
      verifier.handle(
        model: model,
        onSuccess: mockOnSuccess,
        onError: mockOnError,
      );

      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.selfieVerificationScreen.name,
            extra: any(
                named: 'extra',
                that: isA<SelfieVerificationScreenArgs>()
                    .having((SelfieVerificationScreenArgs arg) => arg.flowType, 'verify flow',
                        SelfieVerificationFlowType.logIn)
                    .having((SelfieVerificationScreenArgs arg) => arg.sessionToken,
                        'verify sessionToken', kTestSessionToken)
                    .having((SelfieVerificationScreenArgs arg) => arg.onPopSuccess,
                        'verify onPopSuccess', mockOnSuccess)),
          )).called(1);
    });
  });
}
