import 'package:evoapp/feature/login/new_device/challenge_navigator/new_device_login_challenge_type.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/base_new_device_login_verifier.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/impl/biometric_enabler_verifier.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/impl/mpin_verifier.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/impl/none_verifier.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/impl/selfie_verifier.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/new_device_login_verifier_factory.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late final NewDeviceLoginVerifierFactory verifierFactory;
  setUpAll(() {
    verifierFactory = NewDeviceLoginVerifierFactory();
  });

  group('NewDeviceLoginVerifierFactory', () {
    test('should return MPinVerifier for verifyPin challenge type', () {
      // Arrange
      final NewDeviceLoginChallengeType challengeType = NewDeviceLoginChallengeType.verifyPin;

      // Act
      final BaseNewDeviceLoginVerifier? verifier = verifierFactory.getStrategy(challengeType);

      // Assert
      expect(verifier, isNotNull);
      expect(verifier, isA<MPinVerifier>());
    });

    test('should return SelfieVerifier for faceAuth challenge type', () {
      // Arrange
      final NewDeviceLoginChallengeType challengeType = NewDeviceLoginChallengeType.faceAuth;

      // Act
      final BaseNewDeviceLoginVerifier? verifier = verifierFactory.getStrategy(challengeType);

      // Assert
      expect(verifier, isNotNull);
      expect(verifier, isA<SelfieVerifier>());
    });

    test('should return BiometricEnablerVerifier for enableBiometric challenge type', () {
      // Arrange
      final NewDeviceLoginChallengeType challengeType = NewDeviceLoginChallengeType.enableBiometric;

      // Act
      final BaseNewDeviceLoginVerifier? verifier = verifierFactory.getStrategy(challengeType);

      // Assert
      expect(verifier, isNotNull);
      expect(verifier, isA<BiometricEnablerVerifier>());
    });

    test('should return NoneVerifier for none challenge type', () {
      // Arrange
      final NewDeviceLoginChallengeType challengeType = NewDeviceLoginChallengeType.none;

      // Act
      final BaseNewDeviceLoginVerifier? verifier = verifierFactory.getStrategy(challengeType);

      // Assert
      expect(verifier, isNotNull);
      expect(verifier, isA<NoneVerifier>());
    });

    test('should return null for unsupported challenge type', () {
      final BaseNewDeviceLoginVerifier? verifier =
          verifierFactory.getStrategy(NewDeviceLoginChallengeType.unsupported);
      expect(verifier, isNull);
    });
  });
}