import 'package:evoapp/feature/login/new_device/challenge_navigator/new_device_login_challenge_type.dart';
    import 'package:flutter_test/flutter_test.dart';

    void main() {
      group('NewDeviceLoginChallengeType', () {
        test('fromString returns verifyPin for "verify_pin"', () {
          expect(
            NewDeviceLoginChallengeType.fromString('verify_pin'),
            equals(NewDeviceLoginChallengeType.verifyPin),
          );
        });

        test('fromString returns faceAuth for "face_auth"', () {
          expect(
            NewDeviceLoginChallengeType.fromString('face_auth'),
            equals(NewDeviceLoginChallengeType.faceAuth),
          );
        });

        test('fromString returns biometricToken for "biometric_token"', () {
          expect(
            NewDeviceLoginChallengeType.fromString('biometric_token'),
            equals(NewDeviceLoginChallengeType.enableBiometric),
          );
        });

        test('fromString returns none for "none"', () {
          expect(
            NewDeviceLoginChallengeType.fromString('none'),
            equals(NewDeviceLoginChallengeType.none),
          );
        });

        test('fromString returns unsupported for invalid inputs', () {
          // Testing unknown string value
          expect(
            NewDeviceLoginChallengeType.fromString('unknown'),
            equals(NewDeviceLoginChallengeType.unsupported),
          );

          // Testing null value
          expect(
            NewDeviceLoginChallengeType.fromString(null),
            equals(NewDeviceLoginChallengeType.unsupported),
          );

          // Testing empty string
          expect(
            NewDeviceLoginChallengeType.fromString(''),
            equals(NewDeviceLoginChallengeType.unsupported),
          );
        });

        test('enum values have correct string representation', () {
          expect(NewDeviceLoginChallengeType.verifyPin.value, equals('verify_pin'));
          expect(NewDeviceLoginChallengeType.faceAuth.value, equals('face_auth'));
          expect(NewDeviceLoginChallengeType.none.value, equals('none'));
          expect(NewDeviceLoginChallengeType.enableBiometric.value, equals('biometric_token'));
          expect(NewDeviceLoginChallengeType.unsupported.value, equals('unsupported'));
        });
      });
    }