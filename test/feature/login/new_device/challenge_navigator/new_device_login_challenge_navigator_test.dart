import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/new_device_login_challenge_navigator.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/new_device_login_challenge_type.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/base_new_device_login_verifier.dart';
import 'package:evoapp/feature/login/new_device/challenge_navigator/verifier/new_device_login_verifier_factory.dart';
import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:mocktail/mocktail.dart';

class MockBaseNewDeviceLoginVerifier extends Mock implements BaseNewDeviceLoginVerifier {}

class MockNewDeviceLoginVerifierFactory extends Mock implements NewDeviceLoginVerifierFactory {}

void main() {
  late NewDeviceLoginChallengeNavigator navigator;
  late MockBaseNewDeviceLoginVerifier mockVerifier;
  late MockNewDeviceLoginVerifierFactory mockVerifierFactory;
  late Function(ErrorUIModel?) mockOnError;

  setUpAll(() {
    registerFallbackValue(NewDeviceLoginChallengeType.verifyPin);
  });

  setUp(() {
    mockVerifier = MockBaseNewDeviceLoginVerifier();
    mockVerifierFactory = MockNewDeviceLoginVerifierFactory();
    mockOnError = (ErrorUIModel? error) {};
    navigator = NewDeviceLoginChallengeNavigator(
      onError: mockOnError,
      verifierFactory: mockVerifierFactory,
    );
  });

  group('NewDeviceLoginChallengeNavigator', () {
    test('should call onError when entity is not LoginNewDeviceEntity', () {
      // Arrange
      bool errorCalled = false;
      navigator = NewDeviceLoginChallengeNavigator(
        onError: (ErrorUIModel? error) {
          errorCalled = true;
        },
        verifierFactory: mockVerifierFactory,
      );
      final ChallengeSuccessModel model = ChallengeSuccessModel(entity: null);

      // Act
      navigator.nextChallenge(model);

      // Assert
      expect(errorCalled, true);
    });

    test('should get and use correct verifier strategy based on challenge type', () {
      // Arrange
      final LoginNewDeviceEntity loginEntity = LoginNewDeviceEntity(
        challengeType: 'verify_pin',
        // Add other required properties as needed
      );
      final ChallengeSuccessModel model = ChallengeSuccessModel(entity: loginEntity);

      when(() => mockVerifierFactory.getStrategy(any())).thenReturn(mockVerifier);

      // Act
      navigator.nextChallenge(model);

      // Assert
      verify(() => mockVerifierFactory.getStrategy(NewDeviceLoginChallengeType.verifyPin))
          .called(1);
      verify(() => mockVerifier.handle(
            model: model,
            onSuccess: navigator.nextChallenge,
            onError: any(named: 'onError'),
          )).called(1);
    });

    test('should do nothing when strategy is null', () {
      // Arrange
      final LoginNewDeviceEntity loginEntity = LoginNewDeviceEntity(
        challengeType: 'UNKNOWN_TYPE',
        // Add other required properties as needed
      );
      final ChallengeSuccessModel model = ChallengeSuccessModel(entity: loginEntity);

      when(() => mockVerifierFactory.getStrategy(NewDeviceLoginChallengeType.unsupported))
          .thenReturn(null);

      // Act & Assert (should not throw)
      expect(() => navigator.nextChallenge(model), returnsNormally);
    });
  });
}
