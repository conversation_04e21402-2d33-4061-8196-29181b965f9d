import 'package:evoapp/feature/login/mock/mock_login_new_device_file_name_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MockLoginNewDeviceFileNameUseCase', () {
    test('should return verify username success file name', () {
      expect(
          getMockLoginNewDeviceFileNameByCase(
              MockLoginNewDeviceFileNameUseCase.getVerifyUsernameSuccess),
          'login_new_device_verify_username_success.json');
    });

    test('should return verify username bad request file name', () {
      expect(
          getMockLoginNewDeviceFileNameByCase(
              MockLoginNewDeviceFileNameUseCase.getVerifyUsernameBadRequest),
          'login_new_device_verify_username_bad_request.json');
    });

    test('should return verify mpin success file name', () {
      expect(
          getMockLoginNewDeviceFileNameByCase(
              MockLoginNewDeviceFileNameUseCase.getVerifyMPinSuccess),
          'login_new_device_verify_mpin_success.json');
    });

    test('should return verify mpin bad request file name', () {
      expect(
          getMockLoginNewDeviceFileNameByCase(
              MockLoginNewDeviceFileNameUseCase.getVerifyMPinBadRequest),
          'login_new_device_verify_mpin_bad_request.json');
    });

    test('should return verify selfie authentication file name', () {
      expect(
          getMockLoginNewDeviceFileNameByCase(
              MockLoginNewDeviceFileNameUseCase.getVerifySelfieSuccess),
          'login_new_device_verify_selfie_success.json');
    });

    test('should return verify biometric token success file name', () {
      expect(
          getMockLoginNewDeviceFileNameByCase(
              MockLoginNewDeviceFileNameUseCase.getVerifyBiometricTokenSuccess),
          'login_new_device_verify_biometric_token_success.json');
    });
  });
}
