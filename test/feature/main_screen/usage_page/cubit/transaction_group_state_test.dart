import 'package:evoapp/data/response/transaction_list_entity.dart';
import 'package:evoapp/feature/main_screen/usage_page/cubit/transaction_group_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TransactionGroupState', () {
    test('Subclasses should extend TransactionGroupState', () {
      expect(TransactionGroupInitial(), isA<TransactionGroupState>());
      expect(TransactionGroupLoading(), isA<TransactionGroupState>());
      expect(TransactionGroupSuccess(groups: <TransactionGroupEntity>[]),
          isA<TransactionGroupState>());
      expect(TransactionGroupError(), isA<TransactionGroupState>());
    });

    test('TransactionGroupSuccess should extend TransactionGroupState', () {
      final List<TransactionGroupEntity> groups = <TransactionGroupEntity>[
        TransactionGroupEntity(
          name: 'March 2024',
          total: 150.0,
          transactions: <TransactionGroupItemEntity>[],
        ),
      ];
      final TransactionGroupSuccess state = TransactionGroupSuccess(groups: groups);

      expect(state.groups, equals(groups));
      expect(state.groups.length, 1);
      expect(state.groups.first.name, 'March 2024');
      expect(state.groups.first.total, 150.0);
    });
  });
}
