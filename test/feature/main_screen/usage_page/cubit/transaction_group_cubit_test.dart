import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/main_screen/usage_page/cubit/transaction_group_cubit.dart';
import 'package:evoapp/feature/main_screen/usage_page/cubit/transaction_group_state.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../constant.dart';

void main() {
  late TransactionGroupCubit cubit;

  setUp(() {
    cubit = TransactionGroupCubit();
  });

  tearDown(() {
    cubit.close();
  });

  test('Initial state should be TransactionGroupInitial', () {
    expect(cubit.state, isA<TransactionGroupInitial>());
  });

  group('getTransactionGroups', () {
    blocTest<TransactionGroupCubit, TransactionGroupState>(
      'emits [TransactionGroupSuccess] when request is successful',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (TransactionGroupCubit cubit) => cubit.getTransactionGroups(),
      expect: () => <dynamic>[
        isA<TransactionGroupLoading>(),
        isA<TransactionGroupSuccess>(),
      ],
    );
  });
}
