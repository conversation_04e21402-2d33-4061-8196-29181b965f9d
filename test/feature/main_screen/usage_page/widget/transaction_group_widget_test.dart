import 'package:evoapp/data/response/transaction_list_entity.dart';
import 'package:evoapp/feature/main_screen/usage_page/widget/transaction_group_header_widget.dart';
import 'package:evoapp/feature/main_screen/usage_page/widget/transaction_group_item_list_widget.dart';
import 'package:evoapp/feature/main_screen/usage_page/widget/transaction_group_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

void main() {
  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();

    getIt.registerSingleton(EvoUtilFunction());
    final CommonImageProvider imageProvider = getIt.registerSingleton(MockCommonImageProvider());
    when(() => imageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
        )).thenReturn(SizedBox());
  });

  tearDownAll(() {
    getIt.reset();
  });

  final List<TransactionGroupItemEntity> testTransactions = <TransactionGroupItemEntity>[
    TransactionGroupItemEntity(
      id: '123',
      paidTo: 'Netflix',
      amount: 100.0,
      dateTime: DateTime(2024, 3, 15),
      paidBy: '5638',
    ),
    TransactionGroupItemEntity(
      id: '456',
      paidTo: 'Spotify',
      amount: 50.0,
      dateTime: DateTime(2024, 3, 16),
      paidBy: '5638',
    ),
  ];

  final TransactionGroupEntity testGroup = TransactionGroupEntity(
    name: 'March 2024',
    total: 150.0,
    transactions: testTransactions,
  );

  group('TransactionGroupWidget', () {
    testWidgets('should render with header and item list', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionGroupWidget(group: testGroup),
          ),
        ),
      );

      final Finder headerFinder = find.byType(TransactionGroupHeaderWidget);
      final Finder listFinder = find.byType(TransactionGroupItemListWidget);

      expect(headerFinder, findsOneWidget);
      expect(listFinder, findsOneWidget);

      final TransactionGroupHeaderWidget headerWidget = tester.widget(headerFinder);
      expect(headerWidget.name, 'March 2024');
      expect(headerWidget.total, 150.0);

      final TransactionGroupItemListWidget listWidget = tester.widget(listFinder);
      expect(listWidget.items, equals(testTransactions));
      expect(listWidget.items.length, 2);
    });
  });
}
