import 'package:evoapp/data/response/transaction_list_entity.dart';
import 'package:evoapp/feature/main_screen/usage_page/widget/transaction_group_item_list_widget.dart';
import 'package:evoapp/feature/main_screen/usage_page/widget/transaction_group_item_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

void main() {
  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();

    getIt.registerSingleton(EvoUtilFunction());
    final CommonImageProvider imageProvider = getIt.registerSingleton(MockCommonImageProvider());
    when(() => imageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
        )).thenReturn(SizedBox());
  });

  tearDownAll(() {
    getIt.reset();
  });

  final List<TransactionGroupItemEntity> singleItem = <TransactionGroupItemEntity>[
    TransactionGroupItemEntity(
      id: '123',
      paidTo: 'Netflix',
      amount: 100.0,
      dateTime: DateTime(2024, 3, 15),
      paidBy: '5638',
    ),
  ];

  final List<TransactionGroupItemEntity> multipleItems = <TransactionGroupItemEntity>[
    TransactionGroupItemEntity(
      id: '123',
      paidTo: 'Netflix',
      amount: 100.0,
      dateTime: DateTime(2024, 3, 15),
      paidBy: '5638',
    ),
    TransactionGroupItemEntity(
      id: '456',
      paidTo: 'Spotify',
      amount: 100.0,
      dateTime: DateTime(2024, 3, 16),
      paidBy: '5638',
    ),
    TransactionGroupItemEntity(
      id: '789',
      paidTo: 'Amazon',
      amount: 100.0,
      dateTime: DateTime(2024, 3, 17),
      paidBy: '5638',
    ),
  ];

  group('TransactionGroupItemListWidget', () {
    testWidgets('should render empty widget when items are empty', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TransactionGroupItemListWidget(items: <TransactionGroupItemEntity>[]),
        ),
      );

      expect(find.byType(TransactionGroupItemWidget), findsNothing);
      expect(find.byType(TransactionDivider), findsNothing);
    });

    testWidgets('should render single item without divider', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionGroupItemListWidget(items: singleItem),
          ),
        ),
      );

      expect(find.byType(TransactionGroupItemWidget), findsOneWidget);
      expect(find.byType(TransactionDivider), findsNothing);
      expect(find.text('Netflix'), findsOneWidget);
    });

    testWidgets('should render multiple items with dividers', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionGroupItemListWidget(items: multipleItems),
          ),
        ),
      );

      expect(find.byType(TransactionGroupItemWidget), findsExactly(3));
      expect(find.byType(TransactionDivider), findsExactly(2));
      expect(find.text('Netflix'), findsOneWidget);
      expect(find.text('Spotify'), findsOneWidget);
      expect(find.text('Amazon'), findsOneWidget);
    });

    testWidgets('TransactionDivider should have correct properties', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionGroupItemListWidget(items: multipleItems),
          ),
        ),
      );

      final Finder dividerFinder = find.byType(Divider);
      expect(dividerFinder, findsExactly(2));

      final Divider divider = tester.widget(dividerFinder.first);
      expect(divider.height, 1);
      expect(divider.thickness, 1);
      expect(divider.indent, isNotNull);
      expect(divider.endIndent, isNotNull);
      expect(divider.color, isNotNull);
    });
  });
}
