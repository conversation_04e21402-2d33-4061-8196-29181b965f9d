import 'package:evoapp/data/response/transaction_list_entity.dart';
import 'package:evoapp/feature/main_screen/usage_page/widget/transaction_group_item_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/extension.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

class _FakeImage extends SizedBox {}

void main() {
  late BuildContext navigator;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();

    getIt.registerSingleton(EvoUtilFunction());
    final CommonImageProvider imageProvider = getIt.registerSingleton(MockCommonImageProvider());
    when(() => imageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
        )).thenReturn(_FakeImage());

    navigator = MockContext();
    getItRegisterNavigator(context: navigator);
  });

  tearDownAll(() {
    getIt.reset();
  });

  final DateTime testDateTime = DateTime(2023, 5, 15);

  final TransactionGroupItemEntity mockItem = TransactionGroupItemEntity(
    id: 'id',
    paidTo: 'Grocery Store',
    amount: 125.50,
    dateTime: testDateTime,
    paidBy: '8551',
  );

  group('TransactionGroupItemWidget', () {
    testWidgets('should display all elements correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionGroupItemWidget(item: mockItem),
          ),
        ),
      );

      expect(find.byType(_FakeImage), findsAtLeast(2));
      expect(find.text('Grocery Store'), findsOneWidget);
      expect(find.text('8551'), findsOneWidget);
      expect(find.text(testDateTime.toStringFormatDate()), findsOneWidget);
      expect(find.byType(GestureDetector), findsOneWidget);
    });

    testWidgets('should handle null values', (WidgetTester tester) async {
      final TransactionGroupItemEntity item = TransactionGroupItemEntity(
        id: null,
        paidTo: null,
        amount: null,
        dateTime: null,
        paidBy: null,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionGroupItemWidget(item: item),
          ),
        ),
      );

      expect(find.text('-'), findsOneWidget);
      expect(
        find.byWidgetPredicate(
          (Widget w) => w is SizedBox && w.width == 0 && w.height == 0,
          description: 'null values render SizedBox.shrink()',
        ),
        findsExactly(3),
      );
    });

    testWidgets('should navigate to transaction details screen on tap',
        (WidgetTester tester) async {
      when(() => navigator.pushNamed(
            Screen.transactionDetailsScreen.name,
            extra: any(named: 'extra'),
          )).thenReturn(null);

      final TransactionGroupItemEntity item = TransactionGroupItemEntity(
          id: null, paidTo: null, amount: null, dateTime: null, paidBy: null);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TransactionGroupItemWidget(item: item),
          ),
        ),
      );

      await tester.tap(find.byType(TransactionGroupItemWidget));
      await tester.pumpAndSettle();

      verify(() => navigator.pushNamed(
            Screen.transactionDetailsScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });
  });
}
