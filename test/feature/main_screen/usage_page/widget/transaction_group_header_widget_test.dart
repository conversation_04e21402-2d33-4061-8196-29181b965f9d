import 'package:evoapp/feature/main_screen/usage_page/widget/transaction_group_header_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();

    getIt.registerSingleton(EvoUtilFunction());
  });

  tearDownAll(() {
    getIt.reset();
  });

  testWidgets('TransactionGroupHeaderWidget displays name and total amount',
      (WidgetTester tester) async {
    const String testName = 'March 2024';
    const double testTotal = 5000.0;

    await tester.pumpWidget(
      MaterialApp(
        home: TransactionGroupHeaderWidget(name: testName, total: testTotal),
      ),
    );

    expect(find.text(testName), findsOneWidget);
    expect(find.text(EvoStrings.totalMonthSpend), findsOneWidget);
    expect(find.text('₱5,000.00'), findsOneWidget);
  });

  testWidgets('TransactionGroupHeaderWidget uses correct text styles',
      (WidgetTester tester) async {
    const String testName = 'March 2024';
    const double testTotal = 5000.0;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TransactionGroupHeaderWidget(
            name: testName,
            total: testTotal,
          ),
        ),
      ),
    );

    final Finder groupTextFinder = find.text(testName);
    final Text groupText = tester.widget(groupTextFinder);
    expect(groupText.style, evoTextStyles.bold(TextSize.base, color: evoColors.greyScale90));

    final Finder totalLabelFinder = find.text(EvoStrings.totalMonthSpend);
    final Text totalLabelText = tester.widget(totalLabelFinder);
    expect(totalLabelText.style, evoTextStyles.regular(TextSize.sm, color: evoColors.greyScale80));

    final Finder totalAmountFinder = find.text('₱5,000.00');
    final Text totalAmountText = tester.widget(totalAmountFinder);
    expect(totalAmountText.style, evoTextStyles.bold(TextSize.sm, color: evoColors.greyScale90));
  });
}
