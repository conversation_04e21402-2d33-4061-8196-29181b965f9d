import 'package:evoapp/feature/main_screen/card_page/models/transaction_model.dart';
import 'package:evoapp/feature/main_screen/home_page/cubit/home_page_cubit.dart';
import 'package:evoapp/feature/main_screen/home_page/models/credit_limit_ui_model.dart';
import 'package:evoapp/feature/main_screen/home_page/models/payment_summary_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('HomePageState', () {
    test('InitialHomePageState should be HomePageState', () {
      expect(InitialHomePageState(), isA<HomePageState>());
    });

    test('InactiveCardPanelState should be HomePageState', () {
      expect(InitialHomePageState(), isA<HomePageState>());
    });

    test('GetCreditDataSuccess should be HomePageState with correct properties', () {
      final PaymentSummaryUiModel paymentUiModel = PaymentSummaryUiModel(
        totalAmount: 0,
        paidAmount: 0,
        minAmount: 0,
        dueDay: DateTime.now(),
        status: PaymentStatus.paid,
      );
      final CreditLimitUiModel creditUiModel = CreditLimitUiModel(
        availableCredit: 0,
        totalCredit: 0,
        cutOffDate: DateTime.now(),
      );
      final GetCreditDataSuccess state = GetCreditDataSuccess(
        paymentUiModel: paymentUiModel,
        creditUiModel: creditUiModel,
      );

      expect(state, isA<HomePageState>());
      expect(state.paymentUiModel, paymentUiModel);
      expect(state.creditUiModel, creditUiModel);
    });

    test('GetTransactionsSuccess should be HomePageState with correct properties', () {
      final List<TransactionModel> transactions = <TransactionModel>[];
      final GetTransactionsSuccess state = GetTransactionsSuccess(transactions);
      expect(state, isA<HomePageState>());
      expect(state.transactions, transactions);
    });
  });
}
