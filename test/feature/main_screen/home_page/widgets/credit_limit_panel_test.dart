import 'package:evoapp/feature/main_screen/home_page/models/credit_limit_ui_model.dart';
import 'package:evoapp/feature/main_screen/home_page/widgets/credit_limit_panel.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/extension.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  const double mockAvailableCredit = 123456.78;
  const double mockTotalCredit = 65.43;
  final DateTime mockCutOffDate = DateTime(1987);
  late final CreditLimitUiModel uiModel = CreditLimitUiModel(
    availableCredit: mockAvailableCredit,
    totalCredit: mockTotalCredit,
    cutOffDate: mockCutOffDate,
  );
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());

    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenAnswer((_) => const SizedBox.shrink());
  });

  testWidgets('verify build CreditLimitPanel correctly', (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(MaterialApp(
      home: CreditLimitPanel(uiModel: uiModel),
    ));

    expect(find.text(evoUtilFunction.evoFormatCurrency(mockAvailableCredit)), findsOne);
    expect(find.text(mockCutOffDate.toStringFormatDate()), findsOne);

    expect(
        find.byWidgetPredicate((Widget widget) =>
            widget is LinearProgressIndicator &&
            widget.value == mockAvailableCredit / mockTotalCredit),
        findsOne);

    expect(
      find.text(
          '${EvoStrings.creditLimitProgressPrefix} ${evoUtilFunction.evoFormatCurrency(mockTotalCredit)} ${EvoStrings.creditLimitProgressSuffix}'),
      findsOne,
    );
  });
}
