import 'package:evoapp/feature/main_screen/home_page/models/payment_summary_ui_model.dart';
import 'package:evoapp/feature/main_screen/home_page/widgets/payment_summary_panel/payment_due_tag.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../../util/flutter_test_config.dart';

void main() {
  final DateTime mockDueDate = DateTime(1987);

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
  });

  Color findBorderColor(WidgetTester tester) {
    final Finder containerFinder = find.byType(Container);
    final Container containerWidget = tester.widget<Container>(containerFinder);
    final Color color =
        ((containerWidget.decoration as ShapeDecoration).shape as RoundedRectangleBorder)
            .side
            .color;

    return color;
  }

  verifyWidgetSurface({
    required Color color,
    required String text,
    required WidgetTester tester,
  }) {
    final Color borderColor = findBorderColor(tester);
    final Finder textFinder = find.text(text);
    final Text textWidget = tester.widget(textFinder);

    expect(borderColor, color);
    expect(textWidget.style?.color, borderColor);
  }

  test('verify PaymentDueTag.fromUiModel should have correct field', () {
    final PaymentDueTag tag = PaymentDueTag.fromUiModel(PaymentSummaryUiModel(
      totalAmount: 12345,
      status: PaymentStatus.paid,
      dueDay: mockDueDate,
    ));

    expect(tag.status, PaymentStatus.paid);
    expect(tag.dueDay, mockDueDate);
  });

  testWidgets('verify PaymentDueTag should render correctly when status == paid',
      (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(
      const MaterialApp(
          home: PaymentDueTag(
        status: PaymentStatus.paid,
      )),
    );

    verifyWidgetSurface(
      tester: widgetTester,
      color: evoColors.primary,
      text: EvoStrings.paid,
    );
  });

  group('verify PaymentDueTag when status == preDue', () {
    testWidgets('should render correctly with dueDay is null', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        const MaterialApp(home: PaymentDueTag(status: PaymentStatus.preDue)),
      );

      verifyWidgetSurface(
        tester: widgetTester,
        color: evoColors.accent90,
        text: '',
      );
    });

    testWidgets('should render correctly with dueDay is notK null',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          home: PaymentDueTag(status: PaymentStatus.preDue, dueDay: mockDueDate),
        ),
      );

      verifyWidgetSurface(
        tester: widgetTester,
        color: evoColors.accent90,
        text: '${EvoStrings.dueDate}: ${mockDueDate.toStringFormatDate()}',
      );
    });
  });

  testWidgets('verify PaymentDueTag should render correctly when status == overdue',
      (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(
      const MaterialApp(
          home: PaymentDueTag(
        status: PaymentStatus.overdue,
      )),
    );

    verifyWidgetSurface(
      tester: widgetTester,
      color: evoColors.error,
      text: EvoStrings.overDue,
    );
  });

  testWidgets('verify PaymentDueTag should render correctly when status == forceClosed',
      (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(
      const MaterialApp(
          home: PaymentDueTag(
        status: PaymentStatus.forceClosed,
      )),
    );

    verifyWidgetSurface(
      tester: widgetTester,
      color: evoColors.error,
      text: EvoStrings.overDue,
    );
  });

  group('verify PaymentDueTag when status == nearlyDue', () {
    testWidgets('should render correctly with dueDay is null', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        const MaterialApp(
            home: PaymentDueTag(
          status: PaymentStatus.nearlyDue,
        )),
      );

      verifyWidgetSurface(
        tester: widgetTester,
        color: evoColors.warning100,
        text: '',
      );
    });

    testWidgets('should render correctly with dueDay is in the past',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
            home: PaymentDueTag(
          status: PaymentStatus.nearlyDue,
          dueDay: mockDueDate,
        )),
      );

      verifyWidgetSurface(
        tester: widgetTester,
        color: evoColors.warning100,
        text: '',
      );
    });

    testWidgets('should render correctly with dueDay is today', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
            home: PaymentDueTag(
          status: PaymentStatus.nearlyDue,
          dueDay: DateTime.now(),
        )),
      );

      verifyWidgetSurface(
        tester: widgetTester,
        color: evoColors.warning100,
        text: EvoStrings.payToday,
      );
    });

    testWidgets('should render correctly with dueDay is tomorrow',
        (WidgetTester widgetTester) async {
      final DateTime tmr = DateTime.now().add(const Duration(days: 1));

      await widgetTester.pumpWidget(
        MaterialApp(
            home: PaymentDueTag(
          status: PaymentStatus.nearlyDue,
          dueDay: tmr,
        )),
      );

      verifyWidgetSurface(
        tester: widgetTester,
        color: evoColors.warning100,
        text: EvoStrings.payTomorrow,
      );
    });

    testWidgets('should render correctly with dueDay is any days after tmr',
        (WidgetTester widgetTester) async {
      final DateTime day = DateTime.now().add(const Duration(days: 5));

      await widgetTester.pumpWidget(
        MaterialApp(
            home: PaymentDueTag(
          status: PaymentStatus.nearlyDue,
          dueDay: day,
        )),
      );

      verifyWidgetSurface(
        tester: widgetTester,
        color: evoColors.warning100,
        text: '${EvoStrings.dueDate}: ${day.toStringFormatDate()}',
      );
    });
  });
}
