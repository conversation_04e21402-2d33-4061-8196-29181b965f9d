import 'package:evoapp/feature/main_screen/home_page/models/payment_summary_ui_model.dart';
import 'package:evoapp/feature/main_screen/home_page/widgets/payment_summary_panel/payment_summary_footer.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    final CommonImageProvider commonImageProvider = getIt.get<CommonImageProvider>();

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
        )).thenReturn(const SizedBox.shrink());
  });

  testWidgets('should render correctly when status is not paid', (WidgetTester widgetTester) async {
    final List<PaymentStatus> statusList = <PaymentStatus>[
      PaymentStatus.preDue,
      PaymentStatus.forceClosed,
      PaymentStatus.nearlyDue,
      PaymentStatus.overdue
    ];

    for (final PaymentStatus status in statusList) {
      await widgetTester.pumpWidget(MaterialApp(
        home: PaymentSummaryFooter(status: status),
      ));
      expect(find.byType(Divider), findsOneWidget);
      expect(find.byType(CommonButton), findsOneWidget);
    }
  });

  testWidgets('should render correctly when status is paid', (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(const MaterialApp(
      home: PaymentSummaryFooter(status: PaymentStatus.paid),
    ));
    expect(find.byType(Divider), findsNothing);
    expect(find.byType(CommonButton), findsNothing);
  });
}
