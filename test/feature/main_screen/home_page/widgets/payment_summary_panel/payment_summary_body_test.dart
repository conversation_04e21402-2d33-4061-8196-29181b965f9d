import 'package:evoapp/feature/main_screen/home_page/models/payment_summary_ui_model.dart';
import 'package:evoapp/feature/main_screen/home_page/widgets/payment_summary_panel/payment_summary_panel.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../util/flutter_test_config.dart';

void main() {
  final DateTime mockDueDay = DateTime(1987);
  const double mockTotalAmount = 12345;
  const double mockPaidAmount = 54321;
  const double mockMinAmount = 1122;

  List<Widget> getAmountItems({
    required WidgetTester widgetTester,
  }) {
    final Finder finder = find.byWidgetPredicate((Widget widgetTester) {
      return widgetTester is PaymentAmountItem;
    });

    final List<Widget> widgets = widgetTester.widgetList(finder).toList();

    return widgets;
  }

  verifyAmountItem({required Widget widget, required String title, required double amount}) {
    if (widget is! PaymentAmountItem) {
      return;
    }

    expect(widget.title, title);
    expect(find.text(evoUtilFunction.evoFormatCurrency(amount)), findsOne);
  }

  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());

    final CommonImageProvider commonImageProvider = getIt.get<CommonImageProvider>();

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenReturn(const SizedBox.shrink());
  });

  PaymentSummaryUiModel getPaymentSummaryUiModel(PaymentStatus status) {
    return PaymentSummaryUiModel(
      totalAmount: mockTotalAmount,
      paidAmount: mockPaidAmount,
      minAmount: mockMinAmount,
      status: status,
      dueDay: mockDueDay,
    );
  }

  testWidgets('should render correctly when status is paid', (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(
      MaterialApp(
          home: PaymentSummaryBody(
              uiModel: getPaymentSummaryUiModel(
        PaymentStatus.paid,
      ))),
    );

    final [Widget totalAmountItem, Widget paidAmountItem] =
        getAmountItems(widgetTester: widgetTester);

    verifyAmountItem(
      widget: totalAmountItem,
      title: EvoStrings.totalAmountTitle,
      amount: mockTotalAmount,
    );
    verifyAmountItem(
      widget: paidAmountItem,
      title: EvoStrings.lastAmountPaidTitle,
      amount: mockPaidAmount,
    );
  });

  testWidgets('should render correctly when status is paid', (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(
      MaterialApp(
          home: PaymentSummaryBody(
              uiModel: getPaymentSummaryUiModel(
        PaymentStatus.paid,
      ))),
    );

    final [Widget totalAmountItem, Widget paidAmountItem] =
        getAmountItems(widgetTester: widgetTester);

    verifyAmountItem(
      widget: totalAmountItem,
      title: EvoStrings.totalAmountTitle,
      amount: mockTotalAmount,
    );
    verifyAmountItem(
      widget: paidAmountItem,
      title: EvoStrings.lastAmountPaidTitle,
      amount: mockPaidAmount,
    );
  });

  testWidgets('should render correctly when status is overdue / preDue / nearlyDue',
      (WidgetTester widgetTester) async {
    final List<PaymentStatus> statusList = [
      PaymentStatus.overdue,
      PaymentStatus.preDue,
      PaymentStatus.nearlyDue,
    ];

    for (final PaymentStatus status in statusList) {
      await widgetTester.pumpWidget(
        MaterialApp(
            home: PaymentSummaryBody(
          uiModel: getPaymentSummaryUiModel(status),
        )),
      );

      final [Widget totalAmountItem, Widget paidAmountItem] =
          getAmountItems(widgetTester: widgetTester);

      verifyAmountItem(
        widget: totalAmountItem,
        title: EvoStrings.totalAmountTitle,
        amount: mockTotalAmount,
      );
      verifyAmountItem(
        widget: paidAmountItem,
        title: EvoStrings.paidAmountTitle,
        amount: mockMinAmount,
      );
    }
  });

  testWidgets('should render correctly when status is forcedClose',
      (WidgetTester widgetTester) async {
    await widgetTester.pumpWidget(
      MaterialApp(
          home: PaymentSummaryBody(
              uiModel: getPaymentSummaryUiModel(
        PaymentStatus.forceClosed,
      ))),
    );

    final List<Widget> widgets = getAmountItems(widgetTester: widgetTester);

    expect(widgets.length, 1);
    verifyAmountItem(
      widget: widgets.first,
      title: EvoStrings.foreClosedTitle,
      amount: mockTotalAmount,
    );
  });
}
