import 'package:evoapp/feature/main_screen/home_page/widgets/inactive_card_panel.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/elevated_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class _FakeImage extends SizedBox {}

class MockCallback extends Mock {
  void call();
}

void main() {
  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();
    getItRegisterButtonStyle();

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    when(() => getIt.get<CommonImageProvider>().asset(
          any(),
          height: any(named: 'height'),
          width: any(named: 'width'),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
        )).thenAnswer((_) => _FakeImage());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('InactiveCardPanel', () {
    testWidgets('should display correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InactiveCardPanel(onTap: () {}),
          ),
        ),
      );

      expect(find.text(EvoStrings.activateCardDesc), findsOneWidget);
      expect(find.text(EvoStrings.activeCTAText), findsOneWidget);
      expect(find.byType(CommonButton), findsOneWidget);
      expect(find.byType(_FakeImage), findsOneWidget);

      final ElevatedContainer container =
          tester.widget<ElevatedContainer>(find.byType(ElevatedContainer));
      expect(container.surfaceColor, evoColors.primary);
    });

    testWidgets('should display correctly and button calls onTap', (WidgetTester tester) async {
      // Arrange
      final MockCallback mockCallback = MockCallback();

      // Act - Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: InactiveCardPanel(onTap: mockCallback.call),
          ),
        ),
      );

      // Find and tap the button
      await tester.tap(find.byType(CommonButton));
      await tester.pump();

      // Verify callback was called once
      verify(() => mockCallback.call()).called(1);
    });
  });
}
