import 'package:evoapp/feature/main_screen/home_page/utils/credit_and_payment_utils.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CreditAndPaymentUtils', () {
    final CreditAndPaymentUtils utils = CreditAndPaymentUtils();

    test('getNearlyDueTagText returns empty string when dueDay is null', () {
      expect(utils.getNearlyDueTagText(dueDay: null), '');
    });

    test('getNearlyDueTagText returns empty string when dueDay is in the past', () {
      final DateTime pastDate = DateTime.now().subtract(const Duration(days: 1));
      expect(utils.getNearlyDueTagText(dueDay: pastDate), '');
    });

    test('getNearlyDueTagText returns payToday when dueDay is today', () {
      final DateTime today = DateTime.now();
      expect(utils.getNearlyDueTagText(dueDay: today), EvoStrings.payToday);
    });

    test('getNearlyDueTagText returns payTomorrow when dueDay is tomorrow', () {
      final DateTime tomorrow = DateTime.now().add(const Duration(days: 1));
      expect(utils.getNearlyDueTagText(dueDay: tomorrow), EvoStrings.payTomorrow);
    });

    test('getNearlyDueTagText returns due date text when dueDay is in the future', () {
      final DateTime futureDate = DateTime.now().add(const Duration(days: 2));
      expect(utils.getNearlyDueTagText(dueDay: futureDate), utils.getDueDateText(futureDate));
    });

    test('getDueDateText returns empty string when date is null', () {
      expect(utils.getDueDateText(null), '');
    });

    test('getDueDateText returns formatted due date text when date is not null', () {
      final DateTime date = DateTime(2023, 12, 25);
      expect(utils.getDueDateText(date), '${EvoStrings.dueDate}: ${date.toStringFormatDate()}');
    });
  });
}
