import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/main_screen/bloc/main_cubit.dart';
import 'package:evoapp/feature/main_screen/main_screen_dialog_handler/main_screen_dialog_handler.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  late AppState appState;
  late MainScreenDialogHandler mainScreenDialogHandler;
  late MainCubit cubit;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    appState = AppState();
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  setUp(() {
    mainScreenDialogHandler = MainScreenDialogHandler();
    cubit = MainCubit(
      appState: appState,
      mainScreenDialogHandler: mainScreenDialogHandler,
    );
  });

  test('Default data', () {
    expect(cubit.state, isA<MainInitial>());
    expect(mainScreenDialogHandler.features.length, 2);
  });
}
