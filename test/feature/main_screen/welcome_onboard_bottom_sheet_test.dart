// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/main_screen/welcome_onboard_bottom_sheet.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test_config.dart';

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();

    registerFallbackValue(SizedBox());

    when(() => mockDialogFunction.showDialogBottomSheet(
          hasCloseButton: any(named: 'hasCloseButton'),
          title: any(named: 'title'),
          content: any(named: 'content'),
        )).thenAnswer((_) async {});
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  group('WelcomeOnboardBottomSheet', () {
    test('show method should not show bottom sheet dialog when isCardActivated is null', () {
      WelcomeOnboardBottomSheet.show(isCardActivated: null);

      verifyNever(() => mockDialogFunction.showDialogBottomSheet(
            hasCloseButton: any(named: 'hasCloseButton'),
            title: any(named: 'title'),
            content: any(named: 'content'),
          ));
    });

    testWidgets('should display activated card content when isCardActivated is true',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WelcomeOnboardBottomSheet(true),
          ),
        ),
      );

      // Verify image is displayed
      verify(() => evoImageProvider.asset(
            EvoImages.imgWelcomeOnboardDialog,
            height: 160,
            fit: BoxFit.contain,
          )).called(1);

      // Verify activated card title is displayed
      expect(find.text(EvoStrings.welcomeOnboardCardActivatedTitle), findsOneWidget);

      // Verify all three items are displayed
      expect(find.text('1.'), findsOneWidget);
      expect(find.textContaining(EvoStrings.welcomeOnboardCardActivatedItem1), findsOneWidget);
      expect(find.text('2.'), findsOneWidget);
      expect(find.textContaining(EvoStrings.welcomeOnboardCardActivatedItem2), findsOneWidget);
      expect(find.text('3.'), findsOneWidget);
      expect(find.textContaining(EvoStrings.welcomeOnboardCardActivatedItem3), findsOneWidget);

      expect(find.text(EvoStrings.welcomeOnboardCardNotActivated), findsNothing);
    });

    testWidgets('should display non-activated card content when isCardActivated is false',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WelcomeOnboardBottomSheet(false),
          ),
        ),
      );

      // Verify image is displayed
      verify(() => evoImageProvider.asset(
            EvoImages.imgWelcomeOnboardDialog,
            height: 160,
            fit: BoxFit.contain,
          )).called(1);

      expect(find.text(EvoStrings.welcomeOnboardCardNotActivated), findsOneWidget);

      expect(find.text(EvoStrings.welcomeOnboardCardActivatedTitle), findsNothing);
      expect(find.text('1.'), findsNothing);
      expect(find.textContaining(EvoStrings.welcomeOnboardCardActivatedItem1), findsNothing);
      expect(find.text('2.'), findsNothing);
      expect(find.textContaining(EvoStrings.welcomeOnboardCardActivatedItem2), findsNothing);
      expect(find.text('3.'), findsNothing);
      expect(find.textContaining(EvoStrings.welcomeOnboardCardActivatedItem3), findsNothing);
    });

    test('show method should show bottom sheet dialog when isCardActivated is not null', () {
      // When
      WelcomeOnboardBottomSheet.show(isCardActivated: true);

      // Then
      verify(() => mockDialogFunction.showDialogBottomSheet(
            hasCloseButton: true,
            title: EvoStrings.welcomeOnboardTitle,
            content: any(named: 'content'),
          )).called(1);
    });
  });
}
