import 'package:evoapp/feature/main_screen/main_screen_dialog_handler/main_screen_dialog_handler.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MainScreenDialogHandler', () {
    late MainScreenDialogHandler dialogHandler;

    setUp(() {
      dialogHandler = MainScreenDialogHandler();
    });

    test('initial state of features', () {
      expect(dialogHandler.features, <FeaturesWithDialogDisplay>{
        FeaturesWithDialogDisplay.forceUpdate,
        FeaturesWithDialogDisplay.requestActiveBiometric,
      });
    });

    test('isAllFeaturesWithDialogProcessed returns false initially', () {
      expect(dialogHandler.isAllFeaturesWithDialogProcessed, isFalse);
    });

    test('handleFeatureProcessed removes feature from set', () {
      dialogHandler.handleFeatureProcessed(FeaturesWithDialogDisplay.forceUpdate);
      expect(dialogHandler.features,
          <FeaturesWithDialogDisplay>{FeaturesWithDialogDisplay.requestActiveBiometric});
    });

    test('isAllFeaturesWithDialogProcessed returns true when all features are processed', () {
      dialogHandler.handleFeatureProcessed(FeaturesWithDialogDisplay.forceUpdate);
      dialogHandler.handleFeatureProcessed(FeaturesWithDialogDisplay.requestActiveBiometric);
      expect(dialogHandler.isAllFeaturesWithDialogProcessed, isTrue);
    });
  });
}
