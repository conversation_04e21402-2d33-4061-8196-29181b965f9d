import 'package:evoapp/feature/main_screen/main_screen_controller.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MainScreenSubPage', () {
    test('getByIndex returns correct enum for valid indices', () {
      expect(MainScreenSubPage.getByIndex(0), MainScreenSubPage.home);
      expect(MainScreenSubPage.getByIndex(1), MainScreenSubPage.cards);
      expect(MainScreenSubPage.getByIndex(2), MainScreenSubPage.payCard);
      expect(MainScreenSubPage.getByIndex(3), MainScreenSubPage.usage);
      expect(MainScreenSubPage.getByIndex(4), MainScreenSubPage.profile);
    });

    test('getByIndex returns home for invalid indices', () {
      expect(MainScreenSubPage.getByIndex(-1), MainScreenSubPage.home);
      expect(MainScreenSubPage.getByIndex(5), MainScreenSubPage.home);
      expect(MainScreenSubPage.getByIndex(100), MainScreenSubPage.home);
    });
  });
}