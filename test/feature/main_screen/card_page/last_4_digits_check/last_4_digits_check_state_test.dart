import 'package:evoapp/feature/main_screen/card_page/last_4_digits_check/last_4_digits_check_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Last4DigitsCheckState', () {
    test('States should be subtype of Last4DigitsCheckState', () {
      expect(Last4DigitsCheckInitial(), isA<Last4DigitsCheckState>());
      expect(Last4DigitsCheckLoading(), isA<Last4DigitsCheckState>());
      expect(Last4DigitsCheckSuccess(), isA<Last4DigitsCheckState>());
      expect(Last4DigitsCheckFailure(error: 'error'), isA<Last4DigitsCheckState>());
    });

    test('Last4DigitsCheckFailure should have correct properties', () {
      const String error = 'error';
      final Last4DigitsCheckFailure state = Last4DigitsCheckFailure(error: error);
      expect(state.error, error);
    });
  });
}
