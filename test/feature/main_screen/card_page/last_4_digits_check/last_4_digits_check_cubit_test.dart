import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/main_screen/card_page/last_4_digits_check/last_4_digits_check_cubit.dart';
import 'package:evoapp/feature/main_screen/card_page/last_4_digits_check/last_4_digits_check_state.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../constant.dart';

void main() {
  group('Last4DigitsCubit', () {
    blocTest<Last4DigitsCheckCubit, Last4DigitsCheckState>(
      'should emit [Last4DigitsCheckInitial] when input is empty',
      build: () => Last4DigitsCheckCubit(),
      act: (Last4DigitsCheckCubit cubit) => cubit.onInputChange(''),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <TypeMatcher<Last4DigitsCheckState>>[
        isA<Last4DigitsCheckInitial>(),
      ],
    );

    blocTest<Last4DigitsCheckCubit, Last4DigitsCheckState>(
      'should emit NONE when input is NOT empty',
      build: () => Last4DigitsCheckCubit(),
      act: (Last4DigitsCheckCubit cubit) => cubit.onInputChange('1234'),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <TypeMatcher<Last4DigitsCheckState>>[],
    );
  });
}
