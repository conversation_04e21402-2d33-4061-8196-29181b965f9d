import 'package:evoapp/feature/main_screen/card_page/models/card_state.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('isCardInactivated returns true for inactive and replaced', () {
    expect(CardState.inactive.isCardInactivated(), isTrue);
    expect(CardState.replaced.isCardInactivated(), isTrue);
    expect(CardState.active.isCardInactivated(), isFalse);
    expect(CardState.frozen.isCardInactivated(), isFalse);
    expect(CardState.userBlocked.isCardInactivated(), isFalse);
    expect(CardState.bankBlocked.isCardInactivated(), isFalse);
  });

  test('isCardBlocked returns true for userBlocked and bankBlocked', () {
    expect(CardState.userBlocked.isCardBlocked(), isTrue);
    expect(CardState.bankBlocked.isCardBlocked(), isTrue);
    expect(CardState.active.isCardBlocked(), isFalse);
    expect(CardState.inactive.isCardBlocked(), isFalse);
    expect(CardState.frozen.isCardBlocked(), isFalse);
    expect(CardState.replaced.isCardBlocked(), isFalse);
  });

  test('isCardFreeze returns true for frozen', () {
    expect(CardState.frozen.isCardFreeze(), isTrue);
    expect(CardState.active.isCardFreeze(), isFalse);
    expect(CardState.inactive.isCardFreeze(), isFalse);
    expect(CardState.replaced.isCardFreeze(), isFalse);
    expect(CardState.userBlocked.isCardFreeze(), isFalse);
    expect(CardState.bankBlocked.isCardFreeze(), isFalse);
  });
}