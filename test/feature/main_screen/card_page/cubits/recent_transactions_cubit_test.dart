import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/main_screen/card_page/cubits/recent_transactions_cubit.dart';
import 'package:evoapp/feature/main_screen/card_page/cubits/recent_transactions_state.dart';
import 'package:evoapp/feature/main_screen/card_page/models/card_model.dart';
import 'package:evoapp/feature/main_screen/card_page/models/transaction_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCardModel extends Mock implements CardModel {}

void main() {
  late RecentTransactionsCubit cubit;
  late CardModel mockCard;

  setUp(() {
    cubit = RecentTransactionsCubit();
    mockCard = MockCardModel();
  });

  tearDown(() {
    cubit.close();
  });

  test('initial state is RecentTransactionsInitial', () {
    final RecentTransactionsCubit cubit = RecentTransactionsCubit();
    expect(cubit.state, isA<RecentTransactionsInitial>());
  });

  blocTest<RecentTransactionsCubit, RecentTransactionsState>(
    'emits [RecentTransactionsLoading, RecentTransactionsSuccess] when getTransactions is called',
    build: () => cubit,
    act: (RecentTransactionsCubit cubit) => cubit.getTransactions(mockCard),
    expect: () => <Matcher>[
      isA<RecentTransactionsLoading>(),
      isA<RecentTransactionsSuccess>(),
    ],
  );

  blocTest<RecentTransactionsCubit, RecentTransactionsState>(
    'emits [RecentTransactionsInitial] when reset is called',
    build: () => cubit,
    seed: () => RecentTransactionsSuccess(transactions: <TransactionModel>[]),
    act: (RecentTransactionsCubit cubit) => cubit.reset(),
    expect: () => <Matcher>[
      isA<RecentTransactionsInitial>(),
    ],
  );
}
