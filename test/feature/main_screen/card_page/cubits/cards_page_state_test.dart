import 'package:evoapp/feature/main_screen/card_page/cubits/cards_page_cubit.dart';
import 'package:evoapp/feature/main_screen/card_page/models/card_model.dart';
import 'package:evoapp/feature/main_screen/card_page/models/card_state.dart';
import 'package:evoapp/feature/main_screen/card_page/widgets/card_widget.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CardsPageState', () {
    test('Subclasses should be a CardsPageState', () {
      expect(CardPageInitial(), isA<CardsPageState>());
      expect(CardInfoLoading(), isA<CardsPageState>());
      expect(CardFreezeLoading(), isA<CardsPageState>());
    });

    test('CardCheckingLast4Digits should be a CardsPageState', () {
      expect(CardCheckingLast4Digits(), isA<CardsPageState>());
    });

    test('CardActivateSuccess should store card type and be a CardsPageState', () {
      const CardType cardType = CardType.virtual;
      final CardActivateSuccess state = CardActivateSuccess(cardType: cardType);
      expect(state, isA<CardsPageState>());
      expect(state.cardType, cardType);
    });

    test('CardActivateFailure should store error and be a CardsPageState', () {
      final ErrorUIModel error = ErrorUIModel(
          userMessage: 'An error occurred'); // Replace with actual ErrorUIModel initialization
      final CardActivateFailure state = CardActivateFailure(error: error);

      expect(state, isA<CardsPageState>());
      expect(state.error, error);
    });

    test('CardFreezeFailure should be a CardsPageState', () {
      expect(CardFreezeFailure(), isA<CardsPageState>());
    });

    test('CardInfoLoaded should store card info and be a CardsPageState', () {
      final CardModel card = CardModel(
        type: CardType.physical,
        visibility: CardDetailsVisibility.visible,
        state: CardState.active,
      );
      final CardInfoLoaded state = CardInfoLoaded(cardModel: card);

      expect(state, isA<CardsPageState>());
      expect(state.cardModel, card);
    });

    test('ShowCardInfoDetailedError should store error and be a CardsPageState', () {
      final ErrorUIModel error = ErrorUIModel(userMessage: 'An error occurred');
      final ShowCardInfoDetailedError state = ShowCardInfoDetailedError(error: error);

      expect(state, isA<CardsPageState>());
      expect(state.error, error);
    });
  });
}
