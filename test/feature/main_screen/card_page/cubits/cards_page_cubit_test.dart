import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/main_screen/card_page/cubits/cards_page_cubit.dart';
import 'package:evoapp/feature/main_screen/card_page/models/card_state.dart';
import 'package:evoapp/feature/main_screen/card_page/widgets/card_widget.dart';
import 'package:evoapp/feature/privilege_action/privilege_access_guard_module.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/get_card_information_handler.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/privilege_action_factory.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/unfreeze_card_handler.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';

class MockPrivilegeAccessGuardModule extends Mock implements PrivilegeAccessGuardModule {}

class MockPrivilegeAccessGuardCallback extends Mock implements PrivilegeAccessGuardCallback {}

void main() {
  late CardsPageCubit cubit;
  late PrivilegeAccessGuardModule guardModule;

  setUpAll(() {
    registerFallbackValue(MockPrivilegeAccessGuardCallback());
    registerFallbackValue(PrivilegeActionType.showCardInformation);
  });

  setUp(() {
    guardModule = MockPrivilegeAccessGuardModule();
    cubit = CardsPageCubit(privilegeAccessGuardModule: guardModule);

    when(() => guardModule.confirm(
          callback: any(named: 'callback'),
          type: any(named: 'type'),
        )).thenAnswer((_) async {});
  });

  group('CardsPageCubit', () {
    blocTest<CardsPageCubit, CardsPageState>(
      'should emit CardCheckingLast4Digits when calling onVerifyOtpSuccess with physical card',
      setUp: () {
        cubit.cardType = CardType.physical;
      },
      build: () => cubit,
      act: (CardsPageCubit cubit) => cubit.onVerifyOtpSuccess(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<CardCheckingLast4Digits>(),
      ],
    );

    blocTest<CardsPageCubit, CardsPageState>(
      'should emit CardActivateSuccess when calling onVerifyOtpSuccess with virtual card',
      setUp: () {
        cubit.cardType = CardType.virtual;
      },
      build: () => cubit,
      act: (CardsPageCubit cubit) => cubit.onVerifyOtpSuccess(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<CardActivateSuccess>().having(
          (CardActivateSuccess state) => state.cardType,
          'card type should be virtual',
          CardType.virtual,
        ),
        isA<CardsPageState>(),
      ],
    );

    blocTest<CardsPageCubit, CardsPageState>(
      'should emit CardActivateSuccess when calling onCheckLast4DigitsSuccess',
      build: () => cubit,
      act: (CardsPageCubit cubit) => cubit.onCheckLast4DigitsSuccess(),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<CardActivateSuccess>().having(
          (CardActivateSuccess state) => state.cardType,
          'card type should be physical',
          CardType.physical,
        ),
        isA<CardsPageState>(),
      ],
    );

    test('initializeCardType() should update card type', () {
      cubit.initializeCardType(cardType: CardType.virtual);
      expect(cubit.cardType, CardType.virtual);

      cubit.initializeCardType(cardType: CardType.physical);
      expect(cubit.cardType, CardType.physical);
    });

    blocTest<CardsPageCubit, CardsPageState>(
      'initializeCardType should emit [CardInfoLoading, CardInfoLoaded]',
      build: () => cubit,
      act: (CardsPageCubit cubit) => cubit.initializeCardType(cardType: CardType.virtual),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<CardInfoLoading>(),
        isA<CardInfoLoaded>(),
      ],
    );

    blocTest<CardsPageCubit, CardsPageState>(
      'onPrivilegeActionError() should emit CardActivateFailure',
      build: () => cubit,
      act: (CardsPageCubit cubit) => cubit.onPrivilegeActionError(ErrorUIModel()),
      wait: TestConstant.blocEmitStateDelayDuration,
      expect: () => <Matcher>[
        isA<CardActivateFailure>(),
      ],
    );
  });

  group('Card Details Visibility', () {
    test('toggleCardDetailsVisibility() should update visibility from visible to hidden', () async {
      cubit.visibility = CardDetailsVisibility.visible;

      cubit.toggleCardDetailsVisibility();

      expect(cubit.visibility, CardDetailsVisibility.hidden);
    });

    test(
        'toggleCardDetailsVisibility() should call PrivilegeAccessGuardModule when updating visibility from hidden to visible',
        () async {
      when(() => guardModule.confirm(
            callback: any(named: 'callback'),
            type: any(named: 'type'),
          )).thenAnswer((_) async {});
      cubit.visibility = CardDetailsVisibility.hidden;

      cubit.toggleCardDetailsVisibility();

      verify(() => guardModule.confirm(
            callback: cubit,
            type: PrivilegeActionType.showCardInformation,
          )).called(1);
    });

    test('hideCardDetails() should update visibility to hidden', () async {
      cubit.visibility = CardDetailsVisibility.visible;

      cubit.hideCardDetails();

      expect(cubit.visibility, CardDetailsVisibility.hidden);
    });

    test(
        'onPrivilegeActionSuccess() should update visibility to visible when response is GetCardInformationResponse',
        () async {
      cubit.visibility = CardDetailsVisibility.hidden;

      cubit.onPrivilegeActionSuccess(GetCardInformationResponse(
        cardNumber: 'cardNumber',
        cardHolderName: 'cardHolderName',
        cardExpiryDate: 'cardExpiryDate',
        cvv: 'cvv',
      ));

      expect(cubit.visibility, CardDetailsVisibility.visible);
    });
  });

  group('Freeze/Unfreeze card', () {
    blocTest<CardsPageCubit, CardsPageState>(
      'onPrivilegeActionSuccess() update card when response is UnfreezeCardActionData',
      build: () => cubit,
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (CardsPageCubit cubit) => cubit.onPrivilegeActionSuccess(UnfreezeCardActionData()),
      expect: () => <Matcher>[
        isA<CardInfoLoaded>(),
      ],
    );

    blocTest<CardsPageCubit, CardsPageState>(
      'toggleCardFreezeStatus() should freeze and unfreeze a virtual card',
      build: () => cubit..cardType = CardType.virtual,
      seed: () => CardInfoLoaded(
        cardModel: cardData.get(CardType.virtual).copyWith(state: CardState.active),
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (CardsPageCubit cubit) async {
        await cubit.toggleCardFreezeStatus();
        await cubit.toggleCardFreezeStatus();
      },
      expect: () => <Matcher>[
        isA<CardFreezeLoading>(),
        isA<CardInfoLoaded>(),
      ],
      verify: (_) {
        verify(() => guardModule.confirm(callback: cubit, type: PrivilegeActionType.unfreezeCard))
            .called(1);
      },
    );

    blocTest<CardsPageCubit, CardsPageState>(
      'toggleCardFreezeStatus() should fail to freeze a physical card',
      build: () => cubit..cardType = CardType.physical,
      seed: () => CardInfoLoaded(
        cardModel: cardData.get(CardType.physical).copyWith(state: CardState.active),
      ),
      wait: TestConstant.blocEmitStateDelayDuration,
      act: (CardsPageCubit cubit) async {
        await cubit.toggleCardFreezeStatus();
      },
      expect: () => <Matcher>[
        isA<CardFreezeFailure>(),
      ],
    );
  });
}
