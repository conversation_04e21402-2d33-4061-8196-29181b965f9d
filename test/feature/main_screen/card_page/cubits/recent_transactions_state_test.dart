import 'package:evoapp/feature/main_screen/card_page/cubits/recent_transactions_state.dart';
import 'package:evoapp/feature/main_screen/card_page/models/transaction_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('RecentTransactionsState', () {
    test('Subclasses should extend RecentTransactionsState', () {
      expect(RecentTransactionsInitial(), isA<RecentTransactionsState>());
      expect(RecentTransactionsLoading(), isA<RecentTransactionsState>());
    });

    test('RecentTransactionsSuccess should contain transactions list', () {
      final TransactionModel model = TransactionModel(
          merchantName: '', purchaseAt: '', amount: 0, fourLastDigitOfCardNumberUsed: '');
      final List<TransactionModel> transactions = <TransactionModel>[model, model];

      final RecentTransactionsSuccess state = RecentTransactionsSuccess(
        transactions: transactions,
      );

      expect(state, isA<RecentTransactionsState>());
      expect(state.transactions, equals(transactions));
      expect(state.transactions.length, equals(2));
    });
  });
}
