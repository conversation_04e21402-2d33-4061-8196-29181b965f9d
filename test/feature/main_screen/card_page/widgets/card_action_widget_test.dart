import 'package:evoapp/feature/main_screen/card_page/widgets/card_action_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/widget/elevated_container.dart';
import 'package:evoapp/widget/evo_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  const double mockHeight = 10;
  const double mockWidth = 50;

  late EvoUtilFunction mockEvoUtilFunction;
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    registerFallbackValue(MockContext());

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    registerFallbackValue(BoxFit.scaleDown);

    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();

    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();
    when(() => mockEvoUtilFunction.calculateVerticalSpace(
          context: any(named: 'context'),
          heightPercentage: any(named: 'heightPercentage'),
        )).thenReturn(mockHeight);

    when(() => mockEvoUtilFunction.calculateHorizontalSpace(
          context: any(named: 'context'),
          widthPercentage: any(named: 'widthPercentage'),
        )).thenReturn(mockWidth);
  });

  setUp(() {
    when(() => mockCommonImageProvider.asset(
          any(),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
        )).thenAnswer((_) => Container());
  });

  tearDown(() {
    reset(mockCommonImageProvider);
  });

  testWidgets('verify order of child', (WidgetTester tester) async {
    // Arrange
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: CardActionWidget(),
        ),
      ),
    );

    // Find root widgets
    final Finder rowFinder = find.byType(Row).first;
    final Row rowWidget = tester.widget<Row>(rowFinder);

    // Extract children from the Row
    final List<Widget> children = rowWidget.children;

    // Assert
    expect(children.length, 3); // Two Expanded widgets and one SizedBox

    // Check types and order
    expect(children[0], isA<Expanded>());

    expect(children[1], isA<SizedBox>());
    expect((children[1] as SizedBox).width, 8);

    expect(children[2], isA<Expanded>());
  });

  testWidgets('should display correct child widgets', (WidgetTester tester) async {
    // Arrange
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: CardActionWidget(),
        ),
      ),
    );

    // Act & Assert
    expect(find.byType(EvoSwitch), findsOneWidget);
    expect(find.text(EvoStrings.ctaFreezeCard),
        findsOneWidget); // Assuming EvoStrings.ctaFreezeCard = 'Freeze Card'

    expect(find.byType(SizedBox), findsWidgets); // To check for SizedBox used for spacing

    //Assert Show Details button
    expect(find.text(EvoStrings.ctaShowDetails), findsOneWidget);
    when(() => mockCommonImageProvider.asset(
          EvoImages.icEyeVisibilityOn,
          color: evoColors.accent90,
          fit: BoxFit.scaleDown,
        )).thenAnswer((_) => Container());
  });

  testWidgets('should trigger onFreezeCardClick callback', (WidgetTester tester) async {
    // Arrange
    bool freezeCardClicked = false;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: CardActionWidget(
            onFreezeCardClick: () {
              freezeCardClicked = true;
            },
          ),
        ),
      ),
    );

    // find the ElevatedContainer that contains the text 'Freeze Card'
    final Finder freezeCardTextFinder = find.text(EvoStrings.ctaFreezeCard);
    final Finder elevatedContainerFinder = find.ancestor(
      of: freezeCardTextFinder,
      matching: find.byType(ElevatedContainer),
    );

    await tester.tap(elevatedContainerFinder);
    await tester.pumpAndSettle();

    // Assert
    expect(freezeCardClicked, isTrue);
  });

  testWidgets('should trigger onShowDetailsClick callback', (WidgetTester tester) async {
    // Arrange
    bool showDetailsClicked = false;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: CardActionWidget(
            onShowDetailsClick: () {
              showDetailsClicked = true;
            },
          ),
        ),
      ),
    );

    // find the ElevatedContainer that contains the text 'Show Details'
    final Finder freezeCardTextFinder = find.text(EvoStrings.ctaShowDetails);
    final Finder elevatedContainerFinder = find.ancestor(
      of: freezeCardTextFinder,
      matching: find.byType(ElevatedContainer),
    );

    await tester.tap(elevatedContainerFinder);
    await tester.pumpAndSettle();

    // Assert
    expect(showDetailsClicked, isTrue);
  });

  group('verify UI based on freezeCardState', () {
    testWidgets('should display correct child widgets if freezeCardState = FreezeCardState.freeze', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CardActionWidget(
              freezeCardState: FreezeCardState.freeze,
            ),
          ),
        ),
      );

      // Assert show Evo switch
      expect(find.byType(EvoSwitch), findsOneWidget);
      final EvoSwitch evoSwitchWidget = tester.widget(find.byType(EvoSwitch));
      expect(evoSwitchWidget.value, true);

      //Assert Show Details button
      final Text text = tester.widget(find.text(EvoStrings.ctaShowDetails));
      expect(text.style?.color, evoColors.greyScale70);

      verify(() => mockCommonImageProvider.asset(
        EvoImages.icEyeVisibilityOn,
        color: evoColors.greyScale70,
        fit: BoxFit.scaleDown,
      )).called(1);
    });

    testWidgets('should display correct child widgets if freezeCardState = FreezeCardState.unfreeze', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: CardActionWidget(),
          ),
        ),
      );

      // Assert show Evo switch
      expect(find.byType(EvoSwitch), findsOneWidget);
      final EvoSwitch evoSwitchWidget = tester.widget(find.byType(EvoSwitch));
      expect(evoSwitchWidget.value, false);

      //Assert Show Details button
      final Text text = tester.widget(find.text(EvoStrings.ctaShowDetails));
      expect(text.style?.color, evoColors.textNormal);

      verify(() => mockCommonImageProvider.asset(
        EvoImages.icEyeVisibilityOn,
        color: evoColors.accent90,
        fit: BoxFit.scaleDown,
      )).called(1);
    });

  });
}
