import 'package:evoapp/feature/main_screen/card_page/models/card_information_model.dart';
import 'package:evoapp/feature/main_screen/card_page/widgets/active_card_widget.dart';
import 'package:evoapp/feature/main_screen/card_page/widgets/card_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/device_info_plugin_wrapper/device_info_plugin_wrapper.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockDeviceInfoPluginWrapper extends Mock implements DeviceInfoPluginWrapper {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class ActiveCardWidgetTestHelper {
  static void setupGetItRegister() {
    getIt.registerSingleton<DeviceInfoPluginWrapper>(MockDeviceInfoPluginWrapper());
    getIt.registerSingleton<EvoSnackBar>(MockEvoSnackBar());
  }
}

void main() {
  final CardInformationModel mockCardInformation = CardInformationModel(
    cardNumber: '1234 5678 9012 3456',
    expiryDate: '12/34',
    cvv: '123',
    cardHolderName: 'John Doe',
  );

  late CommonImageProvider mockImageProvider;

  setUpAll(() {
    registerFallbackValue(BoxFit.scaleDown);

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    mockImageProvider = getIt.get<CommonImageProvider>();

    ActiveCardWidgetTestHelper.setupGetItRegister();

    getItRegisterTextStyle();
    getItRegisterColor();
  });

  setUp(() {
    when(
      () => mockImageProvider.asset(
        any(),
        width: any(named: 'width'),
        height: any(named: 'height'),
        fit: any(named: 'fit'),
      ),
    ).thenReturn(Container()); // Mock the image widget
  });

  tearDown(() {
    reset(mockImageProvider);
  });

  group('verify widget with CardType = CardType.virtual', () {
    testWidgets('ActiveCardWidget displays card information correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ActiveCardWidget(
              cardInformationModel: mockCardInformation,
              visibility: CardDetailsVisibility.visible,
              cardType: CardType.virtual,
            ),
          ),
        ),
      );

      expect(find.text('1234 5678 9012 3456'), findsOneWidget);
      expect(find.text('12/34'), findsOneWidget);
      expect(find.text('123'), findsOneWidget);
      expect(find.text('John Doe'), findsOneWidget);
    });

    testWidgets('ActiveCardWidget displays virtual card label', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ActiveCardWidget(
              cardInformationModel: mockCardInformation,
              visibility: CardDetailsVisibility.visible,
              cardType: CardType.virtual,
            ),
          ),
        ),
      );

      expect(find.text('Virtual Card'), findsOneWidget);
    });

    testWidgets('ActiveCardWidget displays background image', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ActiveCardWidget(
              cardInformationModel: mockCardInformation,
              visibility: CardDetailsVisibility.visible,
              cardType: CardType.virtual,
            ),
          ),
        ),
      );

      verify(() => mockImageProvider.asset(EvoImages.frameVirtualCardDetails, fit: BoxFit.fill))
          .called(1);
    });

    testWidgets('ActiveCardWidget applies correct text styles', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ActiveCardWidget(
              cardInformationModel: mockCardInformation,
              visibility: CardDetailsVisibility.visible,
              cardType: CardType.virtual,
            ),
          ),
        ),
      );

      // Verify the text and styles
      final Text virtualCardText = tester.widget<Text>(find.text(EvoStrings.virtualCard));
      expect(virtualCardText.style,
          evoTextStyles.regular(TextSize.base, color: evoColors.defaultWhite));

      final Text cardNumberLabelText = tester.widget<Text>(find.text(EvoStrings.cardNumber));
      expect(cardNumberLabelText.style,
          evoTextStyles.regular(TextSize.sm, color: evoColors.greyScale75));

      final Text cardNumberText = tester.widget<Text>(find.text(mockCardInformation.cardNumber));
      expect(cardNumberText.style, evoTextStyles.bold(TextSize.xl, color: evoColors.defaultWhite));

      verify(() => mockImageProvider.asset(EvoImages.icCopy,
          width: any(named: 'width'), height: any(named: 'height'), fit: BoxFit.fill)).called(1);

      final Text expiryDateLabelText = tester.widget<Text>(find.text(EvoStrings.expiryDate));
      expect(expiryDateLabelText.style,
          evoTextStyles.regular(TextSize.sm, color: evoColors.greyScale75));

      final Text expiryDateText = tester.widget<Text>(find.text(mockCardInformation.expiryDate));
      expect(
          expiryDateText.style, evoTextStyles.bold(TextSize.base, color: evoColors.defaultWhite));

      final Text cvvLabelText = tester.widget<Text>(find.text(EvoStrings.cvv));
      expect(cvvLabelText.style, evoTextStyles.regular(TextSize.sm, color: evoColors.greyScale75));

      final Text cvvText = tester.widget<Text>(find.text(mockCardInformation.cvv));
      expect(cvvText.style, evoTextStyles.bold(TextSize.base, color: evoColors.defaultWhite));

      final Text nameOnCardLabelText = tester.widget<Text>(find.text(EvoStrings.nameOnCard));
      expect(nameOnCardLabelText.style,
          evoTextStyles.regular(TextSize.sm, color: evoColors.greyScale75));

      final Text cardHolderNameText =
          tester.widget<Text>(find.text(mockCardInformation.cardHolderName));
      expect(cardHolderNameText.style,
          evoTextStyles.bold(TextSize.base, color: evoColors.defaultWhite));
    });

    testWidgets(
      'ActiveCardWidget triggers fade in/out animation when visibility changes',
      (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ActiveCardWidget(
                  cardInformationModel: mockCardInformation,
                  visibility: CardDetailsVisibility.hidden,
                  cardType: CardType.virtual),
            ),
          ),
        );

        expect(find.text('1234 5678 9012 3456'), findsNothing);

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ActiveCardWidget(
                  cardInformationModel: mockCardInformation,
                  visibility: CardDetailsVisibility.visible,
                  cardType: CardType.virtual),
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('1234 5678 9012 3456'), findsOneWidget);
      },
    );

    testWidgets(
      'ActiveCardWidget hides card details when visibility is hidden',
      (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ActiveCardWidget(
                cardInformationModel: mockCardInformation,
                visibility: CardDetailsVisibility.hidden,
                cardType: CardType.virtual,
              ),
            ),
          ),
        );

        // Verify that card details are not visible
        expect(find.text('1234 5678 9012 3456'), findsNothing);
        expect(find.text('12/34'), findsNothing);
        expect(find.text('123'), findsNothing);
        expect(find.text('John Doe'), findsNothing);

        // Verify that the virtual card label is still visible
        expect(find.text('Virtual Card'), findsNothing);

        // Verify that the background image is displayed
        verify(() => mockImageProvider.asset(EvoImages.frameVirtualCard, fit: BoxFit.fill))
            .called(1);

        final Text labelText = tester.widget<Text>(find.text('···· 1234'));
        expect(labelText.style, evoTextStyles.bold(TextSize.base, color: evoColors.defaultWhite));
      },
    );
  });

  group('verify widget with CardType = CardType.virtual', () {
    testWidgets('ActiveCardWidget displays physical card label', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ActiveCardWidget(
              cardInformationModel: mockCardInformation,
              visibility: CardDetailsVisibility.visible,
              cardType: CardType.physical,
            ),
          ),
        ),
      );

      expect(find.text('Physical Card'), findsOneWidget);
    });

    testWidgets('ActiveCardWidget displays physical card background image',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ActiveCardWidget(
              cardInformationModel: mockCardInformation,
              visibility: CardDetailsVisibility.visible,
              cardType: CardType.physical,
            ),
          ),
        ),
      );

      verify(() => mockImageProvider.asset(EvoImages.framePhysicalCardDetails, fit: BoxFit.fill))
          .called(1);
    });

    testWidgets('ActiveCardWidget applies correct text styles for physical card',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ActiveCardWidget(
              cardInformationModel: mockCardInformation,
              visibility: CardDetailsVisibility.visible,
              cardType: CardType.physical,
            ),
          ),
        ),
      );

      // Verify the text and styles
      final Text cardNumberLabelText = tester.widget<Text>(find.text(EvoStrings.cardNumber));
      expect(cardNumberLabelText.style,
          evoTextStyles.regular(TextSize.sm, color: evoColors.greyScale60));

      final Text expiryDateLabelText = tester.widget<Text>(find.text(EvoStrings.expiryDate));
      expect(expiryDateLabelText.style,
          evoTextStyles.regular(TextSize.sm, color: evoColors.greyScale60));

      final Text cvvLabelText = tester.widget<Text>(find.text(EvoStrings.cvv));
      expect(cvvLabelText.style, evoTextStyles.regular(TextSize.sm, color: evoColors.greyScale60));

      final Text nameOnCardLabelText = tester.widget<Text>(find.text(EvoStrings.nameOnCard));
      expect(nameOnCardLabelText.style,
          evoTextStyles.regular(TextSize.sm, color: evoColors.greyScale60));
    });
  });
}
