import 'package:evoapp/feature/main_screen/card_page/widgets/card_toggle_button.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();
  });

  void assertAllButtonStyle(
      {required CommonFinders find, required WidgetTester tester, required List<String> titles}) {
    final CardToggleButtonState cardToggleButtonState =
        tester.state<CardToggleButtonState>(find.byType(CardToggleButton));

    final Finder commonButtonFinder = find.byType(CommonButton);
    expect(commonButtonFinder, findsNWidgets(titles.length));

    for (int i = 0; i < titles.length; i++) {
      final CommonButton button = tester.widget<CommonButton>(commonButtonFinder.at(i));
      final bool isSelected = cardToggleButtonState.selectedIndex == i;
      final ButtonStyle style = button.style;

      // Verify the foreground color
      final Color expectedForegroundColor =
          isSelected ? evoColors.defaultWhite : evoColors.greyScale100;
      expect(style.foregroundColor?.resolve(<WidgetState>{}), expectedForegroundColor);

      // Verify the background color
      final Color expectedBackgroundColor =
          isSelected ? evoColors.greyScale100 : evoColors.utilityButtonBg;
      expect(style.backgroundColor?.resolve(<WidgetState>{}), expectedBackgroundColor);

      // Verify the text style
      final TextStyle expectedTextStyle = evoTextStyles.bold(TextSize.sm);
      expect(style.textStyle?.resolve(<WidgetState>{}), expectedTextStyle);

      // Verify the overlay color
      final Color expectedOverlayColor = evoColors.greyScale90;
      expect(style.overlayColor?.resolve(<WidgetState>{}), expectedOverlayColor);
    }
  }

  testWidgets('returns correct ButtonStyle for selected and unselected index',
      (WidgetTester tester) async {
    // Arrange
    final List<String> titles = <String>['Button 1', 'Button 2', 'Button 3'];

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: CardToggleButton(titleButtons: titles),
        ),
      ),
    );

    assertAllButtonStyle(find: find, tester: tester, titles: titles);
  });

  testWidgets('selects button and updates style on press', (WidgetTester tester) async {
    // Arrange
    final List<String> titles = <String>['Button 1', 'Button 2', 'Button 3'];

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: CardToggleButton(titleButtons: titles),
        ),
      ),
    );

    // Act
    // Tap the second button
    await tester.tap(find.text('Button 2'));
    await tester.pumpAndSettle();

    // Assert
    // Verify that the second button is now selected
    final CardToggleButtonState cardToggleButtonState =
        tester.state<CardToggleButtonState>(find.byType(CardToggleButton));
    expect(cardToggleButtonState.selectedIndex, 1);

    assertAllButtonStyle(find: find, tester: tester, titles: titles);
  });

  testWidgets('applies correct padding to buttons', (WidgetTester tester) async {
    // Arrange
    final List<String> titles = <String>['Button 1', 'Button 2', 'Button 3'];

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: CardToggleButton(titleButtons: titles),
        ),
      ),
    );

    final Finder commonButtonFinder = find.byType(CommonButton);
    expect(commonButtonFinder, findsNWidgets(titles.length));

    for (int i = 0; i < titles.length; i++) {
      final EdgeInsetsGeometry buttonPadding = tester
          .widget<Padding>(
              find.ancestor(of: commonButtonFinder.at(i), matching: find.byType(Padding)))
          .padding;

      if (i == titles.length - 1) {
        expect(buttonPadding, EdgeInsets.zero);
      } else {
        expect(buttonPadding, const EdgeInsets.only(right: 8));
      }
    }
  });
}
