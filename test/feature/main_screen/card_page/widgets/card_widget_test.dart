import 'package:evoapp/feature/main_screen/card_page/models/card_state.dart';
import 'package:evoapp/feature/main_screen/card_page/widgets/active_card_widget.dart';
import 'package:evoapp/feature/main_screen/card_page/widgets/card_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';
import 'active_card_widget_test.dart';

void main() {
  late CommonImageProvider mockCommonImageProvider;
  setUpAll(() {
    registerFallbackValue(BoxFit.scaleDown);

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    ActiveCardWidgetTestHelper.setupGetItRegister();

    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();
  });

  setUp(() {
    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
        )).thenAnswer((_) => Container());
  });

  AspectRatio getRootWidget(WidgetTester tester) {
    // Find the AspectRatio widget
    final Finder aspectRatioFinder = find.byType(AspectRatio);
    // Verify that there is one AspectRatio widget
    expect(aspectRatioFinder, findsOneWidget);

    // Access the AspectRatio widget
    return tester.widget<AspectRatio>(aspectRatioFinder);
  }

  /// Skeleton card widget must follow below tree structure:
  /// AspectRatio(
  ///   Stack[
  ///     Positioned.fill(),
  ///     ....
  ///   ],
  /// )
  void assertSkeletonCardWidget(AspectRatio aspectRatioWidget) {
    // Verify the aspect ratio value
    expect(aspectRatioWidget.aspectRatio, 342 / 216);

    // Verify that the child of AspectRatio is a Stack
    expect(aspectRatioWidget.child, isA<Stack>());

    // Access the Stack widget
    final Stack stackWidget = aspectRatioWidget.child as Stack;

    // Verify the first child of the Stack
    // Assuming the first child is a Positioned widget with a background image
    expect(stackWidget.children.first, isA<Positioned>());

    final Positioned positionedWidget = stackWidget.children.first as Positioned;
    expect(positionedWidget.top, 0.0);
    expect(positionedWidget.left, 0.0);
    expect(positionedWidget.right, 0.0);
    expect(positionedWidget.bottom, 0.0);
    expect(positionedWidget.width, null);
    expect(positionedWidget.height, null);
  }

  /// Assert the default text position and padding of the Text Widget if Card State is in
  /// [Active], [Inactive], or [Frozen] state.
  void assertDefaultTextOfCardWidget(AspectRatio aspectRatioWidget) {
    final Stack stackWidget = aspectRatioWidget.child as Stack;
    final Positioned positionedWidget = stackWidget.children[1] as Positioned;
    expect(positionedWidget.left, 1);
    expect(positionedWidget.bottom, 1);
    expect(positionedWidget.top, null);
    expect(positionedWidget.right, null);
    expect(positionedWidget.width, null);
    expect(positionedWidget.height, null);

    final Padding textPadding = positionedWidget.child as Padding;
    expect(textPadding.padding, const EdgeInsets.only(left: 24, bottom: 14));
  }

  /// verify card image rendered with the correct card type
  verifyImageWithCardType(CardType cardType) {
    final String backgroundImage = switch (cardType) {
      CardType.virtual => EvoImages.frameVirtualCardInactive,
      CardType.physical => EvoImages.framePhysicalCardInactive,
    };

    verify(() => mockCommonImageProvider.asset(
          backgroundImage,
          fit: BoxFit.fill,
        )).called(1);
  }

  testWidgets('verify render correctly when card state is inactive or replaced ',
      (WidgetTester tester) async {
    for (final CardState cardState in <CardState>[
      CardState.inactive,
      CardState.replaced,
    ]) {
      for (final CardType cardType in CardType.values) {
        await tester.pumpWidget(
          MaterialApp(
            home: CardWidget(
              cardState: cardState,
              cardType: cardType,
            ),
          ),
        );

        final AspectRatio rootWidget = getRootWidget(tester);
        assertSkeletonCardWidget(rootWidget);

        /// Verify the background image for active state
        verifyImageWithCardType(cardType);

        /// Verify the default text position and padding of the Text Widget
        assertDefaultTextOfCardWidget(rootWidget);

        ///  Verify the title text for inactive state
        expect(find.text(EvoStrings.cardStateInactiveTitle), findsOneWidget);
      }
    }
  });

  testWidgets('displays frozen card correctly', (WidgetTester tester) async {
    for (final CardType cardType in CardType.values) {
      await tester.pumpWidget(
        MaterialApp(
          home: CardWidget(
            cardState: CardState.frozen,
            cardType: cardType,
          ),
        ),
      );

      final AspectRatio rootWidget = getRootWidget(tester);
      assertSkeletonCardWidget(rootWidget);

      /// Verify the background image for active state
      verifyImageWithCardType(cardType);

      /// Verify the default text position and padding of the Text Widget
      assertDefaultTextOfCardWidget(rootWidget);

      // Verify the title text for frozen state
      expect(find.text(EvoStrings.cardStateFrozenTitle), findsOneWidget);
    }
  });

  group('verify Active Card frame', () {
    void assertSkeletonActiveCardWidget(AspectRatio aspectRatioWidget) {
      // Verify the aspect ratio value
      expect(aspectRatioWidget.aspectRatio, 342 / 216);

      // Verify that the child of AspectRatio is a Stack
      expect(aspectRatioWidget.child, isA<ActiveCardWidget>());
    }

    testWidgets('displays active card with hidden details correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: CardWidget(
            cardState: CardState.active,
            cardType: CardType.virtual,
            cardDetailsVisibility: CardDetailsVisibility.hidden,
            fourLastDigitNumbers: '1234',
          ),
        ),
      );

      final AspectRatio rootWidget = getRootWidget(tester);
      assertSkeletonActiveCardWidget(rootWidget);
    });

    testWidgets('displays active card with visible details correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: CardWidget(
            cardState: CardState.active,
            cardType: CardType.virtual,
            cardDetailsVisibility: CardDetailsVisibility.visible,
          ),
        ),
      );

      final AspectRatio rootWidget = getRootWidget(tester);
      assertSkeletonActiveCardWidget(rootWidget);
    });

    testWidgets('should render correct UI when card is blocked by user or bank',
        (WidgetTester tester) async {
      for (final CardState cardState in <CardState>[
        CardState.userBlocked,
        CardState.bankBlocked,
      ]) {
        for (final CardType cardType in CardType.values) {
          await tester.pumpWidget(
            MaterialApp(
              home: CardWidget(
                cardState: cardState,
                cardType: cardType,
              ),
            ),
          );

          final AspectRatio rootWidget = getRootWidget(tester);
          assertSkeletonCardWidget(rootWidget);
          verifyImageWithCardType(cardType);
          assertDefaultTextOfCardWidget(rootWidget);
          expect(find.text(EvoStrings.cardStateBlockedTitle), findsOneWidget);
        }
      }
    });
  });
}
