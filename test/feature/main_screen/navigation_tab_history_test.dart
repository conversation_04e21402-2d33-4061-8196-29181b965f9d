import 'package:evoapp/feature/main_screen/main_screen_controller.dart';
import 'package:evoapp/feature/main_screen/navigation_tab_history.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('size of stack = 2', () {
    const int defaultSize = 2;
    const MainScreenSubPage defaultPage = MainScreenSubPage.home;
    late NavigationHistoryStack historyStack;
    setUp(() {
      historyStack = NavigationHistoryStack(size: defaultSize, defaultPage: defaultPage);
    });

    test('push() correctly if stack size not exceed limited size', () {
      historyStack.push(MainScreenSubPage.cards);

      expect(historyStack.isEmpty, false);
      expect(historyStack.top, MainScreenSubPage.cards);
      expect(historyStack.length, 1);

      historyStack.push(MainScreenSubPage.usage);
      expect(historyStack.first, MainScreenSubPage.cards);
      expect(historyStack.top, MainScreenSubPage.usage);
      expect(historyStack.length, defaultSize);
    });

    test('stack always store 2 newest page which pushed', () {
      historyStack.push(MainScreenSubPage.cards);
      historyStack.push(MainScreenSubPage.usage);
      historyStack.push(MainScreenSubPage.cards);

      expect(historyStack.first, MainScreenSubPage.usage);
      expect(historyStack.top, MainScreenSubPage.cards);
      expect(historyStack.length, defaultSize);
    });

    test('do not allow push value equal top value', () {
      historyStack.push(MainScreenSubPage.cards);
      historyStack.push(MainScreenSubPage.cards);

      expect(historyStack.length, 1);
      expect(historyStack.top, MainScreenSubPage.cards);
    });

    test('back() return value correctly if stack is not empty', () {
      //prepare
      historyStack.push(MainScreenSubPage.cards);
      historyStack.push(MainScreenSubPage.usage);
      expect(historyStack.length, defaultSize);

      final MainScreenSubPage currentValue = historyStack.pop();
      expect(historyStack.length, 1);
      expect(currentValue, MainScreenSubPage.cards);
    });

    test('back() return value correctly if stack is empty', () {
      //prepare
      historyStack.push(MainScreenSubPage.cards);
      historyStack.push(MainScreenSubPage.usage);
      expect(historyStack.length, defaultSize);

      // action
      final MainScreenSubPage currentValue = historyStack.pop();
      expect(historyStack.length, 1);
      expect(currentValue, MainScreenSubPage.cards);

      // action
      final MainScreenSubPage lastValue = historyStack.pop();
      expect(historyStack.length, 0);
      expect(lastValue, defaultPage);
    });
  });

  group('case is size of stack = 0', () {
    const MainScreenSubPage defaultPage = MainScreenSubPage.home;

    test('size of stack is invalid  (size < 1)', () {
      expect(() => NavigationHistoryStack(size: 0, defaultPage: defaultPage),
          throwsA(isA<AssertionError>()));
      expect(() => NavigationHistoryStack(size: -1, defaultPage: defaultPage),
          throwsA(isA<AssertionError>()));
    });
  });
}
