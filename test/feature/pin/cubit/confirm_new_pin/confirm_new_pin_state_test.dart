import 'package:evoapp/feature/pin/cubit/confirm_new_pin/confirm_new_pin_cubit.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('should be subtype of ConfirmResetPinState', () {
    expect(ConfirmInitialState(), isA<ConfirmNewPinState>());
    expect(ConfirmLoadingState(), isA<ConfirmNewPinState>());
    expect(PinUnmatchedState(), isA<ConfirmNewPinState>());
    expect(ConfirmNewPinSessionExpired(), isA<ConfirmNewPinState>());
    expect(ConfirmNewPinBadRequest(error: ErrorUIModel()), isA<ConfirmNewPinState>());
    expect(ConfirmNewPinFailure(error: ErrorUIModel()), isA<ConfirmNewPinState>());
    expect(ConfirmNewPinSuccess(BaseEntity()), isA<ConfirmNewPinState>());
  });

  test('verify ConfirmResetPinBadRequest should hold correct data', () {
    final ErrorUIModel error = ErrorUIModel();
    final ConfirmNewPinBadRequest state = ConfirmNewPinBadRequest(error: error);
    expect(state.error, error);
  });

  test('verify ConfirmResetPinFailure should hold correct data', () {
    final ErrorUIModel error = ErrorUIModel();
    final ConfirmNewPinFailure state = ConfirmNewPinFailure(error: error);
    expect(state.error, error);
  });

  test('verify ConfirmNewPinSuccess should hold correct data', () {
    final BaseEntity entity = BaseEntity();
    final ConfirmNewPinSuccess state = ConfirmNewPinSuccess(entity);
    expect(state.entity, entity);
  });
}
