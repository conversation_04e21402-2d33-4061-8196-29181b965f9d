import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/request/change_pin_request.dart';
import 'package:evoapp/data/request/reset_pin_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/data/response/change_pin_entity.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/feature/pin/change_pin/create_new_pin_flow.dart';
import 'package:evoapp/feature/pin/cubit/confirm_new_pin/confirm_new_pin_cubit.dart';
import 'package:evoapp/feature/pin/mock/mock_pin_use_case.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../constant.dart';
import '../../../../util/test_util.dart';

class MockUserRepo extends Mock implements UserRepo {}

class MockAuthRepo extends Mock implements AuthenticationRepo {}

void main() {
  late ConfirmNewPinCubit cubit;
  late UserRepo mockUserRepo;
  late AuthenticationRepo mockAuthRepo;
  const String mockPin = 'mock-pin';
  const String mockSessionToken = 'mock-session-token';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(VerifyCurrentPinRequest(pin: '', sessionToken: ''));
    registerFallbackValue(
      ResetPinConfirmNewPinRequest(newPin: '', sessionToken: ''),
    );
    registerFallbackValue(ActivateAccountCreatePinRequest(
      pin: '',
      sessionToken: '',
    ));
  });

  setUp(() {
    mockUserRepo = MockUserRepo();
    mockAuthRepo = MockAuthRepo();
    cubit = ConfirmNewPinCubit(
      userRepo: mockUserRepo,
      authRepo: mockAuthRepo,
    );
  });

  void verifyRepoCalled() {
    final dynamic captured = verify(() => mockUserRepo.changePin(
          request: captureAny(named: 'request'),
          mockConfig: any(named: 'mockConfig'),
        )).captured.first;

    expect(
        captured,
        isA<ConfirmNewPinRequest>().having(
          (ConfirmNewPinRequest req) => req.pin,
          'verify pin',
          mockPin,
        ));
  }

  group('Test ConfirmNewPinCubit', () {
    test('verify initial state', () {
      expect(cubit.state, isA<ConfirmInitialState>());
    });
  });

  group('Test onConfirm', () {
    setUp(() {
      when(() => mockUserRepo.changePin(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        return ChangePinEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(getMockPinFileNameByCase(
              MockPinUseCase.getChangePinVerifyCurrentSuccess,
            ))));
      });

      when(() => mockAuthRepo.resetPin(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        return ResetPinEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(getMockPinFileNameByCase(
              MockPinUseCase.getResetPinSuccess,
            ))));
      });

      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        return AccountActivationEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: {},
        ));
      });
    });

    test('should call changePin when flow is CreateNewPinFlow.changePin ', () async {
      await cubit.onConfirm(
        pin: mockPin,
        confirmPin: mockPin,
        flow: CreateNewPinFlow.changePin,
        sessionToken: mockSessionToken,
      );

      final ChangePinRequest captured = verify(() => mockUserRepo.changePin(
            request: captureAny(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).captured.first as ChangePinRequest;

      expect(
          captured,
          isA<ConfirmNewPinRequest>().having(
              (ConfirmNewPinRequest req) => (req.sessionToken, req.pin, req.type),
              'verify request value',
              equals((mockSessionToken, mockPin, AuthChallengeType.changePin))));

      verifyNever(() => mockAuthRepo.resetPin(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          ));
    });

    test('should call createPin when flow is CreateNewPinFlow.createPin ', () async {
      await cubit.onConfirm(
        pin: mockPin,
        confirmPin: mockPin,
        flow: CreateNewPinFlow.createPin,
        sessionToken: mockSessionToken,
      );

      verify(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should call resetPin when flow is CreateNewPinFlow.resetPin ', () async {
      await cubit.onConfirm(
        pin: mockPin,
        confirmPin: mockPin,
        flow: CreateNewPinFlow.resetPin,
        sessionToken: mockSessionToken,
      );

      final ResetPinRequest captured = verify(() => mockAuthRepo.resetPin(
            request: captureAny(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).captured.first as ResetPinRequest;

      expect(
          captured,
          isA<ResetPinConfirmNewPinRequest>().having(
              (ResetPinConfirmNewPinRequest req) => (req.sessionToken, req.newPin, req.type),
              'verify request value',
              equals((mockSessionToken, mockPin, ResetPinType.none))));

      verifyNever(() => mockUserRepo.changePin(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          ));
    });

    blocTest<ConfirmNewPinCubit, ConfirmNewPinState>(
        'verify emit PinUnmatchedState when pin not matched',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.onConfirm(
              pin: mockPin,
              sessionToken: mockSessionToken,
              confirmPin: 'confirm-pin',
              flow: CreateNewPinFlow.changePin,
            ),
        expect: () => [
              isA<PinUnmatchedState>(),
            ],
        verify: (_) {
          verifyNever(() => mockUserRepo.changePin(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              ));
        });

    blocTest<ConfirmNewPinCubit, ConfirmNewPinState>('verify emit ConfirmResetSuccess',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.onConfirm(
              pin: mockPin,
              sessionToken: mockSessionToken,
              confirmPin: mockPin,
              flow: CreateNewPinFlow.changePin,
            ),
        expect: () => [
              isA<ConfirmLoadingState>(),
              isA<ConfirmNewPinSuccess>(),
            ],
        verify: (_) {
          verifyRepoCalled();
        });

    blocTest<ConfirmNewPinCubit, ConfirmNewPinState>(
        'verify emit ConfirmResetSessionExpired when status code = INVALID_TOKEN',
        setUp: () {
          when(() => mockUserRepo.changePin(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async {
            return ChangePinEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.INVALID_TOKEN,
              response: <String, dynamic>{},
            ));
          });
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.onConfirm(
              pin: mockPin,
              sessionToken: mockSessionToken,
              confirmPin: mockPin,
              flow: CreateNewPinFlow.changePin,
            ),
        expect: () => [
              isA<ConfirmLoadingState>(),
              isA<ConfirmNewPinSessionExpired>(),
            ],
        verify: (_) {
          verifyRepoCalled();
        });

    blocTest<ConfirmNewPinCubit, ConfirmNewPinState>(
        'verify emit ConfirmResetSessionExpired when status code = BAD_REQUEST',
        setUp: () {
          when(() => mockUserRepo.changePin(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async {
            return ChangePinEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: await TestUtil.getResponseMock(getMockPinFileNameByCase(
                MockPinUseCase.getConfirmResetPinFailure,
              )),
            ));
          });
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.onConfirm(
              pin: mockPin,
              sessionToken: mockSessionToken,
              confirmPin: mockPin,
              flow: CreateNewPinFlow.changePin,
            ),
        expect: () => [
              isA<ConfirmLoadingState>(),
              isA<ConfirmNewPinBadRequest>().having(
                (ConfirmNewPinBadRequest state) => state.error.userMessage,
                'verify user message',
                'confirm_reset_pin_user_message',
              ),
            ],
        verify: (_) {
          verifyRepoCalled();
        });

    blocTest<ConfirmNewPinCubit, ConfirmNewPinState>(
        'verify emit ConfirmResetPinFailure when unknown error occurred',
        setUp: () {
          when(() => mockUserRepo.changePin(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async {
            return ChangePinEntity.fromBaseResponse(BaseResponse(
              statusCode: CommonHttpClient.UNKNOWN_ERRORS,
              response: await TestUtil.getResponseMock(getMockPinFileNameByCase(
                MockPinUseCase.getConfirmResetPinFailure,
              )),
            ));
          });
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.onConfirm(
              pin: mockPin,
              sessionToken: mockSessionToken,
              confirmPin: mockPin,
              flow: CreateNewPinFlow.changePin,
            ),
        expect: () => <TypeMatcher<ConfirmNewPinState>>[
              isA<ConfirmLoadingState>(),
              isA<ConfirmNewPinFailure>().having(
                (ConfirmNewPinFailure state) => state.error.statusCode,
                'verify statusCode',
                CommonHttpClient.UNKNOWN_ERRORS,
              ),
            ],
        verify: (_) {
          verifyRepoCalled();
        });
  });

  blocTest<ConfirmNewPinCubit, ConfirmNewPinState>('verify resetState',
      wait: TestConstant.blocEmitStateDelayDuration,
      build: () => cubit,
      act: (_) {
        cubit.emit(ConfirmNewPinSuccess(BaseEntity()));
        cubit.resetState();
      },
      expect: () => <TypeMatcher<ConfirmNewPinState>>[
            isA<ConfirmNewPinSuccess>(),
            isA<ConfirmInitialState>(),
          ]);
}
