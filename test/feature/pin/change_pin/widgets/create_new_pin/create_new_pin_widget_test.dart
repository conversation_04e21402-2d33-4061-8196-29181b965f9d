import 'package:evoapp/feature/pin/change_pin/widgets/create_new_pin/create_new_pin_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/validator/mpin_validator.dart';
import 'package:evoapp/widget/evo_pin_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../util/flutter_test_config.dart';

class MockMpinValidator extends Mock implements MpinValidator {}

void main() {
  late MpinValidator validator;

  Future<EvoPinTextField> getPinTextField(WidgetTester tester, {required String pin}) async {
    await tester.enterText(find.byType(TextField), pin);
    await tester.pump(Duration(seconds: 1));
    return tester.widget(find.byType(EvoPinTextField));
  }

  group('CreateNewPinWidget', () {
    setUpAll(() {
      validator = getIt.registerSingleton(MockMpinValidator());

      getItRegisterColor();
      getItRegisterTextStyle();

      getItRegisterMockCommonUtilFunctionAndImageProvider();

      final CommonImageProvider imageProvider = getIt.get<CommonImageProvider>();
      when(() => imageProvider.asset(any(), height: any(named: 'height'))).thenReturn(SizedBox());
    });

    tearDownAll(() {
      getIt.reset();
    });

    testWidgets('should call submit when pin is valid', (WidgetTester tester) async {
      when(() => validator.validate(any())).thenReturn(null);
      String pinSubmitted = '';
      await tester.pumpWidget(MaterialApp(
        home: Material(
          child: CreateNewPinWidget(onSubmit: (String pin) => pinSubmitted = pin),
        ),
      ));

      final String pin = '1234';
      final EvoPinTextField pinField = await getPinTextField(tester, pin: pin);

      expect(pinField.isError, isFalse);
      expect(pinSubmitted, equals(pin));
    });

    testWidgets('should show error message returned by the validator', (WidgetTester tester) async {
      final String error = 'error';
      when(() => validator.validate(any())).thenReturn(error);
      await tester.pumpWidget(MaterialApp(
        home: Material(
          child: CreateNewPinWidget(onSubmit: (_) {}),
        ),
      ));

      final EvoPinTextField pinField = await getPinTextField(tester, pin: '1111');

      expect(pinField.isError, isTrue);
      expect(pinField.errorMessage, equals(error));
    });

    testWidgets('should clear error when pin is cleared', (WidgetTester tester) async {
      when(() => validator.validate(any())).thenReturn('error');
      await tester.pumpWidget(MaterialApp(
        home: Material(
          child: CreateNewPinWidget(onSubmit: (_) {}),
        ),
      ));

      // Invalid pin is entered to show error
      EvoPinTextField pinField = await getPinTextField(tester, pin: '1111');
      expect(pinField.errorMessage, isNotEmpty);

      // Pin is cleared and error should also be cleared
      pinField = await getPinTextField(tester, pin: '');
      expect(pinField.errorMessage, isNull);
    });
  });
}
