import 'package:evoapp/feature/pin/change_pin/widgets/create_new_pin/cubit/create_new_pin_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MPINCodeState', () {
    test('MPINInitState is a subclass of MPINCodeState', () {
      final CreateNewPinInitState state = CreateNewPinInitState();
      expect(state, isA<CreateNewPinCodeState>());
    });

    test('MPINValid is a subclass of MPINCodeState and holds correct mpin', () {
      const String mpin = '1234';
      final NewPinValid state = NewPinValid(mpin);
      expect(state, isA<CreateNewPinCodeState>());
      expect(state.mpin, mpin);
    });

    test('MPINInvalid is a subclass of MPINCodeState and holds correct errorMessage', () {
      const String errorMessage = 'Invalid MPIN';
      final NewPinInvalid state = NewPinInvalid(errorMessage);
      expect(state, isA<CreateNewPinCodeState>());
      expect(state.errorMessage, errorMessage);
    });
  });
}
