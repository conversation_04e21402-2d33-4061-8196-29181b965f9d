import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/pin/change_pin/widgets/create_new_pin/cubit/create_new_pin_cubit.dart';
import 'package:evoapp/util/validator/mpin_validator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockMpinValidator extends Mock implements MpinValidator {}

void main() {
  late MpinValidator validator;
  late CreateNewPinCubit cubit;
  const String mockMPIN = 'mock_mpin';
  const String mockErrMsg = 'mock_err_msg';

  setUp(() {
    validator = MockMpinValidator();
    cubit = CreateNewPinCubit(validator: validator);
    when(() => validator.validate(any())).thenReturn(null);
  });

  blocTest<CreateNewPinCubit, CreateNewPinCodeState>(
    'verify initial state',
    build: () => cubit,
    verify: (CreateNewPinCubit cubit) {
      expect(cubit.state, isA<CreateNewPinInitState>());
    },
  );

  blocTest<CreateNewPinCubit, CreateNewPinCodeState>(
    'verify valid state',
    build: () => cubit,
    act: (_) => cubit.validate(mockMPIN),
    expect: () => <TypeMatcher<NewPinValid>>[
      isA<NewPinValid>().having(
        (NewPinValid state) => state.mpin,
        'verify mpin code',
        mockMPIN,
      ),
    ],
  );

  blocTest<CreateNewPinCubit, CreateNewPinCodeState>(
    'verify invalid state',
    setUp: () {
      when(() => validator.validate(any())).thenReturn(mockErrMsg);
    },
    build: () => cubit,
    act: (_) => cubit.validate(mockMPIN),
    expect: () => <TypeMatcher<NewPinInvalid>>[
      isA<NewPinInvalid>().having(
        (NewPinInvalid state) => state.errorMessage,
        'verify errorMessage',
        mockErrMsg,
      ),
    ],
  );
}
