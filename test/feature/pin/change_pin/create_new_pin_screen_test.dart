import 'package:evoapp/feature/pin/change_pin/create_new_pin_flow.dart';
import 'package:evoapp/feature/pin/change_pin/create_new_pin_screen.dart';
import 'package:evoapp/feature/pin/change_pin/widgets/create_new_pin/create_new_pin_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/validator/mpin_validator.dart';
import 'package:evoapp/widget/appbar/evo_appbar.dart';
import 'package:evoapp/widget/appbar/evo_appbar_leading_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();

    getIt.registerSingleton(MpinValidator());
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('FaceCaptureCheckScreen', () {
    testWidgets('should have needed widgets for flow activateAccount', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: CreateNewPinScreen(
            sessionToken: 'session_token',
            flow: CreateNewPinFlow.createPin,
            onSuccess: (_) {},
          ),
        ),
      );

      expect(find.byType(EvoAppBar), findsOneWidget);
      expect(find.byType(EvoAppBarLeadingButton), findsOneWidget);

      expect(find.text(EvoStrings.activateAccountCreatePinTitle), findsOneWidget);
      expect(find.text(EvoStrings.activateAccountCreatePinDesc), findsOneWidget);

      expect(find.byType(CreateNewPinWidget), findsOneWidget);

      expect(find.textContaining(EvoStrings.infoBannerNotSamePrevious), findsNothing);
      expect(find.textContaining(EvoStrings.infoBannerNotIncreasing), findsOneWidget);
      expect(find.textContaining(EvoStrings.infoBannerNotDecreasing), findsOneWidget);
      expect(find.textContaining(EvoStrings.infoBannerNotSameDigits), findsOneWidget);
      expect(find.textContaining(EvoStrings.infoBannerMustBe4Digits), findsOneWidget);
    });

    testWidgets('should have needed widgets for flow resetPin or changePin',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: CreateNewPinScreen(
            sessionToken: 'session_token',
            flow: CreateNewPinFlow.resetPin,
            onSuccess: (_) {},
          ),
        ),
      );

      expect(find.byType(EvoAppBar), findsOneWidget);
      expect(find.byType(EvoAppBarLeadingButton), findsOneWidget);

      expect(find.text(EvoStrings.createNewMPINTitle), findsOneWidget);
      expect(find.text(EvoStrings.createNewMPINDesc), findsOneWidget);

      expect(find.byType(CreateNewPinWidget), findsOneWidget);

      expect(find.textContaining(EvoStrings.infoBannerNotSamePrevious), findsOneWidget);
      expect(find.textContaining(EvoStrings.infoBannerNotIncreasing), findsOneWidget);
      expect(find.textContaining(EvoStrings.infoBannerNotDecreasing), findsOneWidget);
      expect(find.textContaining(EvoStrings.infoBannerNotSameDigits), findsOneWidget);
      expect(find.textContaining(EvoStrings.infoBannerMustBe4Digits), findsOneWidget);
    });

    test('should self navigate on push', () {
      CreateNewPinScreen.pushNamed(flow: CreateNewPinFlow.resetPin, onSuccess: (_) {});
      verify(() => mockNavigatorContext.pushNamed(Screen.createNewPinScreen.name,
          extra: any(named: 'extra'))).called(1);

      CreateNewPinScreen.pushReplacement(flow: CreateNewPinFlow.resetPin, onSuccess: (_) {});
      verify(() => mockNavigatorContext.pushReplacementNamed(Screen.createNewPinScreen.name,
          extra: any(named: 'extra'))).called(1);
    });

    testWidgets('CreateNewPinWidget onSubmit should navigate to ConfirmNewPinScreen',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: CreateNewPinScreen(
            sessionToken: 'session_token',
            flow: CreateNewPinFlow.createPin,
            onSuccess: (_) {},
          ),
        ),
      );

      final CreateNewPinWidget pinWidget = tester.widget(find.byType(CreateNewPinWidget));
      pinWidget.onSubmit('pin');

      verify(() => mockNavigatorContext.pushNamed(
            Screen.confirmNewPinScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });
  });
}
