import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/request/change_pin_request.dart';
import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/data/response/change_pin_entity.dart';
import 'package:evoapp/feature/pin/change_pin/verify_current/cubit/current_pin_verification_cubit.dart';
import 'package:evoapp/feature/pin/mock/mock_pin_use_case.dart';
import 'package:evoapp/feature/pin/models/change_pin_status.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../constant.dart';
import '../../../../../util/test_util.dart';

class MockUserRepo extends Mock implements UserRepo {}

class MockAppState extends Mock implements AppState {
  @override
  final ChangePinStatusNotifier changePinStatusNotifier = ChangePinStatusNotifier();
}

void main() {
  late CurrentPINVerificationCubit cubit;
  late UserRepo mockUserRepo;
  late AppState mockAppState;
  const String mockSessionToken = 'token';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(VerifyCurrentPinRequest(
      pin: '',
      sessionToken: '',
    ));
  });

  setUp(() {
    mockUserRepo = MockUserRepo();
    mockAppState = MockAppState();
    cubit = CurrentPINVerificationCubit(
      userRepo: mockUserRepo,
      appState: mockAppState,
    );
  });

  group('verify verifyCurrentPin', () {
    const String mockPin = '1234';

    late ChangePinEntity mockEntity;

    setUp(() {
      registerFallbackValue(AuthChallengeType.none);
      when(() => mockUserRepo.changePin(
            mockConfig: any(named: 'mockConfig'),
            request: any(named: 'request'),
          )).thenAnswer((_) async {
        return mockEntity = ChangePinEntity.fromBaseResponse(BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: await TestUtil.getResponseMock(
              getMockPinFileNameByCase(MockPinUseCase.getChangePinVerifyCurrentSuccess),
            )));
      });
    });

    verifyUserRepoCalled() {
      final dynamic captured = verify(() => mockUserRepo.changePin(
          request: captureAny(named: 'request'),
          mockConfig: any(named: 'mockConfig'))).captured.first;
      expect(
          captured,
          isA<VerifyCurrentPinRequest>().having(
            (VerifyCurrentPinRequest req) => req.pin,
            'verify pin',
            mockPin,
          ));
    }

    blocTest<CurrentPINVerificationCubit, CurrentPINVerificationState>('check pin success',
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.verifyCurrentPin(
              pin: mockPin,
              sessionToken: mockSessionToken,
            ),
        verify: (_) {
          verifyUserRepoCalled();
          expect(mockAppState.changePinStatusNotifier.value, ChangePinStatus.available);
        },
        expect: () => <TypeMatcher<CurrentPINVerificationState>>[
              isA<VerifyCurrentPinLoading>(),
              isA<VerifyCurrentPinSuccess>().having(
                (VerifyCurrentPinSuccess state) => state.sessionToken,
                'verify sessionToken',
                mockEntity.sessionToken,
              ),
            ]);

    blocTest<CurrentPINVerificationCubit, CurrentPINVerificationState>(
        'check pin failure with status code 400',
        setUp: () {
          when(() => mockUserRepo.changePin(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async {
            return ChangePinEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.BAD_REQUEST,
                response: await TestUtil.getResponseMock(
                  getMockPinFileNameByCase(MockPinUseCase.getVerifyPinInvalidCredential),
                )));
          });
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.verifyCurrentPin(
              pin: mockPin,
              sessionToken: mockSessionToken,
            ),
        verify: (_) {
          verifyUserRepoCalled();
          expect(mockAppState.changePinStatusNotifier.value, ChangePinStatus.available);
        },
        expect: () => <TypeMatcher<CurrentPINVerificationState>>[
              isA<VerifyCurrentPinLoading>(),
              isA<VerifyCurrentPinBadRequest>().having(
                (VerifyCurrentPinBadRequest state) => state.errorMessage,
                'verify entity',
                'verify_pin_invalid_credential',
              ),
            ]);

    blocTest<CurrentPINVerificationCubit, CurrentPINVerificationState>(
        'check pin failure with status verdict limit_exceeded',
        setUp: () {
          when(() => mockUserRepo.changePin(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async {
            return ChangePinEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.LIMIT_EXCEEDED,
                response: await TestUtil.getResponseMock(
                  getMockPinFileNameByCase(MockPinUseCase.getChangePinVerifyCurrentLockedResource),
                )));
          });
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.verifyCurrentPin(
              pin: mockPin,
              sessionToken: mockSessionToken,
            ),
        verify: (_) {
          verifyUserRepoCalled();
          expect(mockAppState.changePinStatusNotifier.value, ChangePinStatus.locked);
        },
        expect: () => <TypeMatcher<CurrentPINVerificationState>>[
              isA<VerifyCurrentPinLoading>(),
              isA<VerifyCurrentPinLocked>().having(
                (VerifyCurrentPinLocked state) => state.errorMessage,
                'verify entity',
                'mock_locked_resource_user_message',
              ),
            ]);

    blocTest<CurrentPINVerificationCubit, CurrentPINVerificationState>(
        'check pin failure with status code unknown',
        setUp: () {
          when(() => mockUserRepo.changePin(
                request: any(named: 'request'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async {
            return ChangePinEntity.fromBaseResponse(BaseResponse(
                statusCode: CommonHttpClient.UNKNOWN_ERRORS, response: <String, dynamic>{}));
          });
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.verifyCurrentPin(
              pin: mockPin,
              sessionToken: mockSessionToken,
            ),
        verify: (_) {
          verifyUserRepoCalled();
          expect(mockAppState.changePinStatusNotifier.value, ChangePinStatus.available);
        },
        expect: () => <TypeMatcher<CurrentPINVerificationState>>[
              isA<VerifyCurrentPinLoading>(),
              isA<VerifyCurrentPinFailure>(),
            ]);
  });
}
