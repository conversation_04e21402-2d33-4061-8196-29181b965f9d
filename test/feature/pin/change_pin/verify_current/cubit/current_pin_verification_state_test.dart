import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/feature/pin/change_pin/verify_current/cubit/current_pin_verification_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ChangePinVerifyCurrentState', () {
    test('VerifyCurrentPinInitState should be an instance of ChangePinVerifyCurrentState', () {
      final VerifyCurrentPinInitState state = VerifyCurrentPinInitState();
      expect(state, isA<CurrentPINVerificationState>());
    });

    test('VerifyCurrentPinLoading should be an instance of ChangePinVerifyCurrentState', () {
      final VerifyCurrentPinLoading state = VerifyCurrentPinLoading();
      expect(state, isA<CurrentPINVerificationState>());
    });

    test('CurrentPINVerificationState should be an instance of ChangePinVerifyCurrentState', () {
      const String token = 'mock-token';
      const AuthChallengeType type = AuthChallengeType.changePin;
      final VerifyCurrentPinSuccess state = VerifyCurrentPinSuccess(
        sessionToken: token,
        type: type,
      );
      expect(state, isA<CurrentPINVerificationState>());
      expect(state.sessionToken, token);
      expect(state.type, type);
    });

    test('VerifyCurrentPinBadRequest should be an instance of ChangePinVerifyCurrentState', () {
      final VerifyCurrentPinBadRequest state = VerifyCurrentPinBadRequest();
      expect(state, isA<CurrentPINVerificationState>());
    });

    test('VerifyCurrentPinBadRequest should hold the correct error message', () {
      const String errorMessage = 'Invalid PIN';
      final VerifyCurrentPinBadRequest state =
          VerifyCurrentPinBadRequest(errorMessage: errorMessage);
      expect(state.errorMessage, errorMessage);
    });

    test('VerifyCurrentPinLocked should hold the correct error message', () {
      const String errorMessage = 'locked error message';
      final VerifyCurrentPinLocked state = VerifyCurrentPinLocked(errorMessage: errorMessage);
      expect(state.errorMessage, errorMessage);
    });

    test('VerifyCurrentPinFailure should hold the correct error', () {
      final ErrorUIModel error = ErrorUIModel();
      final VerifyCurrentPinFailure state = VerifyCurrentPinFailure(error);
      expect(state, isA<CurrentPINVerificationState>());
      expect(state.error, error);
    });
  });
}
