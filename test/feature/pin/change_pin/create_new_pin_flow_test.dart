import 'package:evoapp/feature/pin/change_pin/create_new_pin_flow.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CreateNewPinFlow', () {
    test('should have correct enum values', () {
      expect(CreateNewPinFlow.values.length, 3);
      expect(CreateNewPinFlow.resetPin.toString(), 'CreateNewPinFlow.resetPin');
      expect(CreateNewPinFlow.changePin.toString(), 'CreateNewPinFlow.changePin');
      expect(CreateNewPinFlow.createPin.toString(), 'CreateNewPinFlow.createPin');
    });

    test('isActivateAccount should return true only for activateAccount', () {
      expect(CreateNewPinFlow.resetPin.isCreatePin, false);
      expect(CreateNewPinFlow.changePin.isCreatePin, false);
      expect(CreateNewPinFlow.createPin.isCreatePin, true);
    });
  });
}
