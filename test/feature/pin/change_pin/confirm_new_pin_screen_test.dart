import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/pin/change_pin/confirm_new_pin_screen.dart';
import 'package:evoapp/feature/pin/change_pin/create_new_pin_flow.dart';
import 'package:evoapp/feature/pin/cubit/confirm_new_pin/confirm_new_pin_cubit.dart';
import 'package:evoapp/feature/pin/widgets/confirm_new_pin/confirm_new_pin_widget.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/validator/mpin_validator.dart';
import 'package:evoapp/widget/appbar/evo_appbar.dart';
import 'package:evoapp/widget/appbar/evo_appbar_leading_button.dart';
import 'package:evoapp/widget/evo_pin_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class MockConfirmNewPinCubit extends MockCubit<ConfirmNewPinState> implements ConfirmNewPinCubit {}

class MockOnConfirmNewPinSuccess extends Mock {
  void call(ChallengeSuccessModel model);
}

void main() {
  final MockConfirmNewPinCubit cubit = MockConfirmNewPinCubit();
  final MockOnConfirmNewPinSuccess onConfirmNewPinSuccess = MockOnConfirmNewPinSuccess();

  setUpAll(() {
    initConfigEvoPageStateBase();

    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});

    registerFallbackValue(CreateNewPinFlow.createPin);
    registerFallbackValue(ChallengeSuccessModel());

    getIt.registerSingleton(MpinValidator());

    when(() => cubit.state).thenReturn(ConfirmInitialState());
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
    reset(onConfirmNewPinSuccess);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('ConfirmNewPinScreen', () {
    Widget buildWidgetInTest({CreateNewPinFlow flow = CreateNewPinFlow.createPin}) {
      return MaterialApp(
        home: BlocProvider<ConfirmNewPinCubit>(
          create: (_) => cubit,
          child: ConfirmNewPinScreen(
            pin: '1234',
            sessionToken: 'session_token',
            flow: flow,
            onConfirmNewPinSuccess: onConfirmNewPinSuccess.call,
          ),
        ),
      );
    }

    testWidgets('should have needed widgets', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetInTest());

      expect(find.byType(EvoAppBar), findsOneWidget);
      expect(find.byType(EvoAppBarLeadingButton), findsOneWidget);

      expect(find.text(EvoStrings.confirmResetPinTitle), findsOneWidget);
      expect(find.text(EvoStrings.confirmResetPinDesc), findsOneWidget);

      expect(find.byType(EvoPinTextField), findsOneWidget);
    });

    test('should self navigate on push', () {
      ConfirmNewPinScreen.pushNamed(
        pin: '1234',
        flow: CreateNewPinFlow.createPin,
        onSuccess: (_) {},
      );
      verify(() => mockNavigatorContext.pushNamed(Screen.confirmNewPinScreen.name,
          extra: any(named: 'extra'))).called(1);

      ConfirmNewPinScreen.pushReplacementNamed(
        pin: '1234',
        flow: CreateNewPinFlow.createPin,
        onSuccess: (_) {},
        sessionToken: 'token',
      );
      verify(() => mockNavigatorContext.pushReplacementNamed(Screen.confirmNewPinScreen.name,
          extra: any(named: 'extra'))).called(1);
    });

    testWidgets('ConfirmNewPinWidget onSubmit should call cubit onConfirm',
        (WidgetTester tester) async {
      when(() => cubit.onConfirm(
            pin: any(named: 'pin'),
            confirmPin: any(named: 'confirmPin'),
            flow: any(named: 'flow'),
            sessionToken: any(named: 'sessionToken'),
          )).thenAnswer((_) async {});

      await tester.pumpWidget(buildWidgetInTest());

      final ConfirmNewPinWidget pinWidget = tester.widget(find.byType(ConfirmNewPinWidget));
      pinWidget.onSubmit?.call('pin');

      verify(() => cubit.onConfirm(
            pin: any(named: 'pin'),
            confirmPin: any(named: 'confirmPin'),
            flow: any(named: 'flow'),
            sessionToken: any(named: 'sessionToken'),
          )).called(1);
    });

    testWidgets('ConfirmNewPinWidget onChange should call cubit resetState with empty string',
        (WidgetTester tester) async {
      when(() => cubit.resetState()).thenReturn(null);

      await tester.pumpWidget(buildWidgetInTest());

      final ConfirmNewPinWidget pinWidget = tester.widget(find.byType(ConfirmNewPinWidget));
      pinWidget.onChange?.call('');

      verify(() => cubit.resetState()).called(1);
    });

    testWidgets('should do nothing when state is PinUnmatchedState and show correct error string',
        (WidgetTester tester) async {
      whenListen(cubit, Stream<ConfirmNewPinState>.fromIterable([PinUnmatchedState()]));

      await tester.pumpWidget(buildWidgetInTest());

      verifyNever(() => mockEvoUtilFunction.showHudLoading());
      verifyNever(() => mockEvoUtilFunction.hideHudLoading());
      verifyNever(() => mockDialogFunction.showDialogSessionTokenExpired(
            onClickPositive: any(named: 'onClickPositive'),
            type: any(named: 'type'),
          ));

      await tester.pump();
      expect(find.text(EvoStrings.mpinNotMatch), findsOneWidget);
    });

    testWidgets('should showHudLoading when state is ConfirmLoadingState',
        (WidgetTester tester) async {
      whenListen(cubit, Stream<ConfirmNewPinState>.fromIterable([ConfirmLoadingState()]));

      await tester.pumpWidget(buildWidgetInTest());

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
    });

    testWidgets('should hideHudLoading when state is NOT ConfirmLoadingState',
        (WidgetTester tester) async {
      whenListen(
        cubit,
        Stream<ConfirmNewPinState>.fromIterable([ConfirmNewPinSuccess(BaseEntity())]),
      );

      await tester.pumpWidget(buildWidgetInTest());

      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
    });

    testWidgets('should call onConfirmNewPinSuccess when state is ConfirmNewPinSuccess',
        (WidgetTester tester) async {
      final BaseEntity entity = BaseEntity();
      whenListen(
        cubit,
        Stream<ConfirmNewPinState>.fromIterable([ConfirmNewPinSuccess(entity)]),
      );

      await tester.pumpWidget(buildWidgetInTest());

      verify(() => onConfirmNewPinSuccess(any(
            that: isA<ChallengeSuccessModel>().having(
              (ChallengeSuccessModel m) => m.entity,
              'should have entity',
              entity,
            ),
          ))).called(1);
    });

    testWidgets(
        'should call showDialogSessionTokenExpired with type == SessionDialogType.reset'
        'when state is ConfirmNewPinSessionExpired and flow == CreateNewPinFlow.resetPin',
        (WidgetTester tester) async {
      when(() => mockDialogFunction.showDialogSessionTokenExpired(
            onClickPositive: any(named: 'onClickPositive'),
            type: any(named: 'type'),
          )).thenAnswer((_) async {});
      whenListen(cubit, Stream<ConfirmNewPinState>.fromIterable([ConfirmNewPinSessionExpired()]));

      await tester.pumpWidget(buildWidgetInTest(flow: CreateNewPinFlow.resetPin));

      final VoidCallback onClickPositive = verify(
        () => mockDialogFunction.showDialogSessionTokenExpired(
          onClickPositive: captureAny(named: 'onClickPositive'),
          type: any(named: 'type', that: equals(SessionDialogType.resetPin)),
        ),
      ).captured.first as VoidCallback;

      onClickPositive.call();

      verify(() => mockNavigatorContext.popUntilNamed(Screen.mainScreen.name)).called(1);
    });

    testWidgets(
        'should call showDialogSessionTokenExpired with type == SessionDialogType.activateAccount'
        'when state is ConfirmNewPinSessionExpired and flow == CreateNewPinFlow.createPin',
        (WidgetTester tester) async {
      when(() => mockDialogFunction.showDialogSessionTokenExpired(
            onClickPositive: any(named: 'onClickPositive'),
            type: any(named: 'type'),
          )).thenAnswer((_) async {});
      whenListen(cubit, Stream<ConfirmNewPinState>.fromIterable([ConfirmNewPinSessionExpired()]));

      await tester.pumpWidget(buildWidgetInTest());

      final VoidCallback onClickPositive = verify(
        () => mockDialogFunction.showDialogSessionTokenExpired(
          onClickPositive: captureAny(named: 'onClickPositive'),
          type: any(named: 'type', that: equals(SessionDialogType.activateAccount)),
        ),
      ).captured.first as VoidCallback;

      onClickPositive.call();

      verify(() => mockNavigatorContext.popUntilNamed(Screen.mobileNumberCheckScreen.name))
          .called(1);
    });

    testWidgets('should show correct error string when state is ConfirmNewPinBadRequest ',
        (WidgetTester tester) async {
      final ErrorUIModel error = ErrorUIModel(userMessage: 'error');
      whenListen(
        cubit,
        Stream<ConfirmNewPinState>.fromIterable([ConfirmNewPinBadRequest(error: error)]),
      );

      await tester.pumpWidget(buildWidgetInTest());
      await tester.pump();

      expect(find.text('error'), findsOneWidget);
    });
  });
}
