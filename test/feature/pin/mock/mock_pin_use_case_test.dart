import 'package:evoapp/feature/pin/mock/mock_pin_use_case.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify getMockBiometricTokenFileNameByCase should return corrected file names', () {
    expect(getMockPinFileNameByCase(MockPinUseCase.getVerifyPinSuccess), 'verify_pin_success.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getVerifyPinLimitExceeded),
        'verify_pin_limit_exceeded.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getVerifyPinInvalidCredential),
        'verify_pin_invalid_credential.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getChangePinVerifyCurrentSuccess),
        'change_pin_verify_current_success.json');
    expect(
        getMockPinFileNameByCase(MockPinUseCase.getChangePinVerifyCurrentSuccessNullSessionToken),
        'change_pin_verify_current_success_null_token.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getChangePinVerifyCurrentLockedResource),
        'change_pin_verify_current_locked_resource.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getConfirmResetPinSuccess),
        'confirm_reset_pin_success.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getConfirmResetPinFailure),
        'confirm_reset_pin_failure.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getResetPinInitializeSuccess),
        'reset_pin_initialize_success.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getResetPinVerifyOTPSuccess),
        'reset_pin_verify_otp_success.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getResetPinSuccess), 'reset_pin_success.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getResetPinFaceAuthChallenge),
        'reset_pin_face_auth_challenge.json');
    expect(getMockPinFileNameByCase(MockPinUseCase.getResetPinFaceAuthSuccess),
        'reset_pin_face_auth_success.json');
  });
}
