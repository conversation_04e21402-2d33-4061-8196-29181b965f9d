import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_verification_flow_type.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_verification_screen.dart';
import 'package:evoapp/feature/pin/change_pin/change_pin_arg.dart';
import 'package:evoapp/feature/pin/change_pin/create_new_pin_flow.dart';
import 'package:evoapp/feature/pin/new_pin_success_screen.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_ui_handler.dart';
import 'package:evoapp/feature/verify_otp/cubit/otp_success_model.dart';
import 'package:evoapp/feature/verify_otp/cubit/verify_otp_cubit.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockDialogFunction extends Mock implements DialogFunction {}

class MockOnSuccessCallback extends Mock {
  void call(ResetPinEntity entity);
}

class MockOnErrorCallback extends Mock {
  void call(ErrorUIModel? error);
}

class MockOnFaceAuthSuccessCallback extends Mock {
  void call(BaseEntity entity);
}

class MocEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  final ResetPinUiHandler uiHandler = ResetPinUiHandler();
  const String phoneNumber = 'mock-phone-number';
  late final BuildContext mockNavigatorContext;
  late final DialogFunction mockDialogFunction;
  const String mockSession = 'mock-session';

  void mockCallback() {}

  setUpAll(() {
    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    getIt.registerSingleton<DialogFunction>(MockDialogFunction());

    mockDialogFunction = getIt.get<DialogFunction>();

    registerFallbackValue(EvoDialogId.defaultErrorDialog);
    registerFallbackValue(ResetPinEntity());
  });

  tearDown(() {
    reset(mockNavigatorContext);
  });

  group('verify handleVerifyOTP', () {
    late MockOnSuccessCallback mockOnSuccess;
    const String mockChallengeType = 'mock-challenge-type';
    const String mockSessionToken = 'mock-session-token';
    final Map<String, String> mockEkycClientSettings = <String, String>{'key': 'value'};

    setUp(() {
      mockOnSuccess = MockOnSuccessCallback();
    });

    test('should navigate [VerifyOtpPage] with correct args', () async {
      await uiHandler.handleVerifyOTP(
          onSuccess: mockOnSuccess.call,
          entity: ResetPinEntity(
            sessionToken: mockSession,
            otpValiditySecs: 1,
            otpResendSecs: 2,
          ),
          phoneNumber: phoneNumber);

      final VerifyOtpPageArg captured = verify(
        () => mockNavigatorContext.pushNamed(
          Screen.verifyOtpScreen.name,
          extra: captureAny(named: 'extra'),
        ),
      ).captured.first as VerifyOtpPageArg;

      expect(captured.sessionToken, mockSession);
      expect(captured.contactInfo, phoneNumber);
      expect(captured.otpValiditySecs, 1);
      expect(captured.otpResendSecs, 2);
      expect(captured.verifyOtpType, VerifyOtpType.resetPin);
    });

    test('should [onSuccess] be called if state is [VerifyOtpSuccess]', () async {
      await uiHandler.handleVerifyOTP(
        onSuccess: mockOnSuccess.call,
        entity: ResetPinEntity(
          sessionToken: mockSession,
          otpValiditySecs: 1,
          otpResendSecs: 2,
        ),
        phoneNumber: phoneNumber,
      );

      final VerifyOtpPageArg captured = verify(
        () => mockNavigatorContext.pushNamed(
          Screen.verifyOtpScreen.name,
          extra: captureAny(named: 'extra'),
        ),
      ).captured.first as VerifyOtpPageArg;

      captured.onPopSuccess?.call(VerifyOtpSuccess(OtpSuccessModel(
        challengeType: mockChallengeType,
        sessionToken: mockSessionToken,
        ekycClientSettings: mockEkycClientSettings,
      )));

      final dynamic capturedEntity = verify(() => mockOnSuccess.call(captureAny())).captured.first;
      expect(
          capturedEntity,
          isA<ResetPinEntity>().having(
              (ResetPinEntity entity) =>
                  (entity.challengeType, entity.sessionToken, entity.ekycClientSettings),
              'verify entity',
              (
                mockChallengeType,
                mockSessionToken,
                mockEkycClientSettings,
              )));
    });

    test('should [onSuccess] not be called if state is [VerifyOtpSuccess]', () async {
      await uiHandler.handleVerifyOTP(
        onSuccess: mockOnSuccess.call,
        entity: ResetPinEntity(
          sessionToken: mockSession,
          otpValiditySecs: 1,
          otpResendSecs: 2,
        ),
        phoneNumber: phoneNumber,
      );

      final VerifyOtpPageArg captured = verify(
        () => mockNavigatorContext.pushNamed(
          Screen.verifyOtpScreen.name,
          extra: captureAny(named: 'extra'),
        ),
      ).captured.first as VerifyOtpPageArg;

      captured.onPopSuccess?.call(LimitExceedOtp(errorText: ''));
      verifyNever(() => mockOnSuccess.call(captureAny()));
    });
  });

  test('should [showDialogConfirm] when handleLimitExceeded be called', () async {
    when(() => mockDialogFunction.showDialogConfirm(
        isDismissible: any(named: 'isDismissible'),
        alertType: any(named: 'alertType'),
        title: any(named: 'title'),
        content: any(named: 'content'),
        textPositive: any(named: 'textPositive'),
        dialogId: any(named: 'dialogId'),
        onClickPositive: any(named: 'onClickPositive'))).thenAnswer((_) async {});

    await uiHandler.handleLimitExceeded(
      error: ErrorUIModel(userMessage: 'mock-user-message'),
      onClickPositive: mockCallback,
    );

    verify(() => mockDialogFunction.showDialogConfirm(
          isDismissible: any(named: 'isDismissible'),
          alertType: any(named: 'alertType'),
          title: EvoStrings.resetMPINLimitExceededTitle,
          content: 'mock-user-message',
          textPositive: EvoStrings.ok,
          dialogId: EvoDialogId.defaultErrorDialog,
          onClickPositive: any(named: 'onClickPositive'),
        )).called(1);
  });

  test('should navigate [CreateNewPinScreen] when handleChangePIN be called', () async {
    uiHandler.handleChangePIN(
      sessionToken: mockSession,
      onSuccess: (_) {},
    );

    final CreateNewPinArgs captured = verify(
      () => mockNavigatorContext.pushNamed(
        Screen.createNewPinScreen.name,
        extra: captureAny(named: 'extra'),
      ),
    ).captured.first as CreateNewPinArgs;

    expect(captured.sessionToken, mockSession);
    expect(captured.flow, CreateNewPinFlow.resetPin);
  });

  test('should navigate [CreateNewPinScreen] when handleChangePIN be called', () async {
    uiHandler.handleResetPinSuccess(
      onSuccess: mockCallback,
    );

    final NewPinSuccessScreenArg captured = verify(
      () => mockNavigatorContext.pushReplacementNamed(
        Screen.newPinSuccessScreen.name,
        extra: captureAny(named: 'extra'),
      ),
    ).captured.first as NewPinSuccessScreenArg;

    expect(captured.buttonText, EvoStrings.gotIt);
    expect(captured.onNext, mockCallback);
  });

  group('verify handleOTP', () {
    final Map<String, String> mockEkycClientSettings = <String, String>{'key': 'value'};

    test('should navigate [SelfieVerificationScreen] with correct args', () async {
      uiHandler.handleFaceAuth(
        entity: ResetPinEntity(
          sessionToken: mockSession,
          ekycClientSettings: mockEkycClientSettings,
        ),
        onSuccess: (_) {},
      );

      final SelfieVerificationScreenArgs captured = verify(
        () => mockNavigatorContext.pushNamed(
          Screen.selfieVerificationScreen.name,
          extra: captureAny(named: 'extra'),
        ),
      ).captured.first as SelfieVerificationScreenArgs;

      expect(captured.sessionToken, mockSession);
      expect(captured.flowType, SelfieVerificationFlowType.resetPin);
    });

    test('should [onSuccess] be called if entity is ResetPinEntity', () {
      final MockOnFaceAuthSuccessCallback mockOnSuccessCb = MockOnFaceAuthSuccessCallback();

      uiHandler.handleFaceAuth(
        entity: ResetPinEntity(
          sessionToken: mockSession,
          ekycClientSettings: mockEkycClientSettings,
        ),
        onSuccess: mockOnSuccessCb.call,
      );

      final SelfieVerificationScreenArgs captured = verify(
        () => mockNavigatorContext.pushNamed(
          Screen.selfieVerificationScreen.name,
          extra: captureAny(named: 'extra'),
        ),
      ).captured.first as SelfieVerificationScreenArgs;

      captured.onPopSuccess(ChallengeSuccessModel(entity: ResetPinEntity()));
      verify(() => mockOnSuccessCb.call(captureAny())).called(1);
    });

    test('should [onSuccess] not be called if entity is not ResetPinEntity', () {
      final MockOnFaceAuthSuccessCallback mockOnSuccessCb = MockOnFaceAuthSuccessCallback();

      uiHandler.handleFaceAuth(
        entity: ResetPinEntity(
          sessionToken: mockSession,
          ekycClientSettings: mockEkycClientSettings,
        ),
        onSuccess: mockOnSuccessCb.call,
      );

      final SelfieVerificationScreenArgs captured = verify(
        () => mockNavigatorContext.pushNamed(
          Screen.selfieVerificationScreen.name,
          extra: captureAny(named: 'extra'),
        ),
      ).captured.first as SelfieVerificationScreenArgs;

      captured.onPopSuccess(ChallengeSuccessModel(entity: BaseEntity()));
      verifyNever(() => mockOnSuccessCb.call(captureAny()));
    });
  });
}
