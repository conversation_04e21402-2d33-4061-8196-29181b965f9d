import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/reset_pin_request.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/feature/pin/mock/mock_pin_use_case.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_handler.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_handler_impl.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_ui_handler.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';
import '../../../util/test_util.dart';

class ResetPinUiHandlerMock extends Mock implements ResetPinUiHandler {}

class AuthenticationRepoMock extends Mock implements AuthenticationRepo {}

class EvoUtilFunctionMock extends Mock implements EvoUtilFunction {}

class DialogFunctionMock extends Mock implements DialogFunction {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockCallback extends Mock {
  void call();
}

class MockErrorCallback extends Mock {
  void call(ErrorUIModel? error);
}

void main() {
  late ResetPinUiHandlerMock resetPinUiHandlerMock;
  late AuthenticationRepoMock authenticationRepoMock;
  late EvoUtilFunctionMock evoUtilFunctionMock;
  late DialogFunction dialogFunctionMock;
  late ResetPinHandler resetPinHandler;
  late MockErrorCallback errorCallback;
  late MockBuildContext mockNavigatorContext;

  const String phoneNumber = '0355089123';
  const String sessionToken = 'mock-session-token';
  const String mockScreenName = 'mock-screen-name';

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();

    resetPinUiHandlerMock = ResetPinUiHandlerMock();
    authenticationRepoMock = AuthenticationRepoMock();
    evoUtilFunctionMock = EvoUtilFunctionMock();
    dialogFunctionMock = DialogFunctionMock();

    getIt.registerLazySingleton<EvoUtilFunction>(() => evoUtilFunctionMock);
    getIt.registerLazySingleton<DialogFunction>(() => dialogFunctionMock);
    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);
    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());

    resetPinHandler = ResetPinHandlerImpl(
      resetPinUiHandler: resetPinUiHandlerMock,
      authRepo: authenticationRepoMock,
    );

    registerFallbackValue(InitializeResetPinRequest(phoneNumber: phoneNumber));
    registerFallbackValue(ResetPinEntity());
    registerFallbackValue(SessionDialogType.resetPin);
    registerFallbackValue(ErrorUIModel());

    when(() => evoUtilFunctionMock.showHudLoading()).thenAnswer((_) async {});
    when(() => evoUtilFunctionMock.hideHudLoading()).thenAnswer((_) async {});
    when(() => dialogFunctionMock.showDialogSessionTokenExpired(
        onClickPositive: any(named: 'onClickPositive'),
        type: any(named: 'type'))).thenAnswer((_) async {});
  });

  setUp(() {
    errorCallback = MockErrorCallback();

    when(() => resetPinUiHandlerMock.handleVerifyOTP(
          onSuccess: any(named: 'onSuccess'),
          entity: any(named: 'entity'),
        )).thenAnswer((_) async {});

    when(() => resetPinUiHandlerMock.handleFaceAuth(
          onSuccess: any(named: 'onSuccess'),
          entity: any(named: 'entity'),
        )).thenAnswer((_) async {});

    when(() => resetPinUiHandlerMock.handleChangePIN(
          sessionToken: any(named: 'sessionToken'),
          onSuccess: any(named: 'onSuccess'),
        )).thenAnswer((_) async {});

    when(() => resetPinUiHandlerMock.handleLimitExceeded(
          error: any(named: 'error'),
          onClickPositive: any(named: 'onClickPositive'),
        )).thenAnswer((_) async {});
  });

  tearDown(() {
    reset(authenticationRepoMock);
  });

  void stubRepoCall({
    required Map<String, dynamic> responseData,
    int? statusCode = CommonHttpClient.SUCCESS,
  }) {
    when(() => authenticationRepoMock.resetPin(
          request: any(named: 'request'),
          mockConfig: any(named: 'mockConfig'),
        )).thenAnswer((_) async {
      return ResetPinEntity.fromBaseResponse(
        BaseResponse(
          statusCode: statusCode,
          response: responseData,
        ),
      );
    });
  }

  group('test requestResetPin function with success case', () {
    setUpAll(() {
      registerFallbackValue(InitializeResetPinRequest(phoneNumber: ''));
    });

    test('should call [uiHandler.resetPinUiHandlerMock]', () async {
      stubRepoCall(
          responseData: await TestUtil.getResponseMock(
              getMockPinFileNameByCase(MockPinUseCase.getResetPinInitializeSuccess)));

      await resetPinHandler.requestResetPin(
        phoneNumber: phoneNumber,
        entryScreenName: mockScreenName,
        onError: errorCallback.call,
      );

      verify(() => evoUtilFunctionMock.showHudLoading()).called(1);
      verify(() => evoUtilFunctionMock.hideHudLoading()).called(1);
      verify(() => resetPinUiHandlerMock.handleVerifyOTP(
            onSuccess: any(named: 'onSuccess'),
            entity: any(named: 'entity'),
          )).called(1);
    });
  });

  group('test requestResetPin function with fail case', () {
    test('should call [onError] when phoneNumber is null', () async {
      await resetPinHandler.requestResetPin(
        phoneNumber: null,
        entryScreenName: mockScreenName,
        onError: errorCallback.call,
      );

      verify(() => errorCallback.call(any())).called(1);
      verifyNever(() => evoUtilFunctionMock.showHudLoading());
      verifyNever(() => authenticationRepoMock.resetPin(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          ));
    });

    test('should call [onError] when having http error', () async {
      stubRepoCall(statusCode: CommonHttpClient.BAD_REQUEST, responseData: <String, dynamic>{});

      await resetPinHandler.requestResetPin(
        phoneNumber: phoneNumber,
        entryScreenName: mockScreenName,
        onError: errorCallback.call,
      );

      final dynamic captured = verify(() => errorCallback(captureAny())).captured.first;

      expect(
          captured,
          isA<ErrorUIModel?>().having(
            (ErrorUIModel? error) => error?.statusCode,
            'verify status_code',
            CommonHttpClient.BAD_REQUEST,
          ));
    });

    test('test requestResetPin function with fail case, and verdict is locked_resource', () async {
      stubRepoCall(statusCode: CommonHttpClient.LOCKED_RESOURCE, responseData: <String, dynamic>{});

      await resetPinHandler.requestResetPin(
        phoneNumber: phoneNumber,
        entryScreenName: mockScreenName,
        onError: errorCallback.call,
      );

      verify(() => resetPinUiHandlerMock.handleLimitExceeded(
            error: any(named: 'error'),
            onClickPositive: (resetPinHandler as ResetPinHandlerImpl).onPopUntilEntryScreen,
          )).called(1);
    });

    test('test requestResetPin function with fail case, and verdict is limit_exceeded', () async {
      stubRepoCall(statusCode: CommonHttpClient.LIMIT_EXCEEDED, responseData: <String, dynamic>{});

      await resetPinHandler.requestResetPin(
        phoneNumber: phoneNumber,
        entryScreenName: mockScreenName,
        onError: errorCallback.call,
      );

      verify(() => resetPinUiHandlerMock.handleLimitExceeded(
            error: any(named: 'error'),
            onClickPositive: (resetPinHandler as ResetPinHandlerImpl).onPopUntilEntryScreen,
          )).called(1);
    });
  });

  group('test onChallengeSuccess function', () {
    test('should call [uiHandler.handleVerifyOTP] when challenge_type is verify_otp ', () async {
      resetPinHandler.onChallengeSuccess(ResetPinEntity(
        challengeType: ResetPinType.verifyOTP.value,
      ));

      verify(() => resetPinUiHandlerMock.handleVerifyOTP(
            onSuccess: any(named: 'onSuccess'),
            entity: any(named: 'entity'),
          )).called(1);
    });

    test('should call [uiHandler.handleFaceAuth] when challenge_type is face_auth ', () async {
      resetPinHandler.onChallengeSuccess(ResetPinEntity(
        challengeType: ResetPinType.faceAuth.value,
      ));

      verify(() => resetPinUiHandlerMock.handleFaceAuth(
            onSuccess: any(named: 'onSuccess'),
            entity: any(named: 'entity'),
          )).called(1);
    });

    test('should call [uiHandler.handleChangePIN] when challenge_type is change_pin ', () async {
      resetPinHandler.onChallengeSuccess(ResetPinEntity(
        challengeType: ResetPinType.changePin.value,
        sessionToken: sessionToken,
      ));

      verify(() => resetPinUiHandlerMock.handleChangePIN(
            onSuccess: any(named: 'onSuccess'),
            sessionToken: sessionToken,
          )).called(1);
    });

    test('should call [uiHandler.handleResetPinSuccess] when challenge_type is none ', () async {
      resetPinHandler.onChallengeSuccess(ResetPinEntity(
        challengeType: ResetPinType.none.value,
        sessionToken: sessionToken,
      ));

      verify(() => resetPinUiHandlerMock.handleResetPinSuccess(
            onSuccess: any(named: 'onSuccess'),
          )).called(1);
    });

    test('should handle error when challenge_type is unknown ', () async {
      (resetPinHandler as ResetPinHandlerImpl).onError = errorCallback.call;

      resetPinHandler.onChallengeSuccess(ResetPinEntity(
        challengeType: 'unsupported challenge',
      ));

      verify(() => errorCallback(any())).called(1);
    });
  });

  group('test onPopUntilEntryScreen function', () {
    test('should do nothing if screenName is null', () async {
      final ResetPinHandlerImpl handler = resetPinHandler as ResetPinHandlerImpl;
      handler.entryScreenName = null;

      handler.onPopUntilEntryScreen();

      verifyNever(() => mockNavigatorContext.popUntilNamed(mockScreenName));
    });

    test('should popUntilNamed if screenName is not null', () async {
      final ResetPinHandlerImpl handler = resetPinHandler as ResetPinHandlerImpl;
      handler.entryScreenName = mockScreenName;

      handler.onPopUntilEntryScreen();

      verify(() => mockNavigatorContext.popUntilNamed(mockScreenName)).called(1);
    });
  });
}
