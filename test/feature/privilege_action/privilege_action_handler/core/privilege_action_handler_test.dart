import 'package:evoapp/feature/privilege_action/privilege_action_handler/core/privilege_action_handler.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';

class MockPrivilegeActionHandler extends PrivilegeActionHandler<String> {
  final bool shouldFail;

  MockPrivilegeActionHandler({this.shouldFail = false});

  @override
  Future<PrivilegeActionResponse<String>> execute(
      {String? biometricToken, String? pin, PrivilegeActionRequest? request}) async {
    if (shouldFail) {
      return PrivilegeActionResponse<String>.error(ErrorUIModel(userMessage: 'An error occurred'));
    } else {
      return PrivilegeActionResponse<String>.success('mock data');
    }
  }
}

class MockPrivilegeActionRequest extends PrivilegeActionRequest {}

void main() {
  group('PrivilegeActionResponse', () {
    test('success response should have data and no error', () {
      final PrivilegeActionResponse<String> response =
          PrivilegeActionResponse<String>.success('test data');

      expect(response.isSuccess, isTrue);
      expect(response.data, 'test data');
      expect(response.error, isNull);
    });

    test('error response should have error and no data', () {
      final ErrorUIModel error = ErrorUIModel(userMessage: 'An error occurred');
      final PrivilegeActionResponse<String> response = PrivilegeActionResponse<String>.error(error);

      expect(response.isSuccess, isFalse);
      expect(response.data, isNull);
      expect(response.error, error);
    });
  });

  group('PrivilegeActionHandler', () {
    test('performs should return success response', () async {
      final MockPrivilegeActionHandler handler = MockPrivilegeActionHandler();
      final PrivilegeActionRequest request = MockPrivilegeActionRequest();
      final PrivilegeActionResponse<String> response = await handler.execute(request: request);

      expect(response.isSuccess, isTrue);
      expect(response.data, 'mock data');
      expect(response.error, isNull);
    });

    test('performs should return error response', () async {
      final MockPrivilegeActionHandler handler = MockPrivilegeActionHandler(shouldFail: true);
      final PrivilegeActionRequest request = MockPrivilegeActionRequest();
      final PrivilegeActionResponse<String> response = await handler.execute(request: request);

      expect(response.isSuccess, isFalse);
      expect(response.data, isNull);
      expect(response.error, isNotNull);
    });
  });
}
