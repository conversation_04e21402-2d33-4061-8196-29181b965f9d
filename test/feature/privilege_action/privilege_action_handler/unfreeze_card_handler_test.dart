import 'package:evoapp/feature/privilege_action/privilege_action_handler/core/privilege_action_handler.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/unfreeze_card_handler.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late UnfreezeCardHandler handler;

  setUp(() {
    handler = UnfreezeCardHandler();
  });

  group('UnfreezeCardHandler', () {
    test('should be PrivilegeActionHandler', () {
      expect(handler, isA<PrivilegeActionHandler<dynamic>>());
    });

    test('should return UnfreezeCardActionData data on success', () async {
      // TODO: update with API integration, mock dependencies to succeed
      final PrivilegeActionResponse<dynamic> response = await handler.execute();
      expect(response.data, isA<UnfreezeCardActionData>());
    });
  });
}
