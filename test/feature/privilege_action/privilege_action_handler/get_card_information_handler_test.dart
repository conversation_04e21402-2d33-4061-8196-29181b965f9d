import 'package:evoapp/feature/privilege_action/privilege_action_handler/core/privilege_action_handler.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/get_card_information_handler.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late GetCardInformationHandler handler;

  setUp(() {
    handler = GetCardInformationHandler();
  });

  test('performs returns success response', () async {
    final PrivilegeActionResponse<GetCardInformationResponse> response = await handler.execute();

    expect(response.isSuccess, isTrue);
    expect(response.data, isNotNull);
    expect(response.data?.cardNumber, '111111111');
    expect(response.data?.cardHolderName, 'Username');
    expect(response.data?.cardExpiryDate, '12/12');
    expect(response.data?.cvv, '123');
  });
}
