import 'package:evoapp/feature/privilege_action/privilege_action_handler/core/privilege_action_handler.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/get_card_information_handler.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/privilege_action_factory.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/unfreeze_card_handler.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PrivilegeActionHandlerFactory', () {
    final PrivilegeActionHandlerFactory factory = PrivilegeActionHandlerFactory();

    test('should return GetCardInformationHandler for showCardInformation type', () {
      final PrivilegeActionHandler<dynamic> handler =
          factory.createHandler(PrivilegeActionType.showCardInformation);
      expect(handler, isA<GetCardInformationHandler>());
    });

    test('should return UnfreezeCardHandler for unfreezeCard type', () {
      final PrivilegeActionHandler<dynamic> handler =
          factory.createHandler(PrivilegeActionType.unfreezeCard);
      expect(handler, isA<UnfreezeCardHandler>());
    });
  });
}
