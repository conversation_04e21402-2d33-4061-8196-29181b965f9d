import 'package:evoapp/feature/privilege_action/verify_pin_privilege_action/verify_pin_privilege_action_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/core/privilege_action_handler.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/privilege_action_factory.dart';
import 'package:evoapp/feature/privilege_action/privilege_access_guard_module.dart';
import 'package:evoapp/feature/privilege_action/verify_biometric_privilege_action/verify_biometric_privilege_action.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockVerifyBiometricForPrivilegeAction extends Mock
    implements VerifyBiometricForPrivilegeAction {}

class MockPrivilegeActionHandlerFactory extends Mock implements PrivilegeActionHandlerFactory {}

class MockPrivilegeActionHandler extends Mock implements PrivilegeActionHandler<dynamic> {}

class MockPrivilegeAccessGuardCallback extends Mock implements PrivilegeAccessGuardCallback {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

void main() {
  late BuildContext mockNavigatorContext;
  late CommonNavigator commonNavigator;

  late MockVerifyBiometricForPrivilegeAction mockVerifyBiometricAction;
  late MockPrivilegeActionHandlerFactory mockHandlerFactory;
  late MockPrivilegeActionHandler mockHandler;
  late MockPrivilegeAccessGuardCallback mockPrivilegeAccessGuardCallback;
  late PrivilegeAccessGuardModule privilegeAccessGuardModule;

  setUpAll(() {
    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());
    commonNavigator = getIt.get<CommonNavigator>();

    /// Register fallback values when stubbing methods with optional named parameters
    registerFallbackValue(MockBuildContext());
    registerFallbackValue(PrivilegeActionType.showCardInformation);
    registerFallbackValue(PrivilegeActionType.unfreezeCard);
    registerFallbackValue(MockPrivilegeActionHandler());
    registerFallbackValue(ErrorUIModel());
  });

  setUp(() {
    mockVerifyBiometricAction = MockVerifyBiometricForPrivilegeAction();
    mockHandlerFactory = MockPrivilegeActionHandlerFactory();
    mockHandler = MockPrivilegeActionHandler();
    mockPrivilegeAccessGuardCallback = MockPrivilegeAccessGuardCallback();
    privilegeAccessGuardModule = PrivilegeAccessGuardModule(
      biometricForPrivilegeAction: mockVerifyBiometricAction,
      privilegeActionFactory: mockHandlerFactory,
    );

    when(() => mockHandlerFactory.createHandler(any())).thenReturn(mockHandler);

    when(() => commonNavigator.pushNamed(
          any(),
          any(),
          extra: any(named: 'extra'),
        )).thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    reset(commonNavigator);
    reset(mockVerifyBiometricAction);
    reset(mockPrivilegeAccessGuardCallback);
  });

  void assertVerifyPinPrivilegeActionScreenIsOpenedWithCorrectArgs() {
    final VerifyPinPrivilegeActionScreenArgs arg = verify(() => commonNavigator.pushNamed(
        mockNavigatorContext, Screen.verifyPinPrivilegeActionScreen.name,
        extra: captureAny(named: 'extra'))).captured.single;

    expect(arg.handler, mockHandler);
    expect(arg.onComplete, isNotNull);

    arg.onComplete(PrivilegeActionResponse<String>.success('pin success'));
    verify(() => mockPrivilegeAccessGuardCallback.onPrivilegeActionSuccess('pin success'))
        .called(1);

    final ErrorUIModel error = ErrorUIModel(userMessage: 'pin error');
    arg.onComplete(PrivilegeActionResponse<dynamic>.error(error));

    verify(() => mockPrivilegeAccessGuardCallback.onPrivilegeActionError(error)).called(1);
  }

  test('confirm should show verify pin screen if biometric authentication is not enabled',
      () async {
    when(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled())
        .thenAnswer((_) async => false);

    await privilegeAccessGuardModule.confirm(
      callback: mockPrivilegeAccessGuardCallback,
      type: PrivilegeActionType.showCardInformation,
    );

    verify(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled()).called(1);
    verifyNever(() => mockVerifyBiometricAction.authenticate(
        handler: any(named: 'handler'), request: any(named: 'request')));

    assertVerifyPinPrivilegeActionScreenIsOpenedWithCorrectArgs();
  });

  test('confirm should show verify pin screen if forcePin is enable', () async {
    when(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled())
        .thenAnswer((_) async => true);

    await privilegeAccessGuardModule.confirm(
      callback: mockPrivilegeAccessGuardCallback,
      type: PrivilegeActionType.showCardInformation,
      isForcePin: true,
    );

    verifyNever(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled());
    verifyNever(() => mockVerifyBiometricAction.authenticate(
        handler: any(named: 'handler'), request: any(named: 'request')));

    assertVerifyPinPrivilegeActionScreenIsOpenedWithCorrectArgs();
  });

  group('confirm should use biometric authentication if enabled', () {
    test('should call onPrivilegeActionSuccess callback if PrivilegeActionResponse is success ',
        () async {
      when(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled())
          .thenAnswer((_) async => true);
      when(() => mockVerifyBiometricAction.authenticate(
              handler: any(named: 'handler'), request: any(named: 'request')))
          .thenAnswer((_) async => PrivilegeActionResponse<String>.success('biometric success'));

      await privilegeAccessGuardModule.confirm(
        callback: mockPrivilegeAccessGuardCallback,
        type: PrivilegeActionType.showCardInformation,
      );

      verify(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled()).called(1);
      verify(() => mockVerifyBiometricAction.authenticate(handler: mockHandler)).called(1);
      verify(() => mockPrivilegeAccessGuardCallback.onPrivilegeActionSuccess('biometric success'))
          .called(1);
      verifyNever(() => mockPrivilegeAccessGuardCallback.onPrivilegeActionError(any()));
    });

    test('should call onPrivilegeActionSuccess callback if PrivilegeActionResponse is error ',
        () async {
      when(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled())
          .thenAnswer((_) async => true);

      final ErrorUIModel mockErrorUIModel = ErrorUIModel(userMessage: 'error');
      when(() => mockVerifyBiometricAction.authenticate(
              handler: any(named: 'handler'), request: any(named: 'request')))
          .thenAnswer((_) async => PrivilegeActionResponse<String>.error(mockErrorUIModel));

      await privilegeAccessGuardModule.confirm(
        callback: mockPrivilegeAccessGuardCallback,
        type: PrivilegeActionType.showCardInformation,
      );

      verify(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled()).called(1);
      verify(() => mockVerifyBiometricAction.authenticate(handler: mockHandler)).called(1);

      verifyNever(() => mockPrivilegeAccessGuardCallback.onPrivilegeActionSuccess(any()));
      verify(() => mockPrivilegeAccessGuardCallback.onPrivilegeActionError(mockErrorUIModel))
          .called(1);
    });

    test('confirm should show verify pin screen if biometric authentication fails', () async {
      when(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled())
          .thenAnswer((_) async => true);
      when(() => mockVerifyBiometricAction.authenticate(
          handler: any(named: 'handler'),
          request: any(named: 'request'))).thenAnswer((_) async => null);

      await privilegeAccessGuardModule.confirm(
        callback: mockPrivilegeAccessGuardCallback,
        type: PrivilegeActionType.showCardInformation,
      );

      verify(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled()).called(1);
      verify(() => mockVerifyBiometricAction.authenticate(handler: mockHandler)).called(1);

      assertVerifyPinPrivilegeActionScreenIsOpenedWithCorrectArgs();
    });
  });

  /// Simulate PIN verification screen is opened, after user input input pin.
  /// This will call onCompleteVerifyPinPrivilegeAction callback
  group('verify onCompleteVerifyPinPrivilegeAction callback', () {
    setUp(() async {
      when(() => mockVerifyBiometricAction.isAuthenticateBiometricsEnabled())
          .thenAnswer((_) async => true);

      await privilegeAccessGuardModule.confirm(
        callback: mockPrivilegeAccessGuardCallback,
        type: PrivilegeActionType.showCardInformation,
        isForcePin: true,
      );
    });
  });
}
