import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/feature/privilege_action/verify_biometric_privilege_action/verify_biometric_privilege_action.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/feature/privilege_action/privilege_action_handler/core/privilege_action_handler.dart';
import 'package:mocktail/mocktail.dart';

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockJwtHelper extends Mock implements JwtHelper {}

class MockPrivilegeActionHandler extends Mock implements PrivilegeActionHandler<dynamic> {}

void main() {
  const String mockToken = 'token';

  late VerifyBiometricForPrivilegeAction verifyBiometricForPrivilegeAction;
  late MockBiometricsAuthenticate mockBiometricsAuthenticate;
  late MockEvoLocalStorageHelper mockEvoLocalStorageHelper;
  late MockEvoUtilFunction mockEvoUtilFunction;
  late MockJwtHelper mockJwtHelper;
  late MockPrivilegeActionHandler mockPrivilegeActionHandler;

  setUp(() {
    mockBiometricsAuthenticate = MockBiometricsAuthenticate();
    mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
    mockEvoUtilFunction = MockEvoUtilFunction();
    mockJwtHelper = MockJwtHelper();
    mockPrivilegeActionHandler = MockPrivilegeActionHandler();

    verifyBiometricForPrivilegeAction = VerifyBiometricForPrivilegeAction(
      biometricsAuthenticate: mockBiometricsAuthenticate,
      secureStorageHelper: mockEvoLocalStorageHelper,
      evoUtilFunction: mockEvoUtilFunction,
      jwtHelper: mockJwtHelper,
    );
  });

  group('isAuthenticateBiometricsEnabled', () {
    test('should return true when both biometric authentication and token are usable', () async {
      when(() => mockEvoLocalStorageHelper.isEnableBiometricAuthenticator())
          .thenAnswer((_) async => true);
      when(() => mockEvoLocalStorageHelper.getBiometricToken()).thenAnswer((_) async => mockToken);
      when(() => mockJwtHelper.isCanUse(mockToken)).thenReturn(true);

      final bool result = await verifyBiometricForPrivilegeAction.isAuthenticateBiometricsEnabled();

      expect(result, true);
    });

    test('should return false when biometric authentication is not enabled', () async {
      when(() => mockEvoLocalStorageHelper.isEnableBiometricAuthenticator())
          .thenAnswer((_) async => false);
      when(() => mockEvoLocalStorageHelper.getBiometricToken()).thenAnswer((_) async => mockToken);
      when(() => mockJwtHelper.isCanUse(mockToken)).thenReturn(true);

      final bool result = await verifyBiometricForPrivilegeAction.isAuthenticateBiometricsEnabled();

      expect(result, false);
    });

    test('should return false when token is not usable', () async {
      when(() => mockEvoLocalStorageHelper.isEnableBiometricAuthenticator())
          .thenAnswer((_) async => true);
      when(() => mockEvoLocalStorageHelper.getBiometricToken()).thenAnswer((_) async => mockToken);
      when(() => mockJwtHelper.isCanUse(mockToken)).thenReturn(false);

      final bool result = await verifyBiometricForPrivilegeAction.isAuthenticateBiometricsEnabled();

      expect(result, false);
    });
  });

  group('authenticate', () {
    setUp(() {
      when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) => Future<void>.value());
      when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) => Future<void>.value());
    });

    tearDown(() {
      reset(mockEvoUtilFunction);
      reset(mockBiometricsAuthenticate);
      reset(mockPrivilegeActionHandler);
      reset(mockEvoLocalStorageHelper);
      reset(mockJwtHelper);
    });

    test('should return PrivilegeActionResponse on successful biometric authentication', () async {
      /// stub the biometric authentication
      final BioAuthResult bioAuthResult = BioAuthResult(isAuthSuccess: true);
      when(() => mockBiometricsAuthenticate.authenticate(
          localizedReason: any(named: 'localizedReason'))).thenAnswer((_) async => bioAuthResult);

      /// stub the biometricToken is valid
      when(() => mockEvoLocalStorageHelper.getBiometricToken()).thenAnswer((_) async => 'token');
      when(() => mockJwtHelper.isCanUse(mockToken)).thenReturn(true);

      /// stub the mockPrivilegeActionHandler act properly
      when(() =>
              mockPrivilegeActionHandler.execute(biometricToken: mockToken, request: any(named: 'request')))
          .thenAnswer((_) async => PrivilegeActionResponse<String>.success('data'));

      final PrivilegeActionResponse<dynamic>? result =
          await verifyBiometricForPrivilegeAction.authenticate(
        handler: mockPrivilegeActionHandler,
      );

      verify(() => mockBiometricsAuthenticate.authenticate(
          localizedReason: any(named: 'localizedReason'))).called(1);

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);

      verify(() =>
              mockPrivilegeActionHandler.execute(biometricToken: mockToken, request: any(named: 'request')))
          .called(1);

      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);

      expect(result, isA<PrivilegeActionResponse<String>>());
      expect(result!.isSuccess, true);
    });

    test('should return null on failed biometric authentication', () async {
      /// stub the biometric authentication
      final BioAuthResult bioAuthResult = BioAuthResult(isAuthSuccess: false);
      when(() => mockBiometricsAuthenticate.authenticate(
          localizedReason: any(named: 'localizedReason'))).thenAnswer((_) async => bioAuthResult);

      /// stub the biometricToken is valid
      when(() => mockEvoLocalStorageHelper.getBiometricToken()).thenAnswer((_) async => 'token');
      when(() => mockJwtHelper.isCanUse(mockToken)).thenReturn(true);

      final PrivilegeActionResponse<dynamic>? result =
          await verifyBiometricForPrivilegeAction.authenticate(
        handler: mockPrivilegeActionHandler,
      );

      verify(() => mockBiometricsAuthenticate.authenticate(
          localizedReason: any(named: 'localizedReason'))).called(1);

      verifyNever(() => mockEvoUtilFunction.showHudLoading());

      verifyNever(() => mockPrivilegeActionHandler.execute(
          biometricToken: any(named: 'request'), request: any(named: 'request')));

      verifyNever(() => mockEvoUtilFunction.hideHudLoading());

      expect(result, null);
    });

    test('should return null on biometric token is invalid', () async {
      /// stub the biometric authentication
      final BioAuthResult bioAuthResult = BioAuthResult(isAuthSuccess: true);
      when(() => mockBiometricsAuthenticate.authenticate(
          localizedReason: any(named: 'localizedReason'))).thenAnswer((_) async => bioAuthResult);

      /// stub the biometricToken is valid
      when(() => mockEvoLocalStorageHelper.getBiometricToken()).thenAnswer((_) async => mockToken);
      when(() => mockJwtHelper.isCanUse(mockToken)).thenReturn(false);


      final PrivilegeActionResponse<dynamic>? result =
      await verifyBiometricForPrivilegeAction.authenticate(
        handler: mockPrivilegeActionHandler,
      );

      verify(() => mockBiometricsAuthenticate.authenticate(
          localizedReason: any(named: 'localizedReason'))).called(1);

      verifyNever(() => mockEvoUtilFunction.showHudLoading());

      verifyNever(() => mockPrivilegeActionHandler.execute(
          biometricToken: any(named: 'request'), request: any(named: 'request')));

      verifyNever(() => mockEvoUtilFunction.hideHudLoading());
      
      expect(result, null);
    });
  });


}
