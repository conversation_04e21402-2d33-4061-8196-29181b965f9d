import 'package:evoapp/feature/privilege_action/privilege_action_handler/core/privilege_action_handler.dart';
import 'package:evoapp/feature/privilege_action/verify_pin_privilege_action/verify_pin_privilege_action_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('VerifyPinPrivilegeActionInitState is a subclass of VerifyPinPrivilegeActionState', () {
    expect(VerifyPinPrivilegeActionInitState(), isA<VerifyPinPrivilegeActionState>());
  });

  test('PrivilegeActionLoadingState is a subclass of VerifyPinPrivilegeActionState', () {
    expect(PrivilegeActionLoadingState(), isA<VerifyPinPrivilegeActionState>());
  });

  test('PrivilegeActionComplete contains the correct result', () {
    final PrivilegeActionResponse<bool> result = PrivilegeActionResponse<bool>.success(true);
    final PrivilegeActionCompleteState  state = PrivilegeActionCompleteState(result: result);
    expect(state.result, result);
  });

  test('PrivilegeActionBadRequest contains the correct userMessage', () {
    const String userMessage = 'Bad Request';
    final PrivilegeActionBadRequestState state = PrivilegeActionBadRequestState(userMessage: userMessage);
    expect(state.userMessage, userMessage);
  });
}