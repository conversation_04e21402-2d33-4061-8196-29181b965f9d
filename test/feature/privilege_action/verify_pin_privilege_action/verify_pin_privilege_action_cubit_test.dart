import 'package:evoapp/feature/privilege_action/privilege_action_handler/core/privilege_action_handler.dart';
import 'package:evoapp/feature/privilege_action/verify_pin_privilege_action/verify_pin_privilege_action_cubit.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class MockPrivilegeActionHandler extends Mock implements PrivilegeActionHandler<dynamic> {}

void main() {
  late VerifyPinPrivilegeActionCubit cubit;
  late MockPrivilegeActionHandler mockPrivilegeActionHandler;

  setUp(() {
    mockPrivilegeActionHandler = MockPrivilegeActionHandler();
    cubit = VerifyPinPrivilegeActionCubit(privilegeActionHandler: mockPrivilegeActionHandler);
  });

  tearDown(() {
    cubit.close();
  });

  test('initial state is VerifyPinPrivilegeActionInitState', () {
    expect(cubit.state, isA<VerifyPinPrivilegeActionInitState>());
  });

  blocTest<VerifyPinPrivilegeActionCubit, VerifyPinPrivilegeActionState>(
    'emits [PrivilegeActionLoadingState, PrivilegeActionCompleteState] when performs is successful',
    build: () {
      when(() => mockPrivilegeActionHandler.execute(
              pin: any(named: 'pin'), request: any(named: 'request')))
          .thenAnswer((_) async => PrivilegeActionResponse<bool>.success(true));
      return cubit;
    },
    act: (VerifyPinPrivilegeActionCubit cubit) => cubit.performs(pin: '1234'),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<PrivilegeActionLoadingState>(),
      isA<PrivilegeActionCompleteState>()
          .having((PrivilegeActionCompleteState state) => state.result.data, 'verify data', true)
          .having((PrivilegeActionCompleteState state) => state.result.error, 'verify error', null)
    ],
  );

  blocTest<VerifyPinPrivilegeActionCubit, VerifyPinPrivilegeActionState>(
    'emits [PrivilegeActionLoadingState, PrivilegeActionBadRequestState] when performs returns bad request',
    build: () {
      final ErrorUIModel error =
          ErrorUIModel(statusCode: CommonHttpClient.BAD_REQUEST, userMessage: 'Bad Request');
      when(() => mockPrivilegeActionHandler.execute(
              pin: any(named: 'pin'), request: any(named: 'request')))
          .thenAnswer((_) async => PrivilegeActionResponse<dynamic>.error(error));
      return cubit;
    },
    act: (VerifyPinPrivilegeActionCubit cubit) => cubit.performs(pin: '1234'),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<PrivilegeActionLoadingState>(),
      isA<PrivilegeActionBadRequestState>(),
    ],
  );

  blocTest<VerifyPinPrivilegeActionCubit, VerifyPinPrivilegeActionState>(
    'emits [PrivilegeActionLoadingState, PrivilegeActionCompleteState] when performs returns unknow error',
    build: () {
      final ErrorUIModel errorUIModel =
          ErrorUIModel(statusCode: CommonHttpClient.UNKNOWN_ERRORS, userMessage: 'unknown message');
      when(() => mockPrivilegeActionHandler.execute(
              pin: any(named: 'pin'), request: any(named: 'request')))
          .thenAnswer((_) async => PrivilegeActionResponse<dynamic>.error(errorUIModel));
      return cubit;
    },
    act: (VerifyPinPrivilegeActionCubit cubit) => cubit.performs(pin: '1234'),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<PrivilegeActionLoadingState>(),
      isA<PrivilegeActionCompleteState>()
          .having((PrivilegeActionCompleteState state) => state.result.data, 'verify data', null)
          .having((PrivilegeActionCompleteState state) => state.result.error?.statusCode,
              'verify error', CommonHttpClient.UNKNOWN_ERRORS)
    ],
  );
}
