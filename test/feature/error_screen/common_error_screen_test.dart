import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_handler.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_popup.dart';
import 'package:evoapp/feature/authorization_session_expired/force_logout_popup.dart';
import 'package:evoapp/feature/error_screen/common_error_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test_config.dart';

class MockAuthorizationSessionExpiredHandler extends Mock
    implements AuthorizationSessionExpiredHandler {}

class MockAuthorizationSessionExpiredPopup extends Mock
    implements AuthorizationSessionExpiredPopup {}

class MockForceLogoutPopup extends Mock implements ForceLogoutPopup {}

class MockAppState extends Mock implements AppState {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

void main() {
  late CommonImageProvider commonImageProvider;
  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
  });

  setUp(() {
    commonImageProvider = getIt.get<CommonImageProvider>();
  });

  group('CommonErrorScreen', () {
    const String testTitle = 'Test Error Title';
    const String testDescription = 'Test Error Description';
    const String testButtonText = 'Test Button';
    bool onTapCalled = false;

    void onTap() {
      onTapCalled = true;
    }

    Widget createWidget() {
      return MaterialApp(
        home: CommonErrorScreen(
          title: testTitle,
          description: testDescription,
          buttonText: testButtonText,
          onTap: onTap,
        ),
      );
    }

    setUp(() {
      onTapCalled = false;
    });

    testWidgets('displays all required elements correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      expect(find.text(testTitle), findsOneWidget);
      expect(find.text(testDescription), findsOneWidget);
      expect(find.text(testButtonText), findsOneWidget);

      verify(() => commonImageProvider.asset(
            EvoImages.icAlertError,
            width: any(named: 'width'),
            height: any(named: 'height'),
          )).called(1);
    });

    testWidgets('button tap triggers onTap callback', (WidgetTester tester) async {
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      await tester.tap(find.text(testButtonText));
      await tester.pumpAndSettle();

      expect(onTapCalled, true);
    });
  });
}
