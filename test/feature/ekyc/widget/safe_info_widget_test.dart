import 'package:evoapp/feature/ekyc/widget/safe_info_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class _FakeImage extends SizedBox {}

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

void main() {
  late CommonImageProvider imageProvider;

  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();

    imageProvider = getIt.registerSingleton<CommonImageProvider>(MockCommonImageProvider());
    when(() => imageProvider.asset(EvoImages.icPolicy,
        color: any(named: 'color'),
        height: any(named: 'height'),
        width: any(named: 'width'))).thenAnswer((_) => _FakeImage());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('SafeInfoWidget', () {
    testWidgets('should show correct icon and text', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SafeInfoWidget(),
          ),
        ),
      );

      verify(() => imageProvider.asset(EvoImages.icPolicy,
          color: any(named: 'color'),
          height: any(named: 'height'),
          width: any(named: 'width'))).called(1);

      expect(
        find.textContaining(EvoStrings.selfieSafeInfoDesc, findRichText: true),
        findsOneWidget,
      );
    });
  });
}
