import 'package:evoapp/feature/ekyc/intro/face_capture_check_screen.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/appbar/evo_appbar.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('FaceCaptureCheckScreen', () {
    final String mockSessionToken = 'mock-session-token';
    void mockOnPopSuccess(ChallengeSuccessModel model) {}

    testWidgets('should have needed widgets', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: FaceCaptureCheckScreen(
            sessionToken: mockSessionToken,
            onPopSuccess: mockOnPopSuccess,
          ),
        ),
      );

      expect(find.byType(EvoAppBar), findsOneWidget);

      expect(find.text(EvoStrings.faceCaptureCheckTitle), findsOneWidget);
      expect(find.textContaining(EvoStrings.faceCaptureCheckDesc), findsOneWidget);

      verify(() => evoImageProvider.asset(
            EvoImages.imgFaceCaptureCheck,
            width: any(named: 'width'),
            height: any(named: 'height'),
            fit: any(named: 'fit'),
          )).called(1);

      expect(
        find.descendant(of: find.byType(PrimaryButton), matching: find.text(EvoStrings.ctaProceed)),
        findsOneWidget,
      );
    });

    test('should self navigate with pushNamed', () {
      FaceCaptureCheckScreen.pushNamed(
        sessionToken: mockSessionToken,
        onPopSuccess: mockOnPopSuccess,
      );

      verify(() => mockNavigatorContext.pushNamed(
            Screen.faceCaptureCheckScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should navigate to selfie verification flow on tapping proceed button',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: FaceCaptureCheckScreen(
            sessionToken: mockSessionToken,
            onPopSuccess: mockOnPopSuccess,
          ),
        ),
      );

      await tester.tap(find.descendant(
        of: find.byType(PrimaryButton),
        matching: find.text(EvoStrings.ctaProceed),
      ));

      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.selfieVerificationScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });
  });
}
