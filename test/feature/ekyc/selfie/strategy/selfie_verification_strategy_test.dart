import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/request/login_new_device_request.dart';
import 'package:evoapp/data/request/reset_pin_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/activate_account_selfie_strategy.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/reset_pin_selfie_strategy.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/selfie_verification_strategy.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/log_in_selfie_strategy.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockResetPinEntity extends Mock implements ResetPinEntity {}

class MockLoginNewDeviceEntity extends Mock implements LoginNewDeviceEntity {}

class MockAccountActivationEntity extends Mock implements AccountActivationEntity {}

void main() {
  group('SelfieVerificationStrategy Tests', () {
    late MockAuthenticationRepo mockAuthRepo;
    const String testSelfieType = 'liveness';
    const String testSessionToken = 'test_session_token';
    const List<String> testImageIds = <String>['image1', 'image2'];
    const List<String> testVideoIds = <String>['video1', 'video2'];

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(ResetPinFaceAuthRequest(sessionToken: 'test'));
      registerFallbackValue(SelfieAuthRequest(
        sessionToken: 'test',
        imageIds: <String>[],
        videoIds: <String>[],
        selfieType: 'test',
      ));
      registerFallbackValue(ActivateAccountVerifySelfieRequest(
        selfieType: 'test',
        imageIds: <String>[],
        videoIds: <String>[],
        sessionToken: 'test',
      ));
      registerFallbackValue(FakeMockConfig());
    });

    setUp(() {
      mockAuthRepo = MockAuthenticationRepo();
    });

    group('ResetPinSelfieStrategy', () {
      late SelfieVerificationStrategy strategy;

      setUp(() {
        strategy = ResetPinSelfieStrategy(mockAuthRepo);
      });

      test('should call resetPin with correct parameters', () async {
        // Arrange
        final MockResetPinEntity mockEntity = MockResetPinEntity();
        when(() => mockAuthRepo.resetPin(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => mockEntity);

        // Act
        final BaseEntity result = await strategy.verifySelfie(
          selfieType: testSelfieType,
          sessionToken: testSessionToken,
          imageIds: testImageIds,
          videoIds: testVideoIds,
        );

        // Assert
        expect(result, equals(mockEntity));
        verify(() => mockAuthRepo.resetPin(
              request: any(
                named: 'request',
                that: isA<ResetPinFaceAuthRequest>()
                    .having((ResetPinFaceAuthRequest req) => req.selfieType, 'selfieType',
                        testSelfieType)
                    .having((ResetPinFaceAuthRequest req) => req.sessionToken, 'sessionToken',
                        testSessionToken)
                    .having((ResetPinFaceAuthRequest req) => req.imageIds, 'imageIds', testImageIds)
                    .having(
                        (ResetPinFaceAuthRequest req) => req.videoIds, 'videoIds', testVideoIds),
              ),
              mockConfig: any(named: 'mockConfig', that: isA<MockConfig>()),
            )).called(1);
      });

      test('should handle null parameters correctly', () async {
        // Arrange
        final MockResetPinEntity mockEntity = MockResetPinEntity();
        when(() => mockAuthRepo.resetPin(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => mockEntity);

        // Act
        final BaseEntity result = await strategy.verifySelfie(
          selfieType: testSelfieType,
          sessionToken: null,
          imageIds: null,
          videoIds: null,
        );

        // Assert
        expect(result, equals(mockEntity));
        verify(() => mockAuthRepo.resetPin(
              request: any(
                named: 'request',
                that: isA<ResetPinFaceAuthRequest>()
                    .having((ResetPinFaceAuthRequest req) => req.sessionToken, 'sessionToken', null)
                    .having((ResetPinFaceAuthRequest req) => req.imageIds, 'imageIds', null)
                    .having((ResetPinFaceAuthRequest req) => req.videoIds, 'videoIds', null),
              ),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });
    });

    group('LogInSelfieStrategy', () {
      late SelfieVerificationStrategy strategy;

      setUp(() {
        strategy = LogInSelfieStrategy(mockAuthRepo);
      });

      test('should call loginNewDevice with correct parameters', () async {
        // Arrange
        final MockLoginNewDeviceEntity mockEntity = MockLoginNewDeviceEntity();
        when(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => mockEntity);

        // Act
        final BaseEntity result = await strategy.verifySelfie(
          selfieType: testSelfieType,
          sessionToken: testSessionToken,
          imageIds: testImageIds,
          videoIds: testVideoIds,
        );

        // Assert
        expect(result, equals(mockEntity));
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                named: 'request',
                that: isA<SelfieAuthRequest>()
                    .having((SelfieAuthRequest req) => req.selfieType, 'selfieType', testSelfieType)
                    .having((SelfieAuthRequest req) => req.sessionToken, 'sessionToken',
                        testSessionToken)
                    .having((SelfieAuthRequest req) => req.imageIds, 'imageIds', testImageIds)
                    .having((SelfieAuthRequest req) => req.videoIds, 'videoIds', testVideoIds),
              ),
              mockConfig: any(
                  named: 'mockConfig',
                  that: isA<MockConfig>()
                      .having((MockConfig config) => config.enable, 'enable', false)),
            )).called(1);
      });
    });

    group('ActivateAccountSelfieStrategy', () {
      late SelfieVerificationStrategy strategy;

      setUp(() {
        strategy = ActivateAccountSelfieStrategy(mockAuthRepo);
      });

      test('should call activateAccount with correct parameters', () async {
        // Arrange
        final MockAccountActivationEntity mockEntity = MockAccountActivationEntity();
        when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => mockEntity);

        // Act
        final BaseEntity result = await strategy.verifySelfie(
          selfieType: testSelfieType,
          sessionToken: testSessionToken,
          imageIds: testImageIds,
          videoIds: testVideoIds,
        );

        // Assert
        expect(result, equals(mockEntity));
        verify(() => mockAuthRepo.activateAccount(
              request: any(
                named: 'request',
                that: isA<ActivateAccountVerifySelfieRequest>()
                    .having((ActivateAccountVerifySelfieRequest req) => req.selfieType,
                        'selfieType', testSelfieType)
                    .having((ActivateAccountVerifySelfieRequest req) => req.sessionToken,
                        'sessionToken', testSessionToken)
                    .having((ActivateAccountVerifySelfieRequest req) => req.imageIds, 'imageIds',
                        testImageIds)
                    .having((ActivateAccountVerifySelfieRequest req) => req.videoIds, 'videoIds',
                        testVideoIds),
              ),
              mockConfig: any(named: 'mockConfig', that: isA<MockConfig>()),
            )).called(1);
      });
    });
  });
}
