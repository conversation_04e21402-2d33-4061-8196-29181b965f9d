import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_verification_flow_type.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/activate_account_selfie_strategy.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/reset_pin_selfie_strategy.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/selfie_verification_strategy.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/selfie_verification_strategy_factory.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/log_in_selfie_strategy.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  group('SelfieVerificationStrategyFactory Tests', () {
    late MockAuthenticationRepo mockAuthRepo;
    late SelfieVerificationStrategyFactory factory;

    setUp(() {
      mockAuthRepo = MockAuthenticationRepo();
      factory = SelfieVerificationStrategyFactory();
    });

    test('should create ResetPinSelfieStrategy for resetPin flow type', () {
      // Act
      final SelfieVerificationStrategy strategy = factory.create(
        SelfieVerificationFlowType.resetPin,
        mockAuthRepo,
      );

      // Assert
      expect(strategy, isA<ResetPinSelfieStrategy>());
    });

    test('should create LogInSelfieStrategy for LogIn flow type', () {
      // Act
      final SelfieVerificationStrategy strategy = factory.create(
        SelfieVerificationFlowType.logIn,
        mockAuthRepo,
      );

      // Assert
      expect(strategy, isA<LogInSelfieStrategy>());
    });

    test('should create ActivateAccountSelfieStrategy for activateAccount flow type', () {
      // Act
      final SelfieVerificationStrategy strategy = factory.create(
        SelfieVerificationFlowType.activateAccount,
        mockAuthRepo,
      );

      // Assert
      expect(strategy, isA<ActivateAccountSelfieStrategy>());
    });

    test('should create different strategy instances for each call', () {
      // Act
      final SelfieVerificationStrategy strategy1 = factory.create(
        SelfieVerificationFlowType.resetPin,
        mockAuthRepo,
      );
      final SelfieVerificationStrategy strategy2 = factory.create(
        SelfieVerificationFlowType.resetPin,
        mockAuthRepo,
      );

      // Assert
      expect(strategy1, isA<ResetPinSelfieStrategy>());
      expect(strategy2, isA<ResetPinSelfieStrategy>());
      expect(strategy1, isNot(same(strategy2))); // Different instances
    });

    test('should pass the same authRepo instance to created strategies', () {
      // This test verifies that the factory correctly passes dependencies
      // We can't directly test this without exposing internal state,
      // but we can verify the strategy works with the repo

      // Act
      final SelfieVerificationStrategy strategy = factory.create(
        SelfieVerificationFlowType.resetPin,
        mockAuthRepo,
      );

      // Assert
      expect(strategy, isA<ResetPinSelfieStrategy>());
      // The strategy should be created successfully with the provided repo
    });
  });
}
