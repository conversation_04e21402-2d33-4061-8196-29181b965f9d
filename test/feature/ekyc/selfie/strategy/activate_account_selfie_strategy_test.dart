// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/selfie_verification_strategy.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/activate_account_selfie_strategy.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockAccountActivationEntity extends Mock implements AccountActivationEntity {}

void main() {
  group('ActivateAccountSelfieStrategy Tests', () {
    late MockAuthenticationRepo mockAuthRepo;
    late SelfieVerificationStrategy strategy;
    const String testSelfieType = 'liveness';
    const String testSessionToken = 'test_session_token';
    const List<String> testImageIds = <String>['image1', 'image2'];
    const List<String> testVideoIds = <String>['video1', 'video2'];

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(ActivateAccountVerifySelfieRequest(
        selfieType: 'test',
        imageIds: <String>[],
        videoIds: <String>[],
        sessionToken: '',
      ));
      registerFallbackValue(FakeMockConfig());
    });

    setUp(() {
      mockAuthRepo = MockAuthenticationRepo();
      strategy = ActivateAccountSelfieStrategy(mockAuthRepo);
    });

    test('should call activateAccount with correct parameters', () async {
      // Arrange
      final MockAccountActivationEntity mockEntity = MockAccountActivationEntity();
      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => mockEntity);

      // Act
      final BaseEntity result = await strategy.verifySelfie(
        selfieType: testSelfieType,
        sessionToken: testSessionToken,
        imageIds: testImageIds,
        videoIds: testVideoIds,
      );

      // Assert
      expect(result, equals(mockEntity));
      verify(() => mockAuthRepo.activateAccount(
            request: any(
              named: 'request',
              that: isA<ActivateAccountVerifySelfieRequest>()
                  .having((ActivateAccountVerifySelfieRequest req) => req.selfieType, 'selfieType', testSelfieType)
                  .having((ActivateAccountVerifySelfieRequest req) => req.sessionToken, 'sessionToken', testSessionToken)
                  .having((ActivateAccountVerifySelfieRequest req) => req.imageIds, 'imageIds', testImageIds)
                  .having((ActivateAccountVerifySelfieRequest req) => req.videoIds, 'videoIds', testVideoIds),
            ),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should handle null parameters correctly', () async {
      // Arrange
      final MockAccountActivationEntity mockEntity = MockAccountActivationEntity();
      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => mockEntity);

      // Act
      final BaseEntity result = await strategy.verifySelfie(
        selfieType: testSelfieType,
        sessionToken: null,
      );

      // Assert
      expect(result, equals(mockEntity));
      verify(() => mockAuthRepo.activateAccount(
            request: any(
              named: 'request',
              that: isA<ActivateAccountVerifySelfieRequest>()
                  .having((ActivateAccountVerifySelfieRequest req) => req.selfieType, 'selfieType', testSelfieType)
                  .having((ActivateAccountVerifySelfieRequest req) => req.sessionToken, 'sessionToken', isNull)
                  .having((ActivateAccountVerifySelfieRequest req) => req.imageIds, 'imageIds', isNull)
                  .having((ActivateAccountVerifySelfieRequest req) => req.videoIds, 'videoIds', isNull),
            ),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should use correct mock config for activateAccount', () async {
      // Arrange
      final MockAccountActivationEntity mockEntity = MockAccountActivationEntity();
      when(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => mockEntity);

      // Act
      await strategy.verifySelfie(
        selfieType: testSelfieType,
        sessionToken: testSessionToken,
      );

      // Assert
      verify(() => mockAuthRepo.activateAccount(
            request: any(named: 'request'),
            mockConfig: any(
              named: 'mockConfig',
              that: isA<MockConfig>().having((MockConfig config) => config.enable, 'enable', false),
            ),
          )).called(1);
    });
  });
}
