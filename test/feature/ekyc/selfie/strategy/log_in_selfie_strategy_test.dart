// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/login_new_device_request.dart';
import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/selfie_verification_strategy.dart';
import 'package:evoapp/feature/ekyc/selfie/strategy/log_in_selfie_strategy.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockLoginNewDeviceEntity extends Mock implements LoginNewDeviceEntity {}

void main() {
  group('LogInSelfieStrategy Tests', () {
    late MockAuthenticationRepo mockAuthRepo;
    late SelfieVerificationStrategy strategy;
    const String testSelfieType = 'liveness';
    const String testSessionToken = 'test_session_token';
    const List<String> testImageIds = <String>['image1', 'image2'];
    const List<String> testVideoIds = <String>['video1', 'video2'];

    setUpAll(() {
      // Register fallback values for mocktail
      registerFallbackValue(SelfieAuthRequest(
        sessionToken: 'test',
        imageIds: <String>[],
        videoIds: <String>[],
        selfieType: '',
      ));
      registerFallbackValue(FakeMockConfig());
    });

    setUp(() {
      mockAuthRepo = MockAuthenticationRepo();
      strategy = LogInSelfieStrategy(mockAuthRepo);
    });

    test('should call loginNewDevice with correct parameters', () async {
      // Arrange
      final MockLoginNewDeviceEntity mockEntity = MockLoginNewDeviceEntity();
      when(() => mockAuthRepo.loginNewDevice(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => mockEntity);

      // Act
      final BaseEntity result = await strategy.verifySelfie(
        selfieType: testSelfieType,
        sessionToken: testSessionToken,
        imageIds: testImageIds,
        videoIds: testVideoIds,
      );

      // Assert
      expect(result, equals(mockEntity));
      verify(() => mockAuthRepo.loginNewDevice(
            request: any(
              named: 'request',
              that: isA<SelfieAuthRequest>()
                  .having((SelfieAuthRequest req) => req.selfieType, 'selfieType', testSelfieType)
                  .having(
                      (SelfieAuthRequest req) => req.sessionToken, 'sessionToken', testSessionToken)
                  .having((SelfieAuthRequest req) => req.imageIds, 'imageIds', testImageIds)
                  .having((SelfieAuthRequest req) => req.videoIds, 'videoIds', testVideoIds),
            ),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should handle null parameters correctly', () async {
      // Arrange
      final MockLoginNewDeviceEntity mockEntity = MockLoginNewDeviceEntity();
      when(() => mockAuthRepo.loginNewDevice(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => mockEntity);

      // Act
      final BaseEntity result = await strategy.verifySelfie(
        selfieType: testSelfieType,
        sessionToken: null,
      );

      // Assert
      expect(result, equals(mockEntity));
      verify(() => mockAuthRepo.loginNewDevice(
            request: any(
              named: 'request',
              that: isA<SelfieAuthRequest>()
                  .having((SelfieAuthRequest req) => req.selfieType, 'selfieType', testSelfieType)
                  .having((SelfieAuthRequest req) => req.sessionToken, 'sessionToken', isNull)
                  .having((SelfieAuthRequest req) => req.imageIds, 'imageIds', isNull)
                  .having((SelfieAuthRequest req) => req.videoIds, 'videoIds', isNull),
            ),
            mockConfig: any(named: 'mockConfig'),
          )).called(1);
    });

    test('should use correct mock config for login new device', () async {
      // Arrange
      final MockLoginNewDeviceEntity mockEntity = MockLoginNewDeviceEntity();
      when(() => mockAuthRepo.loginNewDevice(
            request: any(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async => mockEntity);

      // Act
      await strategy.verifySelfie(
        selfieType: testSelfieType,
        sessionToken: testSessionToken,
      );

      // Assert
      verify(() => mockAuthRepo.loginNewDevice(
            request: any(named: 'request'),
            mockConfig: any(
              named: 'mockConfig',
              that: isA<MockConfig>().having((MockConfig config) => config.enable, 'enable', false),
            ),
          )).called(1);
    });
  });
}
