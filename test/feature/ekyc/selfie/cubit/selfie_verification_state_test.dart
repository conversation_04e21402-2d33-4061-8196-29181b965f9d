import 'package:evoapp/feature/ekyc/selfie/cubit/selfie_verification_cubit.dart';
import 'package:evoapp/feature/ekyc/selfie/model/selfie_error_ui_model.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SelfieVerificationState', () {
    test('SelfieVerificationInitial is a subtype of SelfieVerificationState', () {
      expect(SelfieVerificationInitial(), isA<SelfieVerificationState>());
    });

    test('SelfieVerificationLoading is a subtype of SelfieVerificationState', () {
      expect(SelfieVerificationLoading(), isA<SelfieVerificationState>());
    });

    test('SelfieVerificationProcessing is a subtype of SelfieVerificationState', () {
      expect(SelfieVerificationProcessing(), isA<SelfieVerificationState>());
    });

    test('SelfieVerificationSuccess is a subtype of SelfieVerificationState', () {
      final BaseEntity entity = BaseEntity();
      final SelfieVerificationSuccess state = SelfieVerificationSuccess(entity);

      expect(state, isA<SelfieVerificationState>());
      expect(state.entity, entity);
    });

    test('SelfieVerificationFailure is a subtype of SelfieCaptureState', () {
      final SelfieErrorUiModel error = SelfieErrorUiModel(
        actionType: ErrorActionType.ignore,
      );
      final SelfieVerificationFailure state = SelfieVerificationFailure(error: error);

      expect(state, isA<SelfieVerificationState>());
      expect(state.error, equals(error));
    });

    test('SelfieCapturingSuccess is a subtype of SelfieVerificationState', () {
      final SelfieCapturingSuccess state = SelfieCapturingSuccess(
        imageIds: <String>['imageIds'],
        videoIds: <String>['videoIds'],
      );

      expect(state, isA<SelfieVerificationState>());
      expect(state.imageIds, <String>['imageIds']);
      expect(state.videoIds, <String>['videoIds']);
    });
  });
}
