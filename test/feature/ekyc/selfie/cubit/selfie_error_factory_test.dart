import 'package:evoapp/feature/ekyc/selfie/cubit/selfie_error_factory.dart';
import 'package:evoapp/feature/ekyc/selfie/model/selfie_error_ui_model.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_error_reason.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  final SelfieVerificationErrorFactory factory = SelfieVerificationErrorFactory();

  group('SelfieVerificationErrorFactory', () {
    test('should create unknown error when no entity or bridge error is provided', () {
      final SelfieErrorUiModel errorModel = factory.create();

      expect(errorModel.title, EvoStrings.selfieErrorTitle);
      expect(errorModel.description, EvoStrings.selfieErrorSubtitle);
      expect(errorModel.actionType, ErrorActionType.retry);
    });

    group('createApiError', () {
      test('should create API error for locked resource', () {
        final BaseEntity entity = BaseEntity(
          statusCode: CommonHttpClient.LOCKED_RESOURCE,
        );
        final SelfieErrorUiModel errorModel = factory.createApiError(entity);
        expect(
          errorModel.actionType,
          ErrorActionType.blocked,
        );
      });

      test('should create API error for limit exceeded', () {
        final BaseEntity entity = BaseEntity(statusCode: CommonHttpClient.LIMIT_EXCEEDED);
        final SelfieErrorUiModel errorModel = factory.createApiError(entity);

        expect(
          errorModel.actionType,
          ErrorActionType.blocked,
        );
      });

      test('should create API error for invalid token', () {
        final BaseEntity entity = BaseEntity(statusCode: CommonHttpClient.INVALID_TOKEN);
        final SelfieErrorUiModel errorModel = factory.createApiError(entity);

        expect(errorModel.actionType, ErrorActionType.showExpiredToken);
      });

      test('should create API error with retry action for other status codes', () {
        final BaseEntity entity = BaseEntity(statusCode: CommonHttpClient.BAD_REQUEST);
        final SelfieErrorUiModel errorModel = factory.createApiError(entity);

        expect(errorModel.actionType, ErrorActionType.retry);
      });

      test('getApiErrorActionType should return correct type', () async {
        ErrorActionType getActionType(int? statusCode, [String? verdict]) {
          return factory.getApiErrorActionType(BaseEntity(
            statusCode: statusCode,
            verdict: verdict,
          ));
        }

        expect(getActionType(CommonHttpClient.INVALID_TOKEN, 'verdict'),
            ErrorActionType.showExpiredToken);
        expect(getActionType(CommonHttpClient.LOCKED_RESOURCE, 'verdict'), ErrorActionType.blocked);
        expect(getActionType(CommonHttpClient.LIMIT_EXCEEDED, 'verdict'), ErrorActionType.blocked);
        expect(getActionType(CommonHttpClient.UNKNOWN_ERRORS), ErrorActionType.blocked);
        expect(getActionType(CommonHttpClient.UNKNOWN_ERRORS, 'verdict'), ErrorActionType.retry);
        expect(getActionType(CommonHttpClient.BAD_REQUEST, 'verdict'), ErrorActionType.retry);
        expect(getActionType(null), ErrorActionType.retry);
      });
    });

    test('getApiErrorTitle should return correct titles for different status codes', () {
      final SelfieVerificationErrorFactory factory = SelfieVerificationErrorFactory();

      String getTitle(int statusCode, [String? verdict, String? apiTitle]) {
        return factory.getApiErrorTitle(BaseEntity(
          statusCode: statusCode,
          verdict: verdict,
          data: <String, dynamic>{'user_message_title': apiTitle},
        ));
      }

      //should return user_message_title when provided in api response
      expect(getTitle(CommonHttpClient.INVALID_TOKEN, 'verdict', 'title'), 'title');
      expect(getTitle(CommonHttpClient.LIMIT_EXCEEDED, 'verdict', 'title'), 'title');
      expect(getTitle(CommonHttpClient.UNKNOWN_ERRORS, 'verdict', 'title'), 'title');
      expect(getTitle(CommonHttpClient.BAD_REQUEST, 'verdict', 'title'), 'title');
      expect(getTitle(CommonHttpClient.LOCKED_RESOURCE, 'verdict', 'title'), 'title');

      // should return default title when user_message_title is not provided
      expect(getTitle(CommonHttpClient.INVALID_TOKEN, 'verdict'), EvoStrings.selfieErrorTitle);
      expect(
          getTitle(CommonHttpClient.LIMIT_EXCEEDED, 'verdict'), EvoStrings.selfieMaxTriesReached);
      expect(getTitle(CommonHttpClient.UNKNOWN_ERRORS), EvoStrings.errorUnknownErrorTitle);
      expect(getTitle(CommonHttpClient.UNKNOWN_ERRORS, 'verdict'), EvoStrings.selfieErrorTitle);
      expect(getTitle(CommonHttpClient.BAD_REQUEST, 'verdict'), EvoStrings.selfieErrorTitle);
      expect(
          getTitle(CommonHttpClient.LOCKED_RESOURCE, 'verdict'), EvoStrings.selfieMaxTriesReached);
    });

    test('getApiErrorDescription should return correct descriptions for different cases', () {
      final SelfieVerificationErrorFactory factory = SelfieVerificationErrorFactory();

      String getDesc(int statusCode, [String? verdict, String? apiMessage]) {
        return factory.getApiErrorDescription(BaseEntity(
          statusCode: statusCode,
          verdict: verdict,
          data: <String, dynamic>{'user_message': apiMessage},
        ));
      }

      //should return api user_message when provided in api response
      expect(getDesc(CommonHttpClient.INVALID_TOKEN, 'verdict', 'msg'), 'msg');
      expect(getDesc(CommonHttpClient.LIMIT_EXCEEDED, 'verdict', 'msg'), 'msg');
      expect(getDesc(CommonHttpClient.UNKNOWN_ERRORS, 'verdict', 'msg'), 'msg');
      expect(getDesc(CommonHttpClient.BAD_REQUEST, 'verdict', 'msg'), 'msg');
      expect(getDesc(CommonHttpClient.LOCKED_RESOURCE, 'verdict', 'msg'), 'msg');

      // should return default title when user_message is not provided
      expect(getDesc(CommonHttpClient.LIMIT_EXCEEDED, 'verdict'), EvoStrings.tryAgainLater);
      expect(getDesc(CommonHttpClient.LOCKED_RESOURCE, 'verdict'), EvoStrings.tryAgainLater);
      expect(getDesc(CommonHttpClient.INVALID_TOKEN, 'verdict'), EvoStrings.selfieErrorSubtitle);
      expect(getDesc(CommonHttpClient.BAD_REQUEST, 'verdict'), EvoStrings.selfieErrorSubtitle);
      final String unknownErrorDesc = EvoStrings.errorUnknownErrorDesc
          .replaceVariableByValue(<String>[ContactInfo.supportNumber]);
      expect(getDesc(CommonHttpClient.UNKNOWN_ERRORS), unknownErrorDesc);
      expect(getDesc(CommonHttpClient.UNKNOWN_ERRORS, 'verdict'), EvoStrings.selfieErrorSubtitle);
    });

    test(
        'getApiErrorDescription should call getMessageByErrorCode for error not LIMIT_EXCEEDED nor LOCKED_RESOURCE',
        () {
      bool isCalled = false;
      final SelfieVerificationErrorFactory factory = SelfieVerificationErrorFactory(
        getMessageByErrorCode: (_) {
          isCalled = true;
          return '';
        },
      );

      factory.getApiErrorDescription(BaseEntity(statusCode: CommonHttpClient.NO_INTERNET));

      expect(isCalled, true);
    });

    group('createUnknownError', () {
      test('should create unknown error', () {
        final SelfieErrorUiModel errorModel = factory.createUnknownError();

        expect(errorModel.title, EvoStrings.selfieErrorTitle);
        expect(errorModel.description, EvoStrings.selfieErrorSubtitle);
        expect(errorModel.actionType, ErrorActionType.retry);
      });
    });

    group('createBridgeError', () {
      test('should create bridge error for userCancelled', () {
        final SelfieErrorUiModel errorModel =
            factory.createBridgeError(EkycBridgeErrorReason.userCancelled);

        expect(errorModel.actionType, ErrorActionType.ignore);
      });

      test('should create bridge error for exceedLimit', () {
        final SelfieErrorUiModel errorModel = factory.createBridgeError(
          EkycBridgeErrorReason.exceedLimit,
        );

        expect(errorModel.title, EvoStrings.maxTriesReached);
        expect(errorModel.description, EvoStrings.tryAgainLater);
        expect(errorModel.actionType, ErrorActionType.blocked);
      });

      test('should create bridge error for sessionExpired, ignore error title and description', () {
        final SelfieErrorUiModel errorModel = factory.createBridgeError(
          EkycBridgeErrorReason.sessionExpired,
        );

        expect(errorModel.actionType, ErrorActionType.showExpiredToken);
      });

      test('should create bridge error for initWithInvalidSession', () {
        final SelfieErrorUiModel errorModel = factory.createBridgeError(
          EkycBridgeErrorReason.initWithInvalidSession,
        );

        expect(errorModel.actionType, ErrorActionType.showExpiredToken);
      });
    });
  });
}
