import 'package:evoapp/feature/ekyc/selfie/model/selfie_error_ui_model.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SelfieErrorUiModel', () {
    test('should set default values when title and description are not provided', () {
      final SelfieErrorUiModel errorModel = SelfieErrorUiModel(actionType: ErrorActionType.retry);

      expect(errorModel.title, EvoStrings.selfieErrorTitle);
      expect(errorModel.description, EvoStrings.selfieErrorSubtitle);
      expect(errorModel.actionType, ErrorActionType.retry);
    });

    test('should set provided title and description', () {
      final String title = 'Custom Title';
      final String description = 'Custom Description';
      final SelfieErrorUiModel errorModel = SelfieErrorUiModel(
        actionType: ErrorActionType.ignore,
        title: title,
        description: description,
      );

      expect(errorModel.title, title);
      expect(errorModel.description, description);
      expect(errorModel.actionType, ErrorActionType.ignore);
    });

    test('should create from ErrorUIModel with provided actionType', () {
      final ErrorUIModel errorUIModel = ErrorUIModel(
        userMessageTitle: 'Error Title',
        userMessage: 'Error Description',
      );
      final SelfieErrorUiModel errorModel = SelfieErrorUiModel.fromModel(
        actionType: ErrorActionType.blocked,
        errorUIModel: errorUIModel,
      );

      expect(errorModel.title, errorUIModel.userMessageTitle);
      expect(errorModel.description, errorUIModel.userMessage);
      expect(errorModel.actionType, ErrorActionType.blocked);
    });

    test('should use default values when ErrorUIModel is null', () {
      final SelfieErrorUiModel errorModel = SelfieErrorUiModel.fromModel(
        actionType: ErrorActionType.retry,
      );

      expect(errorModel.title, EvoStrings.selfieErrorTitle);
      expect(errorModel.description, EvoStrings.selfieErrorSubtitle);
      expect(errorModel.actionType, ErrorActionType.retry);
    });
  });
}
