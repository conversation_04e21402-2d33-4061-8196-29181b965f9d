// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/ekyc/selfie/selfie_success_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('SelfieSuccessScreen', () {
    bool mockOnProceedCalled = false;

    void mockOnProceed() {
      mockOnProceedCalled = true;
    }

    setUp(() {
      mockOnProceedCalled = false;
    });

    testWidgets('should have needed widgets', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: SelfieSuccessScreen(onProceed: mockOnProceed),
        ),
      );

      verify(() => evoImageProvider.asset(
            EvoImages.imgFaceCaptureCheck,
            width: any(named: 'width'),
            height: any(named: 'height'),
            fit: any(named: 'fit'),
          )).called(1);

      expect(find.text(EvoStrings.selfieSuccessTitle), findsOneWidget);

      expect(find.text(EvoStrings.ctaProceed), findsOneWidget);
    });

    test('should self navigate with pushReplacementNamed', () {
      SelfieSuccessScreen.pushReplacementNamed(onProceed: mockOnProceed);

      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.selfieSuccessScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should call onProceed callback when tapping proceed button',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: SelfieSuccessScreen(onProceed: mockOnProceed),
        ),
      );

      expect(mockOnProceedCalled, false);

      await tester.tap(find.text(EvoStrings.ctaProceed));

      expect(mockOnProceedCalled, true);
    });
  });
}
