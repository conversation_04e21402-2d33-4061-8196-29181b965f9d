import 'package:evoapp/feature/ekyc/selfie/widgets/selfie_verification_state_widget.dart';
import 'package:evoapp/feature/ekyc/widget/safe_info_widget.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';

void main() {
  late CommonImageProvider mockImageProvider;

  setUpAll(() {
    initConfigEvoPageStateBase();

    mockImageProvider = getIt.get<CommonImageProvider>();

    when(() => mockImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
        )).thenReturn(SizedBox());
  });

  group('verify build widget', () {
    buildWidget(WidgetTester tester, VerificationStatus status) async {
      await tester.pumpWidget(MaterialApp(
        home: SelfieVerificationStateWidget(status: status),
      ));
    }

    testWidgets('should render correctly with VerificationStatus.none',
        (WidgetTester tester) async {
      await buildWidget(tester, VerificationStatus.none);

      expect(find.byType(SafeInfoWidget), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsNothing);
    });

    testWidgets('should render correctly with VerificationStatus.processing',
        (WidgetTester tester) async {
      await buildWidget(tester, VerificationStatus.processing);

      expect(find.byType(SafeInfoWidget), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text(EvoStrings.selfieProcessingDesc), findsOneWidget);
    });

    testWidgets('should render correctly with VerificationStatus.initializing',
        (WidgetTester tester) async {
      await buildWidget(tester, VerificationStatus.initializing);

      expect(find.byType(SafeInfoWidget), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text(EvoStrings.selfieInitializingDesc), findsOneWidget);
    });
  });
}
