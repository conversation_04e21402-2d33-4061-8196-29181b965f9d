import 'package:evoapp/feature/ekyc/selfie/selfie_retry_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';

class MockCallback extends Mock {
  void call();
}

void main() {
  late MockCallback mockOnRetry;

  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
    mockOnRetry = MockCallback();
  });

  group('SelfieRetryScreen', () {
    test('should self navigate on pushNamed', () async {
      SelfieRetryScreen.pushNamed(onRetry: mockOnRetry.call);

      verify(() => mockNavigatorContext.pushNamed(
            Screen.selfieRetryScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should render correct UI with default error', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: SelfieRetryScreen(onRetry: mockOnRetry.call),
      ));

      verify(() => evoImageProvider.asset(
            EvoImages.icAlertUnsuccessful,
            width: any(named: 'width'),
            height: any(named: 'height'),
          )).called(1);

      expect(find.text(EvoStrings.selfieErrorTitle), findsOneWidget);
      expect(find.text(EvoStrings.selfieErrorSubtitle), findsOneWidget);

      final Finder retryBtn = find.widgetWithText(PrimaryButton, EvoStrings.retry);
      expect(retryBtn, findsOneWidget);
    });

    testWidgets('should render correct UI with provided error', (WidgetTester tester) async {
      final String error = 'Error';
      await tester.pumpWidget(MaterialApp(
        home: SelfieRetryScreen(onRetry: mockOnRetry.call, error: error),
      ));

      verify(() => evoImageProvider.asset(
            EvoImages.icAlertUnsuccessful,
            width: any(named: 'width'),
            height: any(named: 'height'),
          )).called(1);

      expect(find.text(EvoStrings.selfieErrorTitle), findsOneWidget);
      expect(find.text(error), findsOneWidget);
      expect(find.text(EvoStrings.selfieErrorSubtitle), findsNothing);

      final Finder retryBtn = find.widgetWithText(PrimaryButton, EvoStrings.retry);
      expect(retryBtn, findsOneWidget);
    });

    testWidgets('should call onRetry callback when retry button is tapped',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: SelfieRetryScreen(onRetry: mockOnRetry.call),
      ));

      await tester.tap(find.byType(PrimaryButton));

      verify(() => mockNavigatorContext.pop()).called(1);
      verify(() => mockOnRetry.call()).called(1);
    });

    testWidgets('should prevent popping', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: SelfieRetryScreen(onRetry: mockOnRetry.call),
      ));

      final PopScope popScope = tester.firstWidget(find.byType(PopScope));
      expect(popScope.canPop, false);
    });
  });
}
