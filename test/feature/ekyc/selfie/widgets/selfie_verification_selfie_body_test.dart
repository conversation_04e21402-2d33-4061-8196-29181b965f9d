import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/ekyc/selfie/cubit/selfie_error_factory.dart';
import 'package:evoapp/feature/ekyc/selfie/cubit/selfie_verification_cubit.dart';
import 'package:evoapp/feature/ekyc/selfie/model/selfie_error_ui_model.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_locked_screen.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_retry_screen.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_verification_flow_type.dart';
import 'package:evoapp/feature/ekyc/selfie/widgets/selfie_verification_selfie_body.dart';
import 'package:evoapp/feature/ekyc/selfie/widgets/selfie_verification_state_widget.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/feature/ekyc/bridges/models/ekyc_bridge_liveness_mode.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/evo_page_state_base_test_config.dart';

class MockSelfieVerificationCubit extends MockCubit<SelfieVerificationState>
    implements SelfieVerificationCubit {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockOnPopSuccess extends Mock {
  void call(ChallengeSuccessModel model);
}

void main() {
  late SelfieVerificationCubit mockSelfieCubit;
  final SelfieVerificationFlowType mockFlowType = SelfieVerificationFlowType.logIn;
  late MockOnPopSuccess mockOnPopSuccess;
  final String mockSessionToken = 'mock-session-token';
  late DevicePlatform mockDevicePlatform;
  late CommonNavigator mockCommonNavigator;

  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
    mockDevicePlatform = getIt.get<DevicePlatform>();
    when(() => mockDevicePlatform.isAndroid()).thenReturn(true);

    final CommonImageProvider mockImageProvider = getIt.get<CommonImageProvider>();
    when(() => mockImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
        )).thenReturn(SizedBox());

    registerFallbackValue(EkycBridgeLivenessMode.flash_8);
    registerFallbackValue(SelfieVerificationFlowType.logIn);

    mockCommonNavigator = getIt.get<CommonNavigator>();
  });

  setUp(() {
    mockOnPopSuccess = MockOnPopSuccess();
    mockSelfieCubit = MockSelfieVerificationCubit();
    when(() => mockSelfieCubit.state).thenReturn(SelfieVerificationInitial());
    when(() => mockSelfieCubit.initialize(any())).thenAnswer((_) async {});
  });

  buildWidget(WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
          home: BlocProvider<SelfieVerificationCubit>(
        create: (_) => mockSelfieCubit,
        child: SelfieVerificationSelfieBody(
          onPopSuccess: mockOnPopSuccess.call,
          flowType: mockFlowType,
          sessionToken: mockSessionToken,
          errorFactory: SelfieVerificationErrorFactory(),
        ),
      )),
    );
  }

  group('test widget\'s content build', () {
    testWidgets(
        'should show correct VerificationStatus and canPop according to SelfieVerificationState',
        (WidgetTester tester) async {
      Future<void> testStatusAndCanPop({
        required SelfieVerificationState state,
        required VerificationStatus expectedStatus,
        required bool expectedCanPop,
      }) async {
        await tester.pumpWidget(SizedBox(key: UniqueKey()));

        when(() => mockSelfieCubit.state).thenReturn(state);
        await buildWidget(tester);

        final SelfieVerificationStateWidget stateWidget =
            tester.firstWidget(find.byType(SelfieVerificationStateWidget));
        final PopScope popScope = tester.firstWidget(find.byType(PopScope));

        expect(stateWidget.status, expectedStatus);
        expect(popScope.canPop, expectedCanPop);
      }

      await testStatusAndCanPop(
        state: SelfieVerificationInitial(),
        expectedStatus: VerificationStatus.initializing,
        expectedCanPop: false,
      );

      await testStatusAndCanPop(
        state: SelfieVerificationLoading(),
        expectedStatus: VerificationStatus.initializing,
        expectedCanPop: false,
      );

      await testStatusAndCanPop(
        state: InitializeBridgeSuccess(),
        expectedStatus: VerificationStatus.none,
        expectedCanPop: true,
      );

      await testStatusAndCanPop(
        state: SelfieVerificationProcessing(),
        expectedStatus: VerificationStatus.none,
        expectedCanPop: true,
      );

      await testStatusAndCanPop(
        state: SelfieCapturingSuccess(),
        expectedStatus: VerificationStatus.processing,
        expectedCanPop: false,
      );

      await testStatusAndCanPop(
        state: SelfieVerificationSuccess(BaseEntity()),
        expectedStatus: VerificationStatus.none,
        expectedCanPop: true,
      );

      await testStatusAndCanPop(
        state: SelfieVerificationFailure(
            error: SelfieErrorUiModel(actionType: ErrorActionType.ignore)),
        expectedStatus: VerificationStatus.none,
        expectedCanPop: true,
      );
    });
  });

  group('test handling state event', () {
    testWidgets(
        'should call selfieCubit.captureSelfie with selfieCubit.captureSelfie() when [selfieState] is [InitializeBridgeSuccess]',
        (WidgetTester tester) async {
      when(() => mockSelfieCubit.captureSelfie(liveMode: any(named: 'liveMode')))
          .thenAnswer((_) async {});
      whenListen(
        mockSelfieCubit,
        Stream<SelfieVerificationState>.fromIterable([
          SelfieVerificationInitial(),
          InitializeBridgeSuccess(),
        ]),
      );
      await buildWidget(tester);

      verify(() => mockSelfieCubit.captureSelfie(
            liveMode: any(named: 'liveMode'),
          )).called(1);
    });

    testWidgets(
        'should pushReplacementNamed to SelfieSuccessScreen when [selfieState] is [SelfieVerificationSuccess]',
        (WidgetTester tester) async {
      final BaseEntity entity = BaseEntity();
      whenListen(
        mockSelfieCubit,
        Stream<SelfieVerificationState>.fromIterable([
          SelfieVerificationInitial(),
          SelfieVerificationSuccess(entity),
        ]),
      );
      await buildWidget(tester);

      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.selfieSuccessScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should  selfieCubit.verifySelfie() when [selfieState] is [SelfieCapturingSuccess]',
        (WidgetTester tester) async {
      final List<String> mockVideosIds = <String>['video-0', 'video-1'];
      final List<String> mockImageIds = <String>['image-0', 'image-1'];
      when(() => mockSelfieCubit.verifySelfie(
            flowType: any(named: 'flowType'),
            liveMode: any(named: 'liveMode'),
            sessionToken: any(named: 'sessionToken'),
            imageIds: any(named: 'imageIds'),
            videoIds: any(named: 'videoIds'),
          )).thenAnswer((_) async {});

      whenListen(
        mockSelfieCubit,
        Stream<SelfieVerificationState>.fromIterable(<SelfieVerificationState>[
          SelfieVerificationInitial(),
          SelfieCapturingSuccess(
            videoIds: mockVideosIds,
            imageIds: mockImageIds,
          ),
        ]),
      );
      await buildWidget(tester);

      verify(() => mockSelfieCubit.verifySelfie(
            flowType: mockFlowType,
            liveMode: any(named: 'liveMode'),
            sessionToken: mockSessionToken,
            imageIds: mockImageIds,
            videoIds: mockVideosIds,
          )).called(1);
    });

    group('handling error', () {
      final String mockDesc = 'mock-desc';
      final String mockTitle = 'mock-title';

      void stubEmitError(SelfieErrorUiModel error) {
        whenListen(
          mockSelfieCubit,
          Stream<SelfieVerificationState>.fromIterable([
            SelfieVerificationInitial(),
            SelfieVerificationFailure(error: error),
          ]),
        );
      }

      testWidgets('should pushNamed [selfieRetryScreen] when ErrorActionType.retry',
          (WidgetTester tester) async {
        stubEmitError(SelfieErrorUiModel(
          actionType: ErrorActionType.retry,
          description: mockDesc,
        ));

        await buildWidget(tester);

        final SelfieRetryScreenArg captured = verify(() => mockCommonNavigator.pushNamed(
              any(),
              Screen.selfieRetryScreen.name,
              extra: captureAny(named: 'extra'),
            )).captured.first as SelfieRetryScreenArg;
        captured.onRetry();

        /// called twice, first time when app initialized, second time on called retry
        verify(() => mockSelfieCubit.initialize(mockSessionToken)).called(2);
        expect(captured.error, mockDesc);
      });

      testWidgets(
          'should call pushReplacementNamed [SelfieLockedScreen] with ErrorActionType.blocked',
          (WidgetTester tester) async {
        stubEmitError(SelfieErrorUiModel(
          actionType: ErrorActionType.blocked,
          title: mockTitle,
          description: mockDesc,
        ));

        await buildWidget(tester);

        final SelfieLockedScreenArg captured =
            verify(() => mockCommonNavigator.pushReplacementNamed(
                  any(),
                  Screen.selfieLockedScreen.name,
                  extra: captureAny(named: 'extra'),
                )).captured.first as SelfieLockedScreenArg;

        expect(
            captured,
            isA<SelfieLockedScreenArg>().having(
              (SelfieLockedScreenArg arg) => (arg.title, arg.subtitle),
              'verify arg',
              (mockTitle, mockDesc),
            ));
      });

      testWidgets('should show the token expired dialog with ErrorActionType.showExpiredToken',
          (WidgetTester tester) async {
        stubEmitError(SelfieErrorUiModel(
          actionType: ErrorActionType.showExpiredToken,
        ));

        await buildWidget(tester);

        verify(() => evoDialogFunction.showDialogSessionTokenExpired(type: any(named: 'type')))
            .called(1);
      });

      testWidgets('should call pop with ErrorActionType.ignore', (WidgetTester tester) async {
        stubEmitError(SelfieErrorUiModel(
          actionType: ErrorActionType.ignore,
        ));

        await buildWidget(tester);

        verify(() => mockCommonNavigator.pop(
              any(),
              result: any(named: 'result'),
            )).called(1);
      });
    });
  });

  group('SelfieVerificationFlowTypeExt', () {
    test('dialogType should return correct SessionDialogType', () async {
      expect(
        SelfieVerificationFlowType.activateAccount.dialogType,
        SessionDialogType.activateAccount,
      );
      expect(
        SelfieVerificationFlowType.resetPin.dialogType,
        SessionDialogType.resetPin,
      );
      expect(
        SelfieVerificationFlowType.logIn.dialogType,
        SessionDialogType.newDeviceLogIn,
      );
    });
  });
}
