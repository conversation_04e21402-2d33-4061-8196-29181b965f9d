// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/ekyc/selfie/selfie_locked_screen.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

void main() {
  final String testTitle = 'Test Title';
  final String testSubtitle = 'Test Subtitle';

  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  group('SelfieLockedScreen', () {
    test('should self navigate on pushReplacementNamed', () async {
      SelfieLockedScreen.pushReplacementNamed(title: testTitle, subtitle: testSubtitle);

      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.selfieLockedScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should render correct UI', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: SelfieLockedScreen(title: testTitle, subtitle: testSubtitle),
      ));

      verify(() => evoImageProvider.asset(
            EvoImages.icAlertError,
            width: any(named: 'width'),
            height: any(named: 'height'),
          )).called(1);

      expect(find.text(testTitle), findsOneWidget);
      expect(find.text(testSubtitle), findsOneWidget);

      final Finder homePageBtn = find.widgetWithText(PrimaryButton, EvoStrings.backToHomePage);
      expect(homePageBtn, findsOneWidget);
    });

    testWidgets('should navigate to WelcomeScreen when back to home button is tapped',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: SelfieLockedScreen(title: testTitle, subtitle: testSubtitle),
      ));

      await tester.tap(find.byType(PrimaryButton));

      verify(() => mockNavigatorContext.goNamed(
            Screen.welcomeScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('PopScope should prevent popping', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: SelfieLockedScreen(title: testTitle, subtitle: testSubtitle),
      ));

      final PopScope popScope = tester.firstWidget(find.byType(PopScope));
      expect(popScope.canPop, false);
    });
  });
}
