import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/camera_permission/cubit/camera_permission_cubit.dart';
import 'package:evoapp/feature/camera_permission/cubit/camera_permission_state.dart';
import 'package:evoapp/feature/ekyc/selfie/cubit/selfie_verification_cubit.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_verification_flow_type.dart';
import 'package:evoapp/feature/ekyc/selfie/selfie_verification_screen.dart';
import 'package:evoapp/feature/ekyc/selfie/widgets/selfie_verification_selfie_body.dart';
import 'package:evoapp/feature/ekyc/selfie/widgets/selfie_verification_state_widget.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class MockCameraPermissionCubit extends MockCubit<CameraPermissionState>
    implements CameraPermissionCubit {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockSelfieVerificationCubit extends MockCubit<SelfieVerificationState>
    implements SelfieVerificationCubit {}

void main() {
  final SelfieVerificationFlowType mockFlowType = SelfieVerificationFlowType.logIn;
  mockOnPopSuccess(ChallengeSuccessModel model) {}
  final String mockSessionToken = 'mock-session-token';
  late CommonNavigator mockCommonNavigator;

  setUpAll(() {
    initConfigEvoPageStateBase();

    mockCommonNavigator = getIt.get<CommonNavigator>();
  });

  group('Test SelfieVerificationScreen', () {
    test('should self navigate with pushNamed', () {
      SelfieVerificationScreen.pushNamed(
        flowType: mockFlowType,
        onPopSuccess: mockOnPopSuccess,
        sessionToken: mockSessionToken,
      );

      final dynamic captured = verify(() => mockCommonNavigator.pushNamed(
              any(), Screen.selfieVerificationScreen.name, extra: captureAny(named: 'extra')))
          .captured
          .firstOrNull;

      expect(
          captured,
          isA<SelfieVerificationScreenArgs>().having(
              (SelfieVerificationScreenArgs state) =>
                  (state.flowType, state.onPopSuccess, state.sessionToken),
              'verify args',
              (mockFlowType, mockOnPopSuccess, mockSessionToken)));
    });
  });

  group('verify build widget', () {
    late CameraPermissionCubit mockCubit;
    late DevicePlatform mockDevicePlatform;
    late SelfieVerificationCubit mockSelfieCubit;

    setUpAll(() {
      final CommonImageProvider mockImageProvider = getIt.get<CommonImageProvider>();
      when(() => mockImageProvider.asset(
            any(),
            width: any(named: 'width'),
            height: any(named: 'height'),
            fit: any(named: 'fit'),
          )).thenReturn(SizedBox());

      mockDevicePlatform = getIt.get<DevicePlatform>();

      when(() => mockDevicePlatform.isAndroid()).thenReturn(true);
    });

    setUp(() {
      mockCubit = MockCameraPermissionCubit();
      when(() => mockCubit.state).thenReturn(CameraPermissionInitial());

      mockSelfieCubit = MockSelfieVerificationCubit();
      when(() => mockSelfieCubit.state).thenReturn(SelfieVerificationInitial());
      when(() => mockSelfieCubit.initialize(any())).thenAnswer((_) async {});
    });

    buildWidget(WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
            home: MultiBlocProvider(
          providers: [
            BlocProvider<CameraPermissionCubit>.value(
              value: mockCubit,
            ),
            BlocProvider<SelfieVerificationCubit>(
              create: (_) => mockSelfieCubit,
            )
          ],
          child: SelfieVerificationScreen(
            onPopSuccess: mockOnPopSuccess,
            flowType: mockFlowType,
            sessionToken: mockSessionToken,
          ),
        )),
      );
    }

    setUpAll(() {
      setUpMockConfigEvoPageStateBase();
    });

    testWidgets('should render correctly with CameraPermissionInitial',
        (WidgetTester tester) async {
      await buildWidget(tester);

      final Finder finder = find.byWidgetPredicate(
        (Widget widget) =>
            widget is SelfieVerificationStateWidget &&
            widget.status == VerificationStatus.initializing,
      );

      expect(finder, findsOneWidget);
    });

    testWidgets('should render correctly with CameraPermissionDenied', (WidgetTester tester) async {
      when(() => mockCubit.state).thenReturn(CameraPermissionDenied());
      await buildWidget(tester);

      final Finder finder = find.byWidgetPredicate(
        (Widget widget) =>
            widget is SelfieVerificationStateWidget && widget.status == VerificationStatus.none,
      );

      expect(finder, findsOneWidget);
    });

    testWidgets('should render correctly with CameraPermissionGranted',
        (WidgetTester tester) async {
      when(() => mockCubit.state).thenReturn(CameraPermissionGranted());
      await buildWidget(tester);

      final Finder finder = find.byWidgetPredicate(
        (Widget widget) =>
            widget is SelfieVerificationSelfieBody &&
            widget.flowType == mockFlowType &&
            widget.onPopSuccess == mockOnPopSuccess &&
            widget.sessionToken == mockSessionToken,
      );

      expect(finder, findsOneWidget);
    });
  });
}
