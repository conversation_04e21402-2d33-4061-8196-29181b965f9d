// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/feature/ekyc/selfie/selfie_verification_flow_type.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SelfieVerificationFlowType', () {
    test('logIn returns correct dialog type', () {
      expect(
        SelfieVerificationFlowType.logIn.dialogType,
        equals(SessionDialogType.newDeviceLogIn),
      );
    });

    test('resetPin returns correct dialog type', () {
      expect(
        SelfieVerificationFlowType.resetPin.dialogType,
        equals(SessionDialogType.resetPin),
      );
    });

    test('activateAccount returns correct dialog type', () {
      expect(
        SelfieVerificationFlowType.activateAccount.dialogType,
        equals(SessionDialogType.activateAccount),
      );
    });

    test('all enum values are tested', () {
      // This ensures we don't miss testing any values if enum is extended
      final List<SelfieVerificationFlowType> allValues = SelfieVerificationFlowType.values;
      expect(allValues.length, 3);
      expect(allValues, contains(SelfieVerificationFlowType.logIn));
      expect(allValues, contains(SelfieVerificationFlowType.resetPin));
      expect(allValues, contains(SelfieVerificationFlowType.activateAccount));
    });
  });
}
