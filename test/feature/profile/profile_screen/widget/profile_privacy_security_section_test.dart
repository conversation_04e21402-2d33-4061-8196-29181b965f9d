import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/pin/models/change_pin_status.dart';
import 'package:evoapp/widget/action_button_widget.dart';
import 'package:evoapp/feature/profile/profile_screen/widget/profile_privacy_security_section.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/evo_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

class MockAppState extends Mock implements AppState {
  @override
  ChangePinStatusNotifier changePinStatusNotifier = ChangePinStatusNotifier();
}

class MockToggleCallback extends Mock {
  void call(bool value);
}

class MockCallback extends Mock {
  void call();
}

void main() {
  late AppState mockAppState;

  setUpAll(() {
    getIt.registerLazySingleton<AppState>(() => MockAppState());
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterTextStyle();
    getItRegisterColor();

    mockAppState = getIt.get<AppState>();
    final CommonImageProvider commonImageProvider = getIt.get<CommonImageProvider>();

    registerFallbackValue(BoxFit.none);

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
        )).thenAnswer((_) {
      return const SizedBox.shrink();
    });

    when(() => commonImageProvider.network(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
        )).thenAnswer((_) {
      return const SizedBox.shrink();
    });
  });

  group('verify BiometricSwitchTile', () {
    late MockToggleCallback toggleCb;

    setUp(() {
      toggleCb = MockToggleCallback();
    });

    testWidgets('should render nothing when BiometricActivationStatus is unavailable',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: ProfilePrivacySecuritySection(
            biometricStatus: BiometricActivationStatus.unavailable,
            onToggleBiometric: (bool value) {},
            onChangePin: () {},
          ),
        ),
      ));

      expect(find.byWidgetPredicate((Widget widget) {
        return widget is ActionButtonWidget && widget.title == EvoStrings.enableBiometrics;
      }), findsNothing);
    });

    testWidgets(
        'should EvoSwitch called onToggleBiometric(true) when BiometricActivationStatus is deactivate',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: ProfilePrivacySecuritySection(
            biometricStatus: BiometricActivationStatus.deactivated,
            onToggleBiometric: toggleCb.call,
            onChangePin: () {},
          ),
        ),
      ));

      final Finder finder = find.byWidgetPredicate(
        (Widget widget) {
          return widget is EvoSwitch && widget.value == false;
        },
      );

      await tester.tap(finder);

      expect(verify(() => toggleCb.call(captureAny())).captured.first, true);
    });

    testWidgets(
        'should EvoSwitch called onToggleBiometric(false) when BiometricActivationStatus is activate',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: ProfilePrivacySecuritySection(
            biometricStatus: BiometricActivationStatus.activated,
            onToggleBiometric: toggleCb.call,
            onChangePin: () {},
          ),
        ),
      ));

      final Finder finder = find.byWidgetPredicate(
        (Widget widget) {
          return widget is EvoSwitch && widget.value == true;
        },
      );

      await tester.tap(finder);

      expect(verify(() => toggleCb.call(captureAny())).captured.first, false);
    });
  });

  group('verify ChangePinTile', () {
    late MockCallback mockCb;

    setUp(() {
      mockCb = MockCallback();
    });

    testWidgets('should render with default property  when changePinStatusNotifier is available',
        (WidgetTester tester) async {
      mockAppState.changePinStatusNotifier.value = ChangePinStatus.available;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: ProfilePrivacySecuritySection(
            biometricStatus: BiometricActivationStatus.activated,
            onToggleBiometric: (bool value) {},
            onChangePin: () {},
          ),
        ),
      ));

      final Finder finder = find.byWidgetPredicate((Widget widget) {
        return widget is ActionButtonWidget &&
            widget.title == EvoStrings.changeMPIN &&
            <Color?>[
              widget.titleColor,
              widget.trailingColor,
              widget.iconColor,
            ].every((Color? color) => color == null);
      });

      expect(finder, findsOne);
    });

    testWidgets('should render with disabled color when changePinStatusNotifier is locked',
        (WidgetTester tester) async {
      mockAppState.changePinStatusNotifier.value = ChangePinStatus.locked;
      final Color disabledColor = evoColors.greyScale70;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: ProfilePrivacySecuritySection(
            biometricStatus: BiometricActivationStatus.activated,
            onToggleBiometric: (bool value) {},
            onChangePin: () {},
          ),
        ),
      ));

      final Finder finder = find.byWidgetPredicate((Widget widget) {
        return widget is ActionButtonWidget &&
            widget.title == EvoStrings.changeMPIN &&
            <Color?>[
              widget.titleColor,
              widget.trailingColor,
              widget.iconColor,
            ].every((Color? color) => color == disabledColor);
      });

      expect(finder, findsOne);
    });

    testWidgets('should invoked onChangePin when tap on widget', (WidgetTester tester) async {
      mockAppState.changePinStatusNotifier.value = ChangePinStatus.locked;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: ProfilePrivacySecuritySection(
            biometricStatus: BiometricActivationStatus.activated,
            onToggleBiometric: (bool value) {},
            onChangePin: mockCb.call,
          ),
        ),
      ));

      final Finder finder = find.byWidgetPredicate((Widget widget) {
        return widget is ActionButtonWidget && widget.title == EvoStrings.changeMPIN;
      });

      expect(finder, findsOne);

      await tester.tap(finder);

      verify(() => mockCb.call()).called(1);
    });
  });
}
