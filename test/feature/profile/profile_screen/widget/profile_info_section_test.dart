import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/feature/profile/profile_screen/widget/profile_info_section.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider mockImageProvider;

  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    mockImageProvider = getIt.get<CommonImageProvider>();

    registerFallbackValue(BoxFit.none);
  });

  setUp(() {
    when(() => mockImageProvider.network(
          any(),
          fit: any(named: 'fit'),
          height: any(named: 'height'),
          width: any(named: 'width'),
        )).thenReturn(const SizedBox.shrink());

    when(() => mockImageProvider.asset(
          any(),
          fit: any(named: 'fit'),
          height: any(named: 'height'),
          width: any(named: 'width'),
        )).thenReturn(const SizedBox.shrink());
  });

  Widget buildWidget(UserInformationEntity? user) {
    return MaterialApp(
      home: Scaffold(
        body: ProfileInfoSection(
          user: user,
        ),
      ),
    );
  }

  group('verify [ProfileInfoSection] ', () {
    testWidgets('should render correctly when user data is null', (WidgetTester tester) async {
      await tester.pumpWidget(buildWidget(null));
      const String title = '${EvoStrings.hi}!';

      verify(() => mockImageProvider.asset(
            EvoImages.icDefaultAvatar,
            fit: any(named: 'fit'),
            height: any(named: 'height'),
            width: any(named: 'width'),
          )).called(1);

      /// verify no user's info be rendered
      expect(
        find.byWidgetPredicate((Widget widget) {
          return widget is Text && widget.data != title;
        }),
        findsNothing,
      );

      expect(find.text(title), findsOne);
    });

    testWidgets('should render correctly when user data not null', (WidgetTester tester) async {
      const String fullName = 'mock-full-name';
      const String phoneNumber = '631234567890';
      const String email = 'mock-email';
      const String avatarUrl = 'mock-avatar-url';

      const UserInformationEntity user = UserInformationEntity(
        fullName: fullName,
        phoneNumber: phoneNumber,
        email: email,
        avatarUrl: avatarUrl,
      );

      await tester.pumpWidget(buildWidget(user));
      const String title = '${EvoStrings.hi}, $fullName!';

      verify(() => mockImageProvider.network(
            avatarUrl,
            fit: any(named: 'fit'),
            height: any(named: 'height'),
            width: any(named: 'width'),
          )).called(1);

      expect(find.text(fullName), findsOne);
      expect(find.text(title), findsOne);
      expect(find.text('+ 63 ************'), findsOne);
    });
  });
}
