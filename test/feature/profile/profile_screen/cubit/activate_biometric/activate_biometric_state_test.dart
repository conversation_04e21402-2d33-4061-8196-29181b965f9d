import 'package:evoapp/feature/profile/profile_screen/cubit/activate_biometric/activate_biometric_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('should be subtype of BiometricState', () {
    expect(ActivateBiometricInitState(), isA<ActivateBiometricState>());
    expect(BiometricSupported(), isA<ActivateBiometricState>());
    expect(BiometricUnsupported(), isA<ActivateBiometricState>());
    expect(BiometricActivated(), isA<ActivateBiometricState>());
    expect(BiometricDeactivated(), isA<ActivateBiometricState>());
  });
}
