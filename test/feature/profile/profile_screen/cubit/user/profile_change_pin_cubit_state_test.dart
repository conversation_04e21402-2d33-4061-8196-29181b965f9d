import 'package:evoapp/feature/profile/profile_screen/cubit/user/profile_change_pin_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Test UserProfileState', () {
    test('should be subtype of UserProfileState', () {
      expect(ChangePinLoading(), isA<ProfileChangePinState>());
      expect(ChangePinAvailable(sessionToken: ''), isA<ProfileChangePinState>());
      expect(ChangePinLocked(error: ErrorUIModel()), isA<ProfileChangePinState>());
      expect(ChangePinFailure(error: ErrorUIModel()), isA<ProfileChangePinState>());
    });

    test('ChangePinAvailable should hold correct sessionToken', () {
      const String token = 'session_token';
      final ChangePinAvailable state = ChangePinAvailable(
        sessionToken: token,
      );
      expect(token, equals(state.sessionToken));
    });

    test('ChangePinLocked should hold error information', () {
      final ErrorUIModel error = ErrorUIModel();
      final ChangePinLocked state = ChangePinLocked(error: error);
      expect(state.error, equals(error));
    });
  });
}
