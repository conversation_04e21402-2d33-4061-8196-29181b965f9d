import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/request/change_pin_request.dart';
import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/data/response/change_pin_entity.dart';
import 'package:evoapp/feature/pin/mock/mock_pin_use_case.dart';
import 'package:evoapp/feature/pin/models/change_pin_status.dart';
import 'package:evoapp/feature/profile/profile_screen/cubit/user/profile_change_pin_cubit.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../constant.dart';
import '../../../../../util/test_util.dart';

class MockUserRepo extends Mock implements UserRepo {}

class MockAppState extends Mock implements AppState {
  @override
  final ChangePinStatusNotifier changePinStatusNotifier = ChangePinStatusNotifier();
}

void main() {
  group('Test checkChangePinStatus', () {
    late ProfileChangePinCubit cubit;
    late UserRepo mockUserRepo;
    late AppState mockAppState;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      registerFallbackValue(ChangePinStatus.locked);
    });

    setUp(() {
      mockUserRepo = MockUserRepo();
      mockAppState = MockAppState();

      cubit = ProfileChangePinCubit(
        userRepo: mockUserRepo,
        appState: mockAppState,
      );

      registerFallbackValue(AuthChallengeType.none);
      registerFallbackValue(InitializeChangePinSessionRequest());
    });

    stubRepoCall({
      int? statusCode = CommonHttpClient.SUCCESS,
      Map<String, dynamic>? response,
    }) {
      when(() => mockUserRepo.changePin(
            mockConfig: any(named: 'mockConfig'),
            request: any(named: 'request'),
          )).thenAnswer((_) async {
        return ChangePinEntity.fromBaseResponse(BaseResponse(
          statusCode: statusCode,
          response: response,
        ));
      });
    }

    verifyRepoCalled() {
      final dynamic captured = verify(() => mockUserRepo.changePin(
            request: captureAny(named: 'request'),
            mockConfig: any(named: 'mockConfig'),
          )).captured.first;
      expect(captured, isA<InitializeChangePinSessionRequest>());
    }

    blocTest<ProfileChangePinCubit, ProfileChangePinState>(
        'should emit ChangePinAvailable when status_code is not 423 and session_token is not null',
        setUp: () async {
          final Map<String, dynamic> response = await TestUtil.getResponseMock(
              getMockPinFileNameByCase(MockPinUseCase.getChangePinVerifyCurrentSuccess));
          stubRepoCall(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: response,
          );
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.initChangePin(),
        expect: () => <TypeMatcher<ProfileChangePinState>>[
              isA<ChangePinLoading>(),
              isA<ChangePinAvailable>(),
            ],
        verify: (_) {
          verifyRepoCalled();
          expect(mockAppState.changePinStatusNotifier.value, ChangePinStatus.available);
        });

    blocTest<ProfileChangePinCubit, ProfileChangePinState>(
        'should emit ChangePinFailure when status_code is not 423 and session_token is null',
        setUp: () async {
          final Map<String, dynamic> response = await TestUtil.getResponseMock(
              getMockPinFileNameByCase(
                  MockPinUseCase.getChangePinVerifyCurrentSuccessNullSessionToken));
          stubRepoCall(
            response: response,
          );
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.initChangePin(),
        expect: () => <TypeMatcher<ProfileChangePinState>>[
              isA<ChangePinLoading>(),
              isA<ChangePinFailure>(),
            ],
        verify: (_) {
          verifyRepoCalled();
          expect(mockAppState.changePinStatusNotifier.value, ChangePinStatus.available);
        });

    blocTest<ProfileChangePinCubit, ProfileChangePinState>(
        'should emit ChangePinLocked emit when status_code is 423',
        setUp: () {
          stubRepoCall(statusCode: CommonHttpClient.LOCKED_RESOURCE, response: {
            'data': <String, dynamic>{'user_message': 'mock_user_message'}
          });
        },
        build: () => cubit,
        wait: TestConstant.blocEmitStateDelayDuration,
        act: (_) => cubit.initChangePin(),
        expect: () => <TypeMatcher<ProfileChangePinState>>[
              isA<ChangePinLoading>(),
              isA<ChangePinLocked>().having(
                  (ChangePinLocked state) => (state.error?.statusCode, state.error?.userMessage),
                  'verify status_code and user_message',
                  (423, 'mock_user_message')),
            ],
        verify: (_) {
          verifyRepoCalled();
          expect(mockAppState.changePinStatusNotifier.value, ChangePinStatus.locked);
        });
  });
}
