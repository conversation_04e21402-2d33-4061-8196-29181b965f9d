import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/profile/profile_screen/cubit/sign_out/sign_out_cubit.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../constant.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  late SignOutCubit cubit;
  final AuthenticationRepo authenticationRepo = MockAuthenticationRepo();

  setUp(() {
    cubit = SignOutCubit(authenticationRepo);
    when(() => authenticationRepo.logout(mockConfig: any(named: 'mockConfig')))
        .thenAnswer((_) async => null);
  });

  blocTest<SignOutCubit, SignOutState>(
    'Sign out should call AuthenticationRepo.logout and emit SignOutSuccess',
    build: () => cubit,
    act: (SignOutCubit cubit) => cubit.signOut(),
    wait: TestConstant.blocEmitStateDelayDuration,
    expect: () => <dynamic>[
      isA<SignOutSuccess>(),
    ],
    verify: (_) {
      verify(() => authenticationRepo.logout(mockConfig: any(named: 'mockConfig'))).called(1);
    },
  );
}
