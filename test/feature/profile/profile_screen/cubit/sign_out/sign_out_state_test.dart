import 'package:evoapp/feature/profile/profile_screen/cubit//sign_out/sign_out_cubit.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('SignOutInitial should be an instance of SignOutState', () {
    final SignOutInitial state = SignOutInitial();
    expect(state, isA<SignOutState>());
  });

  test('SignOutSuccess should be an instance of SignOutState', () {
    final SignOutSuccess state = SignOutSuccess();
    expect(state, isA<SignOutState>());
  });
}
