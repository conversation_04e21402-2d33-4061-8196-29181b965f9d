// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/profile/cubit/user_profile_cubit.dart';
import 'package:evoapp/feature/profile/profile_screen/cubit/activate_biometric/activate_biometric_cubit.dart';
import 'package:evoapp/feature/profile/profile_screen/cubit/sign_out/sign_out_cubit.dart';
import 'package:evoapp/feature/profile/profile_screen/cubit/user/profile_change_pin_cubit.dart';
import 'package:evoapp/feature/profile/profile_screen/profile_page.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';
import '../../../util/app_mock_cubit.dart';

class MockUserProfileCubit extends AppMockCubit<UserProfileState> implements UserProfileCubit {}

class MockSignOutCubit extends AppMockCubit<SignOutState> implements SignOutCubit {}

class MockProfileChangePinCubit extends AppMockCubit<ProfileChangePinState>
    implements ProfileChangePinCubit {}

class MockActivateBiometricCubit extends AppMockCubit<ActivateBiometricState>
    implements ActivateBiometricCubit {}

class MockVoidCallback extends Mock {
  void call();
}

void main() {
  late UserProfileCubit userProfileCubit;
  late SignOutCubit signOutCubit;
  late ProfileChangePinCubit profileChangePinCubit;
  late ActivateBiometricCubit activateBiometricCubit;

  late AppState appState;

  setUpAll(() {
    initConfigEvoPageStateBase();

    registerFallbackValue(ChallengeSuccessModel());

    appState = getIt.get<AppState>();
  });

  tearDownAll(() {
    getIt.reset();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    userProfileCubit = MockUserProfileCubit()..emit(UserProfileInitial());
    signOutCubit = MockSignOutCubit()..emit(SignOutInitial());
    profileChangePinCubit = MockProfileChangePinCubit()..emit(ChangePinInitialState());
    activateBiometricCubit = MockActivateBiometricCubit()..emit(ActivateBiometricInitState());

    when(() => signOutCubit.signOut()).thenAnswer((_) async {});
    when(() => userProfileCubit.fetchUserInfoFromServer()).thenAnswer((_) async {});
    when(() => activateBiometricCubit.initialize()).thenAnswer((_) async {});
  });

  Widget buildWidgetUnderTest() {
    return MaterialApp(
      home: MultiBlocProvider(
        providers: <BlocProvider<dynamic>>[
          BlocProvider<UserProfileCubit>.value(value: userProfileCubit),
          BlocProvider<SignOutCubit>.value(value: signOutCubit),
          BlocProvider<ProfileChangePinCubit>.value(value: profileChangePinCubit),
          BlocProvider<ActivateBiometricCubit>.value(value: activateBiometricCubit),
        ],
        child: ProfileScreen(),
      ),
    );
  }

  group('ProfileScreen', () {
    testWidgets('should show logout confirm dialog when tapping the logout button',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetUnderTest());

      await tester.tap(find.text(EvoStrings.logout));
      await tester.pump();

      final VoidCallback onClickPositive = verify(() => mockDialogFunction.showDialogConfirm(
            title: EvoStrings.logoutTitle,
            content: EvoStrings.logoutSubtitle,
            textPositive: EvoStrings.ctaProceed,
            autoClosePopupWhenClickCTA: true,
            isDismissible: false,
            textNegative: EvoStrings.ctaCancel,
            dialogId: EvoDialogId.confirmLogOutDialog,
            onClickPositive: captureAny(named: 'onClickPositive'),
          )).captured.first as VoidCallback;

      onClickPositive.call();

      verify(() => signOutCubit.signOut()).called(1);
    });

    testWidgets('should update login status and navigate to LoginOnOldDeviceScreen',
        (WidgetTester tester) async {
      await tester.pumpWidget(buildWidgetUnderTest());

      signOutCubit.emit(SignOutSuccess());
      await tester.pump();

      expect(appState.isUserLogIn, false);

      verify(() => mockNavigatorContext.goNamed(
            Screen.previousLogInScreen.name,
            extra: null,
          )).called(1);
    });
  });
}
