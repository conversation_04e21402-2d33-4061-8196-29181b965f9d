import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/utils/biometric_functions.dart';
import 'package:evoapp/feature/biometric/utils/biometric_status_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometric_type_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/splash_screen/splash_screen_cubit.dart';
import 'package:evoapp/feature/splash_screen/utils/secure_detection_utils/secure_detection.dart';
import 'package:evoapp/main.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/feature/data_collection/data_collector.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/uuid/uuid_generator.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../data/repository/mock_common_http_client.dart';
import '../../util/flutter_test_config.dart';

class MockSessionExpiredHandler extends Mock implements AuthorizationSessionExpiredHandler {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockJwtHelper extends Mock implements JwtHelper {}

class MockBiometricTypeHelper extends Mock implements BiometricTypeHelper {}

class MockBiometricStatusHelper extends Mock implements BiometricStatusHelper {}

class MockCommonSharedPreferencesHelper extends Mock implements CommonSharedPreferencesHelper {}

class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

class MockEvoPlatformWrapper extends Mock implements EvoFlutterWrapper {}

class MockSecureDetection extends Mock implements SecureDetection {}

class MockDataCollector extends Mock implements DataCollector {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockUUIDGenerator extends Mock implements UUIDGenerator {}

class MockBiometricUtilsFunctions extends Mock implements BiometricFunctions {}

class MockSplashScreenCubit extends SplashScreenCubit {
  MockSplashScreenCubit({
    required super.authenticationRepo,
    required super.localStorageHelper,
    required super.jwtHelper,
    required super.biometricTypeHelper,
    required super.appState,
    required super.biometricStatusHelper,
    required super.secureDetection,
    required super.dataCollector,
  });

  StatusApp? statusApp;
  bool hasCallHandleStatusApp = false;
  bool hasCallGetAppStatusOfUserPassedTutorial = false;

  @override
  Future<StatusApp> getAppStatus() {
    return Future<StatusApp>.value(statusApp);
  }

  @override
  Future<void> handleStatusApp(StatusApp statusApp) async {
    hasCallHandleStatusApp = true;
    return Future<void>.value();
  }

  @override
  Future<StatusApp> getAppStatusIfUserPassedTutorial() async {
    hasCallGetAppStatusOfUserPassedTutorial = true;
    return Future<StatusApp>.value(statusApp);
  }
}

void main() {
  late SplashScreenCubit cubit;
  late EvoLocalStorageHelper storageHelper;
  late AuthenticationRepo authenticationRepo;
  late JwtHelper jwtHelper;
  late BiometricTypeHelper biometricTypeHelper;
  late BiometricStatusHelper biometricStatusHelper;
  late AppState appState;
  late CommonSharedPreferencesHelper commonSharedPreferencesHelper;
  late MockDeviceInfoPlugin deviceInfoPlugin;
  late SecureDetection secureDetection;
  final DataCollector mockDataCollector = MockDataCollector();
  late EvoUtilFunction mockEvoUtilFunction;
  late CommonUtilFunction mockCommonUtilFunction;
  late MockUUIDGenerator mockUUIDGenerator;
  late BiometricFunctions mockBiometricUtilityFunctions;

  const String fakeDeviceId = 'fake_device_id';
  const String fakeAccessToken = 'fake_access_token';
  const String fakeRefreshToken = 'fake_refresh_token';
  const String fakeDeviceToken = 'fake_device_token';
  const String fakeKeyAccessTokenHeader = 'fake_key';
  const Map<String, String?> fakeAccessTokenHeader = <String, String?>{
    fakeKeyAccessTokenHeader: fakeAccessToken,
  };
  const String fakeUuidV4 = 'uuid-v4-string';
  const String fakeUserPhoneNumber = 'fake_phone_number';

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(TsBiometricType.face);
    getIt.registerLazySingleton<AuthenticationRepo>(() => MockAuthenticationRepo());
    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());
    getIt.registerLazySingleton<JwtHelper>(() => MockJwtHelper());
    getIt.registerLazySingleton<BiometricTypeHelper>(() => MockBiometricTypeHelper());
    getIt.registerLazySingleton<BiometricStatusHelper>(() => MockBiometricStatusHelper());
    getIt.registerLazySingleton<AppState>(() => AppState());
    getIt.registerLazySingleton<CommonSharedPreferencesHelper>(
        () => MockCommonSharedPreferencesHelper());
    getIt.registerLazySingleton<CommonHttpClient>(() => MockCommonHttpClient());
    getIt.registerLazySingleton<SecureDetection>(() => MockSecureDetection());

    mockUUIDGenerator = MockUUIDGenerator();
    getIt.registerLazySingleton<UUIDGenerator>(() => mockUUIDGenerator);

    storageHelper = getIt.get<EvoLocalStorageHelper>();
    authenticationRepo = getIt.get<AuthenticationRepo>();
    jwtHelper = getIt.get<JwtHelper>();
    biometricTypeHelper = getIt.get<BiometricTypeHelper>();
    biometricStatusHelper = getIt.get<BiometricStatusHelper>();
    appState = getIt.get<AppState>();
    commonSharedPreferencesHelper = getIt.get<CommonSharedPreferencesHelper>();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();
    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();

    deviceInfoPlugin = MockDeviceInfoPlugin();
    getIt.registerLazySingleton<DeviceInfoPlugin>(() => deviceInfoPlugin);
    secureDetection = getIt.get<SecureDetection>();

    getIt.registerLazySingleton<BiometricFunctions>(() => MockBiometricUtilsFunctions());
    mockBiometricUtilityFunctions = getIt.get<BiometricFunctions>();

    testClearAllNotificationExecutable();
  });

  setUp(() {
    appState.userToken = null;

    cubit = SplashScreenCubit(
      authenticationRepo: authenticationRepo,
      localStorageHelper: storageHelper,
      jwtHelper: jwtHelper,
      biometricTypeHelper: biometricTypeHelper,
      appState: appState,
      biometricStatusHelper: biometricStatusHelper,
      secureDetection: secureDetection,
      dataCollector: mockDataCollector,
    );

    when(() => mockEvoUtilFunction.deleteAllData()).thenAnswer((_) async => Future<void>.value());

    when(() => commonSharedPreferencesHelper.isExistingKey(any()))
        .thenAnswer((_) async => Future<bool>.value(false));

    when(() => commonSharedPreferencesHelper.isPassedTutorial())
        .thenAnswer((_) async => Future<bool>.value(false));

    when(() => commonSharedPreferencesHelper.isPassedTutorial())
        .thenAnswer((_) async => Future<bool>.value(false));

    when(() => commonSharedPreferencesHelper.setPassedTutorial(any()))
        .thenAnswer((_) async => Future<void>.value());

    when(() => commonSharedPreferencesHelper.removeKey(any()))
        .thenAnswer((_) async => Future<bool>.value(true));

    when(() => storageHelper.getDeviceId()).thenAnswer((_) async {
      return Future<String>.value(fakeDeviceId);
    });

    when(() => storageHelper.deleteAllData()).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => mockEvoUtilFunction.setNewDeviceId()).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => jwtHelper.isExpired(any())).thenReturn(true);

    when(() => mockCommonUtilFunction.getAccessTokenHeader(any()))
        .thenReturn(fakeAccessTokenHeader);

    when(() => mockEvoUtilFunction.clearDataOnTokenInvalid())
        .thenAnswer((_) async => Future<void>.value());

    when(() => secureDetection.isSecureDevice()).thenAnswer((_) async {
      return Future<bool>.value(true);
    });

    when(() => storageHelper.loadAllDataIntoMemory()).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => biometricStatusHelper.updateBiometricStatus()).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => biometricTypeHelper.getTsBiometricType()).thenAnswer((_) async {
      return Future<TsBiometricType>.value(TsBiometricType.face);
    });

    when(() => mockBiometricUtilityFunctions.getBiometricUIModel(any()))
        .thenReturn(BiometricTypeUIModel.face());

    when(() => jwtHelper.isCanUse(any())).thenReturn(false);
  });

  tearDown(() {
    reset(mockEvoUtilFunction);
    reset(commonSharedPreferencesHelper);
    reset(storageHelper);
    reset(jwtHelper);
    reset(mockCommonUtilFunction);
    reset(secureDetection);
    reset(biometricStatusHelper);
    reset(mockUUIDGenerator);
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  group('verify initData()', () {
    late MockSplashScreenCubit mockSplashScreenCubit;

    setUp(() {
      when(() => mockEvoUtilFunction.detectReinstallAppOnIOSDevice())
          .thenAnswer((_) async => Future<bool>.value(false));
    });

    blocTest<SplashScreenCubit, SplashScreenState>(
      'verify when the device is not secure',
      setUp: () {
        when(() => secureDetection.isSecureDevice()).thenAnswer((_) async {
          return Future<bool>.value(false);
        });
      },
      build: () => cubit,
      act: (SplashScreenCubit cubit) => cubit.initData(),
      expect: () => <dynamic>[
        isA<SplashScreenCompletedState>().having(
          (SplashScreenCompletedState state) => state.statusApp,
          'verify status app',
          StatusApp.insecureDevice,
        ),
      ],
      verify: (_) {
        verify(() => secureDetection.isSecureDevice()).called(1);
      },
    );

    blocTest<SplashScreenCubit, SplashScreenState>(
      'verify when the device is secure and status app is tutorial',
      setUp: () {
        when(() => commonSharedPreferencesHelper.isPassedTutorial())
            .thenAnswer((_) async => Future<bool>.value(false));

        mockSplashScreenCubit = MockSplashScreenCubit(
          authenticationRepo: authenticationRepo,
          localStorageHelper: storageHelper,
          jwtHelper: jwtHelper,
          biometricTypeHelper: biometricTypeHelper,
          appState: appState,
          biometricStatusHelper: biometricStatusHelper,
          secureDetection: secureDetection,
          dataCollector: mockDataCollector,
        );

        mockSplashScreenCubit.statusApp = StatusApp.tutorial;
      },
      build: () => mockSplashScreenCubit,
      act: (SplashScreenCubit cubit) => mockSplashScreenCubit.initData(),
      expect: () => <dynamic>[
        isA<SplashScreenCompletedState>().having(
          (SplashScreenCompletedState state) => state.statusApp,
          'verify status app',
          StatusApp.tutorial,
        ),
      ],
      verify: (_) {
        expect(mockSplashScreenCubit.statusApp, StatusApp.tutorial);
        expect(mockSplashScreenCubit.hasCallHandleStatusApp, true);

        verifyInOrder(<VoidCallback>[
          () => secureDetection.isSecureDevice(),
          () => storageHelper.loadAllDataIntoMemory(),
          () => biometricStatusHelper.updateBiometricStatus(),
          () => biometricTypeHelper.getTsBiometricType(),
        ]);
      },
    );

    blocTest<SplashScreenCubit, SplashScreenState>(
      'verify when the device is secure and status app is nonUser',
      setUp: () {
        when(() => commonSharedPreferencesHelper.isPassedTutorial())
            .thenAnswer((_) async => Future<bool>.value(true));

        mockSplashScreenCubit = MockSplashScreenCubit(
          authenticationRepo: authenticationRepo,
          localStorageHelper: storageHelper,
          jwtHelper: jwtHelper,
          biometricTypeHelper: biometricTypeHelper,
          appState: appState,
          biometricStatusHelper: biometricStatusHelper,
          secureDetection: secureDetection,
          dataCollector: mockDataCollector,
        );

        mockSplashScreenCubit.statusApp = StatusApp.nonUser;
      },
      build: () => mockSplashScreenCubit,
      act: (SplashScreenCubit cubit) {
        mockSplashScreenCubit.initData();
      },
      expect: () => <dynamic>[
        isA<SplashScreenCompletedState>().having(
          (SplashScreenCompletedState state) => state.statusApp,
          'verify status app',
          StatusApp.nonUser,
        ),
      ],
      verify: (_) {
        expect(mockSplashScreenCubit.statusApp, StatusApp.nonUser);
        expect(mockSplashScreenCubit.hasCallHandleStatusApp, true);
        expect(mockSplashScreenCubit.hasCallGetAppStatusOfUserPassedTutorial, true);

        verifyInOrder(<VoidCallback>[
          () => secureDetection.isSecureDevice(),
          () => storageHelper.loadAllDataIntoMemory(),
          () => biometricStatusHelper.updateBiometricStatus(),
          () => mockEvoUtilFunction.clearDataOnTokenInvalid(),
          () => biometricTypeHelper.getTsBiometricType(),
          () => mockBiometricUtilityFunctions.getBiometricUIModel(TsBiometricType.face),
        ]);
      },
    );

    blocTest<SplashScreenCubit, SplashScreenState>(
      'verify when the device is secure and status app is hadLoggedIn',
      setUp: () {
        when(() => commonSharedPreferencesHelper.isPassedTutorial())
            .thenAnswer((_) async => Future<bool>.value(true));

        mockSplashScreenCubit = MockSplashScreenCubit(
          authenticationRepo: authenticationRepo,
          localStorageHelper: storageHelper,
          jwtHelper: jwtHelper,
          biometricTypeHelper: biometricTypeHelper,
          appState: appState,
          biometricStatusHelper: biometricStatusHelper,
          secureDetection: secureDetection,
          dataCollector: mockDataCollector,
        );

        mockSplashScreenCubit.statusApp = StatusApp.hadLoggedIn;
      },
      build: () => mockSplashScreenCubit,
      act: (SplashScreenCubit cubit) {
        mockSplashScreenCubit.statusApp = StatusApp.hadLoggedIn;
        mockSplashScreenCubit.initData();
      },
      expect: () => <dynamic>[
        isA<SplashScreenCompletedState>().having(
          (SplashScreenCompletedState state) => state.statusApp,
          'verify status app',
          StatusApp.hadLoggedIn,
        ),
      ],
      verify: (_) {
        expect(mockSplashScreenCubit.statusApp, StatusApp.hadLoggedIn);
        expect(mockSplashScreenCubit.hasCallHandleStatusApp, true);
        expect(mockSplashScreenCubit.hasCallGetAppStatusOfUserPassedTutorial, true);
        expect(appState.userToken.accessToken, isNull);
        expect(appState.userToken.refreshToken, isNull);

        verifyInOrder(<VoidCallback>[
          () => secureDetection.isSecureDevice(),
          () => storageHelper.loadAllDataIntoMemory(),
          () => biometricStatusHelper.updateBiometricStatus(),
          () => biometricTypeHelper.getTsBiometricType(),
          () => mockBiometricUtilityFunctions.getBiometricUIModel(TsBiometricType.face),
        ]);
      },
    );
  });

  group('Test function startMobileDataCollecting', () {
    test('Should call DataCollector.logMobileDataCollection()', () {
      when(() => mockDataCollector.logMobileDataCollection())
          .thenAnswer((_) => Future<void>.value());

      cubit.startMobileDataCollecting();

      verify(() => mockDataCollector.logMobileDataCollection()).called(1);
    });
  });

  group('verify getAppStatus()', () {
    tearDown(() {
      reset(mockEvoUtilFunction);
      reset(commonSharedPreferencesHelper);
      reset(storageHelper);
      reset(jwtHelper);
    });

    test('verify the user is reInstall app', () async {
      when(() => mockEvoUtilFunction.detectReinstallAppOnIOSDevice())
          .thenAnswer((_) async => Future<bool>.value(true));

      final StatusApp statusApp = await cubit.getAppStatus();
      expect(statusApp, StatusApp.tutorial);

      verify(() => commonSharedPreferencesHelper.isPassedTutorial()).called(1);
      verify(() => mockEvoUtilFunction.detectReinstallAppOnIOSDevice()).called(1);
    });

    test('verify the user is not passed tutorial and not reInstall app', () async {
      when(() => commonSharedPreferencesHelper.isPassedTutorial())
          .thenAnswer((_) async => Future<bool>.value(false));

      when(() => mockEvoUtilFunction.detectReinstallAppOnIOSDevice())
          .thenAnswer((_) async => Future<bool>.value(false));

      final StatusApp statusApp = await cubit.getAppStatus();
      expect(statusApp, StatusApp.tutorial);

      verify(() => commonSharedPreferencesHelper.isPassedTutorial()).called(1);
      verify(() => mockEvoUtilFunction.detectReinstallAppOnIOSDevice()).called(1);
    });

    test('verify StatusApp is non-user', () async {
      when(() => commonSharedPreferencesHelper.isPassedTutorial())
          .thenAnswer((_) async => Future<bool>.value(true));

      when(() => mockEvoUtilFunction.detectReinstallAppOnIOSDevice())
          .thenAnswer((_) async => Future<bool>.value(false));

      when(() => storageHelper.getUserPhoneNumber())
          .thenAnswer((_) async => Future<String?>.value(fakeUserPhoneNumber));
      when(() => storageHelper.getDeviceToken()).thenAnswer((_) async => Future<String?>.value());

      final StatusApp statusApp = await cubit.getAppStatus();
      expect(statusApp, StatusApp.nonUser);

      verify(() => commonSharedPreferencesHelper.isPassedTutorial()).called(1);
      verify(() => mockEvoUtilFunction.detectReinstallAppOnIOSDevice()).called(1);
      verify(() => storageHelper.getUserPhoneNumber()).called(1);
      verify(() => storageHelper.getDeviceToken()).called(1);
    });

    test('verify StatusApp is non-user', () async {
      when(() => commonSharedPreferencesHelper.isPassedTutorial())
          .thenAnswer((_) async => Future<bool>.value(true));

      when(() => mockEvoUtilFunction.detectReinstallAppOnIOSDevice())
          .thenAnswer((_) async => Future<bool>.value(false));

      when(() => storageHelper.getUserPhoneNumber())
          .thenAnswer((_) async => Future<String?>.value(fakeUserPhoneNumber));
      when(() => storageHelper.getDeviceToken()).thenAnswer((_) async => Future<String?>.value());

      final StatusApp statusApp = await cubit.getAppStatus();
      expect(statusApp, StatusApp.nonUser);

      verify(() => commonSharedPreferencesHelper.isPassedTutorial()).called(1);
      verify(() => mockEvoUtilFunction.detectReinstallAppOnIOSDevice()).called(1);
      verify(() => storageHelper.getUserPhoneNumber()).called(1);
      verify(() => storageHelper.getDeviceToken()).called(1);
    });
  });

  group('verify getAppStatusIfUserPassedTutorial()', () {
    test('status app is nonUser', () async {
      when(() => storageHelper.getDeviceToken()).thenAnswer((_) async {
        return Future<String?>.value();
      });

      /// stub phoneNumber & deviceToken is invalid
      when(() => storageHelper.getUserPhoneNumber()).thenAnswer((_) async {
        return Future<String?>.value();
      });
      when(() => storageHelper.getDeviceToken()).thenAnswer((_) async {
        return Future<String?>.value();
      });

      final StatusApp status = await cubit.getAppStatusIfUserPassedTutorial();

      expect(status, StatusApp.nonUser);

      verify(() => storageHelper.getUserPhoneNumber()).called(1);
      verify(() => storageHelper.getDeviceToken()).called(1);
    });

    test('status app is hadLoggedIn', () async {
      when(() => storageHelper.getDeviceToken()).thenAnswer((_) async {
        return Future<String>.value(fakeDeviceToken);
      });
      when(() => storageHelper.getUserPhoneNumber()).thenAnswer((_) async {
        return Future<String>.value(fakeUserPhoneNumber);
      });

      when(() => jwtHelper.isExpired(any())).thenReturn(false);

      final StatusApp status = await cubit.getAppStatusIfUserPassedTutorial();

      expect(status, StatusApp.hadLoggedIn);

      verify(() => storageHelper.getDeviceToken()).called(1);
      verify(() => jwtHelper.isExpired(fakeDeviceToken)).called(1);
    });

    test('Status app is hadLoggedIn', () async {
      when(() => storageHelper.getUserPhoneNumber()).thenAnswer((_) async {
        return Future<String>.value(fakeUserPhoneNumber);
      });

      when(() => storageHelper.getDeviceToken()).thenAnswer((_) async {
        return Future<String>.value(fakeDeviceToken);
      });

      when(() => jwtHelper.isExpired(fakeDeviceToken)).thenReturn(false);

      final StatusApp status = await cubit.getAppStatusIfUserPassedTutorial();

      expect(status, StatusApp.hadLoggedIn);

      verify(() => storageHelper.getUserPhoneNumber()).called(1);
      verify(() => storageHelper.getDeviceToken()).called(1);
      verify(() => jwtHelper.isExpired(fakeDeviceToken)).called(1);
    });
  });

  group('verify handleStatusApp()', () {
    test('verify method when the user is reInstall app', () async {
      // Arrange
      when(() => mockUUIDGenerator.genV4()).thenReturn(fakeUuidV4);

      // Action
      await cubit.handleStatusApp(StatusApp.tutorial);

      // Assert
      verify(() => mockEvoUtilFunction.deleteAllData()).called(1);
      verify(() => mockEvoUtilFunction.setNewDeviceId()).called(1);
    });

    test('verify method when the user is non-user', () async {
      when(() => mockEvoUtilFunction.clearAllUserData()).thenAnswer((_) => Future<void>.value());

      await cubit.handleStatusApp(StatusApp.nonUser);

      verify(() => mockEvoUtilFunction.clearAllUserData()).called(1);
      verifyNever(() => mockEvoUtilFunction.deleteAllData());
      verifyNever(() => mockEvoUtilFunction.setNewDeviceId());

      verifyNever(() => mockCommonUtilFunction.getAccessTokenHeader(fakeAccessToken));
    });

    test('verify method when the user is hadLoggedIn', () async {
      await cubit.handleStatusApp(StatusApp.hadLoggedIn);

      verifyNever(() => mockEvoUtilFunction.deleteAllData());
      verifyNever(() => mockEvoUtilFunction.setNewDeviceId());
    });
  });

  group('verify handleWhenUserReInstallApp()', () {
    test('verify method when the StatusApp is reInstall app', () async {
      when(() => mockUUIDGenerator.genV4()).thenReturn(fakeUuidV4);

      await cubit.handleWhenUserReInstallApp();

      // Assert
      verify(() => mockEvoUtilFunction.deleteAllData()).called(1);
      verify(() => mockEvoUtilFunction.setNewDeviceId()).called(1);
    });
  });

  group('verify handleWhenUserNonUser()', () {
    test('verify method when the StatusApp is NonUser', () async {
      when(() => mockEvoUtilFunction.clearAllUserData()).thenAnswer((_) => Future<void>.value());

      await cubit.handleWhenUserNonUser();

      verify(() => mockEvoUtilFunction.clearAllUserData()).called(1);
    });
  });

  group('verify function hadUserLoggedIn()', () {
    tearDown(() {
      reset(jwtHelper);
    });

    test('returns false when phoneNumber is null', () {
      expect(cubit.hadUserLoggedIn(deviceToken: 'validToken'), false);
    });

    test('returns false when phoneNumber is empty', () {
      expect(cubit.hadUserLoggedIn(phoneNumber: '', deviceToken: 'validToken'), false);
    });

    test('returns false when deviceToken is null', () {
      expect(cubit.hadUserLoggedIn(phoneNumber: '1234567890', deviceToken: null), false);
    });

    test('returns false when deviceToken is empty', () {
      expect(cubit.hadUserLoggedIn(phoneNumber: '1234567890', deviceToken: ''), false);
    });

    test('returns false when deviceToken is expired', () {
      /// arrange
      when(() => jwtHelper.isExpired(any())).thenReturn(true);

      expect(cubit.hadUserLoggedIn(phoneNumber: '1234567890', deviceToken: 'expiredToken'), false);
    });

    test('returns true when phoneNumber and deviceToken are valid and not expired', () {
      /// arrange
      when(() => jwtHelper.isExpired(any())).thenReturn(false);

      expect(cubit.hadUserLoggedIn(phoneNumber: '1234567890', deviceToken: 'validToken'), true);
    });
  });

  group('verify clearOldDataIfNeed()', () {
    test('verify when statusApp = StatusApp.hadLoggedIn', () async {
      await cubit.clearOldDataIfNeed(StatusApp.hadLoggedIn);

      verifyNever(() => mockEvoUtilFunction.clearDataOnTokenInvalid());
    });

    test('verify when statusApp != StatusApp.loggedIn', () async {
      await cubit.clearOldDataIfNeed(StatusApp.nonUser);

      verify(() => mockEvoUtilFunction.clearDataOnTokenInvalid()).called(1);
    });
  });
}
