import 'package:evoapp/feature/splash_screen/splash_screen.dart';
import 'package:evoapp/feature/splash_screen/splash_screen_cubit.dart';
import 'package:evoapp/feature/splash_screen/utils/exit_app_feature/exit_app_feature.dart';
import 'package:evoapp/main.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../base/evo_page_state_base_test_config.dart';
import '../../util/app_mock_cubit.dart';

class MockSplashScreenCubit extends AppMockCubit<SplashScreenState> implements SplashScreenCubit {}

class MockExitAppFeature extends Mock implements ExitAppFeature {}

void main() {
  late MockSplashScreenCubit cubit;
  late ExitAppFeature exitAppFeature;

  setUpAll(() {
    initConfigEvoPageStateBase();

    exitAppFeature = getIt.registerSingleton<ExitAppFeature>(MockExitAppFeature());
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();

    cubit = MockSplashScreenCubit()..emit(SplashScreenInitialState());
    when(() => cubit.initData()).thenAnswer((_) async {});
  });

  tearDownAll(() {
    getIt.reset();
  });

  Widget createWidgetUnderTest() {
    return MaterialApp(
      home: BlocProvider<SplashScreenCubit>.value(
        value: cubit,
        child: SplashScreen(),
      ),
    );
  }

  group('SplashScreen', () {
    testWidgets('should have needed widgets', (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      verify(() => evoImageProvider.asset(
            EvoImages.imgSplashBackgroundImage,
            fit: any(named: 'fit'),
          )).called(1);
      verify(() => evoImageProvider.asset(
            EvoImages.imgBrandName,
            color: evoColors.primaryBase,
            width: any(named: 'width'),
            height: any(named: 'height'),
            fit: any(named: 'fit'),
          )).called(1);
    });

    testWidgets(
        'should navigate to IntroductionScreen when state is completed with StatusApp.tutorial',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(SplashScreenCompletedState(statusApp: StatusApp.tutorial));
      await tester.pump();

      verify(() => mockNavigatorContext.goNamed(
            Screen.introductionScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets(
        'should navigate to LoginOnOldDeviceScreen when state is completed with StatusApp.hadLoggedIn',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(SplashScreenCompletedState(statusApp: StatusApp.hadLoggedIn));
      await tester.pump();

      verify(() => mockNavigatorContext.goNamed(
            Screen.loginOnOldDeviceScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should navigate to WelcomeScreen when state is completed with StatusApp.nonUser',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(SplashScreenCompletedState(statusApp: StatusApp.nonUser));
      await tester.pump();

      verify(() => mockNavigatorContext.goNamed(
            Screen.welcomeScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets(
        'should show block insecure device dialog when state is completed with StatusApp.insecureDevice',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      cubit.emit(SplashScreenCompletedState(statusApp: StatusApp.insecureDevice));
      await tester.pump();

      final Function onClickPositive = verify(() => mockDialogFunction.showDialogConfirm(
            dialogId: EvoDialogId.blockInsecureDeviceDialog,
            title: EvoStrings.titleBlockInsecureDeviceDialog,
            content: EvoStrings.descriptionBlockInsecureDeviceDialog,
            isDismissible: false,
            textPositive: EvoStrings.close,
            onClickPositive: captureAny(named: 'onClickPositive'),
          )).captured.first as Function;

      when(() => exitAppFeature.closeApp()).thenAnswer((_) async {});
      onClickPositive.call();

      verify(() => exitAppFeature.closeApp()).called(1);
    });
  });
}
