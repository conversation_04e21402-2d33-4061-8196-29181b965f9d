import 'package:evoapp/main.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo_impl.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

import '../data/repository/mock_common_http_client.dart';
import '../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  setUpAll(() {
    WidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<LoggingRepo>(
        () => LoggingRepoImpl(commonHttpClient: MockCommonHttpClient()));
    getItRegisterNavigatorObserver();
    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  group('test navigator key', () {
    test('test navigatorKey MaterialApp and GoRouter', () {
      final MaterialApp materialApp = const MyApp().getMaterialApp(MockBuildContext());
      if (materialApp.routerConfig is GoRouter) {
        expect(getIt.get<GlobalKeyProvider>().navigatorKey,
            (materialApp.routerConfig as GoRouter).configuration.navigatorKey);
      } else {
        expect(getIt.get<GlobalKeyProvider>().navigatorKey, materialApp.navigatorKey);
      }
    });
  });
}
