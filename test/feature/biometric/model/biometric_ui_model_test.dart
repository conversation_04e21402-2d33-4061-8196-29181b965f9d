import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BiometricTypeUIModel', () {
    test('should create finger biometric type with correct properties', () {
      final BiometricTypeUIModel model = BiometricTypeUIModel.finger();
      expect(model.biometricTypeName, EvoStrings.fingerText);
      expect(model.iconPath, EvoImages.icFingerId);
      expect(model.iconSettingPath, EvoImages.icSettingFingerId);
    });

    test('should create face biometric type with correct properties', () {
      final BiometricTypeUIModel model = BiometricTypeUIModel.face();
      expect(model.biometricTypeName, EvoStrings.faceText);
      expect(model.iconPath, EvoImages.icFaceId);
      expect(model.iconSettingPath, EvoImages.icSettingFaceId);
    });

    test('should create face and finger biometric type with correct properties', () {
      final BiometricTypeUIModel model = BiometricTypeUIModel.faceAndFinger();
      expect(model.biometricTypeName, EvoStrings.faceFingerText);
      expect(model.iconPath, EvoImages.icFaceFingerId);
      expect(model.iconSettingPath, EvoImages.icSettingFaceFingerId);
    });

    test('should return correct string representation', () {
      final BiometricTypeUIModel model = BiometricTypeUIModel.finger();
      expect(model.toString(),
          'BiometricTypeUIModel{iconPath: ${EvoImages.icFingerId}, biometricTypeName: ${EvoStrings.fingerText}, iconSettingPath: ${EvoImages.icSettingFingerId}}');
    });
  });
}
