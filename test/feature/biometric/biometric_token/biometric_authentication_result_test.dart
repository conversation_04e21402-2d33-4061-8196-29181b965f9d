import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_result.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BiometricAuthenticationFailureType', () {
    test('should have all expected enum values', () {
      expect(BiometricAuthenticationFailureType.values, hasLength(4));
      expect(BiometricAuthenticationFailureType.values, contains(BiometricAuthenticationFailureType.biometrics));
      expect(BiometricAuthenticationFailureType.values, contains(BiometricAuthenticationFailureType.apiError));
      expect(BiometricAuthenticationFailureType.values, contains(BiometricAuthenticationFailureType.noSupportFlow));
      expect(BiometricAuthenticationFailureType.values, contains(BiometricAuthenticationFailureType.unknown));
    });
  });

  group('BiometricAuthenticationSuccess', () {
    test('should create instance with required entity parameter', () {
      final BaseEntity entity = BaseEntity();
      final BiometricAuthenticationSuccess success = BiometricAuthenticationSuccess(entity: entity);

      expect(success.entity, equals(entity));
      expect(success, isA<BiometricAuthenticationResult>());
    });

    test('should create instance with null entity', () {
      final BiometricAuthenticationSuccess success = BiometricAuthenticationSuccess(entity: null);

      expect(success.entity, isNull);
      expect(success, isA<BiometricAuthenticationResult>());
    });
  });

  group('BiometricAuthenticationFailure', () {
    group('constructor', () {
      test('should create instance with required errorType parameter', () {
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.biometrics,
        );

        expect(failure.errorType, equals(BiometricAuthenticationFailureType.biometrics));
        expect(failure.userMessage, isNull);
        expect(failure.apiError, isNull);
        expect(failure.biometricError, isNull);
        expect(failure, isA<BiometricAuthenticationResult>());
      });

      test('should create instance with all optional parameters', () {
        const String userMessage = 'Custom error message';
        final ErrorUIModel apiError = ErrorUIModel(userMessage: 'API error message');
        const BioAuthError biometricError = BioAuthError.notEnrolled;

        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.apiError,
          userMessage: userMessage,
          apiError: apiError,
          biometricError: biometricError,
        );

        expect(failure.errorType, equals(BiometricAuthenticationFailureType.apiError));
        expect(failure.userMessage, equals(userMessage));
        expect(failure.apiError, equals(apiError));
        expect(failure.biometricError, equals(biometricError));
      });
    });

    group('displayMessage getter', () {
      test('should return userMessage when provided and not empty', () {
        const String userMessage = 'Custom user message';
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.unknown,
          userMessage: userMessage,
          apiError: ErrorUIModel(userMessage: 'API message'),
          biometricError: BioAuthError.notEnrolled,
        );

        expect(failure.displayMessage, equals(userMessage));
      });

      test('should return apiError userMessage when userMessage is null', () {
        const String apiMessage = 'API error message';
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.apiError,
          apiError: ErrorUIModel(userMessage: apiMessage),
          biometricError: BioAuthError.notEnrolled,
        );

        expect(failure.displayMessage, equals(apiMessage));
      });

      test('should return apiError userMessage when userMessage is empty', () {
        const String apiMessage = 'API error message';
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.apiError,
          userMessage: '',
          apiError: ErrorUIModel(userMessage: apiMessage),
          biometricError: BioAuthError.notEnrolled,
        );

        expect(failure.displayMessage, equals(apiMessage));
      });

      test('should return biometric error message when userMessage and apiError are null', () {
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.biometrics,
          biometricError: BioAuthError.notEnrolled,
        );

        expect(failure.displayMessage, equals(BioAuthError.getErrMsg(BioAuthError.notEnrolled)));
      });

      test('should return default message when all error sources are null or empty', () {
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.unknown,
        );

        expect(failure.displayMessage, equals(EvoStrings.unknownError));
      });

      test('should return default message when userMessage is empty, apiError is null, and biometricError is null', () {
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.unknown,
          userMessage: '',
        );

        expect(failure.displayMessage, equals(EvoStrings.unknownError));
      });
    });

    group('isBiometricError getter', () {
      test('should return true when errorType is biometrics', () {
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.biometrics,
        );

        expect(failure.isBiometricError, isTrue);
      });

      test('should return false when errorType is not biometrics', () {
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.apiError,
        );

        expect(failure.isBiometricError, isFalse);
      });
    });

    group('isApiError getter', () {
      test('should return true when errorType is apiError', () {
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.apiError,
        );

        expect(failure.isApiError, isTrue);
      });

      test('should return false when errorType is not apiError', () {
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.biometrics,
        );

        expect(failure.isApiError, isFalse);
      });
    });

    group('toString method', () {
      test('should return formatted string with errorType and displayMessage', () {
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.biometrics,
          userMessage: 'Test error message',
        );

        final String result = failure.toString();

        expect(result, contains('BiometricAuthenticationFailure'));
        expect(result, contains('type: ${BiometricAuthenticationFailureType.biometrics}'));
        expect(result, contains('message: Test error message'));
      });
    });
  });



  group('BiometricAuthenticationResultExtensions', () {
    group('isSuccess getter', () {
      test('should return true for BiometricAuthenticationSuccess', () {
        final BiometricAuthenticationResult result = BiometricAuthenticationSuccess(entity: null);

        expect(result.isSuccess, isTrue);
      });

      test('should return false for BiometricAuthenticationFailure', () {
        final BiometricAuthenticationResult result = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.unknown,
        );

        expect(result.isSuccess, isFalse);
      });


    });

    group('isFailure getter', () {
      test('should return true for BiometricAuthenticationFailure', () {
        final BiometricAuthenticationResult result = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.unknown,
        );

        expect(result.isFailure, isTrue);
      });

      test('should return false for BiometricAuthenticationSuccess', () {
        final BiometricAuthenticationResult result = BiometricAuthenticationSuccess(entity: null);

        expect(result.isFailure, isFalse);
      });


    });



    group('asSuccess getter', () {
      test('should return BiometricAuthenticationSuccess when result is success', () {
        final BiometricAuthenticationSuccess success = BiometricAuthenticationSuccess(entity: BaseEntity());
        final BiometricAuthenticationResult result = success;

        final BiometricAuthenticationSuccess? asSuccess = result.asSuccess;

        expect(asSuccess, isNotNull);
        expect(asSuccess, equals(success));
        expect(asSuccess!.entity, equals(success.entity));
      });

      test('should return null when result is failure', () {
        final BiometricAuthenticationResult result = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.unknown,
        );

        final BiometricAuthenticationSuccess? asSuccess = result.asSuccess;

        expect(asSuccess, isNull);
      });
    });

    group('asFailure getter', () {
      test('should return BiometricAuthenticationFailure when result is failure', () {
        final BiometricAuthenticationFailure failure = BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.biometrics,
          userMessage: 'Test error',
        );
        final BiometricAuthenticationResult result = failure;

        final BiometricAuthenticationFailure? asFailure = result.asFailure;

        expect(asFailure, isNotNull);
        expect(asFailure, equals(failure));
        expect(asFailure!.errorType, equals(failure.errorType));
        expect(asFailure.userMessage, equals(failure.userMessage));
      });

      test('should return null when result is success', () {
        final BiometricAuthenticationResult result = BiometricAuthenticationSuccess(entity: null);

        final BiometricAuthenticationFailure? asFailure = result.asFailure;

        expect(asFailure, isNull);
      });
    });
  });
}
