import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_token_usability_mixin.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_service.dart';
import 'package:evoapp/feature/biometric/model/biometric_status_change_notifier.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/biometric/utils/biometric_status_helper.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class TestBiometricExpiredMixin with BiometricTokenUsabilityMixin {}

class MockBiometricAuthenticationService extends Mock implements BiometricAuthenticationService {}

class MockSnackBarWrapper extends Mock implements SnackBarWrapper {}

class MockSnackBar extends Mock implements EvoSnackBar {}

class MockBiometricStatusHelper extends Mock implements BiometricStatusHelper {}

void main() {
  const String biometricTokenKey = EvoSecureStorageHelperImpl.biometricTokenKey;
  const String biometricTokenValue = 'evo_biometric_token_value_test';

  late EvoLocalStorageHelper storageHelper;
  late FlutterSecureStorage secureStorage;
  late TestBiometricExpiredMixin testBiometricChangeMixin;
  late MockBiometricAuthenticationService mockBiometricsAuthenticationService;
  late AppState? appState;
  late MockSnackBar snackBar;
  late BiometricStatusHelper biometricStatusHelper;

  setUpAll(() {
    WidgetsFlutterBinding.ensureInitialized();

    secureStorage = testFlutterSecureStorageExecutable();
    storageHelper = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);

    appState = AppState();
    getIt.registerLazySingleton<AppState>(() => appState!);

    mockBiometricsAuthenticationService = MockBiometricAuthenticationService();
    getIt.registerLazySingleton<BiometricAuthenticationService>(() => mockBiometricsAuthenticationService);

    snackBar = MockSnackBar();
    getIt.registerLazySingleton<EvoSnackBar>(() => snackBar);

    biometricStatusHelper = MockBiometricStatusHelper();
    getIt.registerLazySingleton<BiometricStatusHelper>(() => biometricStatusHelper);
  });

  tearDownAll(() async {
    await getIt.reset();
    appState = null;
  });

  group('check check biometric token expired()', () {
    setUp(() async {
      testBiometricChangeMixin = TestBiometricExpiredMixin();

      await storageHelper.setBiometricToken(biometricTokenValue);
      await storageHelper.setBiometricAuthenticator(true);
    });

    tearDown(() async {
      await storageHelper.deleteAllData();
    });

    test(
      'checkAndHandleBiometricTokenExpired() return false when isEnableBiometricAuthenticator is not enable'
      'and don\'t remove biometricTokenKey',
      () async {
        /// setup
        when(() => mockBiometricsAuthenticationService.isEnableBiometricAuthenticator())
            .thenAnswer((_) async {
          return false;
        });

        final bool isChanged =
            await testBiometricChangeMixin.checkAndHandleBiometricTokenUnUsable();

        expect(isChanged, false);
        expect(await secureStorage.containsKey(key: biometricTokenKey), true);
      },
    );
    test(
      'checkAndHandleBiometricTokenExpired() return false when isEnableBiometricAuthenticator is enable'
      'and token is not expired'
      'and don\'t remove biometricTokenKey',
      () async {
        when(() => mockBiometricsAuthenticationService.isEnableBiometricAuthenticator())
            .thenAnswer((_) async {
          return true;
        });

        when(() => mockBiometricsAuthenticationService.isBiometricTokenUsable()).thenAnswer((_) async {
          return true;
        });

        final bool isExpired =
            await testBiometricChangeMixin.checkAndHandleBiometricTokenUnUsable();

        expect(isExpired, false);
        expect(await secureStorage.containsKey(key: biometricTokenKey), true);
        expect(appState?.biometricStatusChangeNotifier.value, BiometricStatus.usable);
      },
    );
    test(
      'checkAndHandleBiometricTokenExpired() return true when isEnableBiometricAuthenticator is  enable'
      'and token is expired'
      'and remove biometricTokenKey',
      () async {
        when(() => mockBiometricsAuthenticationService.isEnableBiometricAuthenticator())
            .thenAnswer((_) async {
          return true;
        });

        when(() => mockBiometricsAuthenticationService.isBiometricTokenUsable()).thenAnswer((_) async {
          return false;
        });

        when(() => mockBiometricsAuthenticationService.disableBiometricAuthenticationFeature())
            .thenAnswer((_) async {
          secureStorage.delete(key: biometricTokenKey);
        });

        final bool isExpired =
            await testBiometricChangeMixin.checkAndHandleBiometricTokenUnUsable();

        expect(isExpired, true);
        expect(await secureStorage.containsKey(key: biometricTokenKey), false);
        expect(
            appState?.biometricStatusChangeNotifier.value, BiometricStatus.biometricTokenUnusable);
      },
    );
    test(
      'checkAndHandleBiometricTokenExpired() return true and show toast',
      () async {
        appState?.isUserLogIn = true;
        appState?.bioTypeInfo = BiometricTypeUIModel.face();
        final String message = EvoStrings.biometricTokenUnUsableMessage.replaceVariableByValue(
          <String>[
            appState?.bioTypeInfo.biometricTypeName ?? '',
          ],
        );

        when(() => mockBiometricsAuthenticationService.isEnableBiometricAuthenticator())
            .thenAnswer((_) async {
          return true;
        });

        when(() => biometricStatusHelper.updateBiometricStatus()).thenAnswer((_) async {
          return Future<void>.value();
        });

        when(() => mockBiometricsAuthenticationService.isBiometricTokenUsable()).thenAnswer((_) async {
          return false;
        });

        when(() => mockBiometricsAuthenticationService.disableBiometricAuthenticationFeature())
            .thenAnswer((_) async {
          secureStorage.delete(key: biometricTokenKey);
        });

        when(
          () => snackBar.show(
            message,
            typeSnackBar: SnackBarType.error,
          ),
        ).thenAnswer((_) async {
          return true;
        });

        await testBiometricChangeMixin.handleBiometricTokenUnUsable();

        verify(() => snackBar.show(message, typeSnackBar: SnackBarType.error)).called(1);
      },
    );
    test(
      'checkAndHandleBiometricTokenExpired() return true and don\'t show toast because user not login',
      () async {
        appState?.isUserLogIn = false;
        appState?.bioTypeInfo = BiometricTypeUIModel.face();
        final String message = EvoStrings.biometricTokenUnUsableMessage.replaceVariableByValue(
          <String>[
            appState?.bioTypeInfo.biometricTypeName ?? '',
          ],
        );

        when(() => biometricStatusHelper.updateBiometricStatus()).thenAnswer((_) async {
          return Future<void>.value();
        });

        when(() => mockBiometricsAuthenticationService.isEnableBiometricAuthenticator())
            .thenAnswer((_) async {
          return true;
        });

        when(() => mockBiometricsAuthenticationService.isBiometricTokenUsable()).thenAnswer((_) async {
          return false;
        });

        when(() => mockBiometricsAuthenticationService.disableBiometricAuthenticationFeature())
            .thenAnswer((_) async {
          secureStorage.delete(key: biometricTokenKey);
        });

        when(
          () => snackBar.show(
            message,
            typeSnackBar: SnackBarType.error,
          ),
        ).thenAnswer((_) async {
          return true;
        });

        await testBiometricChangeMixin.handleBiometricTokenUnUsable();

        verifyNever(() => snackBar.show(message, typeSnackBar: SnackBarType.error));
      },
    );
  });

  group('test showBiometricTokenUnUsableToastAndUpdateStatus flow', () {
    setUp(() async {
      getIt.unregister(instance: biometricStatusHelper);
      biometricStatusHelper = MockBiometricStatusHelper();
      getIt.registerLazySingleton<BiometricStatusHelper>(() => biometricStatusHelper);
    });
    test('make call update biometric status after show toast', () async {
      appState?.bioTypeInfo = BiometricTypeUIModel.face();
      final String message = EvoStrings.biometricTokenUnUsableMessage.replaceVariableByValue(
        <String>[
          appState?.bioTypeInfo.biometricTypeName ?? '',
        ],
      );

      when(
        () => snackBar.show(
          message,
          typeSnackBar: SnackBarType.error,
        ),
      ).thenAnswer((_) async {
        return true;
      });

      when(() => biometricStatusHelper.updateBiometricStatus()).thenAnswer((_) async {
        return Future<void>.value();
      });

      await testBiometricChangeMixin.showBiometricTokenUnUsableToastAndUpdateStatus();

      verify(() => biometricStatusHelper.updateBiometricStatus()).called(1);
    });
  });
}
