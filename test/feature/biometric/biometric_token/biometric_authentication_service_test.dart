import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/biometric/base/ext_biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_result.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_service.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_retrieval_result.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider_config.dart';
import 'package:evoapp/feature/biometric/model/biometric_status_change_notifier.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockTsBioDetectChanged extends Mock implements TsBioDetectChanged {}

class MockJwtHelper extends Mock implements JwtHelper {}

class MockBiometricTokenProviderFactory extends Mock implements BiometricTokenProviderFactory {}

class MockBiometricTokenProvider extends Mock implements BiometricTokenProvider {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockBiometricStatusChangeNotifier extends Mock implements BiometricStatusChangeNotifier {}

class MockAppState extends Mock implements AppState {}

void main() {
  late BiometricAuthenticationService service;
  late MockBiometricsAuthenticate mockBiometricsAuthenticate;
  late MockEvoLocalStorageHelper mockSecureStorageHelper;
  late MockTsBioDetectChanged mockBioDetectChanged;
  late MockJwtHelper mockJwtHelper;
  late MockBiometricTokenProviderFactory mockProviderFactory;
  late MockBiometricTokenProvider mockProvider;
  late MockLoggingRepo mockLoggingRepo;
  late MockEvoUtilFunction mockEvoUtilFunction;
  late MockBiometricStatusChangeNotifier mockBioStatusNotifier;
  late MockAppState mockAppState;

  const String mockBiometricToken = 'mock_biometric_token';

  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(EnableBiometricAuthenticationFlow.accountActivation);
    registerFallbackValue(BiometricStatus.notSetup);
    registerFallbackValue(const BiometricTokenProviderConfig(
      flow: EnableBiometricAuthenticationFlow.accountActivation,
    ));
    registerFallbackValue(BiometricTokenRetrievalSuccess(
      biometricToken: mockBiometricToken,
      baseEntity: null,
    ));
    registerFallbackValue(BiometricTokenSkipEnableSuccess(
      baseEntity: null,
    ));
    registerFallbackValue(ChallengeType.none);

    // Setup GetIt dependencies
    mockLoggingRepo = MockLoggingRepo();
    getIt.registerLazySingleton<LoggingRepo>(() => mockLoggingRepo);

    mockEvoUtilFunction = MockEvoUtilFunction();
    getIt.registerLazySingleton<EvoUtilFunction>(() => mockEvoUtilFunction);

    mockAppState = MockAppState();
    getIt.registerLazySingleton<AppState>(() => mockAppState);

    mockBioStatusNotifier = MockBiometricStatusChangeNotifier();
  });

  setUp(() {
    mockBiometricsAuthenticate = MockBiometricsAuthenticate();
    mockSecureStorageHelper = MockEvoLocalStorageHelper();
    mockBioDetectChanged = MockTsBioDetectChanged();
    mockJwtHelper = MockJwtHelper();
    mockProviderFactory = MockBiometricTokenProviderFactory();
    mockProvider = MockBiometricTokenProvider();

    // Setup common mocks
    when(() => mockAppState.biometricStatusChangeNotifier).thenReturn(mockBioStatusNotifier);
    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) => Future<void>.value());
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) => Future<void>.value());
    when(() => mockEvoUtilFunction.clearBiometricAuthenticationData())
        .thenAnswer((_) => Future<void>.value());
    when(() => mockSecureStorageHelper.setBiometricToken(any()))
        .thenAnswer((_) => Future<void>.value());
    when(() => mockSecureStorageHelper.setBiometricAuthenticator(any()))
        .thenAnswer((_) => Future<void>.value());
    when(() => mockBioDetectChanged.initialize()).thenAnswer((_) => Future<void>.value());
    when(() => mockBioStatusNotifier.update(any())).thenReturn(null);

    service = BiometricAuthenticationService(
      biometricsAuthenticate: mockBiometricsAuthenticate,
      secureStorageHelper: mockSecureStorageHelper,
      bioDetectChanged: mockBioDetectChanged,
      jwtHelper: mockJwtHelper,
      providerFactory: mockProviderFactory,
    );
  });

  tearDown(() {
    reset(mockBiometricsAuthenticate);
    reset(mockSecureStorageHelper);
    reset(mockBioDetectChanged);
    reset(mockJwtHelper);
    reset(mockProviderFactory);
    reset(mockProvider);
    reset(mockEvoUtilFunction);
    reset(mockBioStatusNotifier);
  });

  group('BiometricAuthenticationService', () {
    group('constructor', () {
      test('should initialize with required dependencies', () {
        expect(service.biometricsAuthenticate, equals(mockBiometricsAuthenticate));
        expect(service.secureStorageHelper, equals(mockSecureStorageHelper));
        expect(service.bioDetectChanged, equals(mockBioDetectChanged));
        expect(service.jwtHelper, equals(mockJwtHelper));
        expect(service.isProcessing, isFalse);
      });

      test('should create default provider factory when not provided', () {
        final BiometricAuthenticationService serviceWithDefaultFactory =
            BiometricAuthenticationService(
          biometricsAuthenticate: mockBiometricsAuthenticate,
          secureStorageHelper: mockSecureStorageHelper,
          bioDetectChanged: mockBioDetectChanged,
          jwtHelper: mockJwtHelper,
        );

        expect(serviceWithDefaultFactory, isNotNull);
      });
    });

    group('provider management', () {
      test('should register provider', () {
        service.registerProvider(mockProvider);
        verify(() => mockProviderFactory.registerProvider(mockProvider)).called(1);
      });

      test('should unregister provider', () {
        service.unregisterProvider(mockProvider);
        verify(() => mockProviderFactory.unregisterProvider(mockProvider)).called(1);
      });

      test('should get all providers', () {
        when(() => mockProviderFactory.getAllProviders())
            .thenReturn(<BiometricTokenProvider>[mockProvider]);

        final List<BiometricTokenProvider> providers = service.getAllProviders();

        expect(providers, equals(<MockBiometricTokenProvider>[mockProvider]));
        verify(() => mockProviderFactory.getAllProviders()).called(1);
      });

      test('should check if provider exists for flow', () {
        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);

        final bool hasProvider = service.hasProviderForFlow(
          EnableBiometricAuthenticationFlow.accountActivation,
        );

        expect(hasProvider, isTrue);
        verify(() => mockProviderFactory.getProvider(
              EnableBiometricAuthenticationFlow.accountActivation,
            )).called(1);
      });

      test('should return false when no provider exists for flow', () {
        when(() => mockProviderFactory.getProvider(any())).thenReturn(null);

        final bool hasProvider = service.hasProviderForFlow(
          EnableBiometricAuthenticationFlow.profileSettings,
        );

        expect(hasProvider, isFalse);
      });
    });

    group('enableWithFlow', () {
      const BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
        flow: EnableBiometricAuthenticationFlow.accountActivation,
      );

      test('should return failure when already processing', () async {
        service.isProcessing = true;

        final BiometricAuthenticationResult result = await service.enableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.unknown);
        expect(failure.userMessage, contains('already in progress'));
      });

      test('should return failure for concurrent calls without await due to _runLocking', () async {
        // Mock provider to exist and simulate a long-running operation
        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);

        // Mock biometric authentication to succeed but with delay
        when(() => mockBiometricsAuthenticate.authenticate(
              localizedReason: any(named: 'localizedReason'),
              cancelButton: any(named: 'cancelButton'),
            )).thenAnswer((_) async {
          return BioAuthResult.success();
        });

        // Mock token retrieval to succeed with delay
        when(() => mockProvider.getBiometricToken(any())).thenAnswer((_) async {
          return BiometricTokenRetrievalSuccess(
            biometricToken: mockBiometricToken,
            baseEntity: BaseEntity(),
          );
        });

        final Future<BiometricAuthenticationResult> call1 = service.enableWithFlow(config: config);
        final Future<BiometricAuthenticationResult> call2 = service.enableWithFlow(config: config);
        final List<BiometricAuthenticationResult> results = await Future.wait([call1, call2]);

        // One should succeed, one should fail due to _runLocking
        final BiometricAuthenticationSuccess? success =
            results.whereType<BiometricAuthenticationSuccess>().firstOrNull;
        final BiometricAuthenticationFailure? failure =
            results.whereType<BiometricAuthenticationFailure>().firstOrNull;

        expect(success, isNotNull, reason: 'Exactly one call should succeed');
        expect(failure, isNotNull, reason: 'Exactly one call should fail');

        // Verify the failure is due to locking mechanism
        expect(failure?.errorType, BiometricAuthenticationFailureType.unknown);
        expect(failure?.userMessage, contains('already in progress'));

        // Verify processing state is reset after completion
        expect(service.isProcessing, isFalse);
      });

      test('should return failure for unsupported flow', () async {
        // Create a mock unsupported flow by using a different enum value
        const BiometricTokenProviderConfig unsupportedConfig = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.profileSettings,
        );

        final BiometricAuthenticationResult result =
            await service.enableWithFlow(config: unsupportedConfig);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.noSupportFlow);
        expect(service.isProcessing, isFalse);
      });

      test('should return failure when biometric authentication fails', () async {
        // Mock that provider exists for the flow
        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);

        when(() => mockBiometricsAuthenticate.authenticate(
                  localizedReason: any(named: 'localizedReason'),
                  cancelButton: any(named: 'cancelButton'),
                ))
            .thenAnswer(
                (_) => Future<BioAuthResult>.value(BioAuthResult.error(BioAuthError.notEnrolled)));

        final BiometricAuthenticationResult result = await service.enableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.biometrics);
        expect(failure.biometricError, BioAuthError.notEnrolled);
        expect(service.isProcessing, isFalse);
      });

      test('should return success when biometric authentication and token retrieval succeed',
          () async {
        when(() => mockBiometricsAuthenticate.authenticate(
              localizedReason: any(named: 'localizedReason'),
              cancelButton: any(named: 'cancelButton'),
            )).thenAnswer((_) => Future<BioAuthResult>.value(BioAuthResult.success()));

        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);
        when(() => mockProvider.getBiometricToken(any())).thenAnswer(
          (_) => Future<BiometricTokenRetrievalResult>.value(BiometricTokenRetrievalSuccess(
            biometricToken: mockBiometricToken,
            baseEntity: BaseEntity(),
          )),
        );

        final BiometricAuthenticationResult result = await service.enableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationSuccess>());
        expect(service.isProcessing, isFalse);

        // Verify UI loading was handled
        verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
        verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);

        // Verify biometric feature was enabled
        verify(() => mockSecureStorageHelper.setBiometricToken(mockBiometricToken)).called(1);
        verify(() => mockSecureStorageHelper.setBiometricAuthenticator(true)).called(1);
        verify(() => mockBioDetectChanged.initialize()).called(1);
        verify(() => mockBioStatusNotifier.update(BiometricStatus.usable)).called(1);
      });

      test('should return failure when no provider found for flow', () async {
        // Mock that no provider exists for the flow - this will cause early return
        when(() => mockProviderFactory.getProvider(any())).thenReturn(null);

        final BiometricAuthenticationResult result = await service.enableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.noSupportFlow);
        expect(service.isProcessing, isFalse);

        // Verify biometric authentication was not called since provider check failed early
        verifyNever(() => mockBiometricsAuthenticate.authenticate(
              localizedReason: any(named: 'localizedReason'),
              cancelButton: any(named: 'cancelButton'),
            ));

        // Verify UI loading was not called since it fails early
        verifyNever(() => mockEvoUtilFunction.showHudLoading());
        verifyNever(() => mockEvoUtilFunction.hideHudLoading());
      });

      test('should return failure when token retrieval fails', () async {
        when(() => mockBiometricsAuthenticate.authenticate(
              localizedReason: any(named: 'localizedReason'),
              cancelButton: any(named: 'cancelButton'),
            )).thenAnswer((_) => Future.value(BioAuthResult.success()));

        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);
        when(() => mockProvider.getBiometricToken(any())).thenAnswer(
          (_) => Future.value(BiometricTokenRetrievalFailure(
            errorType: BiometricAuthenticationFailureType.apiError,
            errorMessage: 'API Error',
          )),
        );

        final BiometricAuthenticationResult result = await service.enableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.apiError);
        expect(service.isProcessing, isFalse);
      });

      test('should handle challenge result (currently returns failure)', () async {
        when(() => mockBiometricsAuthenticate.authenticate(
              localizedReason: any(named: 'localizedReason'),
              cancelButton: any(named: 'cancelButton'),
            )).thenAnswer((_) => Future.value(BioAuthResult.success()));

        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);
        when(() => mockProvider.getBiometricToken(any())).thenAnswer(
          (_) => Future.value(BiometricTokenRetrievalChallenge(ChallengeType.pin)),
        );

        final BiometricAuthenticationResult result = await service.enableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.unknown);
        expect(service.isProcessing, isFalse);
      });

      test('should handle unexpected exceptions (throws from OS)', () async {
        // Mock that provider exists for the flow
        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);

        when(() => mockBiometricsAuthenticate.authenticate(
              localizedReason: any(named: 'localizedReason'),
              cancelButton: any(named: 'cancelButton'),
            )).thenThrow(Exception('Unexpected error'));

        final BiometricAuthenticationResult result = await service.enableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.biometrics);
        expect(failure.userMessage, contains('Unexpected error'));
        expect(service.isProcessing, isFalse);
      });
    });

    group('getBiometricChanged', () {
      test('should return true when biometric changed', () async {
        when(() => mockBioDetectChanged.isBiometricChanged()).thenAnswer((_) => Future.value(true));

        final bool result = await service.getBiometricChanged();

        expect(result, isTrue);
        verify(() => mockBioDetectChanged.isBiometricChanged()).called(1);
      });

      test('should return false when biometric not changed', () async {
        when(() => mockBioDetectChanged.isBiometricChanged())
            .thenAnswer((_) => Future<bool>.value(false));

        final bool result = await service.getBiometricChanged();

        expect(result, isFalse);
        verify(() => mockBioDetectChanged.isBiometricChanged()).called(1);
      });

      test('should return false when biometric changed returns null', () async {
        when(() => mockBioDetectChanged.isBiometricChanged())
            .thenAnswer((_) => Future<bool?>.value(null));

        final bool result = await service.getBiometricChanged();

        expect(result, isFalse);
        verify(() => mockBioDetectChanged.isBiometricChanged()).called(1);
      });

      test('should return false and log error when exception occurs', () async {
        when(() => mockBioDetectChanged.isBiometricChanged())
            .thenThrow(Exception('Test exception'));
        when(() => mockLoggingRepo.logErrorEvent(
              errorType: any(named: 'errorType'),
              args: any(named: 'args'),
            )).thenAnswer((_) => Future<void>.value());

        final bool result = await service.getBiometricChanged();

        expect(result, isFalse);
        verify(() => mockBioDetectChanged.isBiometricChanged()).called(1);
      });
    });

    group('hasEnrolledBiometrics', () {
      test('should return true when biometrics are enrolled', () async {
        when(() => mockBiometricsAuthenticate.getAvailableBiometricType()).thenAnswer(
            (_) => Future.value(<BiometricType>[BiometricType.face, BiometricType.fingerprint]));

        final bool result = await service.hasEnrolledBiometrics();

        expect(result, isTrue);
        verify(() => mockBiometricsAuthenticate.getAvailableBiometricType()).called(1);
      });

      test('should return false when no biometrics are enrolled', () async {
        when(() => mockBiometricsAuthenticate.getAvailableBiometricType())
            .thenAnswer((_) => Future.value(<BiometricType>[]));

        final bool result = await service.hasEnrolledBiometrics();

        expect(result, isFalse);
        verify(() => mockBiometricsAuthenticate.getAvailableBiometricType()).called(1);
      });
    });

    group('isEnableBiometricAuthenticator', () {
      test('should return storage helper result', () async {
        when(() => mockSecureStorageHelper.isEnableBiometricAuthenticator())
            .thenAnswer((_) => Future.value(true));

        final bool result = await service.isEnableBiometricAuthenticator();

        expect(result, isTrue);
        verify(() => mockSecureStorageHelper.isEnableBiometricAuthenticator()).called(1);
      });
    });

    group('enableBiometricAuthenticationFeature', () {
      test('should enable biometric feature when token is provided', () async {
        await service.enableBiometricAuthenticationFeature(mockBiometricToken);

        verify(() => mockSecureStorageHelper.setBiometricToken(mockBiometricToken)).called(1);
        verify(() => mockSecureStorageHelper.setBiometricAuthenticator(true)).called(1);
        verify(() => mockBioDetectChanged.initialize()).called(1);
        verify(() => mockBioStatusNotifier.update(BiometricStatus.usable)).called(1);
      });

      test('should do nothing when token is null', () async {
        await service.enableBiometricAuthenticationFeature(null);

        verifyNever(() => mockSecureStorageHelper.setBiometricToken(any()));
        verifyNever(() => mockSecureStorageHelper.setBiometricAuthenticator(any()));
        verifyNever(() => mockBioDetectChanged.initialize());
        verifyNever(() => mockBioStatusNotifier.update(any()));
      });

      test('should do nothing when token is empty', () async {
        await service.enableBiometricAuthenticationFeature('');

        verifyNever(() => mockSecureStorageHelper.setBiometricToken(any()));
        verifyNever(() => mockSecureStorageHelper.setBiometricAuthenticator(any()));
        verifyNever(() => mockBioDetectChanged.initialize());
        verifyNever(() => mockBioStatusNotifier.update(any()));
      });
    });

    group('disableBiometricAuthenticationFeature', () {
      test('should clear biometric authentication data', () async {
        await service.disableBiometricAuthenticationFeature();

        verify(() => mockEvoUtilFunction.clearBiometricAuthenticationData()).called(1);
      });
    });

    group('isBiometricTokenUsable', () {
      test('should return true when token is usable', () async {
        when(() => mockSecureStorageHelper.getBiometricToken())
            .thenAnswer((_) => Future.value(mockBiometricToken));
        when(() => mockJwtHelper.isCanUse(mockBiometricToken)).thenReturn(true);

        final bool result = await service.isBiometricTokenUsable();

        expect(result, isTrue);
        verify(() => mockSecureStorageHelper.getBiometricToken()).called(1);
        verify(() => mockJwtHelper.isCanUse(mockBiometricToken)).called(1);
      });

      test('should return false when token is not usable', () async {
        when(() => mockSecureStorageHelper.getBiometricToken())
            .thenAnswer((_) => Future.value(mockBiometricToken));
        when(() => mockJwtHelper.isCanUse(mockBiometricToken)).thenReturn(false);

        final bool result = await service.isBiometricTokenUsable();

        expect(result, isFalse);
        verify(() => mockSecureStorageHelper.getBiometricToken()).called(1);
        verify(() => mockJwtHelper.isCanUse(mockBiometricToken)).called(1);
      });

      test('should handle null token', () async {
        when(() => mockSecureStorageHelper.getBiometricToken())
            .thenAnswer((_) => Future.value(null));
        when(() => mockJwtHelper.isCanUse(null)).thenReturn(false);

        final bool result = await service.isBiometricTokenUsable();

        expect(result, isFalse);
        verify(() => mockSecureStorageHelper.getBiometricToken()).called(1);
        verify(() => mockJwtHelper.isCanUse(null)).called(1);
      });
    });

    group('skipEnableWithFlow', () {
      const BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
        flow: EnableBiometricAuthenticationFlow.accountActivation,
      );

      test('should return failure when already processing', () async {
        service.isProcessing = true;

        final BiometricAuthenticationResult result =
            await service.skipEnableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.unknown);
        expect(failure.userMessage, contains('already in progress'));
      });

      test('should return failure for concurrent calls without await due to _runLocking', () async {
        // Mock provider to exist and simulate a long-running operation
        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);

        // Mock skip enable biometric to succeed with delay
        when(() => mockProvider.skipEnableBiometric(any())).thenAnswer((_) async {
          return BiometricTokenSkipEnableSuccess(
            baseEntity: BaseEntity(),
          );
        });

        final Future<BiometricAuthenticationResult> call1 =
            service.skipEnableWithFlow(config: config);
        final Future<BiometricAuthenticationResult> call2 =
            service.skipEnableWithFlow(config: config);
        final List<BiometricAuthenticationResult> results = await Future.wait([call1, call2]);

        // One should succeed, one should fail due to _runLocking
        final BiometricAuthenticationSuccess? success =
            results.whereType<BiometricAuthenticationSuccess>().firstOrNull;
        final BiometricAuthenticationFailure? failure =
            results.whereType<BiometricAuthenticationFailure>().firstOrNull;

        expect(success, isNotNull, reason: 'Exactly one call should succeed');
        expect(failure, isNotNull, reason: 'Exactly one call should fail');

        // Verify the failure is due to locking mechanism
        expect(failure?.errorType, BiometricAuthenticationFailureType.unknown);
        expect(failure?.userMessage, contains('already in progress'));

        // Verify processing state is reset after completion
        expect(service.isProcessing, isFalse);
      });

      test('should return failure for unsupported flow', () async {
        const BiometricTokenProviderConfig unsupportedConfig = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.profileSettings,
        );

        final BiometricAuthenticationResult result =
            await service.skipEnableWithFlow(config: unsupportedConfig);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.noSupportFlow);
        expect(failure.userMessage, contains('Unsupported flow: ${unsupportedConfig.flow}'));
        expect(service.isProcessing, isFalse);
      });

      test('should return failure when no provider found for flow', () async {
        when(() => mockProviderFactory.getProvider(any())).thenReturn(null);

        final BiometricAuthenticationResult result =
            await service.skipEnableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.noSupportFlow);
        expect(failure.userMessage, contains('Unsupported flow: ${config.flow}'));
        expect(service.isProcessing, isFalse);
        verify(() => mockProviderFactory.getProvider(config.flow)).called(1);
      });

      test('should return success when provider returns skip enable success', () async {
        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);
        when(() => mockProvider.skipEnableBiometric(any())).thenAnswer(
          (_) => Future<BiometricTokenRetrievalResult>.value(BiometricTokenSkipEnableSuccess(
            baseEntity: BaseEntity(),
          )),
        );

        final BiometricAuthenticationResult result =
            await service.skipEnableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationSuccess>());
        final BiometricAuthenticationSuccess success = result as BiometricAuthenticationSuccess;
        expect(success.entity, isA<BaseEntity>());
        expect(service.isProcessing, isFalse);
        // getProvider is called twice: once in hasProviderForFlow, once to get the actual provider
        verify(() => mockProviderFactory.getProvider(config.flow)).called(2);
        verify(() => mockProvider.skipEnableBiometric(config)).called(1);
      });

      test('should return failure when provider returns failure', () async {
        final ErrorUIModel errorModel = ErrorUIModel(
          userMessage: 'API error occurred',
        );

        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);
        when(() => mockProvider.skipEnableBiometric(any())).thenAnswer(
          (_) => Future<BiometricTokenRetrievalResult>.value(BiometricTokenRetrievalFailure(
            errorType: BiometricAuthenticationFailureType.apiError,
            apiError: errorModel,
          )),
        );

        final BiometricAuthenticationResult result =
            await service.skipEnableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.apiError);
        expect(failure.apiError, equals(errorModel));
        expect(service.isProcessing, isFalse);
        // getProvider is called twice: once in hasProviderForFlow, once to get the actual provider
        verify(() => mockProviderFactory.getProvider(config.flow)).called(2);
        verify(() => mockProvider.skipEnableBiometric(config)).called(1);
      });

      test('should return failure when provider returns challenge (currently unsupported)',
          () async {
        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);
        when(() => mockProvider.skipEnableBiometric(any())).thenAnswer(
          (_) => Future<BiometricTokenRetrievalResult>.value(
              BiometricTokenRetrievalChallenge(ChallengeType.pin)),
        );

        final BiometricAuthenticationResult result =
            await service.skipEnableWithFlow(config: config);

        expect(result, isA<BiometricAuthenticationFailure>());
        final BiometricAuthenticationFailure failure = result as BiometricAuthenticationFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.unknown);
        expect(service.isProcessing, isFalse);
        // getProvider is called twice: once in hasProviderForFlow, once to get the actual provider
        verify(() => mockProviderFactory.getProvider(config.flow)).called(2);
        verify(() => mockProvider.skipEnableBiometric(config)).called(1);
      });

      test('should verify correct parameters are passed to provider', () async {
        when(() => mockProviderFactory.getProvider(any())).thenReturn(mockProvider);
        when(() => mockProvider.skipEnableBiometric(any())).thenAnswer(
          (_) => Future<BiometricTokenRetrievalResult>.value(
              BiometricTokenSkipEnableSuccess(baseEntity: BaseEntity())),
        );

        await service.skipEnableWithFlow(config: config);

        verify(() => mockProvider.skipEnableBiometric(
              captureAny(
                that: isA<BiometricTokenProviderConfig>()
                    .having((BiometricTokenProviderConfig config) => config.flow, 'flow',
                        equals(config.flow))
                    .having((BiometricTokenProviderConfig config) => config.additionalParams,
                        'additionalParams', equals(config.additionalParams)),
              ),
            )).called(1);
      });
    });

    group('dispose', () {
      test('should dispose provider factory', () {
        service.dispose();

        verify(() => mockProviderFactory.dispose()).called(1);
      });
    });
  });
}
