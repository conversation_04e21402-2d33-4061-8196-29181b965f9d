import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EnableBiometricAuthenticationFlow', () {
    test('should have expected enum values for business flows', () {
      expect(EnableBiometricAuthenticationFlow.values,
          hasLength(EnableBiometricAuthenticationFlow.values.length));
      expect(EnableBiometricAuthenticationFlow.values,
          contains(EnableBiometricAuthenticationFlow.accountActivation));
      expect(EnableBiometricAuthenticationFlow.values,
          contains(EnableBiometricAuthenticationFlow.newDeviceLogin));
      expect(EnableBiometricAuthenticationFlow.values,
          contains(EnableBiometricAuthenticationFlow.profileSettings));
    });
  });

  group('BiometricTokenProviderConfig', () {
    group('account activation flow', () {
      test('should create config for account activation without additional params', () {
        const BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.accountActivation,
        );

        expect(config.flow, equals(EnableBiometricAuthenticationFlow.accountActivation));
        expect(config.additionalParams, isNull);
      });

      test('should create config for account activation with user context', () {
        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.accountActivation,
          additionalParams: <String, dynamic>{
            'userId': 'user123',
            'deviceId': 'device456',
          },
        );

        expect(config.flow, equals(EnableBiometricAuthenticationFlow.accountActivation));
        expect(config.additionalParams!['userId'], equals('user123'));
        expect(config.additionalParams!['deviceId'], equals('device456'));
      });
    });

    group('profile settings flow', () {
      test('should create config for profile settings without additional params', () {
        const BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.profileSettings,
        );

        expect(config.flow, equals(EnableBiometricAuthenticationFlow.profileSettings));
        expect(config.additionalParams, isNull);
      });

      test('should create config for profile settings with security context', () {
        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.profileSettings,
          additionalParams: <String, dynamic>{
            'actionType': 'enable_biometric',
            'securityLevel': 'high',
          },
        );

        expect(config.flow, equals(EnableBiometricAuthenticationFlow.profileSettings));
        expect(config.additionalParams!['actionType'], equals('enable_biometric'));
        expect(config.additionalParams!['securityLevel'], equals('high'));
      });
    });

    test('should create config for new device login without additional params', () {
      final Map<String, String> additionalParams = <String, String>{
        'session_token': 'session_token'
      };
      final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
        flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
        additionalParams: additionalParams,
      );

      expect(config.flow, equals(EnableBiometricAuthenticationFlow.newDeviceLogin));
      expect(config.additionalParams, equals(additionalParams));
    });

    group('copyWith method', () {
      test('should switch from account activation to profile settings flow', () {
        // Scenario: User completes account activation and moves to profile settings
        final BiometricTokenProviderConfig accountActivationConfig = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.accountActivation,
          additionalParams: <String, dynamic>{
            'userId': 'user123',
            'activationToken': 'token456',
          },
        );

        final BiometricTokenProviderConfig profileSettingsConfig = accountActivationConfig.copyWith(
          flow: EnableBiometricAuthenticationFlow.profileSettings,
          additionalParams: <String, dynamic>{
            'userId': 'user123',
            'actionType': 'manage_biometric',
          },
        );

        expect(
            profileSettingsConfig.flow, equals(EnableBiometricAuthenticationFlow.profileSettings));
        expect(profileSettingsConfig.additionalParams!['userId'], equals('user123'));
        expect(profileSettingsConfig.additionalParams!['actionType'], equals('manage_biometric'));
        expect(profileSettingsConfig.additionalParams!.containsKey('activationToken'), isFalse);
      });

      test('should add security context to existing config', () {
        // Scenario: Adding security parameters for sensitive operations
        final BiometricTokenProviderConfig baseConfig = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.profileSettings,
          additionalParams: <String, dynamic>{
            'userId': 'user123',
          },
        );

        final BiometricTokenProviderConfig secureConfig = baseConfig.copyWith(
          additionalParams: <String, dynamic>{
            'userId': 'user123',
            'requireStrongAuth': true,
            'sessionTimeout': 300,
          },
        );

        expect(secureConfig.flow, equals(EnableBiometricAuthenticationFlow.profileSettings));
        expect(secureConfig.additionalParams!['userId'], equals('user123'));
        expect(secureConfig.additionalParams!['requireStrongAuth'], isTrue);
        expect(secureConfig.additionalParams!['sessionTimeout'], equals(300));
      });

      test('should copy information correctly', () {
        // Scenario: Simplifying config for basic operations
        final BiometricTokenProviderConfig complexConfig = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.accountActivation,
          additionalParams: <String, dynamic>{
            'userId': 'user123',
            'deviceId': 'device456',
            'debugMode': true,
          },
        );

        final BiometricTokenProviderConfig copyConfig = complexConfig.copyWith();

        expect(copyConfig.flow, equals(complexConfig.flow));
        expect(copyConfig.additionalParams!['userId'],
            equals(complexConfig.additionalParams!['userId']));
        expect(copyConfig.additionalParams!['deviceId'],
            equals(complexConfig.additionalParams!['deviceId']));
        expect(copyConfig.additionalParams!['debugMode'],
            equals(complexConfig.additionalParams!['debugMode']));
      });
    });
  });
}
