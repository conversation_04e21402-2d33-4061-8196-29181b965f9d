import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_retrieval_result.dart';
import 'package:evoapp/feature/biometric/base/ext_biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_result.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BiometricTokenRetrievalResult', () {
    group('BiometricTokenRetrievalSuccess', () {
      test('should create success result with biometric token', () {
        // Scenario: Successful token retrieval during account activation
        const String expectedToken = 'biometric_token_12345';
        final BaseEntity expectedEntity = BaseEntity();

        final result = BiometricTokenRetrievalSuccess(
          biometricToken: expectedToken,
          baseEntity: expectedEntity,
        );

        expect(result.biometricToken, equals(expectedToken));
        expect(result.baseEntity, equals(expectedEntity));
      });

      test('should create success result with null values', () {
        // Scenario: Success but no token or entity returned
        final result = BiometricTokenRetrievalSuccess(
          biometricToken: null,
          baseEntity: null,
        );

        expect(result.biometricToken, isNull);
        expect(result.baseEntity, isNull);
      });
    });

    group('BiometricTokenSkipEnableSuccess', () {
      test('should create skip enable success result with base entity', () {
        final BaseEntity expectedEntity = BaseEntity();

        final BiometricTokenSkipEnableSuccess result = BiometricTokenSkipEnableSuccess(
          baseEntity: expectedEntity,
        );

        expect(result, isA<BiometricTokenRetrievalResult>());
        expect(result.baseEntity, equals(expectedEntity));
      });

      test('should create skip enable success result with null base entity', () {
        final result = BiometricTokenSkipEnableSuccess(
          baseEntity: null,
        );

        expect(result.baseEntity, isNull);
      });
    });

    group('BiometricTokenRetrievalChallenge', () {
      test('should create challenge result with PIN challenge', () {
        // Scenario: Server requires PIN verification before providing token
        final result = BiometricTokenRetrievalChallenge(ChallengeType.pin);

        expect(result.challengeType, equals(ChallengeType.pin));
      });

      test('should create challenge result with no challenge', () {
        // Scenario: No additional challenge required
        final result = BiometricTokenRetrievalChallenge(ChallengeType.none);

        expect(result.challengeType, equals(ChallengeType.none));
      });
    });

    group('BiometricTokenRetrievalFailure', () {
      test('should create generic failure with biometric error', () {
        // Scenario: Biometric authentication failed on device
        final result = BiometricTokenRetrievalFailure.generic(
          errorType: BiometricAuthenticationFailureType.biometrics,
          errorMessage: 'Biometric authentication failed',
        );

        expect(result.errorType, equals(BiometricAuthenticationFailureType.biometrics));
        expect(result.errorMessage, equals('Biometric authentication failed'));
        expect(result.apiError, isNull);
        expect(result.isApiError, isFalse);
      });

      test('should create API failure with error details', () {
        // Scenario: Server returned error during token request
        final apiError = ErrorUIModel(
          userMessage: 'Server temporarily unavailable',
        );

        final result = BiometricTokenRetrievalFailure.api(error: apiError);

        expect(result.errorType, equals(BiometricAuthenticationFailureType.apiError));
        expect(result.apiError, equals(apiError));
        expect(result.isApiError, isTrue);
        expect(result.displayMessage, equals('Server temporarily unavailable'));
      });

      test('should create failure with unknown error type', () {
        // Scenario: Unexpected error during token retrieval
        final result = BiometricTokenRetrievalFailure(
          errorType: BiometricAuthenticationFailureType.unknown,
          errorMessage: 'Unexpected error occurred',
        );

        expect(result.errorType, equals(BiometricAuthenticationFailureType.unknown));
        expect(result.errorMessage, equals('Unexpected error occurred'));
        expect(result.displayMessage, equals('Unexpected error occurred'));
      });

      test('should prioritize explicit error message in displayMessage', () {
        // Scenario: Both explicit message and API error present
        final apiError = ErrorUIModel(
          userMessage: 'API error message',
        );

        final result = BiometricTokenRetrievalFailure(
          errorType: BiometricAuthenticationFailureType.apiError,
          errorMessage: 'Explicit error message',
          apiError: apiError,
        );

        expect(result.displayMessage, equals('Explicit error message'));
      });

      test('should fallback to API error message when no explicit message', () {
        // Scenario: Only API error message available
        final apiError = ErrorUIModel(
          userMessage: 'Network connection failed',
        );

        final result = BiometricTokenRetrievalFailure(
          errorType: BiometricAuthenticationFailureType.apiError,
          apiError: apiError,
        );

        expect(result.displayMessage, equals('Network connection failed'));
      });

      test('should return null displayMessage when no messages available', () {
        // Scenario: Error with no user-friendly messages
        final result = BiometricTokenRetrievalFailure(
          errorType: BiometricAuthenticationFailureType.unknown,
        );

        expect(result.displayMessage, isNull);
      });
    });

    group('BiometricTokenRetrievalResultExtensions', () {
      test('should correctly identify success result', () {
        // Scenario: Checking result type for success case
        final result = BiometricTokenRetrievalSuccess(
          biometricToken: 'token123',
          baseEntity: null,
        );

        expect(result.isSuccess, isTrue);
        expect(result.isChallenge, isFalse);
        expect(result.isFailure, isFalse);
        expect(result.asSuccess, isNotNull);
        expect(result.asChallenge, isNull);
        expect(result.asFailure, isNull);
      });

      test('should correctly identify challenge result', () {
        // Scenario: Checking result type for challenge case
        final result = BiometricTokenRetrievalChallenge(ChallengeType.pin);

        expect(result.isSuccess, isFalse);
        expect(result.isChallenge, isTrue);
        expect(result.isFailure, isFalse);
        expect(result.asSuccess, isNull);
        expect(result.asChallenge, isNotNull);
        expect(result.asFailure, isNull);
      });

      test('should correctly identify failure result', () {
        // Scenario: Checking result type for failure case
        final result = BiometricTokenRetrievalFailure.generic(
          errorType: BiometricAuthenticationFailureType.biometrics,
          errorMessage: 'Failed',
        );

        expect(result.isSuccess, isFalse);
        expect(result.isChallenge, isFalse);
        expect(result.isFailure, isTrue);
        expect(result.asSuccess, isNull);
        expect(result.asChallenge, isNull);
        expect(result.asFailure, isNotNull);
      });

      test('should correctly identify skip enable success result', () {
        // Scenario: Checking result type for skip enable success case
        final result = BiometricTokenSkipEnableSuccess(
          baseEntity: BaseEntity(),
        );

        expect(result.isSuccess, isFalse);
        expect(result.isChallenge, isFalse);
        expect(result.isFailure, isFalse);
        expect(result.asSuccess, isNull);
        expect(result.asChallenge, isNull);
        expect(result.asFailure, isNull);
      });

      test('should extract biometric token from success result', () {
        // Scenario: Getting token from successful result
        const String expectedToken = 'extracted_token_456';
        final result = BiometricTokenRetrievalSuccess(
          biometricToken: expectedToken,
          baseEntity: null,
        );

        expect(result.biometricToken, equals(expectedToken));
      });

      test('should return null biometric token for non-success results', () {
        // Scenario: Getting token from challenge, failure, and skip enable results
        final challengeResult = BiometricTokenRetrievalChallenge(ChallengeType.pin);
        final failureResult = BiometricTokenRetrievalFailure.generic(
          errorType: BiometricAuthenticationFailureType.biometrics,
        );
        final skipEnableResult = BiometricTokenSkipEnableSuccess(
          baseEntity: BaseEntity(),
        );

        expect(challengeResult.biometricToken, isNull);
        expect(failureResult.biometricToken, isNull);
        expect(skipEnableResult.biometricToken, isNull);
      });

      test('should extract challenge type from challenge result', () {
        // Scenario: Getting challenge type from challenge result
        final result = BiometricTokenRetrievalChallenge(ChallengeType.pin);

        expect(result.challengeType, equals(ChallengeType.pin));
      });

      test('should return null challenge type for non-challenge results', () {
        // Scenario: Getting challenge type from success, failure, and skip enable results
        final BiometricTokenRetrievalSuccess successResult = BiometricTokenRetrievalSuccess(
          biometricToken: 'token',
          baseEntity: null,
        );
        expect(successResult.challengeType, isNull);

        final BiometricTokenRetrievalFailure failureResult = BiometricTokenRetrievalFailure.generic(
          errorType: BiometricAuthenticationFailureType.biometrics,
        );
        expect(failureResult.challengeType, isNull);

        final BiometricTokenSkipEnableSuccess skipEnableResult = BiometricTokenSkipEnableSuccess(
          baseEntity: BaseEntity(),
        );
        expect(skipEnableResult.challengeType, isNull);
      });

      test('should extract error type from failure result', () {
        // Scenario: Getting error type from failure result
        final result = BiometricTokenRetrievalFailure.generic(
          errorType: BiometricAuthenticationFailureType.noSupportFlow,
        );

        expect(result.errorType, equals(BiometricAuthenticationFailureType.noSupportFlow));
      });

      test('should return null error type for non-failure results', () {
        // Scenario: Getting error type from success, challenge, and skip enable results
        final successResult = BiometricTokenRetrievalSuccess(
          biometricToken: 'token',
          baseEntity: null,
        );
        final challengeResult = BiometricTokenRetrievalChallenge(ChallengeType.pin);
        final skipEnableResult = BiometricTokenSkipEnableSuccess(
          baseEntity: BaseEntity(),
        );

        expect(successResult.errorType, isNull);
        expect(challengeResult.errorType, isNull);
        expect(skipEnableResult.errorType, isNull);
      });

      test('should extract error message from failure result', () {
        // Scenario: Getting error message from failure result
        const String expectedMessage = 'Authentication failed';
        final result = BiometricTokenRetrievalFailure.generic(
          errorType: BiometricAuthenticationFailureType.biometrics,
          errorMessage: expectedMessage,
        );

        expect(result.errorMessage, equals(expectedMessage));
      });

      test('should return null error message for non-failure results', () {
        // Scenario: Getting error message from success, challenge, and skip enable results
        final successResult = BiometricTokenRetrievalSuccess(
          biometricToken: 'token',
          baseEntity: null,
        );
        final challengeResult = BiometricTokenRetrievalChallenge(ChallengeType.pin);
        final skipEnableResult = BiometricTokenSkipEnableSuccess(
          baseEntity: BaseEntity(),
        );

        expect(successResult.errorMessage, isNull);
        expect(challengeResult.errorMessage, isNull);
        expect(skipEnableResult.errorMessage, isNull);
      });

      test('should access baseEntity property directly from skip enable success result', () {
        // Scenario: Accessing baseEntity from BiometricTokenSkipEnableSuccess
        final BaseEntity expectedEntity = BaseEntity();
        final skipEnableResult = BiometricTokenSkipEnableSuccess(
          baseEntity: expectedEntity,
        );

        expect(skipEnableResult.baseEntity, equals(expectedEntity));
      });

      test('should handle null baseEntity in skip enable success result', () {
        // Scenario: Accessing null baseEntity from BiometricTokenSkipEnableSuccess
        final skipEnableResult = BiometricTokenSkipEnableSuccess(
          baseEntity: null,
        );

        expect(skipEnableResult.baseEntity, isNull);
      });
    });
  });
}
