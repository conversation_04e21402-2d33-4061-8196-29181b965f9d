import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/account_activation/mock/mock_account_activation_use_case.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_result.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/impl/account_activation_token_provider.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_retrieval_result.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider_config.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../util/test_util.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  group('AccountActivationTokenProvider', () {
    late MockAuthenticationRepo mockAuthRepo;
    late AccountActivationTokenProvider provider;

    setUp(() {
      mockAuthRepo = MockAuthenticationRepo();
      provider = AccountActivationTokenProvider(authRepo: mockAuthRepo);
    });

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      registerFallbackValue(ActivateAccountEnableBiometricRequest(
        skip: false,
        sessionToken: 'test-token',
      ));
      registerFallbackValue(MockConfig(enable: true));
    });

    group('createConfig', () {
      test('should create config with session token', () {
        const String sessionToken = 'test-session-token';

        final BiometricTokenProviderConfig config =
            AccountActivationTokenProvider.createConfig(sessionToken: sessionToken);

        expect(config.flow, EnableBiometricAuthenticationFlow.accountActivation);
        expect(config.additionalParams?[AccountActivationTokenProvider.sessionKey], sessionToken);
      });

      test('should create config with null session token', () {
        final BiometricTokenProviderConfig config =
            AccountActivationTokenProvider.createConfig(sessionToken: null);

        expect(config.flow, EnableBiometricAuthenticationFlow.accountActivation);
        expect(config.additionalParams?[AccountActivationTokenProvider.sessionKey], isNull);
      });
    });

    group('supportedFlow', () {
      test('should return account activation flow', () {
        expect(provider.supportedFlow, EnableBiometricAuthenticationFlow.accountActivation);
      });
    });

    group('getBiometricToken', () {
      test('should return failure when flow is not supported', () async {
        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.profileSettings,
        );

        final BiometricTokenRetrievalResult result = await provider.getBiometricToken(config);

        expect(result, isA<BiometricTokenRetrievalFailure>());
        final BiometricTokenRetrievalFailure failure = result as BiometricTokenRetrievalFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.noSupportFlow);
        verifyNever(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            ));
      });

      test('should return success when API call succeeds', () async {
        const String sessionToken = 'test-session-token';
        const String biometricToken = 'test-biometric-token';

        final AccountActivationEntity successEntity = AccountActivationEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{
              'data': <String, dynamic>{'biometric_token': biometricToken}
            },
            statusCode: CommonHttpClient.SUCCESS,
          ),
        );

        when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => successEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.accountActivation,
          additionalParams: <String, String?>{
            AccountActivationTokenProvider.sessionKey: sessionToken,
          },
        );

        final BiometricTokenRetrievalResult result = await provider.getBiometricToken(config);

        expect(result, isA<BiometricTokenRetrievalSuccess>());
        final BiometricTokenRetrievalSuccess success = result as BiometricTokenRetrievalSuccess;
        expect(success.biometricToken, biometricToken);
        expect(success.baseEntity, successEntity);

        verify(() => mockAuthRepo.activateAccount(
              request: any(
                  named: 'request',
                  that: isA<ActivateAccountEnableBiometricRequest>()
                      .having(
                          (ActivateAccountEnableBiometricRequest req) => req.skip, 'skip', false)
                      .having((ActivateAccountEnableBiometricRequest req) => req.sessionToken,
                          'sessionToken', sessionToken)),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });

      test('should return failure when API call fails', () async {
        const String sessionToken = 'test-session-token';

        final AccountActivationEntity failureEntity = AccountActivationEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{
              'user_message': 'Invalid session token',
              'user_message_title': 'Authentication Error',
              'data': <String, dynamic>{}
            },
            statusCode: CommonHttpClient.INVALID_TOKEN,
          ),
        );

        when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => failureEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.accountActivation,
          additionalParams: <String, String?>{
            AccountActivationTokenProvider.sessionKey: sessionToken,
          },
        );

        final BiometricTokenRetrievalResult result = await provider.getBiometricToken(config);

        expect(result, isA<BiometricTokenRetrievalFailure>());
        final BiometricTokenRetrievalFailure failure = result as BiometricTokenRetrievalFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.apiError);
        expect(failure.apiError, isA<ErrorUIModel>());

        verify(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });
    });

    group('skipEnableBiometric', () {
      const String sessionToken = 'test-session-token';
      late AccountActivationEntity successEntity;

      setUp(() async {
        final Map<String, dynamic> baseResponse =
            await TestUtil.getResponseMock(getMockAccountActivationFileNameByCase(
          MockAccountActivationUseCase.getSkipEnableBiometricSuccess,
        ));
        successEntity = AccountActivationEntity.fromBaseResponse(BaseResponse(
          response: baseResponse,
          statusCode: CommonHttpClient.SUCCESS,
        ));

        when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => successEntity);
      });

      test('should return success when API call succeeds', () async {
        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.accountActivation,
          additionalParams: <String, String?>{
            AccountActivationTokenProvider.sessionKey: sessionToken,
          },
        );

        final BiometricTokenRetrievalResult result = await provider.skipEnableBiometric(config);

        expect(result, isA<BiometricTokenSkipEnableSuccess>());
        final BiometricTokenSkipEnableSuccess success = result as BiometricTokenSkipEnableSuccess;
        expect(success.baseEntity, successEntity);

        verify(() => mockAuthRepo.activateAccount(
              request: any(
                  named: 'request',
                  that: isA<ActivateAccountEnableBiometricRequest>()
                      .having((ActivateAccountEnableBiometricRequest req) => req.skip, 'skip', true)
                      .having((ActivateAccountEnableBiometricRequest req) => req.sessionToken,
                          'sessionToken', sessionToken)),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });

      test('should return failure when API call fails', () async {
        const String sessionToken = 'test-session-token';

        final AccountActivationEntity failureEntity = AccountActivationEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{},
            statusCode: CommonHttpClient.INVALID_TOKEN,
          ),
        );

        when(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => failureEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.accountActivation,
          additionalParams: <String, String?>{
            AccountActivationTokenProvider.sessionKey: sessionToken,
          },
        );

        final BiometricTokenRetrievalResult result = await provider.skipEnableBiometric(config);

        expect(result, isA<BiometricTokenRetrievalFailure>());
        final BiometricTokenRetrievalFailure failure = result as BiometricTokenRetrievalFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.apiError);
        expect(failure.apiError, isA<ErrorUIModel>());

        verify(() => mockAuthRepo.activateAccount(
              request: any(
                  named: 'request',
                  that: isA<ActivateAccountEnableBiometricRequest>()
                      .having((ActivateAccountEnableBiometricRequest req) => req.skip, 'skip', true)
                      .having((ActivateAccountEnableBiometricRequest req) => req.sessionToken,
                          'sessionToken', sessionToken)),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });

      test('should return failure when flow is unsupported', () async {
        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.profileSettings,
        );

        final BiometricTokenRetrievalResult result = await provider.skipEnableBiometric(config);

        expect(result, isA<BiometricTokenRetrievalFailure>());
        final BiometricTokenRetrievalFailure failure = result as BiometricTokenRetrievalFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.noSupportFlow);
        verifyNever(() => mockAuthRepo.activateAccount(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            ));
      });
    });
  });
}
