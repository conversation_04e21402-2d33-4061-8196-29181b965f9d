// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/login_new_device_request.dart';
import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_result.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_retrieval_result.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider_config.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/impl/new_device_login_token_provider.dart';
import 'package:evoapp/feature/login/mock/mock_login_new_device_file_name_use_case.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  group('NewDeviceLoginTokenProvider', () {
    late NewDeviceLoginTokenProvider provider;
    late MockAuthenticationRepo mockAuthRepo;

    setUpAll(() {
      registerFallbackValue(EnableBiometricRequest(
        skip: true,
        sessionToken: '',
      ));
    });

    setUp(() {
      mockAuthRepo = MockAuthenticationRepo();
      provider = NewDeviceLoginTokenProvider(authRepo: mockAuthRepo);
    });

    test('should return newDeviceLogin flow', () {
      expect(provider.supportedFlow, EnableBiometricAuthenticationFlow.newDeviceLogin);
    });

    group('getBiometricToken', () {
      test('should return failure when flow is not supported', () async {
        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.accountActivation,
        );

        final BiometricTokenRetrievalResult result = await provider.getBiometricToken(config);

        expect(result, isA<BiometricTokenRetrievalFailure>());
        final BiometricTokenRetrievalFailure failure = result as BiometricTokenRetrievalFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.noSupportFlow);
        expect(failure.errorMessage, contains('NewDeviceLoginTokenProvider'));
        expect(failure.errorMessage, contains('accountActivation'));

        // Verify authRepo was not called for unsupported flow
        verifyNever(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            ));
      });

      test('should return success when API call succeeds with biometric token', () async {
        const String sessionToken = 'test-session-token';
        const String biometricToken = 'test-biometric-token';

        final LoginNewDeviceEntity successEntity = LoginNewDeviceEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{
              'data': <String, dynamic>{
                'biometric_token': biometricToken,
                'session_token': sessionToken,
              }
            },
            statusCode: CommonHttpClient.SUCCESS,
          ),
        );

        when(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => successEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
          additionalParams: <String, String?>{
            NewDeviceLoginTokenProvider.sessionKey: sessionToken,
          },
        );

        final BiometricTokenRetrievalResult result = await provider.getBiometricToken(config);

        expect(result, isA<BiometricTokenRetrievalSuccess>());
        final BiometricTokenRetrievalSuccess success = result as BiometricTokenRetrievalSuccess;
        expect(success.biometricToken, biometricToken);
        expect(success.baseEntity, successEntity);

        // Verify correct request was made
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                  named: 'request',
                  that: isA<EnableBiometricRequest>()
                      .having((EnableBiometricRequest req) => req.skip, 'skip', false)
                      .having((EnableBiometricRequest req) => req.sessionToken, 'sessionToken',
                          sessionToken)),
              mockConfig: any(
                  named: 'mockConfig',
                  that: isA<MockConfig>()
                      .having((MockConfig config) => config.enable, 'enable', false)
                      .having(
                          (MockConfig config) => config.fileName,
                          'fileName',
                          getMockLoginNewDeviceFileNameByCase(
                              MockLoginNewDeviceFileNameUseCase.getVerifyBiometricTokenSuccess))),
            )).called(1);
      });

      test('should return success when API call succeeds without session token in config',
          () async {
        const String biometricToken = 'test-biometric-token';

        final LoginNewDeviceEntity successEntity = LoginNewDeviceEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{
              'data': <String, dynamic>{
                'biometric_token': biometricToken,
              }
            },
            statusCode: CommonHttpClient.SUCCESS,
          ),
        );

        when(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => successEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
        );

        final BiometricTokenRetrievalResult result = await provider.getBiometricToken(config);

        expect(result, isA<BiometricTokenRetrievalSuccess>());
        final BiometricTokenRetrievalSuccess success = result as BiometricTokenRetrievalSuccess;
        expect(success.biometricToken, biometricToken);
        expect(success.baseEntity, successEntity);

        // Verify request was made with null session token
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                  named: 'request',
                  that: isA<EnableBiometricRequest>()
                      .having((EnableBiometricRequest req) => req.skip, 'skip', false)
                      .having(
                          (EnableBiometricRequest req) => req.sessionToken, 'sessionToken', null)),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });

      test('should return failure when API call fails', () async {
        const String sessionToken = 'test-session-token';

        final LoginNewDeviceEntity failureEntity = LoginNewDeviceEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{
              'user_message': 'Invalid session token',
              'user_message_title': 'Authentication Error',
              'data': <String, dynamic>{}
            },
            statusCode: CommonHttpClient.INVALID_TOKEN,
          ),
        );

        when(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => failureEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
          additionalParams: <String, String?>{
            NewDeviceLoginTokenProvider.sessionKey: sessionToken,
          },
        );

        final BiometricTokenRetrievalResult result = await provider.getBiometricToken(config);

        expect(result, isA<BiometricTokenRetrievalFailure>());
        final BiometricTokenRetrievalFailure failure = result as BiometricTokenRetrievalFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.apiError);
        expect(failure.apiError, isNotNull);

        // Verify request was made with correct parameters
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                  named: 'request',
                  that: isA<EnableBiometricRequest>()
                      .having((EnableBiometricRequest req) => req.skip, 'skip', false)
                      .having((EnableBiometricRequest req) => req.sessionToken, 'sessionToken',
                          sessionToken)),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });
    });

    group('skipEnableBiometric', () {
      test('should return failure when flow is not supported', () async {
        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.profileSettings,
        );

        final BiometricTokenRetrievalResult result = await provider.skipEnableBiometric(config);

        expect(result, isA<BiometricTokenRetrievalFailure>());
        final BiometricTokenRetrievalFailure failure = result as BiometricTokenRetrievalFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.noSupportFlow);
        expect(failure.errorMessage, contains('NewDeviceLoginTokenProvider'));
        expect(failure.errorMessage, contains('profileSettings'));

        // Verify authRepo was not called for unsupported flow
        verifyNever(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            ));
      });

      test('should return success when API call succeeds', () async {
        const String sessionToken = 'test-session-token';

        final LoginNewDeviceEntity successEntity = LoginNewDeviceEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{
              'data': <String, dynamic>{
                'session_token': sessionToken,
              }
            },
            statusCode: CommonHttpClient.SUCCESS,
          ),
        );

        when(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => successEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
          additionalParams: <String, String?>{
            NewDeviceLoginTokenProvider.sessionKey: sessionToken,
          },
        );

        final BiometricTokenRetrievalResult result = await provider.skipEnableBiometric(config);

        expect(result, isA<BiometricTokenSkipEnableSuccess>());
        final BiometricTokenSkipEnableSuccess success = result as BiometricTokenSkipEnableSuccess;
        expect(success.baseEntity, successEntity);

        // Verify correct request was made with skip = true
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                  named: 'request',
                  that: isA<EnableBiometricRequest>()
                      .having((EnableBiometricRequest req) => req.skip, 'skip', true)
                      .having((EnableBiometricRequest req) => req.sessionToken, 'sessionToken',
                          sessionToken)),
              mockConfig: any(
                  named: 'mockConfig',
                  that: isA<MockConfig>()
                      .having((MockConfig config) => config.enable, 'enable', false)
                      .having(
                          (MockConfig config) => config.fileName,
                          'fileName',
                          getMockLoginNewDeviceFileNameByCase(
                              MockLoginNewDeviceFileNameUseCase.getVerifyBiometricTokenSuccess))),
            )).called(1);
      });

      test('should return success when API call succeeds with no additional params', () async {
        final LoginNewDeviceEntity successEntity = LoginNewDeviceEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{'data': <String, dynamic>{}},
            statusCode: CommonHttpClient.SUCCESS,
          ),
        );

        when(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => successEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
        );

        final BiometricTokenRetrievalResult result = await provider.skipEnableBiometric(config);

        expect(result, isA<BiometricTokenSkipEnableSuccess>());
        final BiometricTokenSkipEnableSuccess success = result as BiometricTokenSkipEnableSuccess;
        expect(success.baseEntity, successEntity);

        // Verify request was made with null session token
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                  named: 'request',
                  that: isA<EnableBiometricRequest>()
                      .having((EnableBiometricRequest req) => req.skip, 'skip', true)
                      .having(
                          (EnableBiometricRequest req) => req.sessionToken, 'sessionToken', null)),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });

      test('should return failure when API call fails', () async {
        const String sessionToken = 'test-session-token';

        final LoginNewDeviceEntity failureEntity = LoginNewDeviceEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{
              'user_message': 'Session expired',
              'user_message_title': 'Error',
              'data': <String, dynamic>{}
            },
            statusCode: CommonHttpClient.INVALID_TOKEN,
          ),
        );

        when(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => failureEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
          additionalParams: <String, String?>{
            NewDeviceLoginTokenProvider.sessionKey: sessionToken,
          },
        );

        final BiometricTokenRetrievalResult result = await provider.skipEnableBiometric(config);

        expect(result, isA<BiometricTokenRetrievalFailure>());
        final BiometricTokenRetrievalFailure failure = result as BiometricTokenRetrievalFailure;
        expect(failure.errorType, BiometricAuthenticationFailureType.apiError);
        expect(failure.apiError, isNotNull);

        // Verify request was made with correct parameters
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                  named: 'request',
                  that: isA<EnableBiometricRequest>()
                      .having((EnableBiometricRequest req) => req.skip, 'skip', true)
                      .having((EnableBiometricRequest req) => req.sessionToken, 'sessionToken',
                          sessionToken)),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });
    });

    group('session token extraction', () {
      test('should extract session token from additionalParams', () async {
        const String sessionToken = 'extracted-session-token';

        final LoginNewDeviceEntity successEntity = LoginNewDeviceEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{
              'data': <String, dynamic>{'biometric_token': 'token'}
            },
            statusCode: CommonHttpClient.SUCCESS,
          ),
        );

        when(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => successEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
          additionalParams: <String, String?>{
            'other_param': 'other_value',
            NewDeviceLoginTokenProvider.sessionKey: sessionToken,
            'another_param': 'another_value',
          },
        );

        await provider.getBiometricToken(config);

        // Verify the correct session token was extracted and used
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                  named: 'request',
                  that: isA<EnableBiometricRequest>().having(
                      (EnableBiometricRequest req) => req.sessionToken,
                      'sessionToken',
                      sessionToken)),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });

      test('should handle null additionalParams', () async {
        final LoginNewDeviceEntity successEntity = LoginNewDeviceEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{
              'data': <String, dynamic>{'biometric_token': 'token'}
            },
            statusCode: CommonHttpClient.SUCCESS,
          ),
        );

        when(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => successEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
          additionalParams: null,
        );

        await provider.getBiometricToken(config);

        // Verify null session token was used
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                  named: 'request',
                  that: isA<EnableBiometricRequest>().having(
                      (EnableBiometricRequest req) => req.sessionToken, 'sessionToken', null)),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });

      test('should handle additionalParams without session_token key', () async {
        final LoginNewDeviceEntity successEntity = LoginNewDeviceEntity.fromBaseResponse(
          BaseResponse(
            response: <String, dynamic>{
              'data': <String, dynamic>{'biometric_token': 'token'}
            },
            statusCode: CommonHttpClient.SUCCESS,
          ),
        );

        when(() => mockAuthRepo.loginNewDevice(
              request: any(named: 'request'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => successEntity);

        final BiometricTokenProviderConfig config = BiometricTokenProviderConfig(
          flow: EnableBiometricAuthenticationFlow.newDeviceLogin,
          additionalParams: <String, String?>{
            'other_param': 'other_value',
          },
        );

        await provider.getBiometricToken(config);

        // Verify null session token was used when key is not found
        verify(() => mockAuthRepo.loginNewDevice(
              request: any(
                  named: 'request',
                  that: isA<EnableBiometricRequest>().having(
                      (EnableBiometricRequest req) => req.sessionToken, 'sessionToken', null)),
              mockConfig: any(named: 'mockConfig'),
            )).called(1);
      });
    });

    group('sessionKey constant', () {
      test('should have correct session key constant', () {
        expect(NewDeviceLoginTokenProvider.sessionKey, 'session_token');
      });
    });
  });
}
