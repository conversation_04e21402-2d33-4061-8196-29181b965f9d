import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_retrieval_result.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider_config.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_test/flutter_test.dart';

class TestBiometricTokenProvider extends BiometricTokenProvider {
  final EnableBiometricAuthenticationFlow _supportedFlow;

  TestBiometricTokenProvider(this._supportedFlow);

  @override
  EnableBiometricAuthenticationFlow get supportedFlow => _supportedFlow;

  @override
  Future<BiometricTokenRetrievalResult> getBiometricToken(
      BiometricTokenProviderConfig config) async {
    return BiometricTokenRetrievalSuccess(
      biometricToken: 'test_token',
      baseEntity: null,
    );
  }

  @override
  Future<BiometricTokenRetrievalResult> skipEnableBiometric(
      BiometricTokenProviderConfig config) async {
    return BiometricTokenSkipEnableSuccess(baseEntity: BaseEntity());
  }
}

void main() {
  group('BiometricTokenProviderFactory Functions', () {
    late BiometricTokenProviderFactory factory;

    setUp(() {
      factory = BiometricTokenProviderFactory();
    });

    tearDown(() {
      factory.dispose();
    });

    group('registerProvider function', () {
      test('should add provider to internal list', () {
        // Given
        final TestBiometricTokenProvider provider =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);

        // When
        factory.registerProvider(provider);

        // Then
        expect(factory.getAllProviders(), hasLength(1));
        expect(factory.getAllProviders().first, equals(provider));
      });

      test('should add multiple providers to internal list', () {
        // Given
        final TestBiometricTokenProvider provider1 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        final TestBiometricTokenProvider provider2 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.profileSettings);

        // When
        factory.registerProvider(provider1);
        factory.registerProvider(provider2);

        // Then
        expect(factory.getAllProviders(), hasLength(2));
        expect(factory.getAllProviders(), contains(provider1));
        expect(factory.getAllProviders(), contains(provider2));
      });

      test('should replace existing provider when registering same flow type', () {
        // Given
        final TestBiometricTokenProvider provider1 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        final TestBiometricTokenProvider provider2 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);

        // When
        factory.registerProvider(provider1);
        factory.registerProvider(provider2);

        // Then
        expect(factory.getAllProviders(), hasLength(1));
        expect(factory.getAllProviders().first, equals(provider2));
        expect(factory.getProvider(EnableBiometricAuthenticationFlow.accountActivation),
            equals(provider2));
      });

      test('should enforce one provider per flow type constraint', () {
        // Given
        final TestBiometricTokenProvider accountProvider1 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        final TestBiometricTokenProvider accountProvider2 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        final TestBiometricTokenProvider profileProvider =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.profileSettings);

        // When
        factory.registerProvider(accountProvider1);
        factory.registerProvider(profileProvider);
        expect(factory.getAllProviders(), hasLength(2));

        factory.registerProvider(accountProvider2); // Should replace accountProvider1

        // Then
        expect(factory.getAllProviders(), hasLength(2)); // Still only 2 providers
        expect(factory.getProvider(EnableBiometricAuthenticationFlow.accountActivation),
            equals(accountProvider2));
        expect(factory.getProvider(EnableBiometricAuthenticationFlow.profileSettings),
            equals(profileProvider));
      });
    });

    group('unregisterProvider function', () {
      test('should remove provider from internal list', () {
        // Given
        final TestBiometricTokenProvider provider =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        factory.registerProvider(provider);
        expect(factory.getAllProviders(), hasLength(1));

        // When
        factory.unregisterProvider(provider);

        // Then
        expect(factory.getAllProviders(), hasLength(0));
      });

      test('should do nothing when removing non-existent provider', () {
        // Given
        final TestBiometricTokenProvider provider1 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        final TestBiometricTokenProvider provider2 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.profileSettings);
        factory.registerProvider(provider1);

        // When
        factory.unregisterProvider(provider2);

        // Then
        expect(factory.getAllProviders(), hasLength(1));
        expect(factory.getAllProviders().first, equals(provider1));
      });

      test('should handle removing from empty list', () {
        // Given
        final TestBiometricTokenProvider provider =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        expect(factory.getAllProviders(), hasLength(0));

        // When
        factory.unregisterProvider(provider);

        // Then
        expect(factory.getAllProviders(), hasLength(0));
      });
    });

    group('getProvider function', () {
      test('should return provider for matching flow', () {
        // Given
        final TestBiometricTokenProvider provider =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        factory.registerProvider(provider);

        // When
        final BiometricTokenProvider? result =
            factory.getProvider(EnableBiometricAuthenticationFlow.accountActivation);

        // Then
        expect(result, isNotNull);
        expect(result, equals(provider));
        expect(result!.supportedFlow, equals(EnableBiometricAuthenticationFlow.accountActivation));
      });

      test('should return null for non-matching flow', () {
        // Given
        final TestBiometricTokenProvider provider =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        factory.registerProvider(provider);

        // When
        final BiometricTokenProvider? result =
            factory.getProvider(EnableBiometricAuthenticationFlow.profileSettings);

        // Then
        expect(result, isNull);
      });

      test('should return null when no providers registered', () {
        // When
        final BiometricTokenProvider? result =
            factory.getProvider(EnableBiometricAuthenticationFlow.accountActivation);

        // Then
        expect(result, isNull);
      });

      test('should return correct provider for each flow type', () {
        // Given
        final TestBiometricTokenProvider accountProvider =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        final TestBiometricTokenProvider profileProvider =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.profileSettings);
        factory.registerProvider(accountProvider);
        factory.registerProvider(profileProvider);

        // When & Then
        final BiometricTokenProvider? accountResult =
            factory.getProvider(EnableBiometricAuthenticationFlow.accountActivation);
        expect(accountResult, equals(accountProvider));

        final BiometricTokenProvider? profileResult =
            factory.getProvider(EnableBiometricAuthenticationFlow.profileSettings);
        expect(profileResult, equals(profileProvider));
      });
    });

    group('getAllProviders function', () {
      test('should return empty list when no providers registered', () {
        // When
        final List<BiometricTokenProvider> result = factory.getAllProviders();

        // Then
        expect(result, isEmpty);
        expect(result, isA<List<BiometricTokenProvider>>());
      });

      test('should return all registered providers', () {
        // Given
        final TestBiometricTokenProvider provider1 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        final TestBiometricTokenProvider provider2 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.profileSettings);
        factory.registerProvider(provider1);
        factory.registerProvider(provider2);

        // When
        final List<BiometricTokenProvider> result = factory.getAllProviders();

        // Then
        expect(result, hasLength(2));
        expect(result, contains(provider1));
        expect(result, contains(provider2));
      });

      test('should return unmodifiable list', () {
        // Given
        final TestBiometricTokenProvider provider =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        factory.registerProvider(provider);

        // When
        final List<BiometricTokenProvider> result = factory.getAllProviders();

        // Then
        expect(() => result.add(provider), throwsUnsupportedError);
        expect(() => result.remove(provider), throwsUnsupportedError);
        expect(() => result.clear(), throwsUnsupportedError);
      });

      test('should return snapshot of current providers', () {
        // Given
        final TestBiometricTokenProvider provider1 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        final TestBiometricTokenProvider provider2 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.profileSettings);
        factory.registerProvider(provider1);

        // When
        final List<BiometricTokenProvider> result1 = factory.getAllProviders();
        factory.registerProvider(provider2);
        final List<BiometricTokenProvider> result2 = factory.getAllProviders();

        // Then
        expect(result1, hasLength(1));
        expect(result2, hasLength(2));
        expect(result1, contains(provider1));
        expect(result2, contains(provider1));
        expect(result2, contains(provider2));
      });
    });

    group('dispose function', () {
      test('should clear all providers', () {
        // Given
        final TestBiometricTokenProvider provider1 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        final TestBiometricTokenProvider provider2 =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.profileSettings);
        factory.registerProvider(provider1);
        factory.registerProvider(provider2);
        expect(factory.getAllProviders(), hasLength(2));

        // When
        factory.dispose();

        // Then
        expect(factory.getAllProviders(), isEmpty);
      });

      test('should handle dispose on empty factory', () {
        // Given
        expect(factory.getAllProviders(), isEmpty);

        // When
        factory.dispose();

        // Then
        expect(factory.getAllProviders(), isEmpty);
      });

      test('should allow re-registration after dispose', () {
        // Given
        final TestBiometricTokenProvider provider =
            TestBiometricTokenProvider(EnableBiometricAuthenticationFlow.accountActivation);
        factory.registerProvider(provider);
        factory.dispose();
        expect(factory.getAllProviders(), isEmpty);

        // When
        factory.registerProvider(provider);

        // Then
        expect(factory.getAllProviders(), hasLength(1));
        expect(factory.getProvider(EnableBiometricAuthenticationFlow.accountActivation), isNotNull);
      });
    });
  });
}
