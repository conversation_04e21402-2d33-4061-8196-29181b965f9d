// bio_auth_result_test.dart
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter_common_package/resources/ui_strings.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('BioAuthResult', () {
    test('success factory constructor should create a successful result', () {
      final BioAuthResult result = BioAuthResult.success();

      expect(result.isAuthSuccess, isTrue);
      expect(result.error, isNull);
    });

    test('error factory constructor should create an error result', () {
      const BioAuthError error = BioAuthError.notEnrolled;

      final BioAuthResult result = BioAuthResult.error(error);

      expect(result.isAuthSuccess, isFalse);
      expect(result.error, error);
    });

    test('error factory constructor should handle null error', () {
      final BioAuthResult result = BioAuthResult.error(null);

      expect(result.isAuthSuccess, isFalse);
      expect(result.error, isNull);
    });
  });

  group('BioAuthError', () {
    group('getErrMsg', () {
      test('should return lockedOutError for notEnrolled', () {
        final String result = BioAuthError.getErrMsg(BioAuthError.notEnrolled);

        expect(result, EvoStrings.lockedOutError);
      });

      test('should return lockedOutError for androidLockedOut', () {
        final String result = BioAuthError.getErrMsg(BioAuthError.androidLockedOut);

        expect(result, EvoStrings.lockedOutError);
      });

      test('should return permanentlyLockedOutError for permanentlyLockedOut', () {
        final String result = BioAuthError.getErrMsg(BioAuthError.permanentlyLockedOut);

        expect(result, EvoStrings.permanentlyLockedOutError);
      });

      test('should return otherGenericErrorMessage for unknown', () {
        final String result = BioAuthError.getErrMsg(BioAuthError.unknown);

        expect(result, CommonStrings.otherGenericErrorMessage);
      });

      test('should return otherGenericErrorMessage for null', () {
        final String result = BioAuthError.getErrMsg(null);

        expect(result, CommonStrings.otherGenericErrorMessage);
      });
    });

    group('isBiometricLocked', () {
      test('should return true for androidLockedOut', () {
        final bool result = BioAuthError.androidLockedOut.isBiometricLocked();

        expect(result, isTrue);
      });

      test('should return true for permanentlyLockedOut', () {
        final bool result = BioAuthError.permanentlyLockedOut.isBiometricLocked();

        expect(result, isTrue);
      });

      test('should return false for userDismiss', () {
        final bool result = BioAuthError.userDismiss.isBiometricLocked();

        expect(result, isFalse);
      });

      test('should return false for notEnrolled', () {
        final bool result = BioAuthError.notEnrolled.isBiometricLocked();

        expect(result, isFalse);
      });

      test('should return false for unknown', () {
        final bool result = BioAuthError.unknown.isBiometricLocked();

        expect(result, isFalse);
      });
    });
  });
}
