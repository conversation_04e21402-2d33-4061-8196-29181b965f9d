import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_service.dart';
import 'package:evoapp/feature/biometric/model/biometric_status_change_notifier.dart';
import 'package:evoapp/feature/biometric/utils/biometric_status_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometric_status_helper_impl.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockBiometricsAuthenticationService extends Mock implements BiometricAuthenticationService {}

void main() {
  late MockBiometricsAuthenticationService mockBiometricsTokenModule;
  late AppState? appState;

  setUpAll(() {
    appState = AppState();
    getIt.registerLazySingleton<AppState>(() => appState!);

    mockBiometricsTokenModule = MockBiometricsAuthenticationService();
    getIt.registerLazySingleton<BiometricAuthenticationService>(() => mockBiometricsTokenModule);
  });

  tearDownAll(() async {
    await getIt.reset();
    appState = null;
  });

  test('test biometric notSetUp case', () async {
    final BiometricStatusHelper biometricStatusHelper = BiometricStatusHelperImpl();

    when(() => mockBiometricsTokenModule.isEnableBiometricAuthenticator()).thenAnswer((_) async {
      return false;
    });

    await biometricStatusHelper.updateBiometricStatus();

    expect(appState?.biometricStatusChangeNotifier.value, BiometricStatus.notSetup);
  });

  test('test biometric deviceSettingChanged case', () async {
    final BiometricStatusHelper biometricStatusHelper = BiometricStatusHelperImpl();

    when(() => mockBiometricsTokenModule.isEnableBiometricAuthenticator()).thenAnswer((_) async {
      return true;
    });

    when(() => mockBiometricsTokenModule.hasEnrolledBiometrics()).thenAnswer((_) async {
      return true;
    });

    when(() => mockBiometricsTokenModule.getBiometricChanged()).thenAnswer((_) async {
      return true;
    });

    when(() => mockBiometricsTokenModule.disableBiometricAuthenticationFeature())
        .thenAnswer((_) async {});

    await biometricStatusHelper.updateBiometricStatus();

    expect(appState?.biometricStatusChangeNotifier.value, BiometricStatus.deviceSettingChanged);
  });

  test('test biometric biometricTokenUnusable case', () async {
    final BiometricStatusHelper biometricStatusHelper = BiometricStatusHelperImpl();

    when(() => mockBiometricsTokenModule.isEnableBiometricAuthenticator()).thenAnswer((_) async {
      return true;
    });

    when(() => mockBiometricsTokenModule.hasEnrolledBiometrics()).thenAnswer((_) async {
      return true;
    });

    when(() => mockBiometricsTokenModule.getBiometricChanged()).thenAnswer((_) async {
      return false;
    });

    when(() => mockBiometricsTokenModule.isBiometricTokenUsable()).thenAnswer((_) async {
      return false;
    });

    when(() => mockBiometricsTokenModule.disableBiometricAuthenticationFeature())
        .thenAnswer((_) async {});

    await biometricStatusHelper.updateBiometricStatus();

    expect(appState?.biometricStatusChangeNotifier.value, BiometricStatus.biometricTokenUnusable);
  });
  test('test biometric usable case', () async {
    final BiometricStatusHelper biometricStatusHelper = BiometricStatusHelperImpl();

    when(() => mockBiometricsTokenModule.isEnableBiometricAuthenticator()).thenAnswer((_) async {
      return true;
    });

    when(() => mockBiometricsTokenModule.hasEnrolledBiometrics()).thenAnswer((_) async {
      return true;
    });

    when(() => mockBiometricsTokenModule.getBiometricChanged()).thenAnswer((_) async {
      return false;
    });

    when(() => mockBiometricsTokenModule.isBiometricTokenUsable()).thenAnswer((_) async {
      return true;
    });

    await biometricStatusHelper.updateBiometricStatus();

    expect(appState?.biometricStatusChangeNotifier.value, BiometricStatus.usable);
  });
}
