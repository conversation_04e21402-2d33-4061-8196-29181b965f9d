import 'package:evoapp/feature/biometric/utils/biometric_type_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mocktail/mocktail.dart';

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

class MockIosDeviceInfo extends Mock implements IosDeviceInfo {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

class MockIosUtsname extends Mock implements IosUtsname {}

void main() {
  group('BiometricTypeHelper', () {
    late BiometricsAuthenticate bioAuth;
    late DeviceInfoPlugin deviceInfo;
    late DevicePlatform platform;
    late BiometricTypeHelper helper;

    setUpAll(() {
      bioAuth = MockBiometricsAuthenticate();
      deviceInfo = MockDeviceInfoPlugin();
      platform = MockDevicePlatform();
      helper = BiometricTypeHelper(bioAuth, deviceInfo, platform);
    });

    group('on iOS', () {
      setUpAll(() {
        when(() => platform.isIOS()).thenReturn(true);
        when(() => platform.isAndroid()).thenReturn(false);
      });

      test('should return face biometric type when face is available', () async {
        when(() => bioAuth.getAvailableBiometricType())
            .thenAnswer((_) async => <BiometricType>[BiometricType.face]);

        final TsBiometricType result = await helper.getTsBiometricType();

        expect(result, TsBiometricType.face);
      });

      test('should return finger biometric type when finger is available', () async {
        when(() => bioAuth.getAvailableBiometricType())
            .thenAnswer((_) async => <BiometricType>[BiometricType.fingerprint]);

        final TsBiometricType result = await helper.getTsBiometricType();

        expect(result, TsBiometricType.finger);
      });

      test('should return finger biometric type when device supports fingerprint', () async {
        when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async => <BiometricType>[]);
        final MockIosDeviceInfo iosInfo = MockIosDeviceInfo();
        final MockIosUtsname utsname = MockIosUtsname();
        when(() => deviceInfo.iosInfo).thenAnswer((_) async => iosInfo);
        when(() => iosInfo.utsname).thenReturn(utsname);
        when(() => utsname.machine).thenReturn('iPhone6,1');

        final TsBiometricType result = await helper.getTsBiometricType();

        expect(result, TsBiometricType.finger);
      });

      test('should return face biometric type when device supports face', () async {
        when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async => <BiometricType>[]);
        final MockIosDeviceInfo iosInfo = MockIosDeviceInfo();
        final MockIosUtsname utsname = MockIosUtsname();
        when(() => deviceInfo.iosInfo).thenAnswer((_) async => iosInfo);
        when(() => iosInfo.utsname).thenReturn(utsname);
        when(() => utsname.machine).thenReturn('others');

        final TsBiometricType result = await helper.getTsBiometricType();

        expect(result, TsBiometricType.face);
      });
    });

    group('on Android', () {
      setUpAll(() {
        when(() => platform.isIOS()).thenReturn(false);
        when(() => platform.isAndroid()).thenReturn(true);
      });

      test('should return androidBio biometric type', () async {
        when(() => bioAuth.getAvailableBiometricType())
            .thenAnswer((_) async => <BiometricType>[BiometricType.strong]);

        final TsBiometricType result = await helper.getTsBiometricType();

        expect(result, TsBiometricType.androidBio);
      });
    });

    group('on unknown platform', () {
      setUpAll(() {
        when(() => platform.isIOS()).thenReturn(false);
        when(() => platform.isAndroid()).thenReturn(false);
      });

      test('should return unknown biometric type', () async {
        when(() => bioAuth.getAvailableBiometricType()).thenAnswer((_) async => <BiometricType>[]);

        final TsBiometricType result = await helper.getTsBiometricType();

        expect(result, TsBiometricType.unknown);
      });
    });
  });
}
