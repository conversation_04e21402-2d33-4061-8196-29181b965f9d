import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometric_auth_result_handler.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:local_auth/error_codes.dart' as auth_error;
import 'package:mocktail/mocktail.dart';

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

void main() {
  late BiometricAuthResultHandler handler;
  late MockEvoLocalStorageHelper mockSecureStorageHelper;
  late MockEvoFlutterWrapper mockEvoFlutterWrapper;

  setUp(() {
    mockSecureStorageHelper = MockEvoLocalStorageHelper();
    mockEvoFlutterWrapper = MockEvoFlutterWrapper();
    handler = BiometricAuthResultHandler(
      secureStorageHelper: mockSecureStorageHelper,
      evoFlutterWrapper: mockEvoFlutterWrapper,
    );
  });

  group('getBioAuthResult', () {
    group('Platform Detection', () {
      test('should return unknown error when neither iOS nor Android', () async {
        when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
        when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);

        final BioAuthResult result = await handler.getBioAuthResult(
          authenticated: false,
          errCode: null,
          previouslyHasEnrolled: false,
          currentlyHasEnrolled: false,
        );

        expect(result.isAuthSuccess, isFalse);
        expect(result.error, BioAuthError.unknown);
      });

      test('should call iOS flow when running on iOS', () async {
        when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);
        when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);
        when(() => mockSecureStorageHelper.didAllowBiometricPermission())
            .thenAnswer((_) async => true);

        final BioAuthResult result = await handler.getBioAuthResult(
          authenticated: true,
          errCode: null,
          previouslyHasEnrolled: true,
          currentlyHasEnrolled: true,
        );

        expect(result.isAuthSuccess, isTrue);
        expect(result.error, isNull);
      });

      test('should call Android flow when running on Android', () async {
        when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
        when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

        final BioAuthResult result = await handler.getBioAuthResult(
          authenticated: true,
          errCode: null,
          previouslyHasEnrolled: true,
          currentlyHasEnrolled: true,
        );

        expect(result.isAuthSuccess, isTrue);
        expect(result.error, isNull);
      });
    });

    group('iOS Flow', () {
      setUp(() {
        when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(true);
        when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);
      });

      group('Authentication Success', () {
        test('should return success and set permission when authenticated', () async {
          when(() => mockSecureStorageHelper.didAllowBiometricPermission())
              .thenAnswer((_) async => false);
          when(() => mockSecureStorageHelper.setDidAllowBiometricPermission())
              .thenAnswer((_) async {});

          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: true,
            errCode: null,
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isTrue);
          expect(result.error, isNull);
          verify(() => mockSecureStorageHelper.setDidAllowBiometricPermission()).called(1);
        });

        test('should return success without setting permission when already allowed', () async {
          when(() => mockSecureStorageHelper.didAllowBiometricPermission())
              .thenAnswer((_) async => true);

          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: true,
            errCode: null,
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isTrue);
          expect(result.error, isNull);
          verifyNever(() => mockSecureStorageHelper.setDidAllowBiometricPermission());
        });
      });

      group('Not Enrolled Error', () {
        test('should return notEnrolled error when errCode is notEnrolled', () async {
          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: auth_error.notEnrolled,
            previouslyHasEnrolled: false,
            currentlyHasEnrolled: false,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.notEnrolled);
        });
      });

      group('Permanently Locked Out', () {
        test('should return permanentlyLockedOut when errCode is null and not authenticated',
            () async {
          when(() => mockSecureStorageHelper.didAllowBiometricPermission())
              .thenAnswer((_) async => true);

          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: null,
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.permanentlyLockedOut);
        });
      });

      group('App Biometric Permission Scenarios', () {
        test(
            'should return iosUserDenied when currentlyHasEnrolled is false and no system permission',
            () async {
          when(() => mockSecureStorageHelper.didAllowBiometricPermission())
              .thenAnswer((_) async => false);

          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: 'some_error',
            previouslyHasEnrolled: false,
            currentlyHasEnrolled: false,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.iosUserDenied);
        });

        test(
            'should return permanentlyLockedOut when previously enrolled but currently not and has system permission',
            () async {
          when(() => mockSecureStorageHelper.didAllowBiometricPermission())
              .thenAnswer((_) async => true);

          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: 'some_error',
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: false,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.permanentlyLockedOut);
        });

        test('should return notEnrolled when not previously enrolled and currently not enrolled',
            () async {
          when(() => mockSecureStorageHelper.didAllowBiometricPermission())
              .thenAnswer((_) async => true);

          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: 'some_error',
            previouslyHasEnrolled: false,
            currentlyHasEnrolled: false,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.notEnrolled);
        });
      });

      group('User Dismiss', () {
        test('should return userDismiss when errCode is notAvailable', () async {
          when(() => mockSecureStorageHelper.didAllowBiometricPermission())
              .thenAnswer((_) async => false);
          when(() => mockSecureStorageHelper.setDidAllowBiometricPermission())
              .thenAnswer((_) async {});

          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: auth_error.notAvailable,
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.userDismiss);
          verify(() => mockSecureStorageHelper.setDidAllowBiometricPermission()).called(1);
        });
      });

      group('Unknown Error', () {
        test('should return unknown error for unhandled error codes', () async {
          when(() => mockSecureStorageHelper.didAllowBiometricPermission())
              .thenAnswer((_) async => true);

          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: 'unknown_error_code',
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.unknown);
        });
      });
    });

    group('Android Flow', () {
      setUp(() {
        when(() => mockEvoFlutterWrapper.isIOS()).thenReturn(false);
        when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);
      });

      group('Authentication Success', () {
        test('should return success when authenticated', () async {
          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: true,
            errCode: null,
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isTrue);
          expect(result.error, isNull);
        });

        test('should return success even with error code when authenticated', () async {
          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: true,
            errCode: 'some_error',
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isTrue);
          expect(result.error, isNull);
        });
      });

      group('User Dismiss', () {
        test('should return userDismiss when errCode is null and not authenticated', () async {
          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: null,
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.userDismiss);
        });
      });

      group('Permanently Locked Out', () {
        test('should return permanentlyLockedOut when errCode is permanentlyLockedOut', () async {
          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: auth_error.permanentlyLockedOut,
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.permanentlyLockedOut);
        });
      });

      group('Android Locked Out', () {
        test('should return androidLockedOut when errCode is lockedOut', () async {
          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: auth_error.lockedOut,
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.androidLockedOut);
        });
      });

      group('Not Enrolled', () {
        test('should return notEnrolled when errCode is notEnrolled', () async {
          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: auth_error.notEnrolled,
            previouslyHasEnrolled: false,
            currentlyHasEnrolled: false,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.notEnrolled);
        });

        test('should return notEnrolled when currentlyHasEnrolled is false regardless of errCode',
            () async {
          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: 'some_other_error',
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: false,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.notEnrolled);
        });
      });

      group('Unknown Error', () {
        test('should return unknown error for unhandled error codes', () async {
          final BioAuthResult result = await handler.getBioAuthResult(
            authenticated: false,
            errCode: 'unknown_error_code',
            previouslyHasEnrolled: true,
            currentlyHasEnrolled: true,
          );

          expect(result.isAuthSuccess, isFalse);
          expect(result.error, BioAuthError.unknown);
        });
      });
    });
  });

  group('setDidAllowedSystemPermissionIfNeeded', () {
    test('should set permission when not previously allowed', () async {
      when(() => mockSecureStorageHelper.didAllowBiometricPermission())
          .thenAnswer((_) async => false);
      when(() => mockSecureStorageHelper.setDidAllowBiometricPermission()).thenAnswer((_) async {});

      await handler.setDidAllowedSystemPermissionIfNeeded();

      verify(() => mockSecureStorageHelper.didAllowBiometricPermission()).called(1);
      verify(() => mockSecureStorageHelper.setDidAllowBiometricPermission()).called(1);
    });

    test('should not set permission when already allowed', () async {
      when(() => mockSecureStorageHelper.didAllowBiometricPermission())
          .thenAnswer((_) async => true);

      await handler.setDidAllowedSystemPermissionIfNeeded();

      verify(() => mockSecureStorageHelper.didAllowBiometricPermission()).called(1);
      verifyNever(() => mockSecureStorageHelper.setDidAllowBiometricPermission());
    });
  });
}
