import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/base/evo_page_state_base.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_cubit.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_page.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_state.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_success_screen.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider_config.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometric_functions.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/widget/no_app_bar_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class MockCb extends Mock {
  void call(ChallengeSuccessModel model);
}

class MockActiveBiometricCubit extends MockCubit<ActivateBiometricState>
    implements ActivateBiometricCubit {}

class MockBiometricFunctions extends Mock implements BiometricFunctions {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockHandleEvoApiError extends Mock {
  void call(ErrorUIModel? errorUIModel);
}

class MockActiveBiometricScreen extends ActivateBiometricScreen {
  final MockHandleEvoApiError mockErrorCb;

  const MockActiveBiometricScreen({
    required this.mockErrorCb,
    required super.onSuccess,
    required super.sessionToken,
    required super.flow,
    super.key,
  });

  @override
  EvoPageStateBase<ActivateBiometricScreen> createState() => MockActiveBiometricScreenState();
}

class MockActiveBiometricScreenState extends ActivateBiometricScreenState {
  @override
  Future<void> handleEvoApiError(ErrorUIModel? errorUIModel) async {
    (widget as MockActiveBiometricScreen).mockErrorCb(errorUIModel);
  }
}

void main() {
  late ActivateBiometricCubit mockCubit;
  late MockCb mockCb;
  late CommonImageProvider mockImageProvider;
  late BiometricFunctions mockBioFn;
  late EvoSnackBar mockEvoSnackBar;
  late EvoUtilFunction mockEvoUtilFunction;
  const String mockSessionToken = 'test_session_token';
  late MockHandleEvoApiError mockApiErrorCb;
  final EnableBiometricAuthenticationFlow mockFlow =
      EnableBiometricAuthenticationFlow.accountActivation;

  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();

    mockImageProvider = getIt.get<CommonImageProvider>();
    when(() => mockImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
        )).thenReturn(SizedBox());

    getIt.registerSingleton<BiometricFunctions>(MockBiometricFunctions());
    mockBioFn = getIt.get<BiometricFunctions>();

    getIt.registerSingleton<EvoSnackBar>(MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();

    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();
    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});

    registerFallbackValue(SnackBarType.success);
    registerFallbackValue(ChallengeSuccessModel(entity: BaseEntity()));
  });

  setUp(() {
    mockCubit = MockActiveBiometricCubit();
    when(() => mockCubit.state).thenReturn(ActivateBiometricInitState());
    mockCb = MockCb();
    mockApiErrorCb = MockHandleEvoApiError();

    when(() => mockBioFn.handleBioError(
        bioError: any(named: 'bioError'),
        onActionWhenDismiss: any(
          named: 'onActionWhenDismiss',
        ))).thenAnswer((_) async {});

    when(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInMilliSec: any(named: 'durationInMilliSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).thenAnswer((_) async {
      return null;
    });

    when(() => mockCubit.enableBiometricAuthentication(any())).thenAnswer((_) async {});
    when(() => mockCubit.skipActivateBiometric(any())).thenAnswer((_) async {});
    when(() => mockDialogFunction.showDialogSessionTokenExpired(
          type: any(named: 'type'),
          onClickPositive: any(named: 'onClickPositive'),
        )).thenAnswer((_) async {});
  });

  test('ActiveBiometricScreenArg should hold correct data', () {
    final MockCb cb = MockCb();
    final ActivateBiometricScreenArg arg = ActivateBiometricScreenArg(
      onSuccess: cb.call,
      flow: EnableBiometricAuthenticationFlow.accountActivation,
    );

    expect(arg.onSuccess, cb.call);
  });

  test('ActiveBiometricScreen should have correct routeSettings', () {
    final ActivateBiometricScreen screen = ActivateBiometricScreen(
      onSuccess: (_) {},
      flow: mockFlow,
    );

    expect(screen.routeSettings.name, Screen.activateBiometricScreen.routeName);
  });

  test('ActiveBiometricScreen should navigate correctly on pushNamed', () async {
    await ActivateBiometricScreen.pushNamed(onSuccess: mockCb.call);

    verify(() => mockNavigatorContext.pushNamed(
          Screen.activateBiometricScreen.name,
          extra: any(named: 'extra'),
        )).called(1);
  });

  buildWidget(WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider<ActivateBiometricCubit>.value(
            value: mockCubit,
            child: MockActiveBiometricScreen(
              onSuccess: mockCb.call,
              flow: mockFlow,
              mockErrorCb: mockApiErrorCb,
              sessionToken: mockSessionToken,
            )),
      ),
    );
  }

  testWidgets('test render UI ActiveBiometricScreen should render correct UI',
      (WidgetTester tester) async {
    await buildWidget(tester);
    expect(find.byType(NoAppBarWrapper), findsOneWidget);
    expect(find.text(EvoStrings.activeBiometricTitle), findsOneWidget);
    expect(find.text(EvoStrings.activeBiometricDesc), findsOneWidget);
    expect(find.text(EvoStrings.ctaYes), findsOneWidget);
    expect(find.text(EvoStrings.ctaSkip), findsOneWidget);

    await tester.tap(find.text(EvoStrings.ctaYes));
    await tester.pump();

    verify(() => mockCubit.enableBiometricAuthentication(mockSessionToken)).called(1);

    await tester.tap(find.text(EvoStrings.ctaSkip));
    await tester.pump();

    verify(() => mockCubit.skipActivateBiometric(mockSessionToken)).called(1);
  });

  group('test listen cubit state', () {
    testWidgets('should show loading when state is [ActivateBiometricLoading]',
        (WidgetTester tester) async {
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricLoading(),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
    });

    testWidgets('should hide loading when state is not [ActivateBiometricLoading]',
        (WidgetTester tester) async {
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricInitState(),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
    });

    testWidgets(
        'should navigate to ActivateBiometricSuccessScreen when state is [ActivateBiometricSuccess], skip is false',
        (WidgetTester tester) async {
      final BaseEntity entity = BaseEntity();
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricSuccess(entity: entity, skip: false),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      final ActivateBiometricSuccessScreenArg captured =
          verify(() => mockNavigatorContext.pushReplacementNamed(
                Screen.activateBiometricSuccessScreen.name,
                extra: captureAny(named: 'extra'),
              )).captured.first as ActivateBiometricSuccessScreenArg;

      captured.onProceed.call();
      verify(() => mockCb.call(any(
            that: isA<ChallengeSuccessModel>().having(
              (ChallengeSuccessModel model) => model.entity,
              'verify entity',
              entity,
            ),
          ))).called(1);
    });
    testWidgets('should call onSuccess when state is [ActivateBiometricSuccess], skip is true',
        (WidgetTester tester) async {
      final BaseEntity entity = BaseEntity();
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricSuccess(entity: entity, skip: true),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockCb.call(any())).called(1);
      verify(() => mockNavigatorContext.pop()).called(1);
    });

    testWidgets('should handle bio error when state is [ActivateBiometricLocalAuthError]',
        (WidgetTester tester) async {
      const BioAuthError error = BioAuthError.userDismiss;
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricLocalAuthError(error: error),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockBioFn.handleBioError(
            bioError: error,
          )).called(1);
    });

    testWidgets(
        'should show session token expired dialog when state is [ActivateBiometricInvalidToken]',
        (WidgetTester tester) async {
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricInvalidToken(),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockDialogFunction.showDialogSessionTokenExpired(
            type: SessionDialogType.activateAccount,
          )).called(1);
    });

    testWidgets('should handle API error when state is [ActivateBiometricApiError]',
        (WidgetTester tester) async {
      final ErrorUIModel mockError = ErrorUIModel();
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricApiError(error: mockError),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockApiErrorCb.call(mockError)).called(1);
    });

    testWidgets('should show unknown error snackbar', (WidgetTester tester) async {
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricUnknownError(),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockEvoSnackBar.show(
            EvoStrings.unknownError,
            typeSnackBar: SnackBarType.error,
            durationInMilliSec: any(named: 'durationInMilliSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });

    testWidgets(
        'should navigate to blocked screen when state is [ActivateBiometricLocalAuthError] with biometric locked',
        (WidgetTester tester) async {
      const BioAuthError error = BioAuthError.androidLockedOut;
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricLocalAuthError(error: error),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockNavigatorContext.pushReplacementNamed(
            Screen.activateBiometricBlockedScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });
  });
}
