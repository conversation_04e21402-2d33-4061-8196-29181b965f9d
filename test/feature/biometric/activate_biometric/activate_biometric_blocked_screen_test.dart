import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_blocked_screen.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_cubit.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_state.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider_config.dart';
import 'package:evoapp/feature/biometric/utils/biometric_functions.dart';
import 'package:evoapp/model/challenge_success_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/widget/no_app_bar_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../base/evo_page_state_base_test_config.dart';

class MockCb extends Mock {
  void call(ChallengeSuccessModel model);
}

class MockActiveBiometricCubit extends MockCubit<ActivateBiometricState>
    implements ActivateBiometricCubit {}

class MockBiometricFunctions extends Mock implements BiometricFunctions {}

class MockEvoSnackBar extends Mock implements EvoSnackBar {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockHandleEvoApiError extends Mock {
  void call(ErrorUIModel? errorUIModel);
}

class MockActiveBiometricBlockedScreen extends ActivateBiometricBlockedScreen {
  final MockHandleEvoApiError mockErrorCb;

  const MockActiveBiometricBlockedScreen({
    required this.mockErrorCb,
    required super.sessionToken,
    required super.flow,
    super.onPopSuccess,
    super.key,
  });

  @override
  State<ActivateBiometricBlockedScreen> createState() => MockActiveBiometricBlockedScreenState();
}

class MockActiveBiometricBlockedScreenState extends ActivateBiometricBlockedScreenState {
  @override
  Future<void> handleEvoApiError(ErrorUIModel? errorUIModel) async {
    (widget as MockActiveBiometricBlockedScreen).mockErrorCb(errorUIModel);
  }
}

void main() {
  late ActivateBiometricCubit mockCubit;
  late MockCb mockCb;
  late CommonImageProvider mockImageProvider;
  late EvoSnackBar mockEvoSnackBar;
  late EvoUtilFunction mockEvoUtilFunction;
  const String mockSessionToken = 'test_session_token';
  const EnableBiometricAuthenticationFlow mockFlow =
      EnableBiometricAuthenticationFlow.accountActivation;
  late MockHandleEvoApiError mockApiErrorCb;

  setUpAll(() {
    initConfigEvoPageStateBase();

    mockImageProvider = getIt.get<CommonImageProvider>();
    when(() => mockImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
        )).thenReturn(SizedBox());

    getIt.registerSingleton<EvoSnackBar>(MockEvoSnackBar());
    mockEvoSnackBar = getIt.get<EvoSnackBar>();

    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();
    when(() => mockEvoUtilFunction.showHudLoading()).thenAnswer((_) async {});
    when(() => mockEvoUtilFunction.hideHudLoading()).thenAnswer((_) async {});

    registerFallbackValue(SnackBarType.success);
    registerFallbackValue(ChallengeSuccessModel(entity: BaseEntity()));
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
    mockCubit = MockActiveBiometricCubit();
    when(() => mockCubit.state).thenReturn(ActivateBiometricInitState());
    mockCb = MockCb();
    mockApiErrorCb = MockHandleEvoApiError();

    when(() => mockEvoSnackBar.show(
          any(),
          typeSnackBar: any(named: 'typeSnackBar'),
          durationInMilliSec: any(named: 'durationInMilliSec'),
          description: any(named: 'description'),
          marginBottomRatio: any(named: 'marginBottomRatio'),
        )).thenAnswer((_) async {
      return null;
    });

    when(() => mockCubit.skipActivateBiometric(any())).thenAnswer((_) async {});
    when(() => mockDialogFunction.showDialogSessionTokenExpired(
          type: any(named: 'type'),
          onClickPositive: any(named: 'onClickPositive'),
        )).thenAnswer((_) async {});
  });

  test('ActivateBiometricBlockedScreenArg should hold correct data', () {
    final MockCb cb = MockCb();
    final ActivateBiometricBlockedScreenArg arg = ActivateBiometricBlockedScreenArg(
      sessionToken: mockSessionToken,
      onPopSuccess: cb.call,
      flow: mockFlow,
    );

    expect(arg.sessionToken, mockSessionToken);
    expect(arg.onPopSuccess, cb.call);
    expect(arg.flow, mockFlow);
  });

  test('ActivateBiometricBlockedScreen should have correct routeSettings', () {
    final ActivateBiometricBlockedScreen screen = ActivateBiometricBlockedScreen(
      sessionToken: mockSessionToken,
      flow: mockFlow,
    );

    expect(screen.routeSettings.name, Screen.activateBiometricBlockedScreen.name);
  });

  test('ActivateBiometricBlockedScreen should navigate correctly on pushReplacementNamed',
      () async {
    ActivateBiometricBlockedScreen.pushReplacementNamed(
      sessionToken: mockSessionToken,
      onPopSuccess: mockCb.call,
      flow: mockFlow,
    );

    verify(() => mockNavigatorContext.pushReplacementNamed(
          Screen.activateBiometricBlockedScreen.name,
          extra: any(
              named: 'extra',
              that: isA<ActivateBiometricBlockedScreenArg>().having(
                (ActivateBiometricBlockedScreenArg arg) =>
                    (arg.sessionToken, arg.onPopSuccess, arg.flow),
                'sessionToken',
                (
                  mockSessionToken,
                  mockCb.call,
                  mockFlow,
                ),
              )),
        )).called(1);
  });

  buildWidget(WidgetTester tester, [EnableBiometricAuthenticationFlow? flow]) async {
    await tester.pumpWidget(
      MaterialApp(
        home: BlocProvider<ActivateBiometricCubit>.value(
            value: mockCubit,
            child: MockActiveBiometricBlockedScreen(
              mockErrorCb: mockApiErrorCb,
              sessionToken: mockSessionToken,
              onPopSuccess: mockCb.call,
              flow: flow ?? mockFlow,
            )),
      ),
    );
  }

  testWidgets('test render UI ActivateBiometricBlockedScreen should render correct UI',
      (WidgetTester tester) async {
    await buildWidget(tester);
    expect(find.byType(NoAppBarWrapper), findsOneWidget);
    expect(find.text(EvoStrings.enableBiometricBlockedTitle), findsOneWidget);
    expect(find.text(EvoStrings.enableBiometricBlockedDesc), findsOneWidget);
    expect(find.text(EvoStrings.ctaProceed), findsOneWidget);

    await tester.tap(find.text(EvoStrings.ctaProceed));
    await tester.pump();

    verify(() => mockCubit.skipActivateBiometric(mockSessionToken)).called(1);
  });

  group('test listen cubit state', () {
    testWidgets('should show loading when state is [ActivateBiometricLoading]',
        (WidgetTester tester) async {
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricLoading(),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockEvoUtilFunction.showHudLoading()).called(1);
    });

    testWidgets('should hide loading when state is not [ActivateBiometricLoading]',
        (WidgetTester tester) async {
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricInitState(),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockEvoUtilFunction.hideHudLoading()).called(1);
    });

    testWidgets('should call onPopSuccess and pop when state is [ActivateBiometricSuccess]',
        (WidgetTester tester) async {
      final BaseEntity entity = BaseEntity();
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricSuccess(
            entity: entity,
            skip: true,
          ),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockCb.call(any())).called(1);
      verify(() => mockNavigatorContext.pop()).called(1);
    });

    group('test ActivateBiometricInvalidToken', () {
      testWidgets(
          'show dialog with type SessionDialogType.activateAccount when flow is accountActivation',
          (WidgetTester tester) async {
        whenListen(
          mockCubit,
          Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
            ActivateBiometricInvalidToken(),
          ]),
        );

        await buildWidget(tester);
        await tester.pump();

        verify(() => mockDialogFunction.showDialogSessionTokenExpired(
              type: SessionDialogType.activateAccount,
            )).called(1);
      });

      testWidgets('show dialog with type SessionDialogType. when flow is newDeviceLogin',
          (WidgetTester tester) async {
        whenListen(
          mockCubit,
          Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
            ActivateBiometricInvalidToken(),
          ]),
        );

        await buildWidget(tester, EnableBiometricAuthenticationFlow.newDeviceLogin);
        await tester.pump();

        verify(() => mockDialogFunction.showDialogSessionTokenExpired()).called(1);
      });
    });

    testWidgets('should handle API error when state is [ActivateBiometricApiError]',
        (WidgetTester tester) async {
      final ErrorUIModel mockError = ErrorUIModel();
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricApiError(error: mockError),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockApiErrorCb.call(mockError)).called(1);
    });

    testWidgets('should show unknown error snackbar when state is [ActivateBiometricUnknownError]',
        (WidgetTester tester) async {
      whenListen(
        mockCubit,
        Stream<ActivateBiometricState>.fromIterable(<ActivateBiometricState>[
          ActivateBiometricUnknownError(),
        ]),
      );

      await buildWidget(tester);
      await tester.pump();

      verify(() => mockEvoSnackBar.show(
            EvoStrings.unknownError,
            typeSnackBar: SnackBarType.error,
            durationInMilliSec: any(named: 'durationInMilliSec'),
            description: any(named: 'description'),
            marginBottomRatio: any(named: 'marginBottomRatio'),
          )).called(1);
    });
  });
}
