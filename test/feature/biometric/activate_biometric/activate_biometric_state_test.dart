import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_state.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:flutter_common_package/data/response/base_entity.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ActiveBiometricState', () {
    test('ActiveBiometricInitState should be subtype of ActiveBiometricState', () {
      expect(ActivateBiometricInitState(), isA<ActivateBiometricState>());
    });

    test('ActivateBiometricSuccess should be subtype of ActiveBiometricState', () {
      final BaseEntity entity = BaseEntity();
      expect(
        ActivateBiometricSuccess(entity: entity, skip: false),
        isA<ActivateBiometricState>(),
      );
    });

    test('ActivateBiometricLocalAuthError should be subtype of ActiveBiometricState', () {
      expect(
        ActivateBiometricLocalAuthError(error: BioAuthError.unknown),
        isA<ActivateBiometricState>(),
      );
    });

    test('ActivateBiometricUnknownError should be subtype of ActiveBiometricState', () {
      expect(
        ActivateBiometricUnknownError(),
        isA<ActivateBiometricState>(),
      );
    });

    test('ActivateBiometricInvalidToken should be subtype of ActiveBiometricState', () {
      expect(
        ActivateBiometricInvalidToken(),
        isA<ActivateBiometricState>(),
      );
    });

    test('ActivateBiometricApiError should be subtype of ActiveBiometricState', () {
      final ErrorUIModel error = ErrorUIModel();
      expect(
        ActivateBiometricApiError(error: error),
        isA<ActivateBiometricState>(),
      );
    });

    test('ActivateBiometricLoading should be subtype of ActiveBiometricState', () {
      expect(
        ActivateBiometricLoading(),
        isA<ActivateBiometricState>(),
      );
    });

    group('ActivateBiometricSuccess', () {
      test('should have correct entity value', () {
        final BaseEntity entity = BaseEntity();

        final ActivateBiometricSuccess state = ActivateBiometricSuccess(entity: entity, skip: true);

        expect(state.entity, entity);
        expect(state.skip, isTrue);
      });
    });

    group('ActivateBiometricLocalAuthError', () {
      test('should have correct error value', () {
        final BioAuthError error = BioAuthError.unknown;
        final ActivateBiometricLocalAuthError state = ActivateBiometricLocalAuthError(error: error);
        expect(state.error, error);
      });
    });

    group('ActivateBiometricApiError', () {
      test('should have correct error value', () {
        final ErrorUIModel error = ErrorUIModel();
        final ActivateBiometricApiError state = ActivateBiometricApiError(error: error);
        expect(state.error, error);
      });

      test('should allow null error value', () {
        final ActivateBiometricApiError state = ActivateBiometricApiError(error: null);
        expect(state.error, null);
      });
    });
  });
}
