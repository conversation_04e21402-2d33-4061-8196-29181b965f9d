import 'package:bloc_test/bloc_test.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_cubit.dart';
import 'package:evoapp/feature/biometric/activate_biometric/activate_biometric_state.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_result.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_service.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider_config.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/impl/account_activation_token_provider.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/impl/new_device_login_token_provider.dart';
import 'package:evoapp/feature/biometric/utils/bio_auth_result.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../constant.dart';

class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockBiometricsAuthenticationService extends Mock implements BiometricAuthenticationService {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  late MockBiometricsAuthenticate mockBiometricsAuthenticate;
  late ActivateBiometricCubit activeBiometricCubit;
  late MockBiometricsAuthenticationService mockBiometricsAuthenticationService;
  late MockAuthenticationRepo mockAuthRepo;
  final AccountActivationEntity mockEntity = AccountActivationEntity();
  final EnableBiometricAuthenticationFlow mockFlow =
      EnableBiometricAuthenticationFlow.accountActivation;
  const String sessionToken = 'test-session-token';

  setUpAll(() {
    mockBiometricsAuthenticate = MockBiometricsAuthenticate();
    mockBiometricsAuthenticationService = MockBiometricsAuthenticationService();
    mockAuthRepo = MockAuthenticationRepo();

    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());

    registerFallbackValue(
        BiometricTokenProviderConfig(flow: EnableBiometricAuthenticationFlow.accountActivation));
    registerFallbackValue(AccountActivationTokenProvider(authRepo: mockAuthRepo));
  });

  setUp(() {
    when(() => mockBiometricsAuthenticationService.enableWithFlow(
          config: any(named: 'config'),
        )).thenAnswer((_) async {
      return BiometricAuthenticationSuccess(entity: mockEntity);
    });

    activeBiometricCubit = ActivateBiometricCubit(
      bioAuth: mockBiometricsAuthenticate,
      biometricAuthenticationService: mockBiometricsAuthenticationService,
      authRepo: mockAuthRepo,
      flow: mockFlow,
    );
  });

  tearDown(() {
    reset(mockBiometricsAuthenticationService);
  });

  tearDownAll(() {
    getIt.reset();
  });

  BiometricTokenProviderConfig captureAndVerifyConfig() {
    return captureAny<BiometricTokenProviderConfig>(
        named: 'config',
        that: isA<BiometricTokenProviderConfig>().having(
            (BiometricTokenProviderConfig config) =>
                (config.flow, config.additionalParams?['session_token']),
            'verify config',
            (mockFlow, sessionToken)));
  }

  group('test constructor', () {
    test('should register providers correctly', () {
      verify(() => mockBiometricsAuthenticationService.registerProvider(any(
            that: isA<AccountActivationTokenProvider>(),
          ))).called(1);
      verify(() => mockBiometricsAuthenticationService.registerProvider(any(
            that: isA<NewDeviceLoginTokenProvider>(),
          ))).called(1);
    });
  });

  group('test enableBiometricAuthentication() function', () {
    test('should call biometricsTokenModule.enable with correct parameters', () {
      activeBiometricCubit.enableBiometricAuthentication(sessionToken);

      verify(() => mockBiometricsAuthenticationService.enableWithFlow(
            config: captureAndVerifyConfig(),
          )).called(1);
    });

    test(
        'should emit ActivateBiometricUnknownError when biometricsTokenModule.enable return unknown ActionError ',
        () async {
      when(() => mockBiometricsAuthenticationService.enableWithFlow(
            config: any(named: 'config'),
          )).thenAnswer((_) async {
        return BiometricAuthenticationFailure(
            errorType: BiometricAuthenticationFailureType.unknown);
      });

      await activeBiometricCubit.enableBiometricAuthentication(null);

      expect(activeBiometricCubit.state, isA<ActivateBiometricUnknownError>());
    });

    test('should emit ActivateBiometricSuccess when biometricsTokenModule.enable success',
        () async {
      await activeBiometricCubit.enableBiometricAuthentication('session-token');

      expect(
          activeBiometricCubit.state,
          isA<ActivateBiometricSuccess>().having(
              (ActivateBiometricSuccess state) => (state.entity, false),
              'verify result',
              (mockEntity, false)));
    });
  });

  group('test onHandlingActionError', () {
    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
      'should emit ActivateBiometricInvalidToken when error has INVALID_TOKEN status code',
      build: () => activeBiometricCubit,
      act: (ActivateBiometricCubit cubit) => cubit.onHandlingBiometricAuthenticationFailure(
        BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.apiError,
          apiError: ErrorUIModel(
            statusCode: CommonHttpClient.INVALID_TOKEN,
            userMessage: 'Invalid token',
          ),
        ),
      ),
      expect: () => <TypeMatcher<ActivateBiometricInvalidToken>>[
        isA<ActivateBiometricInvalidToken>(),
      ],
    );

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
      'should emit ActivateBiometricApiError when error is not INVALID_TOKEN status code',
      build: () => activeBiometricCubit,
      act: (ActivateBiometricCubit cubit) => cubit.onHandlingBiometricAuthenticationFailure(
        BiometricAuthenticationFailure(
          errorType: BiometricAuthenticationFailureType.apiError,
          apiError: ErrorUIModel(
            statusCode: 400,
            userMessage: 'Bad request',
          ),
        ),
      ),
      expect: () => <TypeMatcher<ActivateBiometricApiError>>[isA<ActivateBiometricApiError>()],
    );

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
      'should emit ActivateBiometricLocalAuthError when error is ActionBiometricError',
      build: () => activeBiometricCubit,
      act: (ActivateBiometricCubit cubit) => cubit.onHandlingBiometricAuthenticationFailure(
        BiometricAuthenticationFailure(
            errorType: BiometricAuthenticationFailureType.biometrics,
            biometricError: BioAuthError.notEnrolled),
      ),
      expect: () => <TypeMatcher<ActivateBiometricLocalAuthError>>[
        isA<ActivateBiometricLocalAuthError>().having(
          (ActivateBiometricLocalAuthError state) => state.error,
          'error',
          BioAuthError.notEnrolled,
        ),
      ],
    );

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
      'should emit ActivateBiometricUnknownError for any other ActionError',
      build: () => activeBiometricCubit,
      act: (ActivateBiometricCubit cubit) => cubit.onHandlingBiometricAuthenticationFailure(
        BiometricAuthenticationFailure(errorType: BiometricAuthenticationFailureType.unknown),
      ),
      expect: () => <TypeMatcher<ActivateBiometricUnknownError>>[
        isA<ActivateBiometricUnknownError>(),
      ],
    );
  });

  group('test skipActivateBiometric() function', () {
    const String sessionToken = 'test-session-token';
    final AccountActivationEntity mockEntity = AccountActivationEntity();

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
      'should call biometricAuthenticationService.skipEnableWithFlow with correct parameters',
      setUp: () {
        when(() => mockBiometricsAuthenticationService.skipEnableWithFlow(
              config: any(named: 'config'),
            )).thenAnswer((_) async {
          return BiometricAuthenticationSuccess(entity: mockEntity);
        });
      },
      build: () => activeBiometricCubit,
      act: (ActivateBiometricCubit cubit) => cubit.skipActivateBiometric(sessionToken),
      expect: () => <TypeMatcher<ActivateBiometricState>>[
        isA<ActivateBiometricLoading>(),
        isA<ActivateBiometricSuccess>().having(
          (ActivateBiometricSuccess state) => (state.entity, state.skip),
          'entity',
          (mockEntity, true),
        ),
      ],
      wait: TestConstant.blocEmitStateDelayDuration,
      verify: (_) {
        verify(() => mockBiometricsAuthenticationService.skipEnableWithFlow(
              config: captureAndVerifyConfig(),
            )).called(1);
      },
    );

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
      'should emit ActivateBiometricLoading and ActivateBiometricInvalidToken when service returns API error with INVALID_TOKEN',
      setUp: () {
        when(() => mockBiometricsAuthenticationService.skipEnableWithFlow(
              config: any(named: 'config'),
            )).thenAnswer((_) async {
          return BiometricAuthenticationFailure(
            errorType: BiometricAuthenticationFailureType.apiError,
            apiError: ErrorUIModel(
              statusCode: CommonHttpClient.INVALID_TOKEN,
              userMessage: 'Invalid token',
            ),
          );
        });
      },
      build: () => activeBiometricCubit,
      act: (ActivateBiometricCubit cubit) => cubit.skipActivateBiometric(sessionToken),
      expect: () => <TypeMatcher<ActivateBiometricState>>[
        isA<ActivateBiometricLoading>(),
        isA<ActivateBiometricInvalidToken>(),
      ],
      wait: TestConstant.blocEmitStateDelayDuration,
    );

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
      'should emit ActivateBiometricLoading and ActivateBiometricApiError when service returns API error with other status code',
      setUp: () {
        when(() => mockBiometricsAuthenticationService.skipEnableWithFlow(
              config: any(named: 'config'),
            )).thenAnswer((_) async {
          return BiometricAuthenticationFailure(
            errorType: BiometricAuthenticationFailureType.apiError,
            apiError: ErrorUIModel(
              statusCode: CommonHttpClient.BAD_REQUEST,
            ),
          );
        });
      },
      build: () => activeBiometricCubit,
      act: (ActivateBiometricCubit cubit) => cubit.skipActivateBiometric(sessionToken),
      expect: () => <TypeMatcher<ActivateBiometricState>>[
        isA<ActivateBiometricLoading>(),
        isA<ActivateBiometricApiError>().having(
          (ActivateBiometricApiError state) => state.error?.statusCode,
          'verify status code',
          CommonHttpClient.BAD_REQUEST,
        ),
      ],
      wait: TestConstant.blocEmitStateDelayDuration,
    );

    blocTest<ActivateBiometricCubit, ActivateBiometricState>(
      'should emit ActivateBiometricLoading and ActivateBiometricUnknownError when service returns unknown error',
      setUp: () {
        when(() => mockBiometricsAuthenticationService.skipEnableWithFlow(
              config: any(named: 'config'),
            )).thenAnswer((_) async {
          return BiometricAuthenticationFailure(
            errorType: BiometricAuthenticationFailureType.unknown,
          );
        });
      },
      build: () => activeBiometricCubit,
      act: (ActivateBiometricCubit cubit) => cubit.skipActivateBiometric(sessionToken),
      expect: () => <TypeMatcher<ActivateBiometricState>>[
        isA<ActivateBiometricLoading>(),
        isA<ActivateBiometricUnknownError>(),
      ],
      wait: TestConstant.blocEmitStateDelayDuration,
    );
  });

  group('test close() function', () {
    test('should call biometricAuthenticationService.dispose and close cubit', () async {
      // Arrange
      when(() => mockBiometricsAuthenticationService.dispose()).thenAnswer((_) {});

      // Act
      await activeBiometricCubit.close();

      // Assert
      verify(() => mockBiometricsAuthenticationService.dispose()).called(1);
      expect(activeBiometricCubit.isClosed, isTrue);
    });
  });
}
