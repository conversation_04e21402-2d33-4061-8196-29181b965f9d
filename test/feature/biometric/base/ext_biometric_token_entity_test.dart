import 'package:evoapp/data/response/biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/base/ext_biometric_token_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('test isNeedNextChallenge() method', () {
    test('isNeedNextChallenge() return false when challengeType is null', () {
      final BiometricTokenEntity entity = BiometricTokenEntity.newInstance(biometricToken: '');
      final bool isNeedChallenge = entity.isNeedChallenge();
      expect(false, isNeedChallenge);
    });

    test('isNeedNextChallenge() return false when challengeType is empty', () {
      final BiometricTokenEntity entity =
          BiometricTokenEntity.newInstance(biometricToken: '', challengeType: '');
      final bool isNeedChallenge = entity.isNeedChallenge();
      expect(false, isNeedChallenge);
    });

    test('isNeedNextChallenge() return true when challengeType is not empty', () {
      final BiometricTokenEntity entity =
          BiometricTokenEntity.newInstance(biometricToken: '', challengeType: 'otp');
      final bool isNeedChallenge = entity.isNeedChallenge();
      expect(true, isNeedChallenge);
    });
  });

  group('test getChallengeType() method', () {
    test('getChallengeType() return ChallengeType.NoNeed when challengeType is null', () {
      final BiometricTokenEntity entity = BiometricTokenEntity.newInstance(biometricToken: '');
      final ChallengeType type = entity.getChallengeType();
      expect(ChallengeType.none, type);
    });

    test('getChallengeType() return ChallengeType.NoNeed when challengeType is empty', () {
      final BiometricTokenEntity entity = BiometricTokenEntity.newInstance(biometricToken: '');
      final ChallengeType type = entity.getChallengeType();
      expect(ChallengeType.none, type);
    });

    test(
        'getChallengeType() return ChallengeType.NoNeed when challengeType does not match define value ',
        () {
      final BiometricTokenEntity entity =
          BiometricTokenEntity.newInstance(biometricToken: '', challengeType: 'SMS');
      final ChallengeType type = entity.getChallengeType();
      expect(ChallengeType.none, type);
    });

    test('getChallengeType() return ChallengeType.NoNeed when challengeType return value none ',
        () {
      final BiometricTokenEntity entity =
          BiometricTokenEntity.newInstance(biometricToken: '', challengeType: 'none');
      final ChallengeType type = entity.getChallengeType();
      expect(ChallengeType.none, type);
    });
  });
}
