import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../flutter_test_config.dart';

class CommonUtilFunctionMock extends Mock implements CommonUtilFunction {}

void main() {
  late CommonUtilFunctionMock commonUtilFunctionMock;
  late FlutterSecureStorage secureStorage;
  late EvoSecureStorageHelperImpl evoSecureStorageHelper;
  late MockOneSignal mockOneSignal;

  const String refreshTokenValue = 'refresh_token_value';
  const String accessTokenValue = 'access_token_value';

  const String deviceTokenKey = EvoSecureStorageHelperImpl.deviceTokenKey;
  const String deviceTokenValue = 'device_token_value';

  const String userPhoneNumberKey = EvoSecureStorageHelperImpl.usernameKey;
  const String usernameValue = 'username_value';

  const String biometricTokenKey = EvoSecureStorageHelperImpl.biometricTokenKey;
  const String biometricTokenValue = 'biometric_token_value';

  const String isNewDeviceKey = EvoSecureStorageHelperImpl.isNewDeviceKey;
  const bool isNewDeviceKeyValue = false;

  final AppState appState = AppState();

  setUpAll(() {
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    commonUtilFunctionMock = CommonUtilFunctionMock();
    getIt.registerLazySingleton<CommonUtilFunction>(() => commonUtilFunctionMock);
    getIt.registerLazySingleton<AppState>(() => appState);
    secureStorage = testFlutterSecureStorageExecutable();
    testSecureDataSourceExecutable(secureStorage: secureStorage);
    evoSecureStorageHelper = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
  });

  setUp(() {
    appState.userToken = null;

    mockOneSignal = testOneSignalExecutable();

    when(() => commonUtilFunctionMock.clearDataOnTokenInvalid(
          clearAllNotifications: any(named: 'clearAllNotifications'),
        )).thenAnswer((_) async {
      return Future<void>.value();
    });
  });

  tearDown(() {
    reset(mockOneSignal);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('test method clearEvoDataOnLogout()', () {
    setUpAll(() {
      testDioClientImplExecutable();
    });

    test('should_call_method_clearEvoDataOnLogout_correctly', () async {
      expect(appState.userToken.accessToken, isNull);
      expect(appState.userToken.refreshToken, isNull);
      expect(await secureStorage.containsKey(key: deviceTokenKey), false);

      /// Setup
      appState.userToken.accessToken = accessTokenValue;
      appState.userToken.refreshToken = refreshTokenValue;
      await evoSecureStorageHelper.setDeviceToken(deviceTokenValue);
      await evoSecureStorageHelper.setUsername(usernameValue);
      await evoSecureStorageHelper.setBiometricToken(biometricTokenValue);
      await evoSecureStorageHelper.setNewDevice(isNewDeviceKeyValue);

      expect(appState.userToken.accessToken, accessTokenValue);
      expect(appState.userToken.refreshToken, refreshTokenValue);

      expect(await secureStorage.containsKey(key: deviceTokenKey), true);
      expect(await secureStorage.read(key: deviceTokenKey), deviceTokenValue);

      await evoUtilFunction.clearAllUserData(oneSignal: mockOneSignal);

      verify(() => commonUtilFunction.clearDataOnTokenInvalid(clearAllNotifications: true))
          .called(1);

      expect(appState.userToken.accessToken, isNull);
      expect(appState.userToken.refreshToken, isNull);

      /// verify device token
      expect(await secureStorage.containsKey(key: deviceTokenKey), false);
      expect(await secureStorage.read(key: deviceTokenKey), null);
      expect(await evoSecureStorageHelper.getDeviceToken(), null);

      /// verify device token
      expect(await secureStorage.containsKey(key: deviceTokenKey), false);
      expect(await secureStorage.read(key: deviceTokenKey), null);
      expect(await evoSecureStorageHelper.getDeviceToken(), null);

      /// verify phone number
      expect(await secureStorage.containsKey(key: userPhoneNumberKey), false);
      expect(await secureStorage.read(key: userPhoneNumberKey), null);
      expect(await evoSecureStorageHelper.getUsername(), null);

      /// verify biometric token
      expect(await secureStorage.containsKey(key: biometricTokenKey), false);
      expect(await secureStorage.read(key: biometricTokenKey), null);
      expect(await evoSecureStorageHelper.getBiometricToken(), null);

      /// verify is new device
      expect(await secureStorage.containsKey(key: isNewDeviceKey), false);
      expect(await secureStorage.read(key: isNewDeviceKey), null);
      expect(await evoSecureStorageHelper.isNewDevice(), true);
    });

    test('should_call_method_clearDataOnTokenInvalid_with_clear_all_notification_is_true_correctly',
        () async {
      await evoUtilFunction.clearAllUserData(oneSignal: mockOneSignal);

      verify(() => commonUtilFunction.clearDataOnTokenInvalid(
            clearAllNotifications: true,
          )).called(1);
    });

    test('should_clear_user_data_on_memory', () async {
      // setup
      final AppState appState = getIt.get<AppState>();
      appState.isUserLogIn = true;
      appState.userInfo.value = const UserInformationEntity(fullName: 'evo');

      // action
      await evoUtilFunction.clearAllUserData(oneSignal: mockOneSignal);

      // verify
      expect(appState.isUserLogIn, false);
      expect(appState.userInfo.value?.fullName, isNull);
    });
  });
}
