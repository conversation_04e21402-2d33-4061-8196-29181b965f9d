import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/inactive_detector/inactive_detector_widget.dart';
import 'package:evoapp/feature/pin/models/change_pin_status.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/app_settings_wrapper.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/navigator/evo_router_navigator.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:evoapp/util/token_utils/evo_jwt_helper_impl.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/widget/hud_loading/hud_loading.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/webview/webview.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_common_package/resources/global.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/share_preference_helper.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_dialog_bottom_sheet/common_dialog_bottom_sheet.dart';
import 'package:flutter_inappwebview_android/flutter_inappwebview_android.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../data/repository/mock_common_http_client.dart';
import '../../feature/splash_screen/splash_screen_cubit_test.dart';
import '../flutter_test_config.dart';

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockEvoNavigator extends Mock implements EvoRouterNavigator {}

class MockHudLoading extends Mock implements HudLoading {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}



class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockDialogFunction extends Mock implements DialogFunction {}

class MockAppSettingsWrapper extends Mock implements AppSettingsWrapper {}

class MockJWTHelper extends Mock implements JwtHelper {}

void main() {
  late EvoFlutterWrapper mockEvoFlutterWrapper;
  late LoggingRepo mockLoggingRepo;
  late FlutterSecureStorage secureStorage;
  late MockDeviceInfoPlugin deviceInfoPlugin;
  late CommonSharedPreferencesHelper commonSharedPreferencesHelper;
  final MockCommonUtilFunction mockCommonUtilFunction = MockCommonUtilFunction();

  const String deviceId = 'device_id';

  // Correct format based on device_info_plus 11.3.0 official test
  const Map<String, dynamic> iosUtsnameMap = <String, dynamic>{
    'release': '17.0.0',
    'version': 'Darwin Kernel Version 21.0.0',
    'machine': 'iPhone14,2',
    'sysname': 'iOS',
    'nodename': 'iPhone',
  };

  const Map<String, dynamic> iosDeviceInfoMap = <String, dynamic>{
    'name': 'iPhone',
    'model': 'iPhone',
    'modelName': 'iPhone 13 Pro',  // Required field that was missing
    'utsname': iosUtsnameMap,
    'systemName': 'iOS',
    'isPhysicalDevice': true,
    'isiOSAppOnMac': false,  // Required field that was missing
    'systemVersion': '17.0',
    'localizedModel': 'iPhone',
    'identifierForVendor': deviceId,
  };


  void unregisterCommonUtilFunction() {
    getIt.unregister<CommonUtilFunction>();
  }

  void registerCommonUtilFunction(CommonUtilFunction commonUtilFunction) {
    getIt.registerLazySingleton<CommonUtilFunction>(() => commonUtilFunction);
  }

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    AndroidInAppWebViewPlatform.registerWith();
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    getIt.registerLazySingleton<AppState>(() => AppState());

    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
    mockLoggingRepo = getIt.get<LoggingRepo>();

    secureStorage = testFlutterSecureStorageExecutable();

    getIt.registerLazySingleton<EvoFlutterWrapper>(() => MockEvoPlatformWrapper());
    mockEvoFlutterWrapper = getIt.get<EvoFlutterWrapper>();

    deviceInfoPlugin = MockDeviceInfoPlugin();
    getIt.registerLazySingleton<DeviceInfoPlugin>(() => deviceInfoPlugin);

    // Setup default mock for iOS device info using real IosDeviceInfo object
    when(() => deviceInfoPlugin.iosInfo).thenAnswer((_) async {
      return IosDeviceInfo.fromMap(iosDeviceInfoMap);
    });

    getIt.registerLazySingleton<CommonHttpClient>(() => MockCommonHttpClient());

    commonSharedPreferencesHelper = MockCommonSharedPreferencesHelper();
    getIt.registerLazySingleton<CommonSharedPreferencesHelper>(() => commonSharedPreferencesHelper);

    getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
    getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<DialogFunction>(() => MockDialogFunction());
    getIt.registerLazySingleton<FeatureToggle>(() => FeatureToggle());

    registerFallbackValue(EventType.other);
    when(
      () => mockLoggingRepo.logEvent(
        eventType: any(named: 'eventType'),
        data: any(named: 'data'),
      ),
    ).thenAnswer((_) => Future<void>.value());
  });

  setUp(() {
    when(() => mockEvoFlutterWrapper.showDialog<void>(
        builder: any(named: 'builder'),
        barrierDismissible: any(named: 'barrierDismissible'),
        useSafeArea: any(named: 'useSafeArea'))).thenAnswer((_) => Future<void>.value());
  });

  tearDown(() {
    /// reset local data after each test
    secureStorage.deleteAll();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('verify detectReinstallAppOnIOSDevice()', () {
    late MockEvoLocalStorageHelper mockEvoLocalStorageHelper;

    setUpAll(() {
      mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
      getIt.registerLazySingleton<EvoLocalStorageHelper>(() => mockEvoLocalStorageHelper);
      registerCommonUtilFunction(CommonUtilFunction());
    });

    setUp(() {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);
    });

    tearDown(() {
      reset(mockEvoFlutterWrapper);
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
      unregisterCommonUtilFunction();
      reset(mockEvoLocalStorageHelper);
    });

    test('return detectReinstallAppOnIOSDevice() is false on Android', () async {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      final bool isReInstallApp = await evoUtilFunction.detectReinstallAppOnIOSDevice();

      expect(isReInstallApp, false);
    });

    test('return detectReinstallAppOnIOSDevice() is true', () async {
      when(() => mockEvoLocalStorageHelper.getDeviceId())
          .thenAnswer((_) => Future<String?>.value());

      final bool isReInstallApp = await evoUtilFunction.detectReinstallAppOnIOSDevice();

      expect(isReInstallApp, true);
      verify(() => mockEvoLocalStorageHelper.getDeviceId()).called(1);
    });

    test('return detectReinstallAppOnIOSDevice() is false', () async {
      when(() => mockEvoLocalStorageHelper.getDeviceId())
          .thenAnswer((_) => Future<String>.value(deviceId));

      final bool isReInstallApp = await evoUtilFunction.detectReinstallAppOnIOSDevice();

      expect(isReInstallApp, false);
      verify(() => mockEvoLocalStorageHelper.getDeviceId()).called(1);
    });
  });

  group('test getNewDeviceId() function', () {
    late EvoLocalStorageHelper evoLocalStorageHelper;

    setUpAll(() {
      evoLocalStorageHelper = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
      registerCommonUtilFunction(CommonUtilFunction());
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
      reset(mockEvoFlutterWrapper);
      unregisterCommonUtilFunction();
    });

    test('verify currentDeviceId and call getNewDeviceId() function on Android', () async {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      final String? result = await evoUtilFunction.getNewDeviceId();

      expect(result, null);
    });

    test('verify currentDeviceId and call getNewDeviceId() function on iOS', () async {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);

      ///verify deviceId is null
      final String? currentDeviceId = await evoLocalStorageHelper.getDeviceId();

      expect(currentDeviceId, null);

      await evoUtilFunction.setNewDeviceId();

      expect((await commonUtilFunction.getIosInfo()).identifierForVendor, deviceId);

      ///verify newDeviceId
      final String? newDeviceId = await evoUtilFunction.getNewDeviceId();

      expect(newDeviceId, deviceId);
    });
  });

  group('test encode query parameters', () {
    test('test encode query parameter with Map is empty', () {
      final Map<String, String> params = <String, String>{};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = '';
      expect(encodeParam, expectedString);
    });

    test('test encode query parameter with one field is Empty', () {
      final Map<String, String> params = <String, String>{'subject': ''};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = 'subject=';
      expect(encodeParam, expectedString);
    });

    test('test encode query parameter with one field has content', () {
      final Map<String, String> params = <String, String>{'subject': 'Gop y'};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = 'subject=Gop%20y';
      expect(encodeParam, expectedString);
    });

    test('test encode query parameter with two fields have content', () {
      final Map<String, String> params = <String, String>{'subject': 'Gop y', 'body': 'Mo the Evo'};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = 'subject=Gop%20y&body=Mo%20the%20Evo';
      expect(encodeParam, expectedString);
    });

    test('test encode query parameter with two fields and having one field is empty', () {
      final Map<String, String> params = <String, String>{'subject': 'Gop y', 'body': ''};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = 'subject=Gop%20y&body=';
      expect(encodeParam, expectedString);
    });

    test('test encode query parameter with Vietnamese characters', () {
      final Map<String, String> params = <String, String>{'subject': 'Góp ý'};

      final String? encodeParam = evoUtilFunction.encodeQueryParameters(params);
      const String expectedString = 'subject=G%C3%B3p%20%C3%BD';
      expect(encodeParam, expectedString);
    });
  });

  group('test function eovFormatCurrency', () {
    setUpAll(() {
      registerCommonUtilFunction(CommonUtilFunction());
    });

    tearDownAll(() {
      unregisterCommonUtilFunction();
    });

    test('currency == null', () {
      final String result = evoUtilFunction.evoFormatCurrency(null);
      expect(result, '-');
    });

    test('currency != null', () {
      final String result = evoUtilFunction.evoFormatCurrency(124);
      expect(result, '${defaultCurrencySymbol}124.00');
    });

    test('currency < 0', () {
      final String result = evoUtilFunction.evoFormatCurrency(-1);
      expect(result, '-${defaultCurrencySymbol}1.00');
    });

    test('currency = 0', () {
      final String result = evoUtilFunction.evoFormatCurrency(0);
      expect(result, '${defaultCurrencySymbol}0.00');
    });

    /// Test currency > 0
    test('currency > 1000', () {
      final String result = evoUtilFunction.evoFormatCurrency(1100);
      expect(result, '${defaultCurrencySymbol}1,100.00');
    });

    test('currency > 1.000.000', () {
      final String result = evoUtilFunction.evoFormatCurrency(4460000);
      expect(result, '${defaultCurrencySymbol}4,460,000.00');
    });
    test('currency > 10.000.000', () {
      final String result = evoUtilFunction.evoFormatCurrency(71850000);
      expect(result, '${defaultCurrencySymbol}71,850,000.00');
    });
    test('currency > 100.000.000', () {
      final String result = evoUtilFunction.evoFormatCurrency(999444555);
      expect(result, '${defaultCurrencySymbol}999,444,555.00');
    });

    test('currency > 1.000.000.000', () {
      final String result = evoUtilFunction.evoFormatCurrency(1554678000);
      expect(result, '₱1,554,678,000.00');
    });
  });

  test('test clearUserInfoAppState function', () {
    final AppState appState = getIt.get<AppState>();

    /// Set login status to AppState
    appState.isUserLogIn = true;
    appState.userInfo.value = const UserInformationEntity();

    expect(appState.isUserLogIn, isTrue);
    expect(appState.userInfo.value, isA<UserInformationEntity>());

    /// clear user info in App State
    evoUtilFunction.clearUserInfoAppState();

    expect(appState.isUserLogIn, false);
    expect(appState.userInfo.value, null);
    expect(appState.changePinStatusNotifier.value, ChangePinStatus.available);
  });

  group('test isCanLogInOnOldDevice function', () {
    late MockEvoLocalStorageHelper mockEvoLocalStorageHelper;

    setUpAll(() {
      mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
      getIt.registerLazySingleton<EvoLocalStorageHelper>(() => mockEvoLocalStorageHelper);

      getIt.registerLazySingleton<JwtHelper>(() => EvoJwtHelperImpl());
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
      getIt.unregister<JwtHelper>();
    });

    test('test isCanLogInOnOldDevice with valid device token', () async {
      /// Expired date:2050-01-12T03:23:59.917Z
      const String liveToken =
          'eyJhbGciOiJIUzI1NiJ9.eyJSb2xlIjoiQWRtaW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtZSI6IkphdmFJblVzZSIsImV4cCI6MjUyNTU3MDYzOSwiaWF0IjoxNjczNDkzODM5fQ.RF3Hc0RaOqpdu4YUXmrXHXM4RWc697vJBwZ7eEVVWgA';

      when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
        return Future<String?>.value(liveToken);
      });

      expect(await evoUtilFunction.isCanLogInOnOldDevice(), true);
    });

    test('test isCanLogInOnOldDevice with device token null', () async {
      when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
        return Future<String?>.value();
      });

      expect(await evoUtilFunction.isCanLogInOnOldDevice(), false);
    });

    test('test isCanLogInOnOldDevice with expired device token', () async {
      /// Expired date: 2000-01-12T03:23:59.917Z
      const String expiredToken =
          'eyJhbGciOiJIUzI1NiJ9.eyJSb2xlIjoiQWRtaW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtZSI6IkphdmFJblVzZSIsImV4cCI6OTQ3NjQ3NDM5LCJpYXQiOjE2NzM0OTM4Mzl9.3C7OiO2CFlUyFV3H-5bJFd_LO7fOPpXwi5IfR0hpFi0';

      when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) {
        return Future<String?>.value(expiredToken);
      });

      expect(await evoUtilFunction.isCanLogInOnOldDevice(), false);
    });
  });

  group('test setNewDeviceId() function', () {
    late EvoLocalStorageHelper evoLocalStorageHelper;

    setUpAll(() {
      evoLocalStorageHelper = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
      registerCommonUtilFunction(CommonUtilFunction());
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
      reset(mockEvoFlutterWrapper);
      unregisterCommonUtilFunction();
    });

    test('verify currentDeviceId and call setNewDeviceId() function on Android', () async {
      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(true);

      await evoUtilFunction.setNewDeviceId();

      ///verify newDeviceId
      final String? newDeviceId = await evoLocalStorageHelper.getDeviceId();

      expect(newDeviceId, null);
    });

    test('verify currentDeviceId and call setNewDeviceId() function on iOS', () async {
      const String deviceId = 'device_id';
      const Map<String, dynamic> iosUtsnameMap = <String, dynamic>{
        'release': '17.0.0',
        'version': 'Darwin Kernel Version 21.0.0',
        'machine': 'iPhone14,2',
        'sysname': 'iOS',
        'nodename': 'iPhone',
      };
      const Map<String, dynamic> iosDeviceInfoMap = <String, dynamic>{
        'name': 'iPhone',
        'model': 'iPhone',
        'utsname': iosUtsnameMap,
        'systemName': 'iOS',
        'isPhysicalDevice': true,
        'systemVersion': '17.0',
        'localizedModel': 'iPhone',
        'identifierForVendor': deviceId,
      };

      when(() => mockEvoFlutterWrapper.isAndroid()).thenReturn(false);

      ///verify deviceId is null
      final String? currentDeviceId = await evoLocalStorageHelper.getDeviceId();

      expect(currentDeviceId, null);

      await evoUtilFunction.setNewDeviceId();

      expect((await commonUtilFunction.getIosInfo()).identifierForVendor, deviceId);

      ///verify newDeviceId
      final String? newDeviceId = await evoLocalStorageHelper.getDeviceId();

      expect(newDeviceId, deviceId);
    });
  });

  group('test deleteAllData() function', () {
    late EvoLocalStorageHelper evoLocalStorageHelper;

    setUpAll(() {
      evoLocalStorageHelper = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
    });

    test('test function deleteAllData()', () async {
      const String deviceId = 'device_id';

      ///init device id
      await evoLocalStorageHelper.setDeviceId(deviceId);
      expect(await evoLocalStorageHelper.getDeviceId(), deviceId);

      await evoUtilFunction.deleteAllData();

      ///verify storage deleted
      expect(await evoLocalStorageHelper.getDeviceId(), null);
    });
  });

  group('test calculateVerticalSpace function', () {
    const double mockScreenWidth = 375;
    const double mockScreenHeight = 812;

    testWidgets('heightPercentage < 0', (WidgetTester tester) async {
      const double heightPercentage = -1;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              expect(
                () => evoUtilFunction.calculateVerticalSpace(
                  context: context,
                  heightPercentage: heightPercentage,
                ),
                throwsAssertionError,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('0 < heightPercentage < 1', (WidgetTester tester) async {
      const double heightPercentage = 0.1;
      const double expectedHeight = 81.2;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = evoUtilFunction.calculateVerticalSpace(
                context: context,
                heightPercentage: heightPercentage,
              );
              expect(
                calculatedSpace,
                expectedHeight,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('heightPercentage == 1', (WidgetTester tester) async {
      const double heightPercentage = 1;
      const double expectedHeight = 812;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = evoUtilFunction.calculateVerticalSpace(
                context: context,
                heightPercentage: heightPercentage,
              );
              expect(
                calculatedSpace,
                expectedHeight,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('heightPercentage > 1', (WidgetTester tester) async {
      const double heightPercentage = 1.1;
      const double expectedHeight = 893.2;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = evoUtilFunction.calculateVerticalSpace(
                context: context,
                heightPercentage: heightPercentage,
              );
              expect(
                calculatedSpace,
                expectedHeight,
              );
              return Container();
            },
          ),
        ),
      );
    });
  });

  group('test calculateHorizontalSpace function', () {
    const double mockScreenWidth = 375;
    const double mockScreenHeight = 812;

    testWidgets('heightPercentage < 0', (WidgetTester tester) async {
      const double widthPercentage = -1;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              expect(
                () => evoUtilFunction.calculateHorizontalSpace(
                  context: context,
                  widthPercentage: widthPercentage,
                ),
                throwsAssertionError,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('0 < heightPercentage < 1', (WidgetTester tester) async {
      const double widthPercentage = 0.1;
      const double expectedWidth = 37.5;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = evoUtilFunction.calculateHorizontalSpace(
                context: context,
                widthPercentage: widthPercentage,
              );
              expect(
                calculatedSpace,
                expectedWidth,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('heightPercentage == 1', (WidgetTester tester) async {
      const double widthPercentage = 1;
      const double expectedWidth = 375;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = evoUtilFunction.calculateHorizontalSpace(
                context: context,
                widthPercentage: widthPercentage,
              );
              expect(
                calculatedSpace,
                expectedWidth,
              );
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('heightPercentage > 1', (WidgetTester tester) async {
      const double widthPercentage = 1.25;
      const double expectedWidth = 468.75;
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(
            size: Size(
              mockScreenWidth,
              mockScreenHeight,
            ),
          ),
          child: Builder(
            builder: (BuildContext context) {
              final double calculatedSpace = evoUtilFunction.calculateHorizontalSpace(
                context: context,
                widthPercentage: widthPercentage,
              );
              expect(
                calculatedSpace,
                expectedWidth,
              );
              return Container();
            },
          ),
        ),
      );
    });
  });

  group('verify getCurrentTime() method', () {
    test('test getCurrentTime() method', () {
      final DateTime currentTime = DateTime.now();
      final DateTime result = evoUtilFunction.getCurrentTime();
      const String dateFormat = 'yyyy-MM-dd HH:mm:ss';

      expect(result.toStringFormat(dateFormat), currentTime.toStringFormat(dateFormat));
    });
  });

  group('verify getCurrentTimeString() method', () {
    test('test getCurrentTimeString() method', () {
      const String dateFormat = 'yyyy-MM-dd HH:mm:ss';
      final String currentTime = DateTime.now().toStringFormat(dateFormat);
      final String result = evoUtilFunction.getCurrentTimeString();
      final String resultFormat = DateTime.parse(result).toStringFormat(dateFormat);

      expect(resultFormat, currentTime);
    });
  });

  group('test function clearAllUserData', () {
    late MockOneSignal mockOneSignal;
    late EvoLocalStorageHelper mockEvoLocalStorageHelper;

    setUpAll(() {
      /// Only use mock [CommonUtilFunction] in this test group
      registerCommonUtilFunction(mockCommonUtilFunction);

      mockOneSignal = testOneSignalExecutable();

      mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
      getIt.registerLazySingleton<EvoLocalStorageHelper>(() => mockEvoLocalStorageHelper);
    });

    tearDown(() {
      reset(mockCommonUtilFunction);
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
      reset(mockOneSignal);

      unregisterCommonUtilFunction();
    });

    test('should clear all user data', () async {
      // Setup
      final MockOneSignal mockOneSignal = testOneSignalExecutable();

      // Configure the mock objects
      when(() => mockEvoLocalStorageHelper.clearAllUserData())
          .thenAnswer((_) => Future<void>.value());
      when(() => mockCommonUtilFunction.clearDataOnTokenInvalid(
              clearAllNotifications: any(named: 'clearAllNotifications')))
          .thenAnswer((_) => Future<void>.value());

      // Call the function
      await evoUtilFunction.clearAllUserData(
        oneSignal: mockOneSignal,
      );

      // Verify the interactions
      verify(() => mockCommonUtilFunction.clearDataOnTokenInvalid(clearAllNotifications: true))
          .called(1);
      verify(() => mockOneSignal.removeExternalUserId()).called(1);
      verify(() => mockEvoLocalStorageHelper.clearAllUserData()).called(1);
    });
  });

  group('Test clearUserDataOnLogout', () {
    late MockOneSignal mockOneSignal;
    late EvoLocalStorageHelper mockEvoLocalStorageHelper;

    setUpAll(() {
      /// Only use mock [CommonUtilFunction] in this test group
      registerCommonUtilFunction(mockCommonUtilFunction);

      mockOneSignal = testOneSignalExecutable();

      mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
      getIt.registerLazySingleton<EvoLocalStorageHelper>(() => mockEvoLocalStorageHelper);

      when(() => mockEvoLocalStorageHelper.clearAllUserData())
          .thenAnswer((_) => Future<void>.value());
      when(() => mockCommonUtilFunction.clearDataOnTokenInvalid(
              clearAllNotifications: any(named: 'clearAllNotifications')))
          .thenAnswer((_) => Future<void>.value());
      when(() => mockEvoLocalStorageHelper.delete(key: any(named: 'key')))
          .thenAnswer((_) => Future<void>.value());
      when(() => mockEvoLocalStorageHelper.setBiometricAuthenticator(any()))
          .thenAnswer((_) => Future<void>.value());
    });

    tearDown(() {
      reset(mockCommonUtilFunction);
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
      reset(mockOneSignal);
      unregisterCommonUtilFunction();
    });

    test('should clear user data on logout', () async {
      // Call the function
      await evoUtilFunction.clearUserDataOnLogout(
        oneSignal: mockOneSignal,
      );

      // Verify the interactions
      verify(() => mockCommonUtilFunction.clearDataOnTokenInvalid(clearAllNotifications: true))
          .called(1);
      verify(() => mockOneSignal.removeExternalUserId()).called(1);
      verify(() =>
              mockEvoLocalStorageHelper.delete(key: EvoSecureStorageHelperImpl.biometricTokenKey))
          .called(1);
      verify(() => mockEvoLocalStorageHelper.setBiometricAuthenticator(false)).called(1);
    });
  });

  group('Test clearNotificationData', () {
    const MethodChannel channel = MethodChannel('OneSignal');
    final List<MethodCall> log = <MethodCall>[];
    late MockOneSignal mockOneSignal;

    setUpAll(() {
      registerCommonUtilFunction(mockCommonUtilFunction);

      when(
        () => mockCommonUtilFunction.clearDataOnTokenInvalid(
          clearAllNotifications: any(named: 'clearAllNotifications'),
        ),
      ).thenAnswer((_) => Future<void>.value());

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger.setMockMethodCallHandler(
        channel,
        (MethodCall methodCall) async {
          log.add(methodCall);
          if (methodCall.method == 'OneSignal#removeExternalUserId') {
            return <dynamic, dynamic>{};
          }
          return null;
        },
      );

      mockOneSignal = testOneSignalExecutable();
    });

    tearDown(() {
      log.clear();
    });

    tearDownAll(() {
      unregisterCommonUtilFunction();
      reset(mockOneSignal);
      reset(mockCommonUtilFunction);
    });

    test('Default params', () async {
      await evoUtilFunction.clearNotificationData();

      verify(() => mockCommonUtilFunction.clearDataOnTokenInvalid(clearAllNotifications: true))
          .called(1);

      expect(log.length, 1);
      expect(log[0].method, 'OneSignal#removeExternalUserId');
    });

    test('Custom params', () async {
      await evoUtilFunction.clearNotificationData(
        oneSignal: mockOneSignal,
        clearAllNotifications: false,
      );

      verifyNever(
          () => mockCommonUtilFunction.clearDataOnTokenInvalid(clearAllNotifications: true));

      verify(() => mockOneSignal.removeExternalUserId()).called(1);
    });
  });

  group('Test clearBiometricAuthenticationData', () {
    late EvoLocalStorageHelper mockEvoLocalStorageHelper;

    setUpAll(() {
      mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
      getIt.registerLazySingleton<EvoLocalStorageHelper>(() => mockEvoLocalStorageHelper);

      when(() => mockEvoLocalStorageHelper.setBiometricAuthenticator(any()))
          .thenAnswer((_) => Future<void>.value());
      when(() => mockEvoLocalStorageHelper.delete(key: any(named: 'key')))
          .thenAnswer((_) => Future<void>.value());
    });

    tearDownAll(() {
      getIt.unregister<EvoLocalStorageHelper>();
      reset(mockEvoLocalStorageHelper);
    });

    test('should clear biometric authentication data', () async {
      await evoUtilFunction.clearBiometricAuthenticationData();

      verify(() => mockEvoLocalStorageHelper.setBiometricAuthenticator(false)).called(1);
      verify(() =>
              mockEvoLocalStorageHelper.delete(key: EvoSecureStorageHelperImpl.biometricTokenKey))
          .called(1);
    });
  });

  group('Test openAuthenticationScreen', () {
    late EvoLocalStorageHelper mockEvoLocalStorageHelper;
    late JwtHelper mockJwtHelper;
    late CommonNavigator mockNavigator;
    late MockContext mockNavigatorContext;

    setUpAll(() {
      mockEvoLocalStorageHelper = MockEvoLocalStorageHelper();
      getIt.registerLazySingleton<EvoLocalStorageHelper>(() => mockEvoLocalStorageHelper);
      getIt.registerLazySingleton<JwtHelper>(() => MockJWTHelper());
      getIt.registerLazySingleton<CommonNavigator>(() => MockEvoNavigator());

      mockNavigatorContext = MockContext();
      setUpMockGlobalKeyProvider(mockNavigatorContext);

      registerFallbackValue(mockNavigatorContext);

      mockNavigator = getIt.get<CommonNavigator>();
      mockJwtHelper = getIt.get<JwtHelper>();
    });

    setUp(() {
      when(() => mockEvoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => null);
      when(() => mockEvoLocalStorageHelper.clearAllUserData()).thenAnswer((_) async {});
      when(() => mockNavigator.pushNamed(any(), any(), extra: any(named: 'extra')))
          .thenAnswer((_) async => {});
      when(() => mockNavigator.goNamed(any(), any(), extra: any(named: 'extra')))
          .thenAnswer((_) async => {});
    });

    tearDownAll(() {
      getIt.unregister<JwtHelper>();
      getIt.unregister<EvoLocalStorageHelper>();
      getIt.unregister<CommonNavigator>();
      getIt.unregister<GlobalKeyProvider>();
    });

    tearDown(() {
      reset(mockNavigatorContext);
      reset(mockNavigator);
    });

    group('Test navigate LoginOnOldDeviceScreen', () {
      setUp(() {
        when(() => mockJwtHelper.isCanUse(any(),
            bufferTimeExpiredInSec: any(named: 'bufferTimeExpiredInSec'))).thenReturn(false);
      });

      test('should pushNamed LoginOnOldDeviceScreen with isClearNavigationStack = false', () async {
        await evoUtilFunction.openAuthenticationScreen();
        verify(() => mockNavigator.pushNamed(any(), Screen.verifyUsernameScreen.name,
            extra: any(named: 'extra'))).called(1);
        verify(() => mockEvoLocalStorageHelper.clearAllUserData()).called(1);
      });

      test('should goNamed LoginOnOldDeviceScreen with isClearNavigationStack = true', () async {
        await evoUtilFunction.openAuthenticationScreen(isClearNavigationStack: true);
        verify(() => mockNavigator.goNamed(any(), Screen.verifyUsernameScreen.name,
            extra: any(named: 'extra'))).called(1);
        verify(() => mockEvoLocalStorageHelper.clearAllUserData()).called(1);
      });
    });

    group('Test navigate LoginOnOldDeviceScreen', () {
      setUp(() {
        when(() => mockJwtHelper.isCanUse(any(),
            bufferTimeExpiredInSec: any(named: 'bufferTimeExpiredInSec'))).thenReturn(true);
      });

      test('should pushNamed LoginOnOldDeviceScreen with isClearNavigationStack = false', () async {
        await evoUtilFunction.openAuthenticationScreen();
        verify(() => mockNavigator.pushNamed(any(), Screen.previousLogInScreen.name,
            extra: any(named: 'extra'))).called(1);
      });

      test('should  goNamed LoginOnOldDeviceScreen with isClearNavigationStack = true', () async {
        await evoUtilFunction.openAuthenticationScreen(isClearNavigationStack: true);
        verify(() => mockNavigator.goNamed(any(), Screen.previousLogInScreen.name,
            extra: any(named: 'extra'))).called(1);
      });
    });
  });

  group('Test openInAppWebView', () {
    late MockContext mockNavigatorContext;
    const String title = 'mock-title';
    const String url = 'mock-url';

    setUpAll(() {
      getIt.registerLazySingleton<CommonNavigator>(() => MockEvoNavigator());
      mockNavigatorContext = MockContext();
      setUpMockGlobalKeyProvider(mockNavigatorContext);
    });

    tearDownAll(() {
      getIt.unregister<CommonNavigator>();
      getIt.unregister<GlobalKeyProvider>();
    });

    test('should navigate to common WebView with correct data', () {
      evoUtilFunction.openInAppWebView(title: title, url: url);

      final dynamic arg = verify(() => mockNavigatorContext.pushNamed(
            CommonScreen.webViewPage.name,
            extra: captureAny(named: 'extra'),
          )).captured.first;

      expect(
          arg,
          isA<CommonWebViewArg>().having((CommonWebViewArg arg) => (arg.title, arg.url),
              'verify extra title, url', (title, url)));
    });
  });

  group('clearDataOnTokenInvalid()', () {
    setUpAll(() {
      registerCommonUtilFunction(mockCommonUtilFunction);
    });

    tearDownAll(() {
      reset(mockCommonUtilFunction);
      unregisterCommonUtilFunction();
    });

    test('should call common clearDataOnTokenInvalid', () async {
      when(() => mockCommonUtilFunction.clearDataOnTokenInvalid()).thenAnswer((_) async {});

      await evoUtilFunction.clearDataOnTokenInvalid();

      verify(() => mockCommonUtilFunction.clearDataOnTokenInvalid()).called(1);
    });
  });

  group('showDialogBottomSheet()', () {
    setUpAll(() {
      registerCommonUtilFunction(MockCommonUtilFunction());
      setUpMockGlobalKeyProvider(MockContext());
      registerFallbackValue(MockContext());

      when(() => commonUtilFunction.showDialogBottomSheet(
            any(),
            textPositive: any(named: 'textPositive'),
            content: any(named: 'content'),
            title: any(named: 'title'),
            textNegative: any(named: 'textNegative'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            header: any(named: 'header'),
            headerPadding: any(named: 'headerPadding'),
            isDismissible: any(named: 'isDismissible'),
            positiveButtonStyle: any(named: 'positiveButtonStyle'),
            negativeButtonStyle: any(named: 'negativeButtonStyle'),
            titleTextStyle: any(named: 'titleTextStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickClose: any(named: 'onClickClose'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            buttonClose: any(named: 'buttonClose'),
            dialogId: any(named: 'dialogId'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            loggingEventMetaData: any(named: 'loggingEventMetaData'),
            loggingEventOnShowMetaData: any(named: 'loggingEventOnShowMetaData'),
          )).thenAnswer((_) async {});
    });

    tearDownAll(() {
      unregisterCommonUtilFunction();
      getIt.unregister<GlobalKeyProvider>();
    });

    test('should pass properties to CommonUtilFunction', () async {
      const String textPositive = 'Confirm';
      const EvoDialogId dialogId = EvoDialogId.common;
      const String content = 'This is a test content';
      const String title = 'Test Title';
      const String textNegative = 'Cancel';
      final Widget footer = Container();
      onClickPositive() {}
      onClickNegative() {}
      final Widget header = Container();
      final EdgeInsets headerPadding = EdgeInsets.all(8.0);
      const bool isDismissible = false;
      final ButtonStyle positiveButtonStyle = ButtonStyle();
      final ButtonStyle negativeButtonStyle = ButtonStyle();
      final TextStyle titleTextStyle = TextStyle();
      final TextStyle contentTextStyle = TextStyle();
      onClickClose() {}
      const bool isShowButtonClose = true;
      final Widget buttonClose = Container();
      final Map<String, dynamic> loggingEventMetaData = {'key': 'value'};
      final Map<String, dynamic> loggingEventOnShowMetaData = {'key': 'value'};
      const ButtonListOrientation buttonListOrientation = ButtonListOrientation.verticalDown;

      await evoUtilFunction.showDialogBottomSheet(
        textPositive: textPositive,
        dialogId: dialogId,
        content: content,
        title: title,
        textNegative: textNegative,
        footer: footer,
        onClickPositive: onClickPositive,
        onClickNegative: onClickNegative,
        header: header,
        headerPadding: headerPadding,
        isDismissible: isDismissible,
        positiveButtonStyle: positiveButtonStyle,
        negativeButtonStyle: negativeButtonStyle,
        titleTextStyle: titleTextStyle,
        contentTextStyle: contentTextStyle,
        onClickClose: onClickClose,
        isShowButtonClose: isShowButtonClose,
        buttonClose: buttonClose,
        loggingEventMetaData: loggingEventMetaData,
        loggingEventOnShowMetaData: loggingEventOnShowMetaData,
        buttonListOrientation: buttonListOrientation,
      );

      verify(() => commonUtilFunction.showDialogBottomSheet(
            any(),
            textPositive: textPositive,
            content: content,
            title: title,
            textNegative: textNegative,
            footer: footer,
            onClickPositive: onClickPositive,
            onClickNegative: onClickNegative,
            header: header,
            headerPadding: headerPadding,
            isDismissible: isDismissible,
            positiveButtonStyle: positiveButtonStyle,
            negativeButtonStyle: negativeButtonStyle,
            titleTextStyle: titleTextStyle,
            contentTextStyle: contentTextStyle,
            onClickClose: onClickClose,
            isShowButtonClose: isShowButtonClose,
            buttonClose: buttonClose,
            dialogId: dialogId.id,
            buttonListOrientation: buttonListOrientation,
            loggingEventMetaData: loggingEventMetaData,
            loggingEventOnShowMetaData: loggingEventOnShowMetaData,
          )).called(1);
    });

    test('should pass default ButtonStyle to CommonUtilFunction if not provided', () async {
      const String textPositive = 'Confirm';
      const EvoDialogId dialogId = EvoDialogId.common;
      const String content = 'This is a test content';
      const String title = 'Test Title';
      const String textNegative = 'Cancel';
      final Widget footer = Container();
      onClickPositive() {}
      onClickNegative() {}
      final Widget header = Container();
      final EdgeInsets headerPadding = EdgeInsets.all(8.0);
      const bool isDismissible = false;
      final TextStyle titleTextStyle = TextStyle();
      final TextStyle contentTextStyle = TextStyle();
      onClickClose() {}
      const bool isShowButtonClose = true;
      final Widget buttonClose = Container();
      final Map<String, dynamic> loggingEventMetaData = {'key': 'value'};
      final Map<String, dynamic> loggingEventOnShowMetaData = {'key': 'value'};
      const ButtonListOrientation buttonListOrientation = ButtonListOrientation.verticalDown;

      await evoUtilFunction.showDialogBottomSheet(
        textPositive: textPositive,
        dialogId: dialogId,
        content: content,
        title: title,
        textNegative: textNegative,
        footer: footer,
        onClickPositive: onClickPositive,
        onClickNegative: onClickNegative,
        header: header,
        headerPadding: headerPadding,
        isDismissible: isDismissible,
        titleTextStyle: titleTextStyle,
        contentTextStyle: contentTextStyle,
        onClickClose: onClickClose,
        isShowButtonClose: isShowButtonClose,
        buttonClose: buttonClose,
        loggingEventMetaData: loggingEventMetaData,
        loggingEventOnShowMetaData: loggingEventOnShowMetaData,
        buttonListOrientation: buttonListOrientation,
      );

      verify(() => commonUtilFunction.showDialogBottomSheet(
            any(),
            textPositive: any(named: 'textPositive'),
            content: any(named: 'content'),
            title: any(named: 'title'),
            textNegative: any(named: 'textNegative'),
            footer: any(named: 'footer'),
            onClickPositive: any(named: 'onClickPositive'),
            onClickNegative: any(named: 'onClickNegative'),
            header: any(named: 'header'),
            headerPadding: any(named: 'headerPadding'),
            isDismissible: any(named: 'isDismissible'),
            positiveButtonStyle: any(named: 'positiveButtonStyle', that: isA<ButtonStyle>()),
            negativeButtonStyle: captureAny(named: 'negativeButtonStyle', that: isA<ButtonStyle>()),
            titleTextStyle: any(named: 'titleTextStyle'),
            contentTextStyle: any(named: 'contentTextStyle'),
            onClickClose: any(named: 'onClickClose'),
            isShowButtonClose: any(named: 'isShowButtonClose'),
            buttonClose: any(named: 'buttonClose'),
            dialogId: any(named: 'dialogId'),
            buttonListOrientation: any(named: 'buttonListOrientation'),
            loggingEventMetaData: any(named: 'loggingEventMetaData'),
            loggingEventOnShowMetaData: any(named: 'loggingEventOnShowMetaData'),
          )).called(1);
    });
  });

  group('enable disable InactiveDetector()', () {
    test('should call enable function on inactive detector controller', () {
      // Arrange
      bool enableCalled = false;
      final InactiveDetectorController controllerWithFunction = InactiveDetectorController();
      controllerWithFunction.enable = () {
        enableCalled = true;
      };

      getIt.get<AppState>().inactiveDetectorController = controllerWithFunction;

      // Act
      evoUtilFunction.enableInactiveDetector();

      // Assert
      expect(enableCalled, isTrue);
    });

    test('should call disable function on inactive detector controller', () {
      // Arrange
      bool disableCalled = false;
      final InactiveDetectorController controllerWithFunction = InactiveDetectorController();
      controllerWithFunction.disable = () {
        disableCalled = true;
      };

      getIt.get<AppState>().inactiveDetectorController = controllerWithFunction;

      // Act
      evoUtilFunction.disableInactiveDetector();

      // Assert
      expect(disableCalled, isTrue);
    });
  });
}
