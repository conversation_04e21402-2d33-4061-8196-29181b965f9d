import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/widget/custom_snackbar_widget.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'flutter_test_config.dart';

class TestSnackBarWrapper extends SnackBarWrapper {
  bool hasCallCancelSnackBar = false;
  double fakeMarginBottomRatio = 20.0;

  @override
  void cancelSnackBar() {
    super.cancelSnackBar();
    hasCallCancelSnackBar = true;
  }

  @override
  double? getMarginBottom({double? marginBottomRatio, FlutterView? view}) {
    return fakeMarginBottomRatio;
  }
}

void main() {
  late CommonImageProvider commonImageProvider;

  late TestSnackBarWrapper testSnackBarWrapper;
  late EvoSnackBar testEvoSnackBar;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(SnackBarType.success);
    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());

    getItRegisterColor();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterTextStyle();

    commonImageProvider = getIt.get<CommonImageProvider>();

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  setUp(() {
    testSnackBarWrapper = TestSnackBarWrapper();
    testEvoSnackBar = EvoSnackBar(testSnackBarWrapper);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('SnackBarType', () {
    test('should have the correct number of enum values', () {
      const List<SnackBarType> values = SnackBarType.values;
      expect(values.length, 4);
    });

    test('should have a specific order of enum values', () {
      const List<SnackBarType> values = SnackBarType.values;
      expect(values, <SnackBarType>[
        SnackBarType.success,
        SnackBarType.error,
        SnackBarType.neutral,
        SnackBarType.warning,
      ]);
    });
  });

  group('SnackBarDuration', () {
    test('should have the correct number of enum values', () {
      const List<SnackBarDuration> values = SnackBarDuration.values;
      expect(values.length, 3);
    });

    test('should have a specific order of enum values', () {
      const List<SnackBarDuration> values = SnackBarDuration.values;
      expect(values, <SnackBarDuration>[
        SnackBarDuration.short,
        SnackBarDuration.medium,
        SnackBarDuration.long,
      ]);
    });

    test('should have the correct duration values', () {
      expect(SnackBarDuration.short.value, 3000);
      expect(SnackBarDuration.medium.value, 5000);
      expect(SnackBarDuration.long.value, 10000);
    });
  });

  Future<bool?> showSnackBarForTesting({
    required String message,
    required SnackBarType typeSnackBar,
    required String description,
    int? durationInMilliSec,
  }) async {
    return await testEvoSnackBar.show(
      message,
      typeSnackBar: typeSnackBar,
      durationInMilliSec: durationInMilliSec ?? SnackBarDuration.short.value,
      marginBottomRatio: SnackBarWrapper.defaultMarginBottomRatio,
      description: description,
    );
  }

  void verifyParamSnackBar(
    WidgetTester tester, {
    int? durationInMilliSec,
  }) {
    final Finder finderSnackBar = find.byType(SnackBar);
    expect(finderSnackBar, findsOneWidget);
    final SnackBar snackBarWidget = tester.widget(finderSnackBar);
    expect(snackBarWidget.backgroundColor, Colors.transparent);
    expect(snackBarWidget.behavior, SnackBarBehavior.floating);
    expect(snackBarWidget.dismissDirection, DismissDirection.none);
    expect(snackBarWidget.padding, const EdgeInsets.symmetric(horizontal: 20, vertical: 12));
    expect(snackBarWidget.margin, const EdgeInsets.only(bottom: 20.0));
    expect(snackBarWidget.content, isA<CustomSnackBarWidget>());
    expect(snackBarWidget.duration,
        Duration(milliseconds: durationInMilliSec ?? SnackBarDuration.long.value));
  }

  void verifyParamCustomSnackBar(
    WidgetTester tester, {
    required String message,
    required Color background,
    required Color borderColor,
    required String leadingIconName,
    String? description,
  }) {
    final Finder finderCustomSnackBar = find.byType(CustomSnackBarWidget);
    expect(finderCustomSnackBar, findsOneWidget);
    expect(find.text(message), findsOneWidget);
    final CustomSnackBarWidget customSnackBarWidget = tester.widget(finderCustomSnackBar);
    expect(customSnackBarWidget.text, message);
    expect(customSnackBarWidget.background, background);
    expect(customSnackBarWidget.borderColor, borderColor);

    verify(() => commonImageProvider.asset(
          leadingIconName,
          width: 20,
          height: 20,
        )).called(1);

    expect(customSnackBarWidget.description, description);

    customSnackBarWidget.onClose?.call();
    expect(testSnackBarWrapper.hasCallCancelSnackBar, true);
  }

  group('verify EvoSnackBar', () {
    test('verify defaultMarginBottomRatio', () {
      expect(SnackBarWrapper.defaultMarginBottomRatio, 0.08);
    });

    test('verify setEnable', () {
      expect(testEvoSnackBar.enable, true);

      testEvoSnackBar.setEnable = false;
      expect(testEvoSnackBar.enable, false);

      testEvoSnackBar.setEnable = true;
      expect(testEvoSnackBar.enable, true);
    });

    testWidgets('verify show() with type = error', (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_error';
      const String fakeDescriptionSnackBar = 'fake_description_error';

      await tester.pumpWidget(MaterialApp(
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  showSnackBarForTesting(
                    message: fakeMessageSnackBar,
                    typeSnackBar: SnackBarType.error,
                    description: fakeDescriptionSnackBar,
                  );
                },
                child: const Text('Show SnackBar'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('Show SnackBar'));
      await tester.pump();

      // Verify SnackBar
      verifyParamSnackBar(tester, durationInMilliSec: SnackBarDuration.short.value);

      // Verify CustomSnackBarWidget
      verifyParamCustomSnackBar(
        tester,
        message: fakeMessageSnackBar,
        background: evoColors.error70,
        borderColor: evoColors.error70,
        leadingIconName: EvoImages.icSnackBarError,
        description: fakeDescriptionSnackBar,
      );
    });

    testWidgets('verify show() with type = success', (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_success';
      const String fakeDescriptionSnackBar = 'fake_description_success';

      await tester.pumpWidget(MaterialApp(
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  showSnackBarForTesting(
                    message: fakeMessageSnackBar,
                    typeSnackBar: SnackBarType.success,
                    description: fakeDescriptionSnackBar,
                  );
                },
                child: const Text('Show SnackBar'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('Show SnackBar'));
      await tester.pump();

      // Verify SnackBar
      verifyParamSnackBar(tester, durationInMilliSec: SnackBarDuration.short.value);

      // Verify CustomSnackBarWidget
      verifyParamCustomSnackBar(
        tester,
        message: fakeMessageSnackBar,
        background: evoColors.positive70,
        borderColor: evoColors.positive70,
        leadingIconName: EvoImages.icSnackBarSuccess,
        description: fakeDescriptionSnackBar,
      );
    });

    testWidgets('verify show() with type = warning', (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_warning';
      const String fakeDescriptionSnackBar = 'fake_description_warning';

      await tester.pumpWidget(MaterialApp(
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  showSnackBarForTesting(
                    message: fakeMessageSnackBar,
                    typeSnackBar: SnackBarType.warning,
                    description: fakeDescriptionSnackBar,
                  );
                },
                child: const Text('Show SnackBar'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('Show SnackBar'));
      await tester.pump();

      // Verify SnackBar
      verifyParamSnackBar(tester, durationInMilliSec: SnackBarDuration.short.value);

      // Verify CustomSnackBarWidget
      verifyParamCustomSnackBar(
        tester,
        message: fakeMessageSnackBar,
        background: evoColors.warning70,
        borderColor: evoColors.warning70,
        leadingIconName: EvoImages.icSnackBarWarning,
        description: fakeDescriptionSnackBar,
      );
    });

    testWidgets('verify show() with type = neutral', (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_neutral';
      const String fakeDescriptionSnackBar = 'fake_description_neutral';

      await tester.pumpWidget(MaterialApp(
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  showSnackBarForTesting(
                    message: fakeMessageSnackBar,
                    typeSnackBar: SnackBarType.neutral,
                    description: fakeDescriptionSnackBar,
                  );
                },
                child: const Text('Show SnackBar'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('Show SnackBar'));
      await tester.pump();

      // Verify SnackBar
      verifyParamSnackBar(tester, durationInMilliSec: SnackBarDuration.short.value);

      // Verify CustomSnackBarWidget
      verifyParamCustomSnackBar(
        tester,
        message: fakeMessageSnackBar,
        background: evoColors.info70,
        borderColor: evoColors.info70,
        leadingIconName: EvoImages.icSnackBarNeutral,
        description: fakeDescriptionSnackBar,
      );
    });

    testWidgets('verify show() with duplicate message', (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_success';
      const String fakeDescriptionSnackBar = 'fake_description_success';
      final int durationInMilliSec = SnackBarDuration.short.value;

      await tester.runAsync(() async {
        bool? valueShowSnackBar = false;
        await tester.pumpWidget(MaterialApp(
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return GestureDetector(
                  onTap: () async {
                    valueShowSnackBar = await showSnackBarForTesting(
                      message: fakeMessageSnackBar,
                      typeSnackBar: SnackBarType.success,
                      description: fakeDescriptionSnackBar,
                      durationInMilliSec: durationInMilliSec,
                    );
                  },
                  child: const Text('Show SnackBar'),
                );
              },
            ),
          ),
        ));

        await tester.tap(find.text('Show SnackBar'));
        await tester.pump();

        expect(valueShowSnackBar, true);
        final Finder finderSnackBar = find.byType(SnackBar);
        expect(finderSnackBar, findsOneWidget);
        final SnackBar snackBarWidget = tester.widget(finderSnackBar);
        expect(snackBarWidget.duration, Duration(milliseconds: durationInMilliSec));

        // Delay for checking duplicate message
        await Future<void>.delayed(const Duration(milliseconds: 1000));

        // continue to tap
        await tester.tap(find.text('Show SnackBar'));
        await tester.pump();

        expect(valueShowSnackBar, false);

        // Delay for checking duplicate message
        await Future<void>.delayed(Duration(milliseconds: durationInMilliSec));

        // continue to tap
        await tester.tap(find.text('Show SnackBar'));
        await tester.pump();

        expect(valueShowSnackBar, true);
      });
    });

    testWidgets('verify show() after the user pressed close button on SnackBar',
        (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_success';
      const String fakeDescriptionSnackBar = 'fake_description_success';
      final int durationInMilliSec = SnackBarDuration.short.value;

      await tester.runAsync(() async {
        bool? valueShowSnackBar = false;
        await tester.pumpWidget(MaterialApp(
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return GestureDetector(
                  onTap: () async {
                    valueShowSnackBar = await showSnackBarForTesting(
                      message: fakeMessageSnackBar,
                      typeSnackBar: SnackBarType.success,
                      description: fakeDescriptionSnackBar,
                      durationInMilliSec: durationInMilliSec,
                    );
                  },
                  child: const Text('Show SnackBar'),
                );
              },
            ),
          ),
        ));

        await tester.tap(find.text('Show SnackBar'));
        await tester.pump();

        expect(valueShowSnackBar, true);
        final Finder finderSnackBar = find.byType(SnackBar);
        expect(finderSnackBar, findsOneWidget);
        final SnackBar snackBarWidget = tester.widget(finderSnackBar);
        expect(snackBarWidget.duration, Duration(milliseconds: durationInMilliSec));

        // verify close SnackBar
        final Finder finderCustomSnackBar = find.byType(CustomSnackBarWidget);
        expect(finderCustomSnackBar, findsOneWidget);
        final CustomSnackBarWidget customSnackBar = tester.widget(finderCustomSnackBar);
        customSnackBar.onClose?.call();

        await tester.tap(find.text('Show SnackBar'));
        await tester.pump();

        expect(valueShowSnackBar, true);
      });
    });

    group('shouldShowSnackBar()', () {
      final EvoSnackBar snackBar = EvoSnackBar(SnackBarWrapper());

      test('should return true if snackBar is enabled', () {
        snackBar.enable = true;
        expect(snackBar.shouldShowSnackBar('message'), true);
      });

      test('should return false if snackBar is disabled', () {
        snackBar.enable = false;
        expect(snackBar.shouldShowSnackBar('message'), false);
      });

      test('should return false when the same message is shown in a short time', () async {
        snackBar
          ..enable = true
          ..latestToastMessage = 'mes'
          ..latestTimeShowToast = DateTime.now();
        await Future<void>.delayed(const Duration(milliseconds: 10));
        expect(snackBar.shouldShowSnackBar('mes'), false);
      });

      test('should return true when the interval between 2 messages is long enough', () async {
        snackBar
          ..enable = true
          ..latestToastMessage = 'mes'
          ..latestTimeShowToast = DateTime.now();
        await Future<void>.delayed(const Duration(milliseconds: 10));
        expect(snackBar.shouldShowSnackBar('mes', minIntervalInSec: 9), true);
      });
    });
  });

  group('getMarginBottom()', () {
    late GlobalKeyProvider provider;
    late SnackBarWrapper wrapper;

    setUpAll(() async {
      await getIt.unregister<GlobalKeyProvider>();
      provider = getIt.registerSingleton(MockGlobalKeyProvider());
    });

    setUp(() {
      wrapper = SnackBarWrapper();
    });

    test('should return null when view is null AND context is null', () {
      when(() => provider.navigatorContext).thenReturn(null);
      final double? marginBottom = wrapper.getMarginBottom(view: null);
      expect(marginBottom, null);
    });

    testWidgets('should use view height and provided margin bottom ratio to calculate',
        (WidgetTester tester) async {
      final double viewHeight = 100;
      tester.view.physicalSize = Size(100, viewHeight);
      tester.view.devicePixelRatio = 1;
      addTearDown(tester.view.reset);

      await tester.pumpWidget(SizedBox());
      final BuildContext context = tester.element(find.byType(SizedBox));
      when(() => provider.navigatorContext).thenReturn(context);

      final double ratio = 2;
      final double? result = wrapper.getMarginBottom(marginBottomRatio: ratio);

      expect(result, viewHeight * ratio);
    });

    testWidgets('should use view height and default margin bottom ratio to calculate',
        (WidgetTester tester) async {
      final double viewHeight = 100;
      tester.view.physicalSize = Size(50, viewHeight);
      tester.view.devicePixelRatio = 1;
      addTearDown(tester.view.reset);

      await tester.pumpWidget(SizedBox());
      final BuildContext context = tester.element(find.byType(SizedBox));
      when(() => provider.navigatorContext).thenReturn(context);

      final double? result = wrapper.getMarginBottom();

      expect(result, viewHeight * SnackBarWrapper.defaultMarginBottomRatio);
    });
  });
}
