import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/uuid/uuid_generator_impl.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockUuid extends Mock implements Uuid {}

void main() {
  late UUIDGeneratorImpl uuidGeneratorImpl;
  late MockUuid mockUUID;

  const String mockUuidV4Value = 'uuid_v4';

  setUpAll(() {
    mockUUID = MockUuid();
    uuidGeneratorImpl = UUIDGeneratorImpl(mockUUID);

    // setup mock UUID Wrapper
    when(() => mockUUID.v4()).thenReturn(mockUuidV4Value);
  });

  test('should_genV4()_work_correctly', () {
    final String uuid = uuidGeneratorImpl.genV4();

    verify(() => mockUUID.v4()).called(1);
    expect(uuid, mockUuidV4Value);
  });
}
