import 'dart:math';

import 'package:evoapp/util/screen_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// NOTE: DO NOT change the order of the tests.
/// The tests are ordered in a way that the ScreenUtil is first tested without init
/// and then tested with init to ensure that the ScreenUtil is correctly initialized.
/// because the ScreenUtil is a singleton class, the tests are ordered in a way that
void main() {
  late ScreenUtil screenUtil;

  setUpAll(() {
    screenUtil = ScreenUtil();
  });


  group('ScreenUtil without init', () {
    test('returns original value without init', () {
      screenUtil = ScreenUtil();
      expect(screenUtil.getHeight(100), 100);
      expect(screenUtil.getWidth(100), 100);
      expect(screenUtil.getTextScale(16), 16);
    });
  });


  group('ScreenUtil with init', () {
    const Size designSize = Size(375, 812);
    const Size screenSize = Size(750, 1624);

    testWidgets('init method initializes screen dimensions and scale factors',
        (WidgetTester tester) async {
      // Use MediaQuery to simulate the screen size in the test environment
      await tester.pumpWidget(
        MediaQuery(
          data: const MediaQueryData(size: screenSize), // Provide the desired screen size
          child: Builder(
            builder: (BuildContext context) {
              screenUtil.init(context: context, design: designSize);
              return MaterialApp(
                home: Scaffold(
                  body: Container(),
                ),
              );
            },
          ),
        ),
      );

      // Verify that the screen dimensions and scale factors are correctly initialized
      expect(screenUtil.screenWidth, screenSize.width);
      expect(screenUtil.screenHeight, screenSize.height);
      expect(screenUtil.scaleWidth, screenSize.width / designSize.width);
      expect(screenUtil.scaleHeight, screenSize.height / designSize.height);
      expect(screenUtil.textScaleFactor,
          min(screenSize.width / designSize.width, screenSize.height / designSize.height));
    });

    test('getHeight returns correct scaled height', () {
      expect(screenUtil.getHeight(100), 100 * screenUtil.scaleHeight);
      expect(100.h, 100 * screenUtil.scaleHeight);
    });

    test('getWidth returns correct scaled width', () {
      expect(screenUtil.getWidth(100), 100 * screenUtil.scaleWidth);
      expect(100.w, 100 * screenUtil.scaleWidth);
    });

    test('getTextScale returns correct scaled font size', () {
      expect(screenUtil.getTextScale(16), 16 * screenUtil.textScaleFactor);
      expect(16.sp, 16 * screenUtil.textScaleFactor);
    });
  });
}
