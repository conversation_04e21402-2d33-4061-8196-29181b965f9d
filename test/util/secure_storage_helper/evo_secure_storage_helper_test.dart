import 'dart:ui';

import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper_impl.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../flutter_test_config.dart';

class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}

/// Class for testing [EvoSecureStorageHelperImpl]
/// And by pass the [containsKey] method
class TestEvoSecureStorageHelperImpl extends EvoSecureStorageHelperImpl {
  TestEvoSecureStorageHelperImpl({required super.secureStorage});

  @override
  Future<bool> containsKey({required String key}) async {
    return true;
  }
}

void main() {
  late FlutterSecureStorage secureStorage;
  late EvoSecureStorageHelperImpl dataSource;

  setUpAll(() {
    secureStorage = testFlutterSecureStorageExecutable();
    dataSource = testEvoSecureStorageHelperExecutable(secureStorage: secureStorage);
  });

  tearDownAll(() => getIt.reset());

  Future<void> testKey<T>(
    String key,
    T value,
    Future<void> Function(T value) set,
    Future<T?> Function() get,
  ) async {
    await secureStorage.delete(key: key);
    expect(await secureStorage.containsKey(key: key), isFalse);
    expect(dataSource.memoryData.containsKey(key), isFalse);

    await set(value);
    expect(await secureStorage.containsKey(key: key), isTrue);
    expect(dataSource.memoryData[key], value.toString());

    final T? getValue = await get();
    expect(getValue, value);
    expect(dataSource.memoryData[key], value.toString());
  }

  test('verify constant key', () {
    expect(EvoSecureStorageHelperImpl.selectedLanguageCodeKey, 'selected_language_code');
    expect(EvoSecureStorageHelperImpl.latestVersionIgnore, 'latest_version_ignore');
    expect(EvoSecureStorageHelperImpl.deviceId, 'device_id');
    expect(EvoSecureStorageHelperImpl.usernameKey, 'username');
    expect(EvoSecureStorageHelperImpl.biometricTokenKey, 'biometric_token');
    expect(EvoSecureStorageHelperImpl.deviceTokenKey, 'device_token');
    expect(EvoSecureStorageHelperImpl.enableBiometricAuthenticatorKey,
        'enable_biometric_authenticator');
    expect(EvoSecureStorageHelperImpl.timeShowBiometricActiveKey, 'time_show_biometric_active');
    expect(EvoSecureStorageHelperImpl.isNewDeviceKey, 'new_device');
    expect(EvoSecureStorageHelperImpl.userFullNameKey, 'user_full_name');
    expect(EvoSecureStorageHelperImpl.isGrantedBiometricPermissionKey,
        'is_granted_biometric_permission');
  });

  test('should set/get NewDevice correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.isNewDeviceKey,
      true,
      dataSource.setNewDevice,
      dataSource.isNewDevice,
    );
  });

  test('should set/get timeShowBiometricActive correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.timeShowBiometricActiveKey,
      '2024-08-13 22:22:22',
      dataSource.saveTimeShowBiometric,
      dataSource.getTimeShowBiometric,
    );
  });

  test('should set/get userFullName correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.userFullNameKey,
      'userFullName',
      dataSource.setUserFullName,
      dataSource.getUserFullName,
    );
  });

  test('should set/get latestVersionIgnore correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.latestVersionIgnore,
      'latestVersionIgnore',
      dataSource.setLatestVersionIgnore,
      dataSource.getLatestVersionIgnore,
    );
  });

  test('should set/get deviceId correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.deviceId,
      'deviceId',
      dataSource.setDeviceId,
      dataSource.getDeviceId,
    );
  });

  test('should set/get username correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.usernameKey,
      'username',
      dataSource.setUsername,
      dataSource.getUsername,
    );
  });

  test('should set/get biometricToken correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.biometricTokenKey,
      'biometricToken',
      dataSource.setBiometricToken,
      dataSource.getBiometricToken,
    );
  });

  test('should set/get enableBiometricAuthenticator correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.enableBiometricAuthenticatorKey,
      true,
      dataSource.setBiometricAuthenticator,
      dataSource.isEnableBiometricAuthenticator,
    );
  });

  test('should set/get deviceToken correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.deviceTokenKey,
      'deviceToken',
      dataSource.setDeviceToken,
      dataSource.getDeviceToken,
    );
  });

  test('should set/get language correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.selectedLanguageCodeKey,
      Locale('vi', ''),
      (Locale locale) => dataSource.setLocale(locale.languageCode),
      dataSource.getLocale,
    );
  });

  test('should set/get biometric permission correct', () async {
    await testKey(
      EvoSecureStorageHelperImpl.isGrantedBiometricPermissionKey,
      true,
      (bool value) => dataSource.setDidAllowBiometricPermission(),
      dataSource.didAllowBiometricPermission,
    );
  });

  group('verify clearAllUserData()', () {
    final MockFlutterSecureStorage secureStorage = MockFlutterSecureStorage();
    late TestEvoSecureStorageHelperImpl dataSource;

    setUp(() {
      dataSource = TestEvoSecureStorageHelperImpl(secureStorage: secureStorage);

      when(() => secureStorage.delete(key: any(named: 'key'))).thenAnswer((_) async {
        return Future<void>.value();
      });
    });

    test('verify call delete()', () async {
      await dataSource.clearAllUserData();

      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.usernameKey)).called(1);
      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.biometricTokenKey))
          .called(1);
      verify(() =>
              secureStorage.delete(key: EvoSecureStorageHelperImpl.enableBiometricAuthenticatorKey))
          .called(1);
      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.timeShowBiometricActiveKey))
          .called(1);
      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.deviceTokenKey)).called(1);
      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.isNewDeviceKey)).called(1);
      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.userFullNameKey)).called(1);
    });
  });

  group('test set/get deleteAllSecureStorageData()', () {
    final MockFlutterSecureStorage secureStorage = MockFlutterSecureStorage();
    late TestEvoSecureStorageHelperImpl dataSource;

    setUp(() {
      dataSource = TestEvoSecureStorageHelperImpl(secureStorage: secureStorage);

      when(() => secureStorage.delete(key: any(named: 'key'))).thenAnswer((_) async {
        return Future<void>.value();
      });
    });

    test('verify deleteAllSecureStorageData()', () async {
      await dataSource.deleteAllSecureStorageData();

      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.usernameKey)).called(1);
      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.biometricTokenKey))
          .called(1);
      verify(() =>
              secureStorage.delete(key: EvoSecureStorageHelperImpl.enableBiometricAuthenticatorKey))
          .called(1);
      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.timeShowBiometricActiveKey))
          .called(1);
      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.deviceTokenKey)).called(1);
      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.isNewDeviceKey)).called(1);

      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.selectedLanguageCodeKey))
          .called(1);
      verify(() => secureStorage.delete(key: EvoSecureStorageHelperImpl.latestVersionIgnore))
          .called(1);
    });
  });
}
