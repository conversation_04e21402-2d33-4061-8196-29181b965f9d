import 'package:evoapp/util/evo_snackbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  final SnackBarWrapper snackBarWrapper = SnackBarWrapper();
  late MockBuildContext mockNavigatorContext;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);
  });

  group('Test function getMarginBottom', () {
    testWidgets(
        'Give customMarginBottom null, screen size = (50, 100), should return margin bottom = 8',
        (WidgetTester tester) async {
      tester.view.physicalSize = const Size(50, 100);
      tester.view.devicePixelRatio = 1.0;

      /// Resets the screen to its original size after the test end
      addTearDown(() => tester.view.resetPhysicalSize());

      final double? result = snackBarWrapper.getMarginBottom(view: tester.view);
      expect(result, 8);
    });

    testWidgets(
        'Give marginBottomRatio = 0.5, screen size = (50, 100), should return margin bottom = 50',
        (WidgetTester tester) async {
      tester.view.physicalSize = const Size(50, 100);
      tester.view.devicePixelRatio = 1.0;

      /// Resets the screen to its original size after the test end
      addTearDown(() => tester.view.resetPhysicalSize());

      final double? result =
          snackBarWrapper.getMarginBottom(marginBottomRatio: 0.5, view: tester.view);
      expect(result, 50);
    });
  });

  test('Test constants', () {
    expect(SnackBarWrapper.defaultMarginBottomRatio, 0.08);
  });
}
