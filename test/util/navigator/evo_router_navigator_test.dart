import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/navigator/evo_router_navigator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/base/page_base.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';
import 'package:mocktail/mocktail.dart';

import '../../feature/page_base_mock.dart';

class GoRouterMock extends Mock implements GoRouter {}

class BuildContextMock extends Mock implements BuildContext {}

class EvoNavigatorTypeFactoryMock extends Mock implements EvoNavigatorTypeFactory {}

class FlutterNavigatorStateMock extends Mock implements NavigatorState {
  @override
  String toString({DiagnosticLevel minLevel = DiagnosticLevel.debug}) {
    return super.toString();
  }
}

class SpyEvoRouterNavigator extends EvoRouterNavigator {
  int popUntilNamedCalled = 0;
  int pushReplacementNamedCalled = 0;

  @override
  void popUntilNamed(BuildContext context, String pageName,
      {Function? onPageFound, Function? onPageNotFound}) {
    popUntilNamedCalled += 1;
    super
        .popUntilNamed(context, pageName, onPageFound: onPageFound, onPageNotFound: onPageNotFound);
  }

  @override
  void pushReplacementNamed(BuildContext context, String pageName, {PageBaseArg? extra}) {
    pushReplacementNamedCalled += 1;
    super.pushReplacementNamed(context, pageName, extra: extra);
  }
}

void main() {
  late EvoRouterNavigator navigator;
  late GoRouterMock goRouterMock;
  late BuildContext context;
  late EvoNavigatorTypeFactoryMock navigatorTypeFactoryMock;
  late FlutterNavigatorStateMock flutterNavigatorStateMock;

  const String pageName = 'pageName';
  final PageBaseArg extra = PageBaseArg();

  setUpAll(() {
    navigatorTypeFactoryMock = EvoNavigatorTypeFactoryMock();
    getIt.registerLazySingleton<EvoNavigatorTypeFactory>(() => navigatorTypeFactoryMock);

    navigator = EvoRouterNavigator();
    goRouterMock = GoRouterMock();
    flutterNavigatorStateMock = FlutterNavigatorStateMock();
    context = BuildContextMock();

    when(() => navigatorTypeFactoryMock.getGoRouter(context)).thenReturn(goRouterMock);

    when(() => navigatorTypeFactoryMock.getFlutterNavigator(context))
        .thenReturn(flutterNavigatorStateMock);
  });

  tearDown(() {
    reset(goRouterMock);
  });

  group('EvoNavigatorTypeFactory', () {
    testWidgets('getGoRouter()', (WidgetTester tester) async {
      final GoRouter router = GoRouter(
        initialLocation: '/',
        routes: <GoRoute>[
          GoRoute(path: '/', builder: (_, __) => SizedBox()),
        ],
      );

      await tester.pumpWidget(
        MaterialApp.router(routerConfig: router),
      );

      final BuildContext context = tester.element(find.byType(SizedBox));
      final GoRouter target = EvoNavigatorTypeFactory().getGoRouter(context);

      expect(identical(router, target), isTrue);
    });

    testWidgets('getFlutterNavigator()', (WidgetTester tester) async {
      final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

      await tester.pumpWidget(
        MaterialApp(navigatorKey: navigatorKey, home: SizedBox()),
      );

      final BuildContext context = tester.element(find.byType(SizedBox));
      final NavigatorState target = EvoNavigatorTypeFactory().getFlutterNavigator(context);

      expect(identical(navigatorKey.currentState, target), isTrue);
    });
  });

  test('GoRouter.goNamed() should call when EvoRouterNavigator.goPage is executed', () {
    navigator.goPage(
      context,
      const PageBaseMock(
        routeSettings: RouteSettings(name: pageName),
      ),
      extra: extra,
    );

    verify(() => goRouterMock.goNamed(pageName, extra: extra)).called(1);
  });

  test('GoRouter.goNamed() should call when EvoRouterNavigator.goNamed is executed', () {
    navigator.goNamed(context, pageName, extra: extra);
    verify(() => goRouterMock.goNamed(pageName, extra: extra)).called(1);
  });

  test('GoRouter.pushNamed() should call when EvoRouterNavigator.pushPage is executed', () {
    when(() => goRouterMock.pushNamed(any(), extra: any(named: 'extra')))
        .thenAnswer((_) => Future<void>.value());
    navigator.pushPage(
      context,
      const PageBaseMock(
        routeSettings: RouteSettings(name: pageName),
      ),
      extra: extra,
    );

    verify(() => goRouterMock.pushNamed(pageName, extra: extra)).called(1);
  });

  test('GoRouter.pushNamed() should call when EvoRouterNavigator.pushNamed is executed', () {
    when(() => goRouterMock.pushNamed(any(), extra: any(named: 'extra')))
        .thenAnswer((_) => Future<void>.value());
    navigator.pushNamed(context, pageName, extra: extra);
    verify(() => goRouterMock.pushNamed(pageName, extra: extra)).called(1);
  });

  test(
      'GoRouter.pushReplacementNamed() should call when EvoRouterNavigator.pushReplacementPage is executed',
      () {
    when(() => goRouterMock.pushReplacementNamed(any(), extra: any(named: 'extra')))
        .thenAnswer((_) => Future<void>.value());
    navigator.pushReplacementPage(
      context,
      const PageBaseMock(
        routeSettings: RouteSettings(name: pageName),
      ),
      extra: extra,
    );

    verify(() => goRouterMock.pushReplacementNamed(pageName, extra: extra)).called(1);
  });

  test(
    'GoRouter.pushReplacementNamed() should call when EvoRouterNavigator.pushReplacementNamed is executed',
    () {
      when(() => goRouterMock.pushReplacementNamed(any(), extra: any(named: 'extra')))
          .thenAnswer((_) => Future<void>.value());
      navigator.pushReplacementNamed(context, pageName, extra: extra);
      verify(() => goRouterMock.pushReplacementNamed(pageName, extra: extra)).called(1);
    },
  );

  test('GoRouter.pop() should call when EvoRouterNavigator.pop is executed', () {
    navigator.pop(context, result: extra);

    verify(() => goRouterMock.pop<Object>(extra)).called(1);
  });

  test('GoRouter.canPop() should call when EvoRouterNavigator.canPop is executed', () {
    when(() => goRouterMock.canPop()).thenReturn(true);

    final bool canPop = navigator.canPop(context);

    verify(() => goRouterMock.canPop()).called(1);
    expect(canPop, true);
  });

  group('test maybePop function', () {
    test('test can pop is true', () {
      when(() => goRouterMock.canPop()).thenReturn(true);

      final bool canPop = navigator.maybePop(context, result: extra);

      expect(canPop, true);
      verify(() => goRouterMock.pop<Object>(extra)).called(1);
    });

    test('test can pop is false', () {
      when(() => goRouterMock.canPop()).thenReturn(false);

      final bool canPop = navigator.maybePop(context);
      expect(canPop, false);
    });
  });

  test(
    'GoRouter.pushReplacementNamed() and Navigator.popUntil() should call when EvoRouterNavigator.removeUntilAndPushReplacementNamed is executed',
    () {
      when(() => goRouterMock.pushReplacementNamed(any())).thenAnswer((_) => Future<void>.value());

      routePredicate(Route<dynamic> route) => route.isFirst;

      navigator.removeUntilAndPushReplacementNamed(
        context,
        pageName,
        routePredicate,
      );

      verify(() => goRouterMock.pushReplacementNamed(pageName)).called(1);
      verify(() => flutterNavigatorStateMock.popUntil(any())).called(1);
    },
  );

  test('Navigator.popUntil() should call when EvoRouterNavigator.pushReplacementNamed is executed',
      () {
    when(() => goRouterMock.pushReplacementNamed(any())).thenAnswer((_) => Future<void>.value());
    navigator.popUntilAndReplaceNamed(
      context,
      pageName,
    );

    verify(() => flutterNavigatorStateMock.popUntil(any())).called(1);
  });

  group('popUntilNamed()', () {
    test('should call Navigator.popUntil()', () {
      navigator.popUntilNamed(context, '');
      verify(() => flutterNavigatorStateMock.popUntil(any())).called(1);
    });

    test('RoutePredicate should return true when page is found and vice versa', () {
      final String pageName = 'home';
      navigator.popUntilNamed(context, pageName);

      final List<dynamic> captured =
          verify(() => flutterNavigatorStateMock.popUntil(captureAny())).captured;
      final RoutePredicate predicate = captured[0] as RoutePredicate;

      Route<void> route(String name) =>
          MaterialPageRoute<void>(settings: RouteSettings(name: name), builder: (_) => SizedBox());

      expect(predicate(route(pageName)), isTrue);
      expect(predicate(route('user')), isFalse);
    });

    testWidgets('should call onPageFound and onPageNotFound', (WidgetTester tester) async {
      final GlobalKey<NavigatorState> navKey = GlobalKey<NavigatorState>();
      final GoRouter router = GoRouter(
        initialLocation: '/home',
        navigatorKey: navKey,
        routes: <GoRoute>[
          GoRoute(path: '/home', name: 'home', builder: (_, __) => SizedBox()),
          GoRoute(path: '/user', name: 'user', builder: (_, __) => SizedBox()),
        ],
      );

      await tester.pumpWidget(
        MaterialApp.router(routerConfig: router),
      );
      router.pushNamed('user');
      await tester.pumpAndSettle();

      final BuildContext context = navKey.currentContext!;
      when(() => navigatorTypeFactoryMock.getGoRouter(context)).thenReturn(router);
      when(() => navigatorTypeFactoryMock.getFlutterNavigator(context))
          .thenReturn(navKey.currentState!);

      bool onPageFoundCalled = false;
      bool onPageNotFoundCalled = false;
      void onPageFound() => onPageFoundCalled = true;
      void onPageNotFound() => onPageNotFoundCalled = true;

      navigator.popUntilNamed(
        context,
        'home',
        onPageFound: onPageFound,
        onPageNotFound: onPageNotFound,
      );
      await tester.pumpAndSettle();
      expect(onPageFoundCalled, isTrue);
      expect(onPageNotFoundCalled, isFalse);

      onPageFoundCalled = false;
      onPageNotFoundCalled = false;
      navigator.popUntilNamed(
        context,
        'user',
        onPageFound: onPageFound,
        onPageNotFound: onPageNotFound,
      );
      await tester.pumpAndSettle();
      expect(onPageFoundCalled, isFalse);
      expect(onPageNotFoundCalled, isTrue);
    });
  });

  group('popUntilAndReplaceNamed', () {
    testWidgets('should call popUntilNamed and pushReplacementNamed', (WidgetTester tester) async {
      final GlobalKey<NavigatorState> navKey = GlobalKey<NavigatorState>();
      final GoRouter router = GoRouter(
        initialLocation: '/home',
        navigatorKey: navKey,
        routes: <GoRoute>[
          GoRoute(path: '/home', name: 'home', builder: (_, __) => SizedBox()),
          GoRoute(path: '/user', name: 'user', builder: (_, __) => SizedBox()),
        ],
      );

      await tester.pumpWidget(MaterialApp.router(routerConfig: router));
      router.pushNamed('user');
      await tester.pumpAndSettle();

      final BuildContext context = navKey.currentContext!;
      when(() => navigatorTypeFactoryMock.getGoRouter(context)).thenReturn(router);
      when(() => navigatorTypeFactoryMock.getFlutterNavigator(context))
          .thenReturn(navKey.currentState!);

      final SpyEvoRouterNavigator navigator = SpyEvoRouterNavigator();

      bool onDoneCalled = false;
      void onDone() => onDoneCalled = true;

      navigator.popUntilAndReplaceNamed(context, 'home', onDone: onDone);
      await tester.pumpAndSettle();
      expect(onDoneCalled, isTrue);
      expect(navigator.popUntilNamedCalled, 1);

      onDoneCalled = false;
      navigator.popUntilAndReplaceNamed(context, 'user', onDone: onDone);
      await tester.pumpAndSettle();
      expect(onDoneCalled, isTrue);
      expect(navigator.popUntilNamedCalled, 2);
      expect(navigator.pushReplacementNamedCalled, 1);
    });
  });
}
