import 'dart:math';

const int _defaultRandomTextLength = 10;
const String _normalChars = 'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';

String generateRandomNormalText({int length = _defaultRandomTextLength}) {
  final Random random = Random();
  final String randomString = String.fromCharCodes(Iterable<int>.generate(
      length, (_) => _normalChars.codeUnitAt(random.nextInt(_normalChars.length))));
  return randomString;
}

int generateRandomNumber({required int max, int min = 0}) {
  final Random random = Random();
  return min + random.nextInt(max - min);
}
