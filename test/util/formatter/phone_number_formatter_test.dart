import 'package:evoapp/util/formatter/phone_number_formatter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late PhoneNumberFormatter phoneFormatter;

  setUp(() {
    phoneFormatter = PhoneNumberFormatter();
  });

  test('Test constant value', () {
    expect(PhoneNumberFormatter.extraSeparatorLength, 2);
  });

  group('Test formatEditUpdate', () {
    void verifyTextSelectionUpdated(TextEditingValue newValue) {
      expect(newValue.selection.end, newValue.text.length);
    }

    test('should format string correctly', () {
      final TextEditingValue newValue = phoneFormatter.formatEditUpdate(
          const TextEditingValue(text: '9917891982'), const TextEditingValue(text: '9917891982'));
      expect(newValue.text, '************');
      verifyTextSelectionUpdated(newValue);
    });

    test('should limited phone length', () {
      final TextEditingValue newValue = phoneFormatter.formatEditUpdate(
          const TextEditingValue(text: '1'), const TextEditingValue(text: '1234567891011'));
      expect(newValue.text, '************');
      verifyTextSelectionUpdated(newValue);
    });

    test('should keep old value if having none digit characters ', () {
      const TextEditingValue oldValue =
          TextEditingValue(text: '123', selection: TextSelection.collapsed(offset: 1));
      final TextEditingValue newValue =
          phoneFormatter.formatEditUpdate(oldValue, const TextEditingValue(text: '+12z3'));
      expect(newValue.text, '123');

      expect(newValue.selection, oldValue.selection);
    });

    test('should remove space', () {
      final TextEditingValue newValue = phoneFormatter.formatEditUpdate(
          const TextEditingValue(text: '1'), const TextEditingValue(text: '1 2  3'));
      expect(newValue.text, '123');
      verifyTextSelectionUpdated(newValue);
    });

    test('should remove prefix +63', () {
      final TextEditingValue newValue = phoneFormatter.formatEditUpdate(
          const TextEditingValue(text: '1'), const TextEditingValue(text: '+63 ************'));
      expect(newValue.text, '************');
      verifyTextSelectionUpdated(newValue);
    });

    test('should remove 0 prefix', () {
      final TextEditingValue newValue = phoneFormatter.formatEditUpdate(
          const TextEditingValue(text: '1'), const TextEditingValue(text: '0************'));
      expect(newValue.text, '************');
      verifyTextSelectionUpdated(newValue);
    });

    test('should add extra spacing add 3rd and 6th index', () {
      TextEditingValue newValue = phoneFormatter.formatEditUpdate(
          const TextEditingValue(text: '098'), const TextEditingValue(text: '0987'));
      expect(newValue.text, '098 7');
      newValue = phoneFormatter.formatEditUpdate(
          const TextEditingValue(text: '098 7654'), const TextEditingValue(text: '098 7654'));
      expect(newValue.text, '098 765 4');
    });
  });
}
