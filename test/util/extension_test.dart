import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/extension.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/common_package/intl.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  setUpAll(() async {
    await initializeDateFormatting();
  });

  group('test_apply_string_format', () {
    test('should_display_phone', () {
      const String input_10 = '0901234567';
      expect('09 012 34567',
          input_10.applyStringFormat(prefixGroup: 2, stringFormatType: StringFormatType.phone));
      expect('09012 34567',
          input_10.applyStringFormat(suffixGroup: 5, stringFormatType: StringFormatType.phone));
      expect(
          '0 90123 4567',
          input_10.applyStringFormat(
              prefixGroup: 1, suffixGroup: 4, stringFormatType: StringFormatType.phone));

      const String input_11 = '09012345678';
      expect(
          '09012345 678',
          input_11.applyStringFormat(
              prefixGroup: 8, suffixGroup: 7, stringFormatType: StringFormatType.phone));
      expect(
          '090 12345678',
          input_11.applyStringFormat(
              prefixGroup: 3, suffixGroup: 9, stringFormatType: StringFormatType.phone));
      expect('************8', input_11.applyStringFormat(stringFormatType: StringFormatType.phone));

      const String input_12 = '090123456789';
      expect(
          '************ 89',
          input_12.applyStringFormat(
              prefixGroup: 3, stringFormatType: StringFormatType.phone, suffixGroup: 2));
    });

    test('should_display_bar_code', () {
      const String input_1 = '12345';
      const String output_1 = '12345';
      expect(output_1, input_1.applyStringFormat(stringFormatType: StringFormatType.barcode));

      const String input_2 = '12345';
      const String output_2 = '12345';
      expect(output_2,
          input_2.applyStringFormat(stringFormatType: StringFormatType.barcode, suffixGroup: 8));

      const String input_3 = '1234567';
      const String output_3 = '12 34567';
      expect(output_3,
          input_3.applyStringFormat(stringFormatType: StringFormatType.barcode, prefixGroup: 2));

      const String input_4 = '1234356457678768456345623452364575678';
      const String output_4 = '12343564 5767 8768 4563 4562 3452 3645 75678';
      expect(output_4,
          input_4.applyStringFormat(stringFormatType: StringFormatType.barcode, prefixGroup: 8));

      const String input_5 = '45342314';
      const String output_5 = '453423 14';
      expect(output_5,
          input_5.applyStringFormat(stringFormatType: StringFormatType.barcode, suffixGroup: 2));

      const String input_6 = '658768675676767867867';
      const String output_6 = '6 5876 8675 6767 67867 867';
      expect(
          output_6,
          input_6.applyStringFormat(
              stringFormatType: StringFormatType.barcode, prefixGroup: 1, suffixGroup: 3));

      const String input_7 = '32423345345';
      const String output_7 = '3242334 5345';
      expect(output_7,
          input_7.applyStringFormat(stringFormatType: StringFormatType.barcode, suffixGroup: 4));

      const String input_8 = '1';
      const String output_8 = '1';
      expect(output_8,
          input_8.applyStringFormat(stringFormatType: StringFormatType.barcode, prefixGroup: 4));
    });
  });

  group('verify FormatPhoneNumber', () {
    test('adds prefix to a valid phone number', () {
      expect('9123456789'.addPrefixCountryCode(), '639123456789');
    });

    test('trims and adds prefix to a phone number with leading/trailing spaces', () {
      expect(' 9123456789 '.addPrefixCountryCode(), '639123456789');
    });

    test('returns empty string for null input', () {
      String? phoneNumber;
      expect(phoneNumber.addPrefixCountryCode(), '');
    });

    test('returns empty string for empty input', () {
      expect(''.addPrefixCountryCode(), '');
    });

    test('returns empty string for input with only spaces', () {
      expect('   '.addPrefixCountryCode(), '');
    });
  });

  group('Test StringEx', () {
    group('Test getLastCharacters', () {
      const String input = '12345';
      test('Give string length is less than count, should return the string itself', () {
        const String expected = '12345';
        expect(input.getLastCharacters(10), expected);
      });

      test('Give string length equals count, should return the string itself', () {
        const String expected = '12345';
        expect(input.getLastCharacters(5), expected);
      });

      test('Give string length is greater than count, should return the string itself', () {
        const String expected = '45';
        expect(input.getLastCharacters(2), expected);
      });
    });
  });

  group('test create uri for sending email', () {
    const String email = '<EMAIL>';
    const String subject = 'Gop y';
    const String body = 'Mo the Evo';

    setUpAll(() {
      getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());
    });

    tearDownAll(() {
      getIt.unregister<EvoUtilFunction>();
    });

    test('test create Uri send mail with only email', () {
      final Uri uri = email.uriForSendMail();
      final Uri expectedUri = Uri(scheme: 'mailto', path: email, query: 'subject=&body=');
      expect(uri, expectedUri);
    });

    test('test create Uri send mail with subject', () {
      final Uri uri = email.uriForSendMail(subject: subject);
      final Uri expectedUri = Uri(scheme: 'mailto', path: email, query: 'subject=$subject&body=');
      expect(uri, expectedUri);
    });

    test('test create Uri send mail with body', () {
      final Uri uri = email.uriForSendMail(body: body);
      final Uri expectedUri = Uri(scheme: 'mailto', path: email, query: 'subject=&body=$body');
      expect(uri, expectedUri);
    });

    test('test create Uri send mail with subject and body', () {
      final Uri uri = email.uriForSendMail(subject: subject, body: body);
      final Uri expectedUri =
          Uri(scheme: 'mailto', path: email, query: 'subject=$subject&body=$body');
      expect(uri, expectedUri);
    });
  });

  group('verify startOfDay', () {
    test('returns start of the day for a given DateTime', () {
      final DateTime dateTime = DateTime(2024, 11, 25, 15, 30, 45); // 3:30:45 PM
      final DateTime expectedStartOfDay = DateTime(2024, 11, 25);
      final DateTime result = dateTime.startOfDay();

      expect(result, equals(expectedStartOfDay));
    });

    test('handles leap year date correctly', () {
      final DateTime leapYearDateTime = DateTime(2024, 2, 29, 23, 59, 59); // Leap year
      final DateTime expectedStartOfDay = DateTime(2024, 2, 29); // Midnight
      final DateTime result = leapYearDateTime.startOfDay();

      expect(result, equals(expectedStartOfDay));
    });
  });

  group('MapConversion.toNonNullable', () {
    test('toNonNullable converts null map to empty map', () {
      Map<String, Object?>? nullableMap;
      final Map<String, Object> nonNullableMap = nullableMap.toNonNullable();

      expect(nonNullableMap, isEmpty);
    });

    test('toNonNullable converts map with null values to non-nullable map', () {
      final Map<String, Object?> nullableMap = <String, Object?>{
        'key1': 'value1',
        'key2': null,
      };
      final Map<String, Object> nonNullableMap = nullableMap.toNonNullable();

      expect(nonNullableMap, hasLength(2));
      expect(nonNullableMap['key1'], 'value1');
      expect(nonNullableMap['key2'], '');
    });

    test('toNonNullable keeps non-null values unchanged', () {
      final Map<String, Object?> nullableMap = <String, Object?>{
        'key1': 'value1',
        'key2': 123,
      };
      final Map<String, Object> nonNullableMap = nullableMap.toNonNullable();

      expect(nonNullableMap, hasLength(2));
      expect(nonNullableMap['key1'], 'value1');
      expect(nonNullableMap['key2'], 123);
    });
  });
}
