import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/biometric/biometric_token/providers/biometric_token_provider_config.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/verify_otp/verify_otp_page.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/widget/evo_dialog/dialog_confirm.dart';
import 'package:evoapp/widget/evo_dialog/kyko_bottom_sheet.dart';
import 'package:evoapp/widget/evo_dialog/kyko_bottom_sheet_action.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../widget/evo_dialog/dialog_confirm_test.dart';
import 'flutter_test_config.dart';

class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}

class MockCommonHttpClient extends Mock implements CommonHttpClient {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockBuildContext extends Mock implements BuildContext {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockEventTrackingUtils extends Mock implements EventTrackingUtils {}

class MockVoidCallback extends Mock {
  void call();
}

class MockAppState extends Mock implements AppState {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  late CommonImageProvider imageProvider;
  const Key buttonKey = Key('test button');
  late LoggingRepo mockLoggingRepo;

  late CommonNavigator commonNavigator;

  setUpAll(() {
    getIt.registerLazySingleton<DialogFunction>(() => DialogFunction());
    getIt.registerLazySingleton<GlobalKeyProvider>(() => GlobalKeyProvider());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockCommonImageProvider());
    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
    imageProvider = getIt.get<CommonImageProvider>();

    commonNavigator = MockCommonNavigator();
    getIt.registerSingleton<CommonNavigator>(commonNavigator);

    registerFallbackValue(MockBuildContext());

    /// Register for logging
    getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());

    mockLoggingRepo = getIt.get<LoggingRepo>();

    getIt.registerLazySingleton<AppState>(() => MockAppState());
    final AppState appState = getIt.get<AppState>();

    getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
    final FeatureToggle mockFeatureToggle = getIt.get<FeatureToggle>();

    getIt.registerLazySingleton<EventTrackingUtils>(() => MockEventTrackingUtils());
    final EventTrackingUtils eventTrackingUtils = getIt.get<EventTrackingUtils>();

    when(
      () => eventTrackingUtils.sendUserActionEvent(
        eventId: any(named: 'eventId'),
        metaData: any(named: 'metaData'),
      ),
    ).thenAnswer((_) => Future<void>.value());
    when(() => appState.currentScreenId).thenReturn(EventTrackingScreenId.undefined);
    when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);

    registerFallbackValue(EventType.other);

    when(() =>
            mockLoggingRepo.logEvent(eventType: any(named: 'eventType'), data: any(named: 'data')))
        .thenAnswer((_) => Future<void>.value());

    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterButtonStyle();

    when(() => evoUtilFunction.calculateHorizontalSpace(
          context: any(named: 'context'),
          widthPercentage: any(named: 'widthPercentage'),
        )).thenReturn(10);
  });

  setUp(() {
    when(() => imageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());

    reset(commonNavigator);
  });

  group('test showDialogConfirm', () {
    const Key buttonKey = Key('test button');
    const EvoDialogId dialogId = EvoDialogId.common;
    const String title = 'title';
    const String content = 'content';
    const String textPositive = 'textPositive';
    const String textNegative = 'textNegative';

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    tearDown(() {
      getIt.unregister<EvoFlutterWrapper>();
    });

    group('verify showDialogConfirm with given properties', () {
      testWidgets('Must call EvoFunctionWrapper.showDialog', (WidgetTester widgetTester) async {
        getIt.registerLazySingleton<EvoFlutterWrapper>(() => MockEvoFlutterWrapper());
        await widgetTester.pumpWidget(MaterialApp(home: Material(child: Container())));

        when(() => evoFlutterWrapper.showDialog<void>(
              builder: any(named: 'builder'),
              barrierDismissible: any(named: 'barrierDismissible'),
            )).thenAnswer((_) {
          return Future<void>.value();
        });

        evoDialogFunction.showDialogConfirm(
          content: 'content',
          textPositive: 'textPositive',
          dialogId: EvoDialogId.common,
        );

        verify(
          () => evoFlutterWrapper.showDialog<void>(
            builder: any(named: 'builder'),
            barrierDismissible: any(named: 'barrierDismissible'),
          ),
        ).called(1);
      });

      testWidgets('show dialog with right properties', (WidgetTester widgetTester) async {
        const String footerKey = 'footer';
        const String imageHeaderKey = 'imageHeaderKey';

        const Widget footer = SizedBox(key: Key(footerKey));
        onClickPositive() {}
        onClickNegative() {}
        const Widget imageHeader = SizedBox(key: Key(imageHeaderKey));
        const bool isDismissible = false;
        final ButtonStyle positiveButtonStyle =
            ButtonStyle(backgroundColor: WidgetStateProperty.all(Colors.red));
        final ButtonStyle negativeButtonStyle =
            ButtonStyle(backgroundColor: WidgetStateProperty.all(Colors.black));
        const TextStyle titleTextStyle = TextStyle(color: Colors.red);
        const TextStyle contentTextStyle = TextStyle(color: Colors.black);
        const Map<String, dynamic> loggingEventMetaData = <String, dynamic>{'key': 'value'};
        const Map<String, dynamic> loggingEventOnShowMetaData = <String, dynamic>{'key': 'value'};

        getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());

        registerFallbackValue(MockBuildContext());

        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogConfirm(
                      content: content,
                      textPositive: textPositive,
                      dialogId: dialogId,
                      title: title,
                      textNegative: textNegative,
                      footer: footer,
                      onClickPositive: onClickPositive,
                      onClickNegative: onClickNegative,
                      imageHeader: imageHeader,
                      isDismissible: isDismissible,
                      positiveButtonStyle: positiveButtonStyle,
                      negativeButtonStyle: negativeButtonStyle,
                      titleTextStyle: titleTextStyle,
                      contentTextStyle: contentTextStyle,
                      loggingEventMetaData: loggingEventMetaData,
                      loggingEventOnShowMetaData: loggingEventOnShowMetaData);
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        /// Test properties
        expect(
          find.byType(EvoDialogConfirm),
          findsOneWidget,
        );
        final EvoDialogConfirm evoDialogConfirm =
            widgetTester.widget(find.byType(EvoDialogConfirm));
        expect(evoDialogConfirm.content, content);

        final Finder popScopeFinder = find.byType(PopScope);
        expect(popScopeFinder, findsOneWidget);
        final PopScope popScopeWidget = widgetTester.widget(popScopeFinder);
        expect(popScopeWidget.canPop, isDismissible);

        expect(
            popScopeWidget.child,
            isA<EvoDialogConfirm>()
                .having(
                  (EvoDialogConfirm p0) => p0.content,
                  'test content of popup',
                  content,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.textPositive,
                  'test textPositive of popup',
                  textPositive,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.dialogId,
                  'test dialogId of popup',
                  dialogId.id,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.title,
                  'test title of popup',
                  title,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.textNegative,
                  'test textNegative of popup',
                  textNegative,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.footer,
                  'test footer of popup',
                  footer,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.onClickPositive,
                  'test onClickPositive of popup',
                  isNotNull,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.onClickNegative,
                  'test onClickNegative of popup',
                  isNotNull,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.imageHeader,
                  'test imageHeader of popup',
                  imageHeader,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.positiveButtonStyle,
                  'test positiveButtonStyle of popup',
                  positiveButtonStyle,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.negativeButtonStyle,
                  'test negativeButtonStyle of popup',
                  negativeButtonStyle,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.isShowButtonClose,
                  'test isShowButtonClose of popup',
                  false,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.titleTextAlign,
                  'test titleTextAlign of popup',
                  null,
                )
                .having(
                  (EvoDialogConfirm p0) => p0.contentTextAlign,
                  'test titleTextAlign of popup',
                  null,
                ));
      });

      testWidgets('should prioritize imageHeader with DialogAlertType ',
          (WidgetTester widgetTester) async {
        getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
        const Text mockImageHeader = Text('image_header');

        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogConfirm(
                    imageHeader: mockImageHeader,
                    alertType: DialogAlertType.error,
                    textPositive: '',
                    dialogId: EvoDialogId.common,
                  );
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        expect(find.byWidget(mockImageHeader), findsNothing);
        final String image = verify(() => imageProvider.asset(captureAny())).captured.first;
        expect(image, EvoImages.icAlertError);
      });
    });

    group('verify onPositiveClick | onNegativeClick | onDismiss', () {
      final MockVoidCallback onClickPositive = MockVoidCallback();
      final MockVoidCallback onClickNegative = MockVoidCallback();
      final MockVoidCallback onDismiss = MockVoidCallback();

      setUp(() {
        getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
      });

      tearDown(() {
        reset(onClickPositive);
        reset(onDismiss);
        reset(onClickNegative);
      });

      testWidgets('verify onDismiss callback work correctly', (WidgetTester widgetTester) async {
        /// Action
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogConfirm(
                    content: content,
                    textPositive: textPositive,
                    textNegative: textNegative,
                    dialogId: dialogId,
                    title: title,
                    onClickPositive: onClickPositive.call,
                    onClickNegative: onClickNegative.call,
                    onDismiss: onDismiss.call,
                  );
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        /// Assert
        expect(
          find.byType(EvoDialogConfirm),
          findsOneWidget,
        );

        // Tap outside the dialog to dismiss it
        await widgetTester.tapAt(const Offset(10, 10)); // Tap at a position outside the dialog
        await widgetTester.pumpAndSettle(); // Wait for the dialog to disappear

        expect(
          find.byType(EvoDialogConfirm),
          findsNothing,
        );

        verifyNever(() => onClickPositive.call());
        verifyNever(() => onClickNegative.call());
        verify(() => onDismiss.call()).called(1);
      });

      testWidgets('verify onPositiveClick work correctly', (WidgetTester widgetTester) async {
        /// Action
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogConfirm(
                    content: content,
                    textPositive: textPositive,
                    dialogId: dialogId,
                    title: title,
                    textNegative: textNegative,
                    onClickPositive: onClickPositive.call,
                    onClickNegative: onClickNegative.call,
                    onDismiss: onDismiss.call,
                    autoClosePopupWhenClickCTA: true,
                  );
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        /// Assert
        expect(
          find.byType(EvoDialogConfirm),
          findsOneWidget,
        );

        await widgetTester.tap(find.text(textPositive));
        await widgetTester.pumpAndSettle();

        verifyNever(() => onDismiss.call());
        verifyNever(() => onClickNegative.call());
        verify(() => onClickPositive.call()).called(1);
      });

      testWidgets('verify onNegativeClick work correctly', (WidgetTester widgetTester) async {
        /// Action
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogConfirm(
                    content: content,
                    textPositive: textPositive,
                    dialogId: dialogId,
                    title: title,
                    textNegative: textNegative,
                    onClickPositive: onClickPositive.call,
                    onClickNegative: onClickNegative.call,
                    onDismiss: onDismiss.call,
                  );
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        /// Assert
        expect(
          find.byType(EvoDialogConfirm),
          findsOneWidget,
        );

        await widgetTester.tap(find.text(textNegative));
        await widgetTester.pumpAndSettle();

        verifyNever(() => onDismiss.call());
        verifyNever(() => onClickPositive.call());
        verify(() => onClickNegative.call()).called(1);
      });
    });

    group('verify show Dialog with autoDismissPopupWhenClickCTA param', () {
      setUp(() {
        getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());

        when(() => commonNavigator.pop(any(), result: any(named: 'result'))).thenAnswer((_) {});
      });

      testWidgets(
          'if autoDismissPopupWhenClickCTA = TRUE, onPositiveClick & onNegativeClick work correctly',
          (WidgetTester widgetTester) async {
        /// Action
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogConfirm(
                      content: content,
                      textPositive: textPositive,
                      dialogId: dialogId,
                      title: title,
                      textNegative: textNegative,
                      autoClosePopupWhenClickCTA: true);
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        /// Assert
        expect(
          find.byType(EvoDialogConfirm),
          findsOneWidget,
        );

        /// click negative button
        await widgetTester.tap(find.text(textNegative));
        await widgetTester.pumpAndSettle();

        verify(() => commonNavigator.pop(any(), result: any(named: 'result'))).called(1);

        /// click positive button
        await widgetTester.tap(find.text(textPositive));
        await widgetTester.pumpAndSettle();
        verify(() => commonNavigator.pop(any(), result: any(named: 'result'))).called(1);
      });

      testWidgets(
          'if autoDismissPopupWhenClickCTA = FALSE, onPositiveClick & onNegativeClick work correctly',
          (WidgetTester widgetTester) async {
        /// Action
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogConfirm(
                    content: content,
                    textPositive: textPositive,
                    dialogId: dialogId,
                    title: title,
                    textNegative: textNegative,
                  );
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        /// Assert
        expect(
          find.byType(EvoDialogConfirm),
          findsOneWidget,
        );

        /// click negative button
        await widgetTester.tap(find.text(textNegative));
        await widgetTester.pumpAndSettle();

        verifyNever(() => commonNavigator.pop(any(), result: any(named: 'result')));

        /// click positive button
        await widgetTester.tap(find.text(textPositive));
        await widgetTester.pumpAndSettle();
        verifyNever(() => commonNavigator.pop(any(), result: any(named: 'result')));
      });
    });
  });

  group('test getImageByDialogType', () {
    test('should return icAlertError if type is DialogAlertType.error', () async {
      evoDialogFunction.getImageHeaderByAlertType(DialogAlertType.error);

      final String image = verify(() => imageProvider.asset(captureAny())).captured.first;

      expect(image, EvoImages.icAlertError);
    });

    test('should return icAlertWarning if type is DialogAlertType.warning', () async {
      evoDialogFunction.getImageHeaderByAlertType(DialogAlertType.warning);

      final String image = verify(() => imageProvider.asset(captureAny())).captured.first;

      expect(image, EvoImages.icAlertWarning);
    });

    test('should return icAlertUnsuccessful if type is DialogAlertType.unsuccessful', () async {
      evoDialogFunction.getImageHeaderByAlertType(DialogAlertType.unsuccessful);

      final String image = verify(() => imageProvider.asset(captureAny())).captured.first;

      expect(image, EvoImages.icAlertUnsuccessful);
    });
  });

  group('verify showDialogSessionTokenExpired', () {
    final MockVoidCallback clickPositiveCb = MockVoidCallback();

    setUp(() {
      when(() => clickPositiveCb.call()).thenAnswer((_) async {});
    });

    setUpAll(() {
      getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    });

    group('verify DialogSessionTokenExpired with given properties', () {
      testWidgets('should show DialogSessionTokenExpired with ResetPin content',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogSessionTokenExpired(
                    type: SessionDialogType.resetPin,
                  );
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        final Finder popScopeFinder = find.byType(PopScope);
        expect(popScopeFinder, findsOneWidget);
        final PopScope popScopeWidget = widgetTester.widget(popScopeFinder);

        expect(
            popScopeWidget.child,
            isA<EvoDialogConfirm>()
                .having((EvoDialogConfirm p0) => p0.title, 'verify dialog title',
                    EvoStrings.titleSessionTokenExpired)
                .having((EvoDialogConfirm p0) => p0.content, 'verify content',
                    EvoStrings.contentSessionTokenExpiredResetPin)
                .having(
                    (EvoDialogConfirm p0) => p0.textPositive, 'verify textPositive', EvoStrings.ok)
                .having((EvoDialogConfirm p0) => p0.dialogId, 'verify dialogId',
                    EvoDialogId.resetPinSessionTokenExpiredErrorDialog.id));
      });

      testWidgets('should show DialogSessionTokenExpired with ActivateAccount content',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogSessionTokenExpired(
                    type: SessionDialogType.activateAccount,
                  );
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        final Finder popScopeFinder = find.byType(PopScope);
        expect(popScopeFinder, findsOneWidget);
        final PopScope popScopeWidget = widgetTester.widget(popScopeFinder);

        expect(
            popScopeWidget.child,
            isA<EvoDialogConfirm>()
                .having((EvoDialogConfirm p0) => p0.title, 'verify dialog title',
                    EvoStrings.titleSessionTokenExpired)
                .having((EvoDialogConfirm p0) => p0.content, 'verify content',
                    EvoStrings.contentActivationAccountSessionTokenExpired)
                .having((EvoDialogConfirm p0) => p0.textPositive, 'verify textPositive',
                    EvoStrings.ctaBackToActivationAccount)
                .having((EvoDialogConfirm p0) => p0.dialogId, 'verify dialogId',
                    EvoDialogId.activateAccountSessionTokenExpiredErrorDialog.id));
      });

      testWidgets('should show DialogSessionTokenExpired with New Device Login content',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogSessionTokenExpired();
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        final Finder popScopeFinder = find.byType(PopScope);
        expect(popScopeFinder, findsOneWidget);
        final PopScope popScopeWidget = widgetTester.widget(popScopeFinder);

        expect(
            popScopeWidget.child,
            isA<EvoDialogConfirm>()
                .having((EvoDialogConfirm p0) => p0.title, 'verify dialog title',
                    EvoStrings.titleSessionTokenExpired)
                .having((EvoDialogConfirm p0) => p0.content, 'verify content',
                    EvoStrings.contentSessionTokenExpiredSignIn)
                .having((EvoDialogConfirm p0) => p0.textPositive, 'verify textPositive',
                    EvoStrings.ctaLogInAgain)
                .having((EvoDialogConfirm p0) => p0.dialogId, 'verify dialogId',
                    EvoDialogId.newDeviceLogInSessionTokenExpiredErrorDialog.id));
      });

      testWidgets('should show DialogSessionTokenExpired with Previous Device Login content',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogSessionTokenExpired(
                      type: SessionDialogType.previousLogIn);
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        final Finder popScopeFinder = find.byType(PopScope);
        expect(popScopeFinder, findsOneWidget);
        final PopScope popScopeWidget = widgetTester.widget(popScopeFinder);

        expect(
            popScopeWidget.child,
            isA<EvoDialogConfirm>()
                .having((EvoDialogConfirm p0) => p0.title, 'verify dialog title',
                    EvoStrings.titleSessionTokenExpired)
                .having((EvoDialogConfirm p0) => p0.content, 'verify content',
                    EvoStrings.contentSessionTokenExpiredSignIn)
                .having((EvoDialogConfirm p0) => p0.textPositive, 'verify textPositive',
                    EvoStrings.ctaLogInAgain)
                .having((EvoDialogConfirm p0) => p0.dialogId, 'verify dialogId',
                    EvoDialogId.previousLogInSessionTokenExpiredErrorDialog.id));
      });

      testWidgets('should set isDismissible to false for session expired dialog',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogSessionTokenExpired();
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        final Finder popScopeFinder = find.byType(PopScope);
        expect(popScopeFinder, findsOneWidget);
        final PopScope popScopeWidget = widgetTester.widget(popScopeFinder);

        // Verify that the dialog is not dismissible
        expect(popScopeWidget.canPop, false);
      });

      testWidgets('should set alertType to error for session expired dialog',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogSessionTokenExpired();
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        // Verify that error alert image is displayed
        final String image = verify(() => imageProvider.asset(captureAny())).captured.first;
        expect(image, EvoImages.icAlertError);
      });
    });

    group('verify onClickPositive on DialogSessionTokenExpired', () {
      testWidgets('should invoke default when onClickPositive is null with ActivateAccount type',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: TextButton(
              onPressed: () {
                evoDialogFunction.showDialogSessionTokenExpired(
                    type: SessionDialogType.activateAccount);
              },
              child: SizedBox(),
            ),
          ),
        );

        await widgetTester.tap(find.byType(TextButton));
        await widgetTester.pumpAndSettle();
        await widgetTester.tap(find.text(EvoStrings.ctaBackToActivationAccount));

        verify(() => commonNavigator.popUntilNamed(any(), Screen.mobileNumberCheckScreen.name))
            .called(1);
        verifyNever(() => commonNavigator.pop(any(), result: any(named: 'result')));
      });

      testWidgets('should invoke default when onClickPositive is null with New Device Login type',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: TextButton(
              onPressed: () {
                evoDialogFunction.showDialogSessionTokenExpired(
                    type: SessionDialogType.newDeviceLogIn);
              },
              child: SizedBox(),
            ),
          ),
        );

        await widgetTester.tap(find.byType(TextButton));
        await widgetTester.pumpAndSettle();
        await widgetTester.tap(find.text(EvoStrings.ctaLogInAgain));

        verify(() => commonNavigator.goNamed(any(), Screen.verifyUsernameScreen.name)).called(1);
        verifyNever(() => commonNavigator.pop(any(), result: any(named: 'result')));
      });

      testWidgets(
          'should invoke default when onClickPositive is null with Previous Device Login type',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: TextButton(
              onPressed: () {
                evoDialogFunction.showDialogSessionTokenExpired(
                    type: SessionDialogType.previousLogIn);
              },
              child: SizedBox(),
            ),
          ),
        );

        await widgetTester.tap(find.byType(TextButton));
        await widgetTester.pumpAndSettle();
        await widgetTester.tap(find.text(EvoStrings.ctaLogInAgain));

        verify(() => commonNavigator.goNamed(any(), Screen.verifyUsernameScreen.name)).called(1);
        verifyNever(() => commonNavigator.pop(any(), result: any(named: 'result')));
      });

      testWidgets('should pop twice when onClickPositive is tapped with ResetPin type',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: TextButton(
              onPressed: () {
                evoDialogFunction.showDialogSessionTokenExpired(type: SessionDialogType.resetPin);
              },
              child: SizedBox(),
            ),
          ),
        );

        await widgetTester.tap(find.byType(TextButton));
        await widgetTester.pumpAndSettle();
        await widgetTester.tap(find.text(EvoStrings.ok));

        verify(() => commonNavigator.pop(any(), result: any(named: 'result'))).called(2);
      });

      testWidgets('should call custom onClickPositive callback when provided',
          (WidgetTester widgetTester) async {
        await widgetTester.pumpWidget(
          MaterialApp(
            navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
            home: Builder(builder: (BuildContext context) {
              return TextButton(
                key: buttonKey,
                onPressed: () {
                  evoDialogFunction.showDialogSessionTokenExpired(
                    onClickPositive: clickPositiveCb.call,
                    type: SessionDialogType.activateAccount,
                  );
                },
                child: const Text('test button'),
              );
            }),
          ),
        );

        /// Show dialog
        await widgetTester.tap(find.byKey(buttonKey));
        await widgetTester.pump();

        await widgetTester.tap(find.text(EvoStrings.ctaBackToActivationAccount));

        // Should call custom callback instead of default navigation behavior
        verify(() => clickPositiveCb.call()).called(1);
        verifyNever(() => commonNavigator.popUntilNamed(any(), any()));
        verifyNever(() => commonNavigator.pop(any(), result: any(named: 'result')));
      });
    });
  });

  group('verify showDialogBottomSheet', () {
    late EvoFlutterWrapper mockEvoFlutterWrapper;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      if (getIt.isRegistered<EvoFlutterWrapper>()) {
        getIt.unregister<EvoFlutterWrapper>();
      }
      getIt.registerLazySingleton<EvoFlutterWrapper>(() => MockEvoFlutterWrapper());
      mockEvoFlutterWrapper = getIt.get<EvoFlutterWrapper>();
    });

    tearDownAll(() {
      getIt.unregister<EvoFlutterWrapper>();
    });

    setUp(() {
      when(() => mockEvoFlutterWrapper.showBottomSheet<void>(
            builder: any(named: 'builder'),
            isDismissible: any(named: 'isDismissible'),
            enableDrag: any(named: 'enableDrag'),
          )).thenAnswer((_) {
        return Future<void>.value();
      });
    });

    testWidgets('should show bottom sheet dialog with correct properties',
        (WidgetTester widgetTester) async {
      const String title = 'Test Title';
      final Widget content = const Text('Test Content');
      final List<KykoBottomSheetAction> actions = <KykoBottomSheetAction>[
        KykoBottomSheetAction.positive(
          text: 'Action 1',
          onPressed: () {},
        ),
      ];
      const bool hasCloseButton = true;

      await evoDialogFunction.showDialogBottomSheet(
        title: title,
        content: content,
        actions: actions,
        hasCloseButton: hasCloseButton,
      );

      final dynamic builderCapture = verify(() => mockEvoFlutterWrapper.showBottomSheet<void>(
            builder: captureAny(named: 'builder'),
            isDismissible: any(named: 'isDismissible'),
            enableDrag: any(named: 'enableDrag'),
          )).captured.single;

      // Create a test context to build the widget
      final BuildContext context = widgetTester.element(find.byType(Container));
      final Widget builtWidget = builderCapture(context);

      // Verify the built widget is a KykoBottomSheet with correct properties
      expect(builtWidget, isA<KykoBottomSheet>());
      final KykoBottomSheet bottomSheet = builtWidget as KykoBottomSheet;
      expect(bottomSheet.title, equals(title));
      expect(bottomSheet.content, equals(content));
      expect(bottomSheet.actions, equals(actions));
      expect(bottomSheet.hasCloseButton, equals(hasCloseButton));
    });
  });

  group('test SessionDialogType.fromVerifyOtpType', () {
    test('should return signIn for VerifyOtpType.signIn', () {
      expect(
        SessionDialogType.fromVerifyOtpType(VerifyOtpType.signIn),
        equals(SessionDialogType.newDeviceLogIn),
      );
    });

    test('should return resetPin for VerifyOtpType.resetPin', () {
      expect(
        SessionDialogType.fromVerifyOtpType(VerifyOtpType.resetPin),
        equals(SessionDialogType.resetPin),
      );
    });

    test('should return activateAccount for VerifyOtpType.activateAccount', () {
      expect(
        SessionDialogType.fromVerifyOtpType(VerifyOtpType.activateAccount),
        equals(SessionDialogType.activateAccount),
      );
    });

    test('should return activateAccount for VerifyOtpType.email', () {
      expect(
        SessionDialogType.fromVerifyOtpType(VerifyOtpType.email),
        equals(SessionDialogType.activateAccount),
      );
    });

    test('should return activateAccount for VerifyOtpType.activateCard', () {
      expect(
        SessionDialogType.fromVerifyOtpType(VerifyOtpType.activateCard),
        equals(SessionDialogType.activateAccount),
      );
    });

    test('should return null for null input', () {
      expect(
        SessionDialogType.fromVerifyOtpType(null),
        isNull,
      );
    });
  });

  group('test SessionDialogType.fromEnableBiometricAuthenticationFlow', () {
    test('should return activateAccount for EnableBiometricAuthenticationFlow.accountActivation',
        () {
      expect(
        SessionDialogType.fromEnableBiometricAuthenticationFlow(
          EnableBiometricAuthenticationFlow.accountActivation,
        ),
        equals(SessionDialogType.activateAccount),
      );
    });

    test('should return newDeviceLogIn for EnableBiometricAuthenticationFlow.newDeviceLogin', () {
      expect(
        SessionDialogType.fromEnableBiometricAuthenticationFlow(
          EnableBiometricAuthenticationFlow.newDeviceLogin,
        ),
        equals(SessionDialogType.newDeviceLogIn),
      );
    });

    test('should return null for EnableBiometricAuthenticationFlow.profileSettings', () {
      expect(
        SessionDialogType.fromEnableBiometricAuthenticationFlow(
          EnableBiometricAuthenticationFlow.profileSettings,
        ),
        isNull,
      );
    });
  });

  group('showDialogErrorLimitExceeded', () {
    setUpAll(() {
      getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    });

    void verifyDialogProperties({
      required WidgetTester widgetTester,
      required String content,
      required String title,
      required String textPositive,
      required EvoDialogId dialogId,
    }) {
      final PopScope popScopeWidget = widgetTester.widget(find.byType(PopScope));
      expect(popScopeWidget.canPop, isFalse);
      expect(
        popScopeWidget.child,
        isA<EvoDialogConfirm>().having(
              (EvoDialogConfirm d) => (
          d.title,
          d.content,
          d.textPositive,
          d.dialogId,
          ),
          'should render dialog UI',
          (
          title,
          content,
          textPositive,
          dialogId.id,
          ),
        ),
      );
    }

    testWidgets('should show DialogErrorLimitExceeded with correct default parameters',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          home: GestureDetector(
            key: buttonKey,
            onTap: () {
              evoDialogFunction.showDialogErrorLimitExceeded(
                type: SessionDialogType.newDeviceLogIn,
              );
            },
          ),
        ),
      );

      await widgetTester.tap(find.byKey(buttonKey));
      await widgetTester.pumpAndSettle();

      verifyDialogProperties(
        widgetTester: widgetTester,
        dialogId: EvoDialogId.newDeviceLogInErrorLimitExceededDialog,
        content: EvoStrings.loginLimitedExceededDesc,
        title: EvoStrings.loginLimitedExceededTitle,
        textPositive: EvoStrings.backToHomePage,
      );
    });

    testWidgets('should show DialogErrorLimitExceeded with correct custom parameters',
        (WidgetTester widgetTester) async {
      final String content = 'content';
      await widgetTester.pumpWidget(
        MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          home: GestureDetector(
            key: buttonKey,
            onTap: () {
              evoDialogFunction.showDialogErrorLimitExceeded(
                  type: SessionDialogType.newDeviceLogIn, content: content);
            },
          ),
        ),
      );

      await widgetTester.tap(find.byKey(buttonKey));
      await widgetTester.pumpAndSettle();

      final PopScope popScopeWidget = widgetTester.widget(find.byType(PopScope));
      expect(
        popScopeWidget.child,
        isA<EvoDialogConfirm>().having(
          (EvoDialogConfirm d) => (
            d.title,
            d.content,
          ),
          'should render dialog UI',
          (
            EvoStrings.loginLimitedExceededTitle,
            content,
          ),
        ),
      );
    });

    testWidgets('should navigate to WelcomeScreen when onClickPositive is tapped with LogIn type',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          home: GestureDetector(
            key: buttonKey,
            onTap: () {
              evoDialogFunction.showDialogErrorLimitExceeded(
                type: SessionDialogType.newDeviceLogIn,
              );
            },
          ),
        ),
      );

      await widgetTester.tap(find.byKey(buttonKey));
      await widgetTester.pumpAndSettle();

      await widgetTester.tap(find.textContaining(EvoStrings.backToHomePage));
      await widgetTester.pumpAndSettle();

      verify(() => commonNavigator.goNamed(
            any(),
            Screen.welcomeScreen.name,
            extra: any(named: 'extra'),
          )).called(1);
    });

    testWidgets('should pop twice when onClickPositive is tapped with resetPin type',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          home: GestureDetector(
            key: buttonKey,
            onTap: () {
              evoDialogFunction.showDialogErrorLimitExceeded(type: SessionDialogType.resetPin);
            },
          ),
        ),
      );

      await widgetTester.tap(find.byKey(buttonKey));
      await widgetTester.pumpAndSettle();

      await widgetTester.tap(find.textContaining(EvoStrings.backToHomePage));
      await widgetTester.pumpAndSettle();

      verify(() => commonNavigator.pop(any(), result: any(named: 'result'))).called(2);
    });

    testWidgets('should pop twice when onClickPositive is tapped with PreviousLogin type',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(
        MaterialApp(
          navigatorKey: getIt.get<GlobalKeyProvider>().navigatorKey,
          home: GestureDetector(
            key: buttonKey,
            onTap: () {
              evoDialogFunction.showDialogErrorLimitExceeded(type: SessionDialogType.previousLogIn);
            },
          ),
        ),
      );

      await widgetTester.tap(find.byKey(buttonKey));
      await widgetTester.pumpAndSettle();

      verifyDialogProperties(
        widgetTester: widgetTester,
        dialogId: EvoDialogId.previousLogInErrorLimitExceededDialog,
        content: EvoStrings.loginLimitedExceededDesc,
        title: EvoStrings.loginLimitedExceededTitle,
        textPositive: EvoStrings.close,
      );

      await widgetTester.tap(find.textContaining(EvoStrings.close));
      await widgetTester.pumpAndSettle();

      verify(() => commonNavigator.pop(any(), result: any(named: 'result'))).called(2);
    });

    test('getDialogIdBySessionDialogType should return correct DialogId', () async {
      EvoDialogId dialogId =
          evoDialogFunction.getDialogIdBySessionDialogType(SessionDialogType.activateAccount);
      expect(dialogId, EvoDialogId.activateAccountErrorLimitExceededDialog);

      dialogId = evoDialogFunction.getDialogIdBySessionDialogType(SessionDialogType.newDeviceLogIn);
      expect(dialogId, EvoDialogId.newDeviceLogInErrorLimitExceededDialog);

      dialogId = evoDialogFunction.getDialogIdBySessionDialogType(SessionDialogType.resetPin);
      expect(dialogId, EvoDialogId.resetPinErrorLimitExceededDialog);

      dialogId = evoDialogFunction.getDialogIdBySessionDialogType(SessionDialogType.previousLogIn);
      expect(dialogId, EvoDialogId.previousLogInErrorLimitExceededDialog);
    });
  });
}
