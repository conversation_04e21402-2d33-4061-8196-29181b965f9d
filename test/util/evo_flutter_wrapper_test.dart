import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'flutter_test_config.dart';

class MockDevicePlatform extends Mo<PERSON> implements DevicePlatform {}

void main() {
  final DevicePlatform mockDevicePlatform = MockDevicePlatform();

  setUpAll(() {
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper(
          platform: mockDevicePlatform,
        ));
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('test wrapper input/output', () {
    const Key dialogContentKey = Key('dialog content');

    setUpAll(() {
      getIt.registerLazySingleton<GlobalKeyProvider>(() => GlobalKeyProvider());
    });

    tearDownAll(() {
      getIt.unregister<GlobalKeyProvider>();
    });

    Future<void> doShowDialog(
      WidgetTester widgetTester,
      WidgetBuilder builder, {
      bool barrierDismissible = true,
    }) async {
      const Key buttonKey = Key('test button');

      await widgetTester.pumpWidget(
        MaterialApp(
          navigatorKey: globalKeyProvider.navigatorKey,
          home: Builder(builder: (BuildContext context) {
            return TextButton(
              key: buttonKey,
              onPressed: () async {
                await evoFlutterWrapper.showDialog<void>(
                  builder: builder,
                  barrierDismissible: barrierDismissible,
                );
              },
              child: const Text('test button'),
            );
          }),
        ),
      );

      /// Show dialog
      await widgetTester.tap(find.byKey(buttonKey));
      await widgetTester.pump();
    }

    testWidgets(
      'can dismiss dialog',
      (WidgetTester widgetTester) async {
        await doShowDialog(
          widgetTester,
          (BuildContext context) {
            return Container(key: dialogContentKey, height: 100);
          },
        );

        /// Test dialog showing
        expect(find.byKey(dialogContentKey), findsOneWidget);

        /// Close dialog
        await widgetTester.tap(find.byType(ModalBarrier).first, warnIfMissed: false);
        await widgetTester.pump();

        /// Closing success
        expect(find.byKey(dialogContentKey), findsNothing);
      },
    );

    testWidgets(
      'cannot dismiss dialog',
      (WidgetTester widgetTester) async {
        await doShowDialog(
          widgetTester,
          (BuildContext context) {
            return Container(key: dialogContentKey, height: 100);
          },
          barrierDismissible: false,
        );

        /// Test dialog showing
        expect(find.byKey(dialogContentKey), findsOneWidget);

        /// Try to close dialog
        await widgetTester.tap(find.byType(ModalBarrier).first, warnIfMissed: false);
        await widgetTester.pump();

        /// Cannot close dialog
        expect(find.byKey(dialogContentKey), findsOneWidget);
      },
    );

    testWidgets(
      'display right content',
      (WidgetTester widgetTester) async {
        const String textContent = 'This is test content';
        await doShowDialog(
          widgetTester,
          (BuildContext context) {
            return const SizedBox(
              key: dialogContentKey,
              height: 100,
              child: Text(textContent),
            );
          },
        );

        /// Check dialog widget
        expect(find.byKey(dialogContentKey), findsOneWidget);
        expect(find.text(textContent), findsOneWidget);
      },
    );

    test('showDialog() should return null when context is null', () async {
      getIt.unregister<GlobalKeyProvider>();
      final GlobalKeyProvider provider = getIt.registerSingleton(MockGlobalKeyProvider());
      when(() => provider.navigatorContext).thenReturn(null);

      final Object? result = await evoFlutterWrapper.showDialog(builder: (_) => SizedBox());

      expect(result, null);
    });
  });

  group('Test isAndroid', () {
    test('Return right value', () {
      when(() => mockDevicePlatform.isAndroid()).thenAnswer((_) => true);
      expect(evoFlutterWrapper.isAndroid(), true);
    });
  });

  group('Test isIOS', () {
    test('Return right value', () {
      when(() => mockDevicePlatform.isIOS()).thenAnswer((_) => true);
      expect(evoFlutterWrapper.isIOS(), true);
    });
  });

  group('test showBottomSheet', () {
    const Key bottomSheetContentKey = Key('bottom sheet content');

    setUp(() {
      if (getIt.isRegistered<GlobalKeyProvider>()) {
        getIt.unregister<GlobalKeyProvider>();
      }

      getIt.registerLazySingleton<GlobalKeyProvider>(() => GlobalKeyProvider());
    });

    Future<void> doShowBottomSheet(
      WidgetTester widgetTester,
      WidgetBuilder builder, {
      bool isDismissible = true,
      bool enableDrag = false,
    }) async {
      const Key buttonKey = Key('test button');

      await widgetTester.pumpWidget(
        MaterialApp(
          navigatorKey: globalKeyProvider.navigatorKey,
          home: Builder(builder: (BuildContext context) {
            return TextButton(
              key: buttonKey,
              onPressed: () async {
                await evoFlutterWrapper.showBottomSheet<void>(
                  builder: builder,
                  isDismissible: isDismissible,
                  enableDrag: enableDrag,
                );
              },
              child: const Text('test button'),
            );
          }),
        ),
      );

      // Show bottom sheet
      await widgetTester.tap(find.byKey(buttonKey));
      await widgetTester.pump();
    }

    testWidgets(
      'should show bottom sheet with content',
      (WidgetTester widgetTester) async {
        const String textContent = 'Bottom Sheet Content';
        await doShowBottomSheet(
          widgetTester,
          (BuildContext context) {
            return const SizedBox(
              key: bottomSheetContentKey,
              height: 100,
              child: Text(textContent),
            );
          },
        );

        /// Check bottom sheet widget
        expect(find.byKey(bottomSheetContentKey), findsOneWidget);
        expect(find.text(textContent), findsOneWidget);
      },
    );

    testWidgets(
      'should respect isDismissible parameter',
      (WidgetTester widgetTester) async {
        // Test with dismissible = false
        await doShowBottomSheet(
          widgetTester,
          (BuildContext context) {
            return Container(key: bottomSheetContentKey, height: 100);
          },
          isDismissible: false,
        );

        expect(find.byKey(bottomSheetContentKey), findsOneWidget);

        // Try to dismiss by tapping on barrier
        await widgetTester.tap(find.byType(ModalBarrier).first, warnIfMissed: false);
        await widgetTester.pump();

        // Should still be visible
        expect(find.byKey(bottomSheetContentKey), findsOneWidget);
      },
    );

    test('showBottomSheet() should return null when context is null', () async {
      getIt.unregister<GlobalKeyProvider>();
      final GlobalKeyProvider provider = getIt.registerSingleton(MockGlobalKeyProvider());
      when(() => provider.navigatorContext).thenReturn(null);

      final Object? result = await evoFlutterWrapper.showBottomSheet(builder: (_) => SizedBox());

      expect(result, null);
    });
  });
}
