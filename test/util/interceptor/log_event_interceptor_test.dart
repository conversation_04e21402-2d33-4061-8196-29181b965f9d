import 'package:evoapp/util/interceptor/log_event_interceptor.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockErrorInterceptorHandler extends Mock implements ErrorInterceptorHandler {}

void main() {
  late ErrorInterceptorHandler mockHandler;
  late DioException mockError;
  const String eventLogPath = 'event-log';
  final LogEventInterceptor interceptor = LogEventInterceptor(
    eventLogPath: eventLogPath,
  );

  setUp(() {
    registerFallbackValue(DioException(requestOptions: RequestOptions()));
    mockHandler = MockErrorInterceptorHandler();
    when(() => mockHandler.reject(any())).thenAnswer((_) => ());
    when(() => mockHandler.next(any())).thenAnswer((_) => ());
  });

  group('verify onRequest', () {
    test('should call handler.reject if path == eventLogPath', () {
      mockError = DioException(
          requestOptions: RequestOptions(
        path: eventLogPath,
      ));

      interceptor.onError(mockError, mockHandler);

      verify(() => mockHandler.reject(mockError)).called(1);
    });

    test('should call handler.next if path != eventLogPath', () {
      mockError = DioException(
          requestOptions: RequestOptions(
        path: 'any-path',
      ));

      interceptor.onError(mockError, mockHandler);

      verify(() => mockHandler.next(mockError)).called(1);
    });
  });
}
