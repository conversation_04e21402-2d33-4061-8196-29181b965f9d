import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/constants.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired.dart';
import 'package:evoapp/model/user_token.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/interceptor/unauthorized_interceptor.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/common_request_option.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockSessionExpiredHandler extends Mock implements AuthorizationSessionExpiredHandler {}

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockJwtHelper extends Mock implements JwtHelper {}

class MockRequestInterceptorHandler extends Mock implements RequestInterceptorHandler {}

class MockErrorInterceptorHandler extends Mock implements ErrorInterceptorHandler {}

void main() {
  final AppState appState = AppState();

  setUpAll(() {
    registerFallbackValue(UnauthorizedSessionState.unknown);
    registerFallbackValue(UnauthorizedSessionState.forcedLogout);
    registerFallbackValue(UnauthorizedSessionState.invalidToken);
  });

  setUp(() {
    appState.userToken = null;
  });

  group('test refresh token action', () {
    test('test don\'t need to refresh token because api path will be ignored', () {
      final UnauthorizedInterceptor unauthorizedInterceptor = UnauthorizedInterceptor(
        MockSessionExpiredHandler(),
        MockAuthenticationRepo(),
        appState,
        MockJwtHelper(),
        ignoredRefreshTokenApiPath: <String>['event'],
      );

      final RefreshTokenType type =
          unauthorizedInterceptor.checkIsNeedRefreshToken(apiPath: 'event');

      expect(type, RefreshTokenType.none);
    });

    test('test don\'t need to refresh token because access token is null', () {
      final UnauthorizedInterceptor unauthorizedInterceptor = UnauthorizedInterceptor(
        MockSessionExpiredHandler(),
        MockAuthenticationRepo(),
        appState,
        MockJwtHelper(),
      );

      final RefreshTokenType type =
          unauthorizedInterceptor.checkIsNeedRefreshToken(apiPath: 'event');

      expect(type, RefreshTokenType.none);
    });

    test('test don\'t need to refresh token because access token is not expired', () {
      final JwtHelper jwtHelper = MockJwtHelper();

      final UnauthorizedInterceptor unauthorizedInterceptor = UnauthorizedInterceptor(
        MockSessionExpiredHandler(),
        MockAuthenticationRepo(),
        appState,
        jwtHelper,
      );

      when(() => jwtHelper.isCanUse('accessToken')).thenAnswer((_) {
        return true;
      });

      final RefreshTokenType type = unauthorizedInterceptor.checkIsNeedRefreshToken(
          apiPath: 'event', accessToken: 'accessToken');

      expect(type, RefreshTokenType.none);
    });

    test('test refresh token error because access token is expired and refresh token is expired',
        () {
      final JwtHelper jwtHelper = MockJwtHelper();

      final UnauthorizedInterceptor unauthorizedInterceptor = UnauthorizedInterceptor(
        MockSessionExpiredHandler(),
        MockAuthenticationRepo(),
        appState,
        jwtHelper,
      );

      when(() => jwtHelper.isCanUse('accessToken')).thenAnswer((_) {
        return false;
      });

      when(() => jwtHelper.isCanUse('refreshToken')).thenAnswer((_) {
        return false;
      });

      final RefreshTokenType type = unauthorizedInterceptor.checkIsNeedRefreshToken(
          apiPath: 'event', accessToken: 'accessToken', refreshToken: 'refreshToken');

      expect(type, RefreshTokenType.error);
    });
    test('test can refresh token', () {
      final JwtHelper jwtHelper = MockJwtHelper();

      final UnauthorizedInterceptor unauthorizedInterceptor = UnauthorizedInterceptor(
        MockSessionExpiredHandler(),
        MockAuthenticationRepo(),
        appState,
        jwtHelper,
      );

      when(() => jwtHelper.isCanUse('accessToken')).thenAnswer((_) {
        return false;
      });

      when(() => jwtHelper.isCanUse('refreshToken')).thenAnswer((_) {
        return true;
      });

      final RefreshTokenType type = unauthorizedInterceptor.checkIsNeedRefreshToken(
        apiPath: 'event',
        accessToken: 'accessToken',
        refreshToken: 'refreshToken',
      );

      expect(type, RefreshTokenType.needRefresh);
    });

    test('test isIgnoreRefreshTokenError function', () {
      final UnauthorizedInterceptor unauthorizedInterceptor = UnauthorizedInterceptor(
        MockSessionExpiredHandler(),
        MockAuthenticationRepo(),
        appState,
        MockJwtHelper(),
      );

      expect(
        unauthorizedInterceptor.isIgnoreRefreshTokenError(CommonHttpClient.NO_INTERNET),
        true,
      );
      expect(
        unauthorizedInterceptor.isIgnoreRefreshTokenError(CommonHttpClient.SOCKET_ERRORS),
        true,
      );
      expect(
        unauthorizedInterceptor.isIgnoreRefreshTokenError(null),
        false,
      );
      expect(
        unauthorizedInterceptor.isIgnoreRefreshTokenError(CommonHttpClient.BAD_REQUEST),
        false,
      );
    });
  });

  group('Test checkIfNeedRemoveAccessToken', () {
    final UnauthorizedInterceptor unauthorizedInterceptor = UnauthorizedInterceptor(
        MockSessionExpiredHandler(), MockAuthenticationRepo(), appState, MockJwtHelper());
    const String accessToken = 'accessToken';

    setUpAll(() {
      getIt.registerSingleton<CommonUtilFunction>(CommonUtilFunction());
    });

    tearDownAll(() {
      getIt.reset();
    });

    test(
        'Give requestOption with header should_remove_access_token == true, should remove access token in requestOption',
        () {
      final RequestOptions options = RequestOptions();
      options.headers = <String, dynamic>{
        CommonRequestOption.shouldRemoveAccessTokenHeader: true,
        HeaderKey.authorization: accessToken,
      };
      unauthorizedInterceptor.checkIfNeedRemoveAccessToken(options, accessToken);
      expect(options.headers[HeaderKey.authorization], isNull);
    });

    test(
        'Give requestOption with header should_remove_access_token == false, should add access token in requestOption',
        () {
      final RequestOptions options = RequestOptions();
      options.headers = <String, dynamic>{CommonRequestOption.shouldRemoveAccessTokenHeader: false};
      unauthorizedInterceptor.checkIfNeedRemoveAccessToken(options, accessToken);
      expect(options.headers[HeaderKey.authorization], 'Bearer $accessToken');
    });

    test(
        'Give requestOption with header should_remove_access_token == null, should add access token in requestOption',
        () {
      final RequestOptions options = RequestOptions();
      unauthorizedInterceptor.checkIfNeedRemoveAccessToken(options, accessToken);
      expect(options.headers[HeaderKey.authorization], 'Bearer $accessToken');
    });
  });

  group('test emitUnauthorizedState', () {
    late AuthorizationSessionExpiredHandler mockSessionExpiredHandler;
    late UnauthorizedInterceptor unauthorizedInterceptor;
    setUp(() {
      /// Arrange
      mockSessionExpiredHandler = MockSessionExpiredHandler();
      when(() => mockSessionExpiredHandler.emitUnauthorized(any()))
          .thenAnswer((_) => Future<void>.value());

      unauthorizedInterceptor = UnauthorizedInterceptor(
          mockSessionExpiredHandler, MockAuthenticationRepo(), appState, MockJwtHelper());
    });

    test('should return FALSE if ignoredVerdictEmitUnauthorized contains verdict', () {
      const String fakeIgnoreVerdict = 'ignore_verdict';
      final UnauthorizedInterceptor unauthorizedInterceptor = UnauthorizedInterceptor(
        MockSessionExpiredHandler(),
        MockAuthenticationRepo(),
        appState,
        MockJwtHelper(),
        ignoredVerdictEmitUnauthorized: <String>[fakeIgnoreVerdict],
      );

      final bool shouldEmit = unauthorizedInterceptor.emitUnauthorizedState(fakeIgnoreVerdict);

      expect(shouldEmit, false);
    });

    test('should return TRUE if verdict is not defined', () {
      /// Action
      final bool shouldEmit = unauthorizedInterceptor.emitUnauthorizedState('fake_verdict');

      /// Assert
      expect(shouldEmit, true);
      verify(() => mockSessionExpiredHandler.emitUnauthorized(UnauthorizedSessionState.unknown))
          .called(1);
    });

    test('should return TRUE if verdict == [forced_logout]', () {
      /// Action
      final bool shouldEmit = unauthorizedInterceptor
          .emitUnauthorizedState(UnauthorizedSessionState.forcedLogout.value);

      /// Assert
      expect(shouldEmit, true);
      verify(() =>
              mockSessionExpiredHandler.emitUnauthorized(UnauthorizedSessionState.forcedLogout))
          .called(1);
    });

    test('should return TRUE if verdict == [invalid_token]', () {
      /// Action
      final bool shouldEmit = unauthorizedInterceptor
          .emitUnauthorizedState(UnauthorizedSessionState.invalidToken.value);

      /// Assert
      expect(shouldEmit, true);
      verify(() =>
              mockSessionExpiredHandler.emitUnauthorized(UnauthorizedSessionState.invalidToken))
          .called(1);
    });
  });

  group('onRequest()', () {
    late AuthenticationRepo authRepo;
    late AuthorizationSessionExpiredHandler sessionHandler;
    late JwtHelper jwtHelper;
    late RequestInterceptorHandler requestHandler;
    late UnauthorizedInterceptor interceptor;
    late UserToken userToken;

    setUpAll(() {
      getIt.registerSingleton<CommonUtilFunction>(CommonUtilFunction());
      registerFallbackValue(DioException(requestOptions: RequestOptions()));
    });

    tearDownAll(() {
      getIt.unregister<CommonUtilFunction>();
    });

    setUp(() {
      authRepo = MockAuthenticationRepo();
      sessionHandler = MockSessionExpiredHandler();
      jwtHelper = MockJwtHelper();
      requestHandler = MockRequestInterceptorHandler();
      interceptor = UnauthorizedInterceptor(
        sessionHandler,
        authRepo,
        appState,
        jwtHelper,
      );

      userToken = appState.userToken;
      userToken.accessToken = null;
      userToken.refreshToken = null;
    });

    tearDown(() {
      reset(authRepo);
      reset(sessionHandler);
      reset(jwtHelper);
      reset(requestHandler);
    });

    test('should call handler.next() when RefreshTokenType.none', () async {
      final RequestOptions options = RequestOptions();

      await interceptor.onRequest(options, requestHandler);

      verify(() => requestHandler.next(options)).called(1);
    });

    test('should call handler.reject() when RefreshTokenType.error', () async {
      userToken.accessToken = 'accessToken';
      userToken.refreshToken = 'refreshToken';

      when(() => jwtHelper.isCanUse('accessToken')).thenAnswer((_) => false);
      when(() => jwtHelper.isCanUse('refreshToken')).thenAnswer((_) => false);
      when(() => sessionHandler.emitUnauthorized(any())).thenAnswer((_) async {});

      await interceptor.onRequest(RequestOptions(), requestHandler);

      verify(() => requestHandler.reject(any(that: isA<DioException>()))).called(1);
      verify(() => sessionHandler.emitUnauthorized(UnauthorizedSessionState.invalidToken))
          .called(1);
    });

    test('should call handler.next() when RefreshTokenType.needRefresh and refreshToken succeeds',
        () async {
      final String accessToken = 'accessToken';
      final String refreshToken = 'refreshToken';

      userToken.accessToken = accessToken;
      userToken.refreshToken = refreshToken;

      when(() => jwtHelper.isCanUse(accessToken)).thenAnswer((_) => false);
      when(() => jwtHelper.isCanUse(refreshToken)).thenAnswer((_) => true);

      final String newAccessToken = 'newAccessToken';
      when(() => authRepo.refreshToken(any())).thenAnswer((_) async {
        return SignInEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'access_token': newAccessToken,
            },
          },
        ));
      });

      final RequestOptions options = RequestOptions();
      await interceptor.onRequest(options, requestHandler);

      verify(() => authRepo.refreshToken(refreshToken)).called(1);
      expect(options.headers['Authorization'], 'Bearer $newAccessToken');
      verify(() => requestHandler.next(options)).called(1);
    });

    test('should call handler.next() when RefreshTokenType.needRefresh and refreshToken() fails',
        () async {
      final String accessToken = 'accessToken';
      final String refreshToken = 'refreshToken';

      userToken.accessToken = accessToken;
      userToken.refreshToken = refreshToken;

      when(() => jwtHelper.isCanUse(accessToken)).thenAnswer((_) => false);
      when(() => jwtHelper.isCanUse(refreshToken)).thenAnswer((_) => true);
      when(() => authRepo.refreshToken(any())).thenAnswer((_) async {
        return SignInEntity.fromBaseResponse(
            BaseResponse(statusCode: CommonHttpClient.BAD_REQUEST, response: null));
      });
      when(() => sessionHandler.emitUnauthorized(any())).thenAnswer((_) async {});

      final RequestOptions options = RequestOptions();
      await interceptor.onRequest(options, requestHandler);

      verify(() => authRepo.refreshToken(refreshToken)).called(greaterThan(0));
      verify(() => requestHandler.reject(any(that: isA<DioException>()))).called(1);
      verify(() => sessionHandler.emitUnauthorized(UnauthorizedSessionState.invalidToken))
          .called(1);
    });

    test(
        'should call handler.next() when RefreshTokenType.needRefresh and refreshToken fails but ignored',
        () async {
      final String accessToken = 'accessToken';
      final String refreshToken = 'refreshToken';

      userToken.accessToken = accessToken;
      userToken.refreshToken = refreshToken;

      when(() => jwtHelper.isCanUse(accessToken)).thenAnswer((_) => false);
      when(() => jwtHelper.isCanUse(refreshToken)).thenAnswer((_) => true);
      when(() => authRepo.refreshToken(any())).thenAnswer((_) async {
        return SignInEntity.fromBaseResponse(
            BaseResponse(statusCode: CommonHttpClient.NO_INTERNET, response: null));
      });
      when(() => sessionHandler.emitUnauthorized(any())).thenAnswer((_) async {});

      final RequestOptions options = RequestOptions();
      await interceptor.onRequest(options, requestHandler);

      verify(() => authRepo.refreshToken(refreshToken)).called(greaterThan(0));
      verify(() => requestHandler.next(options)).called(1);
    });
  });

  group('onError()', () {
    late AuthorizationSessionExpiredHandler sessionHandler;
    late ErrorInterceptorHandler errorHandler;
    late UnauthorizedInterceptor interceptor;

    setUp(() {
      sessionHandler = MockSessionExpiredHandler();
      errorHandler = MockErrorInterceptorHandler();
      interceptor = UnauthorizedInterceptor(
        sessionHandler,
        MockAuthenticationRepo(),
        appState,
        MockJwtHelper(),
      );
    });

    tearDown(() {
      reset(errorHandler);
    });

    DioException buildError(int statusCode, {String verdict = ''}) => DioException(
          requestOptions: RequestOptions(),
          response: Response<dynamic>(
            requestOptions: RequestOptions(),
            statusCode: statusCode,
            data: <String, dynamic>{
              'verdict': verdict,
            },
          ),
        );

    test('should call handler.reject() when statusCode is INVALID_TOKEN', () async {
      final DioException error = buildError(CommonHttpClient.INVALID_TOKEN);
      when(() => sessionHandler.emitUnauthorized(any())).thenAnswer((_) async {});

      interceptor.onError(error, errorHandler);

      verify(() => errorHandler.reject(error)).called(1);
    });

    test('should call handler.next() when statusCode is INVALID_TOKEN when verdict is ignored',
        () async {
      final String ignoredVerdict = 'test';
      final DioException error =
          buildError(CommonHttpClient.INVALID_TOKEN, verdict: ignoredVerdict);

      interceptor = UnauthorizedInterceptor(
        sessionHandler,
        MockAuthenticationRepo(),
        appState,
        MockJwtHelper(),
        ignoredVerdictEmitUnauthorized: <String>[ignoredVerdict],
      );

      interceptor.onError(error, errorHandler);

      verify(() => errorHandler.next(error)).called(1);
    });

    test('should call handler.next() when statusCode is NOT INVALID_TOKEN', () async {
      final DioException error = buildError(CommonHttpClient.BAD_REQUEST);

      interceptor.onError(error, errorHandler);

      verify(() => errorHandler.next(error)).called(1);
    });
  });

  group('retryRefreshToken()', () {
    late UnauthorizedInterceptor interceptor;
    late AuthenticationRepo authenticationRepo;

    setUp(() {
      authenticationRepo = MockAuthenticationRepo();
      interceptor = UnauthorizedInterceptor(
        MockSessionExpiredHandler(),
        authenticationRepo,
        appState,
        MockJwtHelper(),
      );
    });

    test('should NOT retry if request succeeds', () async {
      final String token = 'token';
      when(() => authenticationRepo.refreshToken(token)).thenAnswer((_) async {
        return SignInEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: null,
        ));
      });

      await interceptor.retryRefreshToken(token, intervalInSec: 0);

      verify(() => authenticationRepo.refreshToken(token)).called(1);
    });

    test('should retry if request fails', () async {
      final String token = 'token';
      final int maxRetryCount = 2;

      when(() => authenticationRepo.refreshToken(token)).thenAnswer((_) async {
        return SignInEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: null,
        ));
      });

      await interceptor.retryRefreshToken(token, maxRetryCount: maxRetryCount, intervalInSec: 0);

      verify(() => authenticationRepo.refreshToken(token)).called(1 + maxRetryCount);
    });

    test('should retry 3 times by default', () async {
      final String token = 'token';

      when(() => authenticationRepo.refreshToken(token)).thenAnswer((_) async {
        return SignInEntity.fromBaseResponse(BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: null,
        ));
      });

      await interceptor.retryRefreshToken(token, intervalInSec: 0);

      verify(() => authenticationRepo.refreshToken(token)).called(1 + 3);
    });
  });
}
