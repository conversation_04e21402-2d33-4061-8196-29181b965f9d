// ignore_for_file: depend_on_referenced_packages
import 'package:app_settings/app_settings.dart';
import 'package:app_settings/app_settings_platform_interface.dart';
import 'package:evoapp/util/app_settings_wrapper.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockAppSettings extends Mock with MockPlatformInterfaceMixin implements AppSettingsPlatform {}

void main() {
  group('AppSettingsWrapper', () {
    late AppSettingsPlatform mockAppSettings;
    late AppSettingsWrapper appSettingsWrapper;

    setUpAll(() {
      mockAppSettings = MockAppSettings();
      appSettingsWrapper = AppSettingsWrapper();
      registerFallbackValue(AppSettingsType.settings);
      AppSettingsPlatform.instance = mockAppSettings;
    });

    test('verify AppSettings.openAppSettings is called', () async {
      const AppSettingsType mockType = AppSettingsType.vpn;

      when(() => mockAppSettings.openAppSettings(
          type: any(named: 'type'),
          asAnotherTask: any(named: 'asAnotherTask'))).thenAnswer((_) => Future<void>.value());
      await appSettingsWrapper.openAppSettings(
        type: mockType,
        asAnotherTask: true,
      );

      verify(() => mockAppSettings.openAppSettings(type: mockType, asAnotherTask: true)).called(1);
    });
  });
}
