import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/validator/mpin_validator.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('MPINValidator', () {
    final MpinValidator validator = MpinValidator();

    test('[extractToListDigit] returns correct list of digits', () {
      expect(validator.extractToListDigit('1234'), <int>[1, 2, 3, 4]);
      expect(validator.extractToListDigit('5678'), <int>[5, 6, 7, 8]);
      expect(validator.extractToListDigit('abed'), isNull);
    });

    test('[isDecreasingSequence] detects decreasing sequences', () {
      expect(validator.isDecreasingSequence(<int>[4, 3, 2, 1]), isTrue);
      expect(validator.isDecreasingSequence(<int>[1, 2, 3, 4]), isFalse);
      expect(validator.isDecreasingSequence(<int>[4, 4, 4, 4]), isFalse);
    });

    test('[isIncreaseSequence] detects increasing sequences', () {
      expect(validator.isIncreaseSequence(<int>[1, 2, 3, 4]), isTrue);
      expect(validator.isIncreaseSequence(<int>[4, 3, 2, 1]), isFalse);
      expect(validator.isIncreaseSequence(<int>[4, 4, 4, 4]), isFalse);
    });

    test('[isSameDigits] detects sequences with same digits', () {
      expect(validator.isSameDigits(<int>[4, 4, 4, 4]), isTrue);
      expect(validator.isSameDigits(<int>[1, 2, 3, 4]), isFalse);
      expect(validator.isSameDigits(<int>[4, 3, 2, 1]), isFalse);
    });

    test('[validate] returns correct error messages', () {
      expect(validator.validate('123'), EvoStrings.mustMatchMPINLength);
      expect(validator.validate('12345'), EvoStrings.mustMatchMPINLength);
      expect(validator.validate('abed'), EvoStrings.mustMatchMPINLength);
      expect(validator.validate('4444'), EvoStrings.noSameAllDigitsMPIN);
      expect(validator.validate('4321'), EvoStrings.noDecreasingMPIN);
      expect(validator.validate('1234'), EvoStrings.noIncreasingMPIN);
      expect(validator.validate('1357'), isNull);
    });
  });
}
