import 'package:evoapp/util/validator/evo_validator.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  final EvoValidator validator = EvoValidator();

  group('verify validateMaxLengthPin method', () {
    test('validate correct length', () {
      const String data = '1234';
      expect(validator.validateMaxLengthPin(data), isTrue);
    });

    test('validate incorrect, data isEmpty', () {
      const String data = '';
      expect(validator.validateMaxLengthPin(data), isFalse);
    });

    test('validate incorrect, length pin too short', () {
      const String data = '123';
      expect(validator.validateMaxLengthPin(data), isFalse);
    });

    test('validate incorrect, length pin too long', () {
      const String data = '12345653485734895';
      expect(validator.validateMaxLengthPin(data), isFalse);
    });
  });
}
