import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/validator/username_validator.dart';
import 'package:flutter_test/flutter_test.dart';


void main() {
  final UsernameValidator validator = UsernameValidator();

  test('verify constant values', () {
    expect(UsernameValidator.defaultUsernameMaxLength, 30);
    expect(UsernameValidator.regexUsername, r'[^a-zA-Z0-9.]');
  });

  test('should return error for null username', () {
    expect(validator.validate(null), EvoStrings.errorUsernameEmpty);
  });

  test('should return error for empty username', () {
    expect(validator.validate(''), EvoStrings.errorUsernameEmpty);
  });

  test('should return error for username exceeding max length', () {
    final String longUsername = 'a' * 31;
    expect(validator.validate(longUsername), EvoStrings.errorUsernameMaxLength);
  });

  test('should return error for username containing invalid symbols', () {
    expect(validator.validate('invalid@username'), EvoStrings.errorUsernameContainsSymbols);
    expect(validator.validate('invalid#username'), EvoStrings.errorUsernameContainsSymbols);
    expect(validator.validate('invalid!username'), EvoStrings.errorUsernameContainsSymbols);
  });

  test('should return null for valid username', () {
    expect(validator.validate('valid.username'), null);
    expect(validator.validate('validusername123'), null);
  });
}