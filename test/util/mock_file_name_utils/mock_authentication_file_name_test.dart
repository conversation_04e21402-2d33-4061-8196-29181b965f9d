import 'package:evoapp/util/mock_file_name_utils/mock_authentication_file_name.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('getRequestResetPinMockFileName', () {
    test('should return correct file name when phoneNumber is provided', () {
      final String result = getRequestResetPinMockFileName(phoneNumber: '1234567890');
      expect(result, 'request_reset_pin_with_national_id.json');
    });

    test('should return correct file name when nationalId is provided', () {
      final String result = getRequestResetPinMockFileName(nationalId: 'A12345678');
      expect(result, 'request_reset_pin_with_otp.json');
    });

    test('should return correct file name when otp is provided', () {
      final String result = getRequestResetPinMockFileName(otp: '123456');
      expect(result, 'request_reset_pin_change_pin.json');
    });

    test('should return correct file name when pin is provided', () {
      final String result = getRequestResetPinMockFileName(pin: '1234');
      expect(result, 'request_reset_pin_success.json');
    });

    test('should return correct file name when all parameters are null', () {
      final String result = getRequestResetPinMockFileName();
      expect(result, 'request_reset_pin_success.json');
    });
  });
}