import 'package:evoapp/util/token_utils/evo_jwt_helper_impl.dart';
import 'package:flutter_test/flutter_test.dart';

/// Gen jwt online: https://www.javainuse.com/jwtgenerator
void main() {
  setUpAll(() async {});

  group('test decode JWT', () {
    test('test decode valid JWT', () {
      final EvoJwtHelperImpl jwtHelper = EvoJwtHelperImpl();
      const String validToken =
          'eyJhbGciOiJIUzI1NiJ9.eyJSb2xlIjoiQWRtaW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtZSI6IkphdmFJblVzZSIsImV4cCI6MTY3MzQ5MzgzOSwiaWF0IjoxNjczNDkzODM5fQ.SuEQYrbYzSdgoHjUJfNP1MSrZ1oHrZkIDshwP55iXik';

      final Map<String, dynamic>? jwt = jwtHelper.decode(validToken);

      expect(jwt, isNotNull);
    });

    test('test decode invalid JWT', () {
      final EvoJwtHelperImpl jwtHelper = EvoJwtHelperImpl();
      const String invalidToken = 'eyJhbGciOiJIUzI1NiJ9';

      final Map<String, dynamic>? jwt = jwtHelper.decode(invalidToken);

      expect(jwt, isNull);
    });
  });

  group('test expired JWT', () {
    test('test expired JWT is not expired', () {
      final EvoJwtHelperImpl jwtHelper = EvoJwtHelperImpl();

      /// Expired date:2050-01-12T03:23:59.917Z
      const String liveToken =
          'eyJhbGciOiJIUzI1NiJ9.eyJSb2xlIjoiQWRtaW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtZSI6IkphdmFJblVzZSIsImV4cCI6MjUyNTU3MDYzOSwiaWF0IjoxNjczNDkzODM5fQ.RF3Hc0RaOqpdu4YUXmrXHXM4RWc697vJBwZ7eEVVWgA';

      final bool isExpired = jwtHelper.isExpired(liveToken);

      expect(isExpired, false);
    });

    test('test expired JWT is expired', () {
      final EvoJwtHelperImpl jwtHelper = EvoJwtHelperImpl();

      /// Expired date: 2000-01-12T03:23:59.917Z
      const String expiredToken =
          'eyJhbGciOiJIUzI1NiJ9.eyJSb2xlIjoiQWRtaW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtZSI6IkphdmFJblVzZSIsImV4cCI6OTQ3NjQ3NDM5LCJpYXQiOjE2NzM0OTM4Mzl9.3C7OiO2CFlUyFV3H-5bJFd_LO7fOPpXwi5IfR0hpFi0';

      final bool isExpired = jwtHelper.isExpired(expiredToken);

      expect(isExpired, true);
    });
  });

  group('test token is can use', () {
    test('test token can not use because it null', () {
      final EvoJwtHelperImpl jwtHelper = EvoJwtHelperImpl();

      final bool isCanUse = jwtHelper.isCanUse(null);

      expect(isCanUse, false);
    });

    test('test token can not use because it is expired', () {
      final EvoJwtHelperImpl jwtHelper = EvoJwtHelperImpl();

      /// Expired date: 2000-01-12T03:23:59.917Z
      const String expiredToken =
          'eyJhbGciOiJIUzI1NiJ9.eyJSb2xlIjoiQWRtaW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtZSI6IkphdmFJblVzZSIsImV4cCI6OTQ3NjQ3NDM5LCJpYXQiOjE2NzM0OTM4Mzl9.3C7OiO2CFlUyFV3H-5bJFd_LO7fOPpXwi5IfR0hpFi0';

      final bool isCanUse = jwtHelper.isCanUse(expiredToken);

      expect(isCanUse, false);
    });
    test('test token can use', () {
      final EvoJwtHelperImpl jwtHelper = EvoJwtHelperImpl();

      /// Expired date:2050-01-12T03:23:59.917Z
      const String liveToken =
          'eyJhbGciOiJIUzI1NiJ9.eyJSb2xlIjoiQWRtaW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtZSI6IkphdmFJblVzZSIsImV4cCI6MjUyNTU3MDYzOSwiaWF0IjoxNjczNDkzODM5fQ.RF3Hc0RaOqpdu4YUXmrXHXM4RWc697vJBwZ7eEVVWgA';

      final bool isCanUse = jwtHelper.isCanUse(liveToken);

      expect(isCanUse, true);
    });
  });
}
