import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';

class TestUtil {
  static Future<Map<String, dynamic>> getResponseMock(String fileName) async {
    return json.decode(await rootBundle.loadString('assets/mock/$fileName'))
        as Map<String, dynamic>;
  }

  /// because context.screenWidth is extension method => we can't mock this function
  /// so we will use widgetTester.pumpWidget to get context with device screen size
  static Future<BuildContext> createContextWithDeviceScreenSize(WidgetTester widgetTester) async {
    late BuildContext ctx;
    const Size fullSize = Size(1080, 1920);
    await widgetTester.pumpWidget(MediaQuery(
        data: const MediaQueryData(size: fullSize),
        child: Builder(builder: (BuildContext context) {
          ctx = context;
          return const SizedBox.shrink();
        })));
    return ctx;
  }
}
