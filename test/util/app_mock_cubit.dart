import 'dart:async';

import 'package:flutter_common_package/base/common_cubit.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:mocktail/mocktail.dart';

/// A mocked implementation of [Cubit] with a real stream
/// so that we can emit a state to make changes to UI.
class AppMockCubit<S> extends Mock implements CommonCubit<S> {
  late final StreamController<S> _stateController = StreamController<S>.broadcast();

  late S _state;

  @override
  S get state => _state;

  @override
  Stream<S> get stream => _stateController.stream;

  @override
  Future<void> emit(S state) async {
    _state = state;
    _stateController.add(state);
  }

  @override
  Future<void> close() async {
    await _stateController.close();
  }
}
