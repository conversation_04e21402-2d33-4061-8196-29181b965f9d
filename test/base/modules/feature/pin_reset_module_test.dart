// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/evo_module_names.dart';
import 'package:evoapp/base/modules/feature/pin_reset_module.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_handler.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_handler_impl.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_ui_handler.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  group('PinResetModule', () {
    late PinResetModule module;
    late GetIt getIt;
    late MockAuthenticationRepo mockAuthRepo;

    setUp(() {
      getIt = GetIt.instance;
      module = PinResetModule(getIt);
      mockAuthRepo = MockAuthenticationRepo();

      // Register required dependencies
      getIt.registerSingleton<AuthenticationRepo>(mockAuthRepo);
    });

    tearDown(() {
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals(EvoModuleNames.pinReset.value));
        expect(module.name, equals('pin_reset_module'));
      });

      test('should have CoreModule as dependency', () {
        expect(module.dependencies.length, equals(1));
        expect(module.dependencies, contains(CoreModule));
      });

      test('should be instance of FeatureModule', () {
        expect(module, isA<FeatureModule>());
      });
    });

    group('Registration', () {
      test('should register ResetPinHandler successfully', () async {
        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<ResetPinHandler>(), isTrue);
        expect(getIt.get<ResetPinHandler>(), isA<ResetPinHandler>());
        expect(getIt.get<ResetPinHandler>(), isA<ResetPinHandlerImpl>());
      });

      test('should register ResetPinHandler as factory with singleton behavior', () async {
        // Act
        await module.register();

        // Assert - Due to implementation, factory registration with instance creates singleton behavior
        final ResetPinHandler handler1 = getIt.get<ResetPinHandler>();
        final ResetPinHandler handler2 = getIt.get<ResetPinHandler>();
        expect(identical(handler1, handler2), isTrue);
      });

      test('should create ResetPinHandler with correct dependencies', () async {
        // Act
        await module.register();

        // Assert
        final ResetPinHandler handler = getIt.get<ResetPinHandler>();
        expect(handler, isA<ResetPinHandlerImpl>());
        
        // Verify the handler was created with proper dependencies
        final ResetPinHandlerImpl handlerImpl = handler as ResetPinHandlerImpl;
        expect(handlerImpl.resetPinUiHandler, isA<ResetPinUiHandler>());
        expect(handlerImpl.authRepo, equals(mockAuthRepo));
      });
    });

    group('Error Handling', () {
      test('should handle registration gracefully when dependencies are available', () async {
        // This test ensures the module can handle registration without errors
        // when GetIt is properly initialized with required dependencies
        expect(() => module.register(), returnsNormally);
      });

      test('should throw StateError when AuthenticationRepo dependency is missing', () async {
        // Arrange - Use isolated GetIt instance to avoid affecting other tests
        final GetIt isolatedGetIt = GetIt.asNewInstance();
        final PinResetModule moduleWithoutDeps = PinResetModule(isolatedGetIt);

        // Act & Assert
        expect(
          () => moduleWithoutDeps.register(),
          throwsA(isA<StateError>()),
        );
      });

      test('should handle registration errors with proper logging', () async {
        // Arrange - Use isolated GetIt instance
        final GetIt isolatedGetIt = GetIt.asNewInstance();
        final PinResetModule moduleWithFailure = PinResetModule(isolatedGetIt);

        // Act & Assert - Should throw StateError when dependency is missing
        expect(
          () => moduleWithFailure.register(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('Integration', () {
      test('should integrate with EvoModuleNames enum and be part of feature modules', () {
        expect(module.name, equals(EvoModuleNames.pinReset.value));
        expect(EvoModuleNames.fromValue(module.name), equals(EvoModuleNames.pinReset));
        expect(EvoModuleNames.featureModules, contains(EvoModuleNames.pinReset));
      });

      test('should have focused responsibility for PIN reset functionality', () {
        // Verify the module only handles PIN reset concerns
        expect(module.name, contains('pin_reset'));
        expect(module.dependencies.length, equals(1)); // Only CoreModule
        expect(module.dependencies.first, equals(CoreModule));
      });

      test('should work with module framework', () {
        // This test ensures the module follows the standard pattern
        expect(module, isA<FeatureModule>());
        expect(module.name, isNotEmpty);
        expect(module.dependencies, isNotEmpty);
      });
    });

    group('Service Lifecycle', () {
      test('should maintain singleton behavior for ResetPinHandler', () async {
        // Act
        await module.register();

        // Assert - Due to implementation, factory registration with instance creates singleton behavior
        final ResetPinHandler handler1 = getIt.get<ResetPinHandler>();
        final ResetPinHandler handler2 = getIt.get<ResetPinHandler>();
        final ResetPinHandler handler3 = getIt.get<ResetPinHandler>();

        expect(identical(handler1, handler2), isTrue);
        expect(identical(handler2, handler3), isTrue);
        expect(identical(handler1, handler3), isTrue);
      });

      test('should create handler with correct dependencies', () async {
        // Act
        await module.register();

        // Assert - Verify the single instance has proper dependencies
        final ResetPinHandler handler = getIt.get<ResetPinHandler>();

        final ResetPinHandlerImpl impl = handler as ResetPinHandlerImpl;

        // AuthRepo should be the same singleton instance
        expect(impl.authRepo, equals(mockAuthRepo));
        expect(impl.resetPinUiHandler, isA<ResetPinUiHandler>());
      });
    });

    group('Module Documentation', () {
      test('should have proper module documentation and dependencies note', () {
        // Verify the module has proper documentation about its purpose
        expect(module.name, equals('pin_reset_module'));
        
        // Verify dependency requirements are documented
        // Note: This module depends on AuthenticationRepo from AuthBiometricModule
        expect(module.dependencies, contains(CoreModule));
      });

      test('should follow naming conventions', () {
        expect(module.name, matches(RegExp(r'^[a-z]+(_[a-z]+)*_module$')));
        expect(module.name, endsWith('_module'));
        expect(module.name, isNot(contains(' ')));
        expect(module.name, isNot(contains(RegExp(r'[A-Z]'))));
      });
    });
  });
}
