// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/evo_module_names.dart';
import 'package:evoapp/base/modules/feature/pin_reset_module.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_handler.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_handler_impl.dart';
import 'package:evoapp/feature/pin/reset_pin/reset_pin_ui_handler.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}

void main() {
  group('PinResetModule', () {
    late PinResetModule module;
    late GetIt getIt;
    late MockAuthenticationRepo mockAuthRepo;

    setUp(() {
      getIt = GetIt.instance;
      module = PinResetModule(getIt);
      mockAuthRepo = MockAuthenticationRepo();

      // Register required dependencies
      getIt.registerSingleton<AuthenticationRepo>(mockAuthRepo);
    });

    tearDown(() {
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals(EvoModuleNames.pinReset.value));
        expect(module.name, equals('pin_reset_module'));
      });

      test('should have CoreModule as dependency', () {
        expect(module.dependencies.length, equals(1));
        expect(module.dependencies, contains(CoreModule));
      });

      test('should be instance of FeatureModule', () {
        expect(module, isA<FeatureModule>());
      });
    });

    group('Registration', () {
      test('should register ResetPinHandler with correct type and dependencies', () async {
        // Act
        await module.register();

        // Assert - Registration and type verification
        expect(getIt.isRegistered<ResetPinHandler>(), isTrue);
        final ResetPinHandler handler = getIt.get<ResetPinHandler>();
        expect(handler, isA<ResetPinHandlerImpl>());

        // Assert - Dependency injection verification
        final ResetPinHandlerImpl handlerImpl = handler as ResetPinHandlerImpl;
        expect(handlerImpl.resetPinUiHandler, isA<ResetPinUiHandler>());
        expect(handlerImpl.authRepo, equals(mockAuthRepo));
      });

      test('should register as factory with singleton behavior', () async {
        // Act
        await module.register();

        // Assert - Singleton behavior due to instance registration
        final ResetPinHandler handler1 = getIt.get<ResetPinHandler>();
        final ResetPinHandler handler2 = getIt.get<ResetPinHandler>();
        expect(identical(handler1, handler2), isTrue);
      });
    });

    group('Error Handling', () {
      test('should register successfully when all dependencies are available', () async {
        // Act & Assert - Should complete without throwing
        await expectLater(module.register(), completes);
        expect(getIt.isRegistered<ResetPinHandler>(), isTrue);
      });

      test('should throw StateError when AuthenticationRepo dependency is missing', () async {
        // Arrange - Use isolated GetIt instance to avoid affecting other tests
        final GetIt isolatedGetIt = GetIt.asNewInstance();
        final PinResetModule moduleWithoutDeps = PinResetModule(isolatedGetIt);

        // Act & Assert
        expect(
          () => moduleWithoutDeps.register(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('Integration', () {
      test('should integrate with EvoModuleNames enum as feature module', () {
        expect(module.name, equals(EvoModuleNames.pinReset.value));
        expect(EvoModuleNames.fromValue(module.name), equals(EvoModuleNames.pinReset));
        expect(EvoModuleNames.featureModules, contains(EvoModuleNames.pinReset));
      });

      test('should have minimal dependencies for focused responsibility', () {
        // Verify the module has focused responsibility with minimal dependencies
        expect(module.name, contains('pin_reset'));
        expect(module.dependencies.length, equals(1)); // Only CoreModule
        expect(module.dependencies.first, equals(CoreModule));
      });
    });

    group('Service Lifecycle', () {
      test('should maintain singleton behavior across multiple retrievals', () async {
        // Act
        await module.register();

        // Assert - Multiple retrievals should return identical instances
        final ResetPinHandler handler1 = getIt.get<ResetPinHandler>();
        final ResetPinHandler handler2 = getIt.get<ResetPinHandler>();
        final ResetPinHandler handler3 = getIt.get<ResetPinHandler>();

        expect(identical(handler1, handler2), isTrue);
        expect(identical(handler2, handler3), isTrue);
        expect(identical(handler1, handler3), isTrue);
      });
    });

    group('Naming Conventions', () {
      test('should follow module naming standards', () {
        expect(module.name, matches(RegExp(r'^[a-z]+(_[a-z]+)*_module$')));
        expect(module.name, endsWith('_module'));
        expect(module.name, isNot(contains(' ')));
        expect(module.name, isNot(contains(RegExp(r'[A-Z]'))));
        expect(module.name, equals('pin_reset_module'));
      });
    });
  });
}
