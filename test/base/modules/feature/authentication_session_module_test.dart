// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/base/modules/feature/authentication_session_module.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_handler.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_popup.dart';
import 'package:evoapp/feature/authorization_session_expired/force_logout_popup.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../util/flutter_test_config.dart';

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

void main() {
  group('AuthenticationSessionModule', () {
    late AuthenticationSessionModule module;
    late GetIt getIt;
    late AppState appState;
    late MockEvoUtilFunction mockEvoUtilFunction;

    setUp(() {
      getIt = GetIt.instance;
      module = AuthenticationSessionModule(getIt);
      appState = AppState();
      mockEvoUtilFunction = MockEvoUtilFunction();

      // Register required dependencies
      getIt.registerSingleton<AppState>(appState);
      getIt.registerSingleton<EvoUtilFunction>(mockEvoUtilFunction);

      // Setup mock behaviors
      when(() => mockEvoUtilFunction.clearAllUserData()).thenAnswer((_) async {});
      when(() => mockEvoUtilFunction.clearUserInfoAppState()).thenReturn(null);
      when(() => mockEvoUtilFunction.clearDataOnTokenInvalid()).thenAnswer((_) async {});
    });

    tearDown(() {
      getIt.reset();
      AppState.resetInstance();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals('authentication_session_module'));
      });

      test('should have CoreModule and UtilityModule as dependencies', () {
        expect(module.dependencies.length, equals(2));
        expect(module.dependencies.any((type) => type.toString().contains('CoreModule')), isTrue);
        expect(module.dependencies.any((type) => type.toString().contains('UtilityModule')), isTrue);
      });
    });

    group('Registration', () {
      test('should register all authentication session services successfully', () async {
        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<AuthorizationSessionExpiredHandler>(), isTrue);
        expect(getIt.isRegistered<AuthorizationSessionExpiredPopup>(), isTrue);
        expect(getIt.isRegistered<ForceLogoutPopup>(), isTrue);
      });

      test('should register session expiration handler', () async {
        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<AuthorizationSessionExpiredHandler>(), isTrue);
        expect(getIt.get<AuthorizationSessionExpiredHandler>(), isA<AuthorizationSessionExpiredHandler>());
      });

      test('should register session popups', () async {
        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<AuthorizationSessionExpiredPopup>(), isTrue);
        expect(getIt.get<AuthorizationSessionExpiredPopup>(), isA<AuthorizationSessionExpiredPopup>());

        expect(getIt.isRegistered<ForceLogoutPopup>(), isTrue);
        expect(getIt.get<ForceLogoutPopup>(), isA<ForceLogoutPopup>());
      });

      test('should register session expiration handling individually', () {
        // Act
        module.registerSessionExpirationHandling();

        // Assert
        expect(getIt.isRegistered<AuthorizationSessionExpiredHandler>(), isTrue);
        expect(getIt.get<AuthorizationSessionExpiredHandler>(), isA<AuthorizationSessionExpiredHandler>());
      });

      test('should register session popups individually', () {
        // Act
        module.registerSessionPopups();

        // Assert
        expect(getIt.isRegistered<AuthorizationSessionExpiredPopup>(), isTrue);
        expect(getIt.get<AuthorizationSessionExpiredPopup>(), isA<AuthorizationSessionExpiredPopup>());

        expect(getIt.isRegistered<ForceLogoutPopup>(), isTrue);
        expect(getIt.get<ForceLogoutPopup>(), isA<ForceLogoutPopup>());
      });

      test('should maintain singleton behavior for registered services', () async {
        // Act
        await module.register();

        // Assert - Verify singleton behavior
        final AuthorizationSessionExpiredHandler handler1 = getIt.get<AuthorizationSessionExpiredHandler>();
        final AuthorizationSessionExpiredHandler handler2 = getIt.get<AuthorizationSessionExpiredHandler>();
        expect(identical(handler1, handler2), isTrue);

        final AuthorizationSessionExpiredPopup popup1 = getIt.get<AuthorizationSessionExpiredPopup>();
        final AuthorizationSessionExpiredPopup popup2 = getIt.get<AuthorizationSessionExpiredPopup>();
        expect(identical(popup1, popup2), isTrue);

        final ForceLogoutPopup forcePopup1 = getIt.get<ForceLogoutPopup>();
        final ForceLogoutPopup forcePopup2 = getIt.get<ForceLogoutPopup>();
        expect(identical(forcePopup1, forcePopup2), isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle registration gracefully when dependencies are available', () async {
        // This test ensures the module can handle registration without errors
        // when GetIt is properly initialized
        expect(() => module.register(), returnsNormally);
      });

      test('should have proper error handling structure in register method', () {
        // Verify that the register method has try-catch structure
        // This is a structural test to ensure error handling is in place
        expect(module.register, isA<Function>());
      });
    });

    group('Integration', () {
      test('should work with module framework', () {
        // This test ensures the module follows the standard pattern
        expect(module, isA<FeatureModule>());
        expect(module.name, isNotEmpty);
        expect(module.dependencies, isNotEmpty);
      });

      test('should have focused responsibility', () {
        // Verify the module only handles authentication session concerns
        expect(module.name, contains('authentication_session'));
        expect(module.dependencies.length, equals(2)); // CoreModule and UtilityModule only
      });
    });
  });
}
