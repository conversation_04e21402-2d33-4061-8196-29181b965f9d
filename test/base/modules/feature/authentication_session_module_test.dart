// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/feature/authentication_session_module.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_handler.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_popup.dart';
import 'package:evoapp/feature/authorization_session_expired/force_logout_popup.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AuthenticationSessionModule', () {
    late AuthenticationSessionModule module;
    late GetIt getIt;

    setUp(() {
      getIt = GetIt.instance;
      module = AuthenticationSessionModule(getIt);
    });

    tearDown(() {
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals('authentication_session_module'));
      });

      test('should have CoreModule and UtilityModule as dependencies', () {
        expect(module.dependencies.length, equals(2));
        expect(module.dependencies.any((type) => type.toString().contains('CoreModule')), isTrue);
        expect(module.dependencies.any((type) => type.toString().contains('UtilityModule')), isTrue);
      });
    });

    group('Registration', () {
      test('should register all authentication session services successfully', () async {
        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<AuthorizationSessionExpiredHandler>(), isTrue);
        expect(getIt.isRegistered<AuthorizationSessionExpiredPopup>(), isTrue);
        expect(getIt.isRegistered<ForceLogoutPopup>(), isTrue);
      });

      test('should register session expiration handler', () async {
        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<AuthorizationSessionExpiredHandler>(), isTrue);
        expect(getIt.get<AuthorizationSessionExpiredHandler>(), isA<AuthorizationSessionExpiredHandler>());
      });

      test('should register session popups', () async {
        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<AuthorizationSessionExpiredPopup>(), isTrue);
        expect(getIt.get<AuthorizationSessionExpiredPopup>(), isA<AuthorizationSessionExpiredPopup>());
        
        expect(getIt.isRegistered<ForceLogoutPopup>(), isTrue);
        expect(getIt.get<ForceLogoutPopup>(), isA<ForceLogoutPopup>());
      });
    });

    group('Error Handling', () {
      test('should handle registration gracefully', () async {
        // This test ensures the module can handle registration without errors
        // when GetIt is properly initialized
        expect(() => module.register(), returnsNormally);
      });
    });

    group('Integration', () {
      test('should work with module framework', () {
        // This test ensures the module follows the standard pattern
        expect(module, isA<FeatureModule>());
        expect(module.name, isNotEmpty);
        expect(module.dependencies, isNotEmpty);
      });

      test('should have focused responsibility', () {
        // Verify the module only handles authentication session concerns
        expect(module.name, contains('authentication_session'));
        expect(module.dependencies.length, equals(2)); // CoreModule and UtilityModule only
      });
    });
  });
}
