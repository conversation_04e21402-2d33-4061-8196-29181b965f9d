// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/utility/utility_wrapper_module.dart';
import 'package:evoapp/base/modules/evo_module_names.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/util/app_settings_wrapper.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/url_launcher_uri_wrapper.dart';
import 'package:evoapp/feature/login/utils/login_old_device_utils.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_service.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/utility_module.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/util/flutter_downloader/common_flutter_downloader.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockGetIt extends Mock implements GetIt {}
class MockCommonFlutterDownloader extends Mock implements CommonFlutterDownloader {}
class MockAppState extends Mock implements AppState {}
class MockBiometricAuthenticationService extends Mock implements BiometricAuthenticationService {}

// Fake classes for fallback values
class FakeGetIt extends Fake implements GetIt {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(FakeGetIt());
  });

  group('UtilityWrapperModule', () {
    late UtilityWrapperModule module;
    late GetIt getIt;
    late MockCommonFlutterDownloader mockFlutterDownloader;
    late MockAppState mockAppState;
    late MockBiometricAuthenticationService mockBiometricService;

    setUp(() {
      getIt = GetIt.instance;
      module = UtilityWrapperModule(getIt);
      mockFlutterDownloader = MockCommonFlutterDownloader();
      mockAppState = MockAppState();
      mockBiometricService = MockBiometricAuthenticationService();

      // Register mock dependencies required by LoginOldDeviceUtils
      getIt.registerSingleton<CommonFlutterDownloader>(mockFlutterDownloader);
      getIt.registerSingleton<AppState>(mockAppState);
      getIt.registerSingleton<BiometricAuthenticationService>(mockBiometricService);
    });

    tearDown(() {
      // Reset GetIt for test isolation
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals(EvoModuleNames.utilityWrapper.value));
        expect(module.name, equals('utility_wrapper_module'));
      });

      test('should have CoreModule and UtilityModule as dependencies', () {
        expect(module.dependencies.length, equals(2));
        expect(module.dependencies, contains(CoreModule));
        expect(module.dependencies, contains(UtilityModule));
      });

      test('should be instance of FeatureModule', () {
        expect(module, isA<FeatureModule>());
      });
    });

    group('Registration', () {
      test('should register all utility wrappers successfully as lazy singletons', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});

        // Act
        await module.register();

        // Assert - Verify all services are registered
        expect(getIt.isRegistered<DialogFunction>(), isTrue);
        expect(getIt.isRegistered<EvoFlutterWrapper>(), isTrue);
        expect(getIt.isRegistered<EvoUtilFunction>(), isTrue);
        expect(getIt.isRegistered<EvoSnackBar>(), isTrue);
        expect(getIt.isRegistered<AppSettingsWrapper>(), isTrue);
        expect(getIt.isRegistered<UrlLauncherWrapper>(), isTrue);
        expect(getIt.isRegistered<LoginOldDeviceUtils>(), isTrue);

        // Verify correct implementations
        final DialogFunction dialogFunction = getIt.get<DialogFunction>();
        final EvoFlutterWrapper flutterWrapper = getIt.get<EvoFlutterWrapper>();
        final EvoUtilFunction utilFunction = getIt.get<EvoUtilFunction>();
        final EvoSnackBar snackBar = getIt.get<EvoSnackBar>();
        final AppSettingsWrapper appSettings = getIt.get<AppSettingsWrapper>();
        final UrlLauncherWrapper urlLauncher = getIt.get<UrlLauncherWrapper>();
        final LoginOldDeviceUtils loginUtils = getIt.get<LoginOldDeviceUtils>();

        expect(dialogFunction, isA<DialogFunction>());
        expect(flutterWrapper, isA<EvoFlutterWrapper>());
        expect(utilFunction, isA<EvoUtilFunction>());
        expect(snackBar, isA<EvoSnackBar>());
        expect(appSettings, isA<AppSettingsWrapper>());
        expect(urlLauncher, isA<UrlLauncherWrapper>());
        expect(loginUtils, isA<LoginOldDeviceUtils>());

        // Verify singleton behavior
        expect(identical(dialogFunction, getIt.get<DialogFunction>()), isTrue);
        expect(identical(flutterWrapper, getIt.get<EvoFlutterWrapper>()), isTrue);
        expect(identical(utilFunction, getIt.get<EvoUtilFunction>()), isTrue);
        expect(identical(snackBar, getIt.get<EvoSnackBar>()), isTrue);
        expect(identical(appSettings, getIt.get<AppSettingsWrapper>()), isTrue);
        expect(identical(urlLauncher, getIt.get<UrlLauncherWrapper>()), isTrue);
        expect(identical(loginUtils, getIt.get<LoginOldDeviceUtils>()), isTrue);

        // Verify Flutter Downloader initialization was called
        verify(() => mockFlutterDownloader.initialize()).called(1);
      });
    });

    group('Individual Service Registration', () {
      test('should register DialogFunction with correct implementation', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});

        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<DialogFunction>(), isTrue);
        final DialogFunction service = getIt.get<DialogFunction>();
        expect(service, isA<DialogFunction>());
      });

      test('should register EvoFlutterWrapper with correct implementation', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});

        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<EvoFlutterWrapper>(), isTrue);
        final EvoFlutterWrapper service = getIt.get<EvoFlutterWrapper>();
        expect(service, isA<EvoFlutterWrapper>());
      });

      test('should register EvoUtilFunction with correct implementation', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});

        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<EvoUtilFunction>(), isTrue);
        final EvoUtilFunction service = getIt.get<EvoUtilFunction>();
        expect(service, isA<EvoUtilFunction>());
      });

      test('should register EvoSnackBar with SnackBarWrapper dependency', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});

        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<EvoSnackBar>(), isTrue);
        final EvoSnackBar service = getIt.get<EvoSnackBar>();
        expect(service, isA<EvoSnackBar>());
        expect(service.snackBarWrapper, isA<SnackBarWrapper>());
      });

      test('should register AppSettingsWrapper with correct implementation', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});

        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<AppSettingsWrapper>(), isTrue);
        final AppSettingsWrapper service = getIt.get<AppSettingsWrapper>();
        expect(service, isA<AppSettingsWrapper>());
      });

      test('should register UrlLauncherWrapper with correct implementation', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});

        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<UrlLauncherWrapper>(), isTrue);
        final UrlLauncherWrapper service = getIt.get<UrlLauncherWrapper>();
        expect(service, isA<UrlLauncherWrapper>());
      });

      test('should register LoginOldDeviceUtils with correct implementation', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});

        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<LoginOldDeviceUtils>(), isTrue);
        final LoginOldDeviceUtils service = getIt.get<LoginOldDeviceUtils>();
        expect(service, isA<LoginOldDeviceUtils>());
      });
    });

    group('Flutter Downloader Initialization', () {
      test('should initialize Flutter Downloader successfully', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});

        // Act
        await module.initializeFlutterDownloader();

        // Assert
        verify(() => mockFlutterDownloader.initialize()).called(1);
      });

      test('should handle Flutter Downloader initialization failure', () async {
        // Arrange
        final Exception testException = Exception('Initialization failed');
        when(() => mockFlutterDownloader.initialize()).thenThrow(testException);

        // Act & Assert
        expect(
          () => module.initializeFlutterDownloader(),
          throwsA(isA<Exception>()),
        );
        verify(() => mockFlutterDownloader.initialize()).called(1);
      });

      test('should call initializeFlutterDownloader during registration', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});

        // Act
        await module.register();

        // Assert
        verify(() => mockFlutterDownloader.initialize()).called(1);
      });
    });

    group('Error Handling', () {
      test('should handle multiple registration calls gracefully', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});
        await module.register();

        // Act & Assert - Second registration should not throw
        expect(() => module.register(), returnsNormally);

        // Verify services are still available
        expect(getIt.isRegistered<DialogFunction>(), isTrue);
        expect(getIt.isRegistered<EvoFlutterWrapper>(), isTrue);
        expect(getIt.isRegistered<EvoUtilFunction>(), isTrue);
        expect(getIt.isRegistered<EvoSnackBar>(), isTrue);
        expect(getIt.isRegistered<AppSettingsWrapper>(), isTrue);
        expect(getIt.isRegistered<UrlLauncherWrapper>(), isTrue);
        expect(getIt.isRegistered<LoginOldDeviceUtils>(), isTrue);
      });

      test('should propagate registration errors with proper logging', () async {
        // Arrange
        final Exception testException = Exception('Registration failed');
        when(() => mockFlutterDownloader.initialize()).thenThrow(testException);

        // Act & Assert
        expect(
          () => module.register(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Integration', () {
      test('should integrate with EvoModuleNames enum', () {
        expect(module.name, equals(EvoModuleNames.utilityWrapper.value));
        expect(EvoModuleNames.fromValue(module.name), equals(EvoModuleNames.utilityWrapper));
        expect(EvoModuleNames.utilityModules, contains(EvoModuleNames.utilityWrapper));
      });

      test('should follow module framework standards', () {
        expect(module, isA<FeatureModule>());
        expect(module.name, isNotEmpty);
        expect(module.dependencies, isNotEmpty);
        expect(module.name, contains('utility_wrapper'));
      });

      test('should have focused responsibility for utility wrappers', () {
        // Verify the module only handles utility wrapper concerns
        expect(module.name, contains('utility_wrapper'));
        expect(module.dependencies.length, equals(2)); // CoreModule and UtilityModule only
      });
    });

    group('Service Functionality', () {
      test('should provide functional services with accessible properties', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});
        await module.register();

        // Act & Assert - Test actual functionality
        final EvoSnackBar snackBar = getIt.get<EvoSnackBar>();
        expect(snackBar.enable, isA<bool>());
        expect(snackBar.snackBarWrapper, isA<SnackBarWrapper>());

        final EvoFlutterWrapper flutterWrapper = getIt.get<EvoFlutterWrapper>();
        expect(flutterWrapper.isAndroid, isA<bool Function()>());
        expect(flutterWrapper.isIOS, isA<bool Function()>());
      });

      test('should maintain singleton instances across multiple registrations', () async {
        // Arrange
        when(() => mockFlutterDownloader.initialize()).thenAnswer((_) async {});
        await module.register();
        final DialogFunction firstInstance = getIt.get<DialogFunction>();

        // Act
        await module.register();
        final DialogFunction secondInstance = getIt.get<DialogFunction>();

        // Assert
        expect(identical(firstInstance, secondInstance), isTrue);
      });
    });
  });
}
