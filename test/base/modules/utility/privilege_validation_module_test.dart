// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/utility/privilege_validation_module.dart';
import 'package:evoapp/base/modules/evo_module_names.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import 'package:evoapp/feature/logging/evo_event_tracking_utils/evo_event_tracking_utils_impl.dart';
import 'package:evoapp/feature/logging/evo_navigator_observer.dart';
import 'package:evoapp/feature/privilege_action/privilege_access_guard_module.dart';
import 'package:evoapp/feature/privilege_action/verify_biometric_privilege_action/verify_biometric_privilege_action.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:evoapp/util/validator/evo_validator.dart';
import 'package:evoapp/util/validator/mpin_validator.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/utility_module.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes for dependencies
class MockBiometricsAuthenticate extends Mock implements BiometricsAuthenticate {}
class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}
class MockJwtHelper extends Mock implements JwtHelper {}
class MockEventTrackingUtils extends Mock implements EventTrackingUtils {}
class MockAppState extends Mock implements AppState {}
class MockFeatureToggle extends Mock implements FeatureToggle {}

void main() {
  group('PrivilegeValidationModule', () {
    late PrivilegeValidationModule module;
    late GetIt getIt;
    late MockBiometricsAuthenticate mockBiometricsAuthenticate;
    late MockEvoLocalStorageHelper mockStorageHelper;
    late MockJwtHelper mockJwtHelper;
    late MockEventTrackingUtils mockEventTrackingUtils;
    late MockAppState mockAppState;
    late MockFeatureToggle mockFeatureToggle;

    setUp(() {
      getIt = GetIt.instance;
      module = PrivilegeValidationModule(getIt);

      // Create mocks for dependencies
      mockBiometricsAuthenticate = MockBiometricsAuthenticate();
      mockStorageHelper = MockEvoLocalStorageHelper();
      mockJwtHelper = MockJwtHelper();
      mockEventTrackingUtils = MockEventTrackingUtils();
      mockAppState = MockAppState();
      mockFeatureToggle = MockFeatureToggle();

      // Register required dependencies that the module expects
      getIt.registerSingleton<BiometricsAuthenticate>(mockBiometricsAuthenticate);
      getIt.registerSingleton<EvoLocalStorageHelper>(mockStorageHelper);
      getIt.registerSingleton<JwtHelper>(mockJwtHelper);
      getIt.registerSingleton<EventTrackingUtils>(mockEventTrackingUtils);
      getIt.registerSingleton<AppState>(mockAppState);
      getIt.registerSingleton<FeatureToggle>(mockFeatureToggle);

      // Setup mock behaviors
      when(() => mockEventTrackingUtils.sendUserActionEvent(
        eventId: any(named: 'eventId'),
        metaData: any(named: 'metaData'),
      )).thenAnswer((_) async {});
      when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);
      when(() => mockAppState.currentScreenId).thenReturn(EventTrackingScreenId.undefined);
    });

    tearDown(() {
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals(EvoModuleNames.privilegeValidation.value));
      });

      test('should have CoreModule and UtilityModule as dependencies', () {
        expect(module.dependencies.length, equals(2));
        expect(module.dependencies, contains(CoreModule));
        expect(module.dependencies, contains(UtilityModule));
      });

      test('should be instance of FeatureModule', () {
        expect(module, isA<FeatureModule>());
      });
    });

    group('Service Registration', () {
      test('should register all utility services successfully', () async {
        // Act
        await module.register();

        // Assert - Verify all utility services are registered
        expect(getIt.isRegistered<EvoUtilFunction>(), isTrue);
        expect(getIt.isRegistered<EvoValidator>(), isTrue);
        expect(getIt.isRegistered<MpinValidator>(), isTrue);
        expect(getIt.isRegistered<EvoEventTrackingUtils>(), isTrue);
        expect(getIt.isRegistered<EvoNavigatorObserver>(), isTrue);

        // Verify correct implementations
        final EvoUtilFunction utilFunction = getIt.get<EvoUtilFunction>();
        final EvoValidator validator = getIt.get<EvoValidator>();
        final MpinValidator mpinValidator = getIt.get<MpinValidator>();
        final EvoEventTrackingUtils eventTracking = getIt.get<EvoEventTrackingUtils>();
        final EvoNavigatorObserver navigatorObserver = getIt.get<EvoNavigatorObserver>();

        expect(utilFunction, isA<EvoUtilFunction>());
        expect(validator, isA<EvoValidator>());
        expect(mpinValidator, isA<MpinValidator>());
        expect(eventTracking, isA<EvoEventTrackingUtilsImpl>());
        expect(navigatorObserver, isA<EvoNavigatorObserver>());
      });

      test('should register privilege action services successfully', () async {
        // Act
        await module.register();

        // Assert - Verify privilege action services are registered
        expect(getIt.isRegistered<VerifyBiometricForPrivilegeAction>(), isTrue);
        expect(getIt.isRegistered<PrivilegeAccessGuardModule>(), isTrue);

        // Verify factory registration behavior
        final VerifyBiometricForPrivilegeAction action1 = getIt.get<VerifyBiometricForPrivilegeAction>();
        final VerifyBiometricForPrivilegeAction action2 = getIt.get<VerifyBiometricForPrivilegeAction>();
        final PrivilegeAccessGuardModule guard1 = getIt.get<PrivilegeAccessGuardModule>();
        final PrivilegeAccessGuardModule guard2 = getIt.get<PrivilegeAccessGuardModule>();

        expect(action1, isA<VerifyBiometricForPrivilegeAction>());
        expect(guard1, isA<PrivilegeAccessGuardModule>());
      });

      test('should register lazy singletons with singleton behavior', () async {
        // Act
        await module.register();

        // Assert - Verify singleton behavior for lazy singletons
        final EvoUtilFunction utilFunction1 = getIt.get<EvoUtilFunction>();
        final EvoUtilFunction utilFunction2 = getIt.get<EvoUtilFunction>();
        final EvoValidator validator1 = getIt.get<EvoValidator>();
        final EvoValidator validator2 = getIt.get<EvoValidator>();
        final MpinValidator mpinValidator1 = getIt.get<MpinValidator>();
        final MpinValidator mpinValidator2 = getIt.get<MpinValidator>();

        expect(identical(utilFunction1, utilFunction2), isTrue);
        expect(identical(validator1, validator2), isTrue);
        expect(identical(mpinValidator1, mpinValidator2), isTrue);
      });
    });

    group('Dependency Injection', () {
      test('should inject correct dependencies into VerifyBiometricForPrivilegeAction', () async {
        // Act
        await module.register();

        // Assert - Verify action is created with proper dependencies
        final VerifyBiometricForPrivilegeAction action = getIt.get<VerifyBiometricForPrivilegeAction>();
        expect(action, isNotNull);
        expect(action, isA<VerifyBiometricForPrivilegeAction>());
      });

      test('should inject VerifyBiometricForPrivilegeAction into PrivilegeAccessGuardModule', () async {
        // Act
        await module.register();

        // Assert - Verify guard module is created with proper dependencies
        final PrivilegeAccessGuardModule guard = getIt.get<PrivilegeAccessGuardModule>();
        expect(guard, isNotNull);
        expect(guard, isA<PrivilegeAccessGuardModule>());
      });
    });

    group('Error Handling', () {
      test('should handle multiple registration calls gracefully', () async {
        // Arrange - Register services first time
        await module.register();

        // Act & Assert - Second registration should not throw
        expect(() => module.register(), returnsNormally);

        // Verify services are still available
        expect(getIt.isRegistered<EvoUtilFunction>(), isTrue);
        expect(getIt.isRegistered<EvoValidator>(), isTrue);
        expect(getIt.isRegistered<MpinValidator>(), isTrue);
        expect(getIt.isRegistered<EvoEventTrackingUtils>(), isTrue);
        expect(getIt.isRegistered<EvoNavigatorObserver>(), isTrue);
        expect(getIt.isRegistered<VerifyBiometricForPrivilegeAction>(), isTrue);
        expect(getIt.isRegistered<PrivilegeAccessGuardModule>(), isTrue);
      });

      test('should rethrow exceptions during registration', () async {
        // Arrange - Create a fresh GetIt instance with missing dependencies
        final GetIt freshGetIt = GetIt.asNewInstance();
        final PrivilegeValidationModule freshModule = PrivilegeValidationModule(freshGetIt);

        // Register only some dependencies to cause error
        freshGetIt.registerSingleton<BiometricsAuthenticate>(MockBiometricsAuthenticate());
        freshGetIt.registerSingleton<EvoLocalStorageHelper>(MockEvoLocalStorageHelper());
        // Missing JwtHelper, EventTrackingUtils, AppState, FeatureToggle to cause error

        // Act & Assert
        await expectLater(() => freshModule.register(), throwsA(isA<StateError>()));
      });
    });

    group('Integration', () {
      test('should integrate with EvoModuleNames enum', () {
        expect(module.name, equals(EvoModuleNames.privilegeValidation.value));
        expect(EvoModuleNames.fromValue(module.name), equals(EvoModuleNames.privilegeValidation));
        expect(EvoModuleNames.utilityModules, contains(EvoModuleNames.privilegeValidation));
      });

      test('should follow module framework standards', () {
        expect(module, isA<FeatureModule>());
        expect(module.name, isNotEmpty);
        expect(module.dependencies, isNotEmpty);
        expect(module.name, contains('privilege_validation'));
      });
    });

    group('Service Functionality', () {
      test('should provide functional validator services', () async {
        // Arrange
        await module.register();
        final EvoValidator evoValidator = getIt.get<EvoValidator>();
        final MpinValidator mpinValidator = getIt.get<MpinValidator>();

        // Act & Assert - Test actual functionality
        expect(evoValidator.validateMaxLengthPin('1234'), isTrue);
        expect(evoValidator.validateMaxLengthPin('123'), isFalse);
        expect(mpinValidator.validate('1357'), isNull); // Valid PIN (not sequential)
        expect(mpinValidator.validate('1111'), isNotNull); // Invalid PIN (same digits)
      });

      test('should provide functional event tracking service', () async {
        // Arrange
        await module.register();
        final EvoEventTrackingUtils eventTracking = getIt.get<EvoEventTrackingUtils>();

        // Act & Assert - Verify service is accessible and functional
        expect(eventTracking, isA<EvoEventTrackingUtilsImpl>());
        expect(() => eventTracking.sendEvoUserEvent(eventActionId: 'test'), returnsNormally);
      });


    });
  });
}
