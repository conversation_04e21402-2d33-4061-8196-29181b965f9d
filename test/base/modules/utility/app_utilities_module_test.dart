// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/utility/app_utilities_module.dart';
import 'package:evoapp/base/modules/evo_module_names.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/splash_screen/utils/exit_app_feature/exit_app_feature.dart';
import 'package:evoapp/feature/splash_screen/utils/exit_app_feature/exit_app_feature_impl.dart';
import 'package:evoapp/feature/splash_screen/utils/secure_detection_utils/secure_detection.dart';
import 'package:evoapp/feature/splash_screen/utils/secure_detection_utils/secure_detection_impl.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockGetIt extends Mock implements GetIt {}

// Fake classes for fallback values
class FakeGetIt extends Fake implements GetIt {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(FakeGetIt());
  });

  group('AppUtilitiesModule', () {
    late AppUtilitiesModule module;
    late GetIt getIt;

    setUp(() {
      getIt = GetIt.instance;
      module = AppUtilitiesModule(getIt);
    });

    tearDown(() {
      // Reset GetIt for test isolation
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals(EvoModuleNames.appUtilities.value));
        expect(module.name, equals('app_utilities_module'));
      });

      test('should have CoreModule as dependency', () {
        expect(module.dependencies.length, equals(1));
        expect(module.dependencies.first, equals(CoreModule));
      });

      test('should be instance of FeatureModule', () {
        expect(module, isA<FeatureModule>());
      });
    });

    group('Registration', () {
      test('should register all utilities successfully as lazy singletons', () async {
        // Act
        await module.register();

        // Assert - Verify all services are registered with correct implementations
        expect(getIt.isRegistered<SecureDetection>(), isTrue);
        expect(getIt.isRegistered<ExitAppFeature>(), isTrue);
        expect(getIt.isRegistered<FeatureToggle>(), isTrue);

        final SecureDetection secureDetection = getIt.get<SecureDetection>();
        final ExitAppFeature exitAppFeature = getIt.get<ExitAppFeature>();
        final FeatureToggle featureToggle = getIt.get<FeatureToggle>();

        expect(secureDetection, isA<SecureDetectionImpl>());
        expect(exitAppFeature, isA<ExitAppFeatureImpl>());
        expect(featureToggle, isA<FeatureToggle>());

        // Verify singleton behavior
        expect(identical(secureDetection, getIt.get<SecureDetection>()), isTrue);
        expect(identical(exitAppFeature, getIt.get<ExitAppFeature>()), isTrue);
        expect(identical(featureToggle, getIt.get<FeatureToggle>()), isTrue);
      });
    });

    group('Individual Utility Registration', () {
      test('should register SecureDetection through registerSecurityUtilities', () {
        // Act
        module.registerSecurityUtilities();

        // Assert
        expect(getIt.isRegistered<SecureDetection>(), isTrue);
        expect(getIt.get<SecureDetection>(), isA<SecureDetectionImpl>());
      });

      test('should register ExitAppFeature through registerAppLifecycleUtilities', () {
        // Act
        module.registerAppLifecycleUtilities();

        // Assert
        expect(getIt.isRegistered<ExitAppFeature>(), isTrue);
        expect(getIt.get<ExitAppFeature>(), isA<ExitAppFeatureImpl>());
      });

      test('should register FeatureToggle through registerFeatureManagementUtilities', () {
        // Act
        module.registerFeatureManagementUtilities();

        // Assert
        expect(getIt.isRegistered<FeatureToggle>(), isTrue);
        expect(getIt.get<FeatureToggle>(), isA<FeatureToggle>());
      });
    });

    group('Error Handling', () {
      test('should handle multiple registration calls gracefully', () async {
        // Arrange - Register services first time
        await module.register();

        // Act & Assert - Second registration should not throw
        expect(() => module.register(), returnsNormally);

        // Verify services are still available
        expect(getIt.isRegistered<SecureDetection>(), isTrue);
        expect(getIt.isRegistered<ExitAppFeature>(), isTrue);
        expect(getIt.isRegistered<FeatureToggle>(), isTrue);
      });
    });

    group('Integration', () {
      test('should integrate with EvoModuleNames enum', () {
        expect(module.name, equals(EvoModuleNames.appUtilities.value));
        expect(EvoModuleNames.fromValue(module.name), equals(EvoModuleNames.appUtilities));
        expect(EvoModuleNames.utilityModules, contains(EvoModuleNames.appUtilities));
      });
    });

    group('Service Functionality', () {
      test('should provide functional FeatureToggle service with accessible properties', () async {
        // Arrange
        await module.register();
        final FeatureToggle featureToggle = getIt.get<FeatureToggle>();

        // Act & Assert - Test actual functionality
        expect(featureToggle.enableEventTrackingFeature, isA<bool>());
        expect(featureToggle.enableModularSystem, isA<bool>());
      });

      test('should maintain singleton instances across multiple registrations', () async {
        // Arrange
        await module.register();
        final SecureDetection firstInstance = getIt.get<SecureDetection>();

        // Act
        await module.register();
        final SecureDetection secondInstance = getIt.get<SecureDetection>();

        // Assert
        expect(identical(firstInstance, secondInstance), isTrue);
      });
    });
  });
}
