// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/utility/app_utilities_module.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/feature/splash_screen/utils/exit_app_feature/exit_app_feature.dart';
import 'package:evoapp/feature/splash_screen/utils/secure_detection_utils/secure_detection.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AppUtilitiesModule', () {
    late AppUtilitiesModule module;
    late GetIt getIt;

    setUp(() {
      getIt = GetIt.instance;
      module = AppUtilitiesModule(getIt);
    });

    tearDown(() {
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals('app_utilities_module'));
      });

      test('should have CoreModule as dependency', () {
        expect(module.dependencies.length, equals(1));
        expect(module.dependencies.first.toString(), contains('CoreModule'));
      });
    });

    group('Registration', () {
      test('should register all utilities successfully', () async {
        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<SecureDetection>(), isTrue);
        expect(getIt.isRegistered<ExitAppFeature>(), isTrue);
        expect(getIt.isRegistered<FeatureToggle>(), isTrue);
      });

      test('should register security utilities', () {
        // Act
        module.registerSecurityUtilities();

        // Assert
        expect(getIt.isRegistered<SecureDetection>(), isTrue);
        expect(getIt.get<SecureDetection>(), isA<SecureDetection>());
      });

      test('should register app lifecycle utilities', () {
        // Act
        module.registerAppLifecycleUtilities();

        // Assert
        expect(getIt.isRegistered<ExitAppFeature>(), isTrue);
        expect(getIt.get<ExitAppFeature>(), isA<ExitAppFeature>());
      });

      test('should register feature management utilities', () {
        // Act
        module.registerFeatureManagementUtilities();

        // Assert
        expect(getIt.isRegistered<FeatureToggle>(), isTrue);
        expect(getIt.get<FeatureToggle>(), isA<FeatureToggle>());
      });
    });

    group('Error Handling', () {
      test('should handle registration gracefully', () async {
        // This test ensures the module can handle registration without errors
        // when GetIt is properly initialized
        expect(() => module.register(), returnsNormally);
      });
    });

    group('Integration', () {
      test('should work with module test framework', () {
        // This test ensures the module follows the standard testing pattern
        expect(module, isA<FeatureModule>());
        expect(module.name, isNotEmpty);
        expect(module.dependencies, isNotEmpty);
      });
    });
  });
}
