// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:ui';

import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/base/modules/core/app_state_module.dart';
import 'package:evoapp/base/modules/evo_module_names.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/common_package/intl.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockPackageInfo extends Mock implements PackageInfo {}

void main() {
  group('AppStateModule', () {
    late AppStateModule module;
    late GetIt getIt;
    late MockPackageInfo mockPackageInfo;

    setUp(() {
      getIt = GetIt.instance;
      module = AppStateModule(getIt);
      mockPackageInfo = MockPackageInfo();
      
      // Register mock PackageInfo as async dependency
      getIt.registerSingletonAsync<PackageInfo>(() async => mockPackageInfo);
    });

    tearDown(() {
      // Reset AppState singleton and GetIt for test isolation
      AppState.resetInstance();
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals(EvoModuleNames.appState.value));
      });

      test('should have CoreModule as dependency', () {
        expect(module.dependencies.length, equals(1));
        expect(module.dependencies.first, equals(CoreModule));
      });

      test('should be instance of FeatureModule', () {
        expect(module, isA<FeatureModule>());
      });
    });

    group('Registration', () {
      test('should register AppState successfully with default configuration', () async {
        // Arrange
        when(() => mockPackageInfo.version).thenReturn('1.0.0');

        // Act
        await module.register();

        // Assert
        expect(getIt.isRegistered<AppState>(), isTrue);

        final AppState appState = getIt.get<AppState>();
        expect(appState, isA<AppState>());
        expect(appState.locale, equals(const Locale('vi')));
        expect(appState.appVersion, equals('1.0.0'));
        expect(Intl.defaultLocale, equals('vi'));

        // Verify singleton behavior
        final AppState instance2 = getIt.get<AppState>();
        expect(identical(appState, instance2), isTrue);
      });

      test('should handle different package versions', () async {
        // Arrange
        const String expectedVersion = '2.1.5';
        when(() => mockPackageInfo.version).thenReturn(expectedVersion);

        // Act
        await module.register();

        // Assert
        final AppState appState = getIt.get<AppState>();
        expect(appState.appVersion, equals(expectedVersion));
      });
    });

    group('AppState Initialization', () {
      test('should initialize AppState with custom locale', () async {
        // Arrange
        const Locale customLocale = Locale('en');
        when(() => mockPackageInfo.version).thenReturn('1.0.0');

        // Act
        await module.initializeAppState(locale: customLocale);

        // Assert
        final AppState appState = getIt.get<AppState>();
        expect(appState.locale, equals(customLocale));
        expect(Intl.defaultLocale, equals('en'));
      });

      test('should handle multiple initialization calls gracefully', () async {
        // Arrange
        when(() => mockPackageInfo.version).thenReturn('1.0.0');

        // Act
        await module.initializeAppState();
        final AppState firstInstance = getIt.get<AppState>();

        await module.initializeAppState(locale: const Locale('en'));
        final AppState secondInstance = getIt.get<AppState>();

        // Assert
        expect(identical(firstInstance, secondInstance), isTrue);
        expect(secondInstance.locale, equals(const Locale('en')));
      });
    });

    group('Error Handling', () {
      test('should handle registration gracefully when GetIt is properly initialized', () async {
        // Arrange
        when(() => mockPackageInfo.version).thenReturn('1.0.0');

        // Act & Assert
        expect(() => module.register(), returnsNormally);
      });

      test('should handle empty version from PackageInfo', () async {
        // Arrange
        when(() => mockPackageInfo.version).thenReturn('');

        // Act
        await module.register();

        // Assert
        final AppState appState = getIt.get<AppState>();
        expect(appState.appVersion, equals(''));
      });
    });

    group('Exception Scenarios', () {
      late GetIt isolatedGetIt;
      late AppStateModule isolatedModule;

      setUp(() {
        isolatedGetIt = GetIt.asNewInstance();
        isolatedModule = AppStateModule(isolatedGetIt);
      });

      tearDown(() {
        AppState.resetInstance();
        isolatedGetIt.reset();
      });

      test('should handle PackageInfo retrieval failure', () async {
        // Arrange
        isolatedGetIt.registerSingletonAsync<PackageInfo>(() async => throw Exception('PackageInfo error'));

        // Act & Assert
        expect(
          () => isolatedModule.register(),
          throwsA(isA<Exception>()),
        );
      });

      test('should rethrow exceptions during registration', () async {
        // Arrange
        isolatedGetIt.registerSingletonAsync<PackageInfo>(() async => throw StateError('Test error'));

        // Act & Assert
        expect(
          () => isolatedModule.register(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('Integration', () {
      test('should have focused responsibility for app state management', () {
        // Verify the module only handles app state concerns
        expect(module.name, contains('app_state'));
        expect(module.dependencies.length, equals(1)); // Only CoreModule
        expect(module.dependencies.first, equals(CoreModule));
      });

      test('should integrate with EvoModuleNames enum', () {
        expect(module.name, equals(EvoModuleNames.appState.value));
        expect(EvoModuleNames.fromValue(module.name), equals(EvoModuleNames.appState));
      });

      test('should be part of core modules group', () {
        expect(EvoModuleNames.coreModules, contains(EvoModuleNames.appState));
      });
    });

    group('Locale Configuration', () {
      test('should support different locale configurations', () async {
        // Arrange
        when(() => mockPackageInfo.version).thenReturn('1.0.0');
        const List<Locale> testLocales = [
          Locale('vi'),
          Locale('en'),
          Locale('ja'),
          Locale('ko'),
        ];

        for (final Locale locale in testLocales) {
          // Reset for each test
          AppState.resetInstance();
          if (getIt.isRegistered<AppState>()) {
            getIt.unregister<AppState>();
          }

          // Act
          await module.initializeAppState(locale: locale);

          // Assert
          final AppState appState = getIt.get<AppState>();
          expect(appState.locale, equals(locale));
          expect(Intl.defaultLocale, equals(locale.languageCode));
        }
      });
    });

    group('Singleton Management', () {
      test('should allow singleton reset for testing purposes', () async {
        // Arrange
        when(() => mockPackageInfo.version).thenReturn('1.0.0');
        await module.register();
        final AppState originalInstance = getIt.get<AppState>();

        // Act
        AppState.resetInstance();
        if (getIt.isRegistered<AppState>()) {
          getIt.unregister<AppState>();
        }
        await module.register();
        final AppState newInstance = getIt.get<AppState>();

        // Assert
        expect(identical(originalInstance, newInstance), isFalse);
      });
    });
  });
}
