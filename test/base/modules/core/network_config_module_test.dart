// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/network_module.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/data/http_client/dio_log_interceptor.dart';
import 'package:flutter_common_package/data/http_client/dio_factory.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo_impl.dart';
import 'package:flutter_common_package/common_package/common_package.dart';



import 'package:evoapp/base/modules/core/network_config_module.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/base/modules/evo_module_names.dart';
import 'package:evoapp/base/modules/constants.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/authentication_repo_impl.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_handler.dart';
import 'package:evoapp/util/interceptor/log_event_interceptor.dart';
import 'package:evoapp/util/interceptor/unauthorized_interceptor.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';

// Mock classes
class MockDio extends Mock implements Dio {}
class MockDioFactory extends Mock implements DioFactory {}
class MockAuthenticationRepo extends Mock implements AuthenticationRepo {}
class MockAppState extends Mock implements AppState {}
class MockJwtHelper extends Mock implements JwtHelper {}
class MockAuthorizationSessionExpiredHandler extends Mock implements AuthorizationSessionExpiredHandler {}
class MockCommonHttpClient extends Mock implements CommonHttpClient {}
class MockInterceptors extends Mock implements Interceptors {}
class MockPackageInfo extends Mock implements PackageInfo {}


// Fake classes for fallback values
class FakeGetIt extends Fake implements GetIt {}
class FakeInterceptor extends Fake implements Interceptor {}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(FakeGetIt());
    registerFallbackValue(FakeInterceptor());
  });
  group('NetworkConfigModule', () {
    late NetworkConfigModule module;
    late GetIt getIt;
    late MockDio mockDio;
    late MockDioFactory mockDioFactory;
    late MockAuthenticationRepo mockAuthRepo;
    late MockAppState mockAppState;
    late MockJwtHelper mockJwtHelper;
    late MockAuthorizationSessionExpiredHandler mockSessionHandler;
    late MockCommonHttpClient mockHttpClient;
    late MockInterceptors mockInterceptors;

    setUp(() {
      getIt = GetIt.instance;
      module = NetworkConfigModule(getIt);


      mockDio = MockDio();
      mockDioFactory = MockDioFactory();
      mockAuthRepo = MockAuthenticationRepo();
      mockAppState = MockAppState();
      mockJwtHelper = MockJwtHelper();
      mockSessionHandler = MockAuthorizationSessionExpiredHandler();
      mockHttpClient = MockCommonHttpClient();
      mockInterceptors = MockInterceptors();

      // Register required dependencies
      getIt.registerSingleton<Dio>(mockDio);
      getIt.registerSingleton<AuthenticationRepo>(mockAuthRepo);
      getIt.registerSingleton<AppState>(mockAppState);
      getIt.registerSingleton<JwtHelper>(mockJwtHelper);
      getIt.registerSingleton<AuthorizationSessionExpiredHandler>(mockSessionHandler);



      // Setup mock behaviors
      when(() => mockDio.interceptors).thenReturn(mockInterceptors);
      when(() => mockInterceptors.addAll(any())).thenReturn(null);
      when(() => mockInterceptors.add(any())).thenReturn(null);
    });

    tearDown(() {
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals(EvoModuleNames.networkConfig.value));
      });

      test('should have CoreModule and NetworkModule as dependencies', () {
        expect(module.dependencies.length, equals(2));
        expect(module.dependencies, contains(CoreModule));
        expect(module.dependencies, contains(NetworkModule));
      });

      test('should be instance of FeatureModule', () {
        expect(module, isA<FeatureModule>());
      });
    });

    group('Registration', () {
      test('should call setUpDioInterceptor during registration', () {
        // Act
        module.setUpDioInterceptor();

        // Assert - Verify that interceptor setup was called
        verify(() => mockDio.interceptors.addAll(any())).called(1);
      });

      test('should handle registration errors gracefully', () async {
        // Arrange
        when(() => mockDio.interceptors).thenThrow(Exception('Setup failed'));

        // Act & Assert
        expect(
          () => module.setUpDioInterceptor(),
          throwsA(isA<Exception>()),
        );
      });
    });



    group('Dio Interceptor Setup', () {
      test('should set up interceptors on main Dio instance', () {
        // Act
        module.setUpDioInterceptor();

        // Assert
        verify(() => mockDio.interceptors.addAll(any())).called(1);
      });

      test('should handle interceptor setup errors', () {
        // Arrange
        when(() => mockDio.interceptors).thenThrow(StateError('Interceptor error'));

        // Act & Assert
        expect(
          () => module.setUpDioInterceptor(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('Interceptor Chain Building', () {
      test('should build interceptor chain in correct order', () {
        // Act
        final List<Interceptor> interceptors = module.buildInterceptorChain();

        // Assert
        expect(interceptors.length, greaterThanOrEqualTo(2)); // At least LogEventInterceptor and UnauthorizedInterceptor

        // Verify types are in correct order
        int logEventIndex = -1;
        int unauthorizedIndex = -1;

        for (int i = 0; i < interceptors.length; i++) {
          if (interceptors[i] is LogEventInterceptor) {
            logEventIndex = i;
          } else if (interceptors[i] is UnauthorizedInterceptor) {
            unauthorizedIndex = i;
          }
        }

        expect(logEventIndex, greaterThanOrEqualTo(0));
        expect(unauthorizedIndex, greaterThanOrEqualTo(0));
        expect(logEventIndex, lessThan(unauthorizedIndex)); // LogEvent should come before Unauthorized
      });

      test('should include debug interceptor in debug mode', () {
        // Act
        final List<Interceptor> interceptors = module.buildInterceptorChain();

        // Assert
        if (kDebugMode) {
          expect(interceptors.any((interceptor) => interceptor is DioLogInterceptor), isTrue);
        }
      });
    });

    group('Individual Interceptor Creation', () {
      test('createDebugLogInterceptor should return interceptor in debug mode', () {
        // Act
        final DioLogInterceptor? interceptor = module.createDebugLogInterceptor();

        // Assert
        if (kDebugMode) {
          expect(interceptor, isNotNull);
          expect(interceptor, isA<DioLogInterceptor>());
        } else {
          expect(interceptor, isNull);
        }
      });

      test('createEventLogInterceptor should return LogEventInterceptor', () {
        // Act
        final LogEventInterceptor interceptor = module.createEventLogInterceptor();

        // Assert
        expect(interceptor, isA<LogEventInterceptor>());
        expect(interceptor.eventLogPath, equals(LoggingRepoImpl.logUrl));
      });

      test('createUnauthorizedInterceptor should return configured interceptor', () {
        // Act
        final UnauthorizedInterceptor interceptor = module.createUnauthorizedInterceptor();

        // Assert
        expect(interceptor, isA<UnauthorizedInterceptor>());
        expect(interceptor.ignoredRefreshTokenApiPath, contains(AuthenticationRepoImpl.signInUrl));
        expect(interceptor.ignoredVerdictEmitUnauthorized, contains(ResetPinEntity.verdictExpiredResetPinSession));
        expect(interceptor.ignoredVerdictEmitUnauthorized, contains(ResetPinEntity.verdictInvalidResetPinSession));
        expect(interceptor.ignoredVerdictEmitUnauthorized, contains(ResetPinEntity.verdictMissingResetPinSession));
      });
    });

    group('Configuration Methods', () {
      test('getIgnoredRefreshTokenPaths should return correct paths', () {
        // Act
        final List<String> paths = module.getIgnoredRefreshTokenPaths();

        // Assert
        expect(paths, isNotEmpty);
        expect(paths, contains(AuthenticationRepoImpl.signInUrl));
      });

      test('getIgnoredUnauthorizedVerdicts should return correct verdicts', () {
        // Act
        final List<String> verdicts = module.getIgnoredUnauthorizedVerdicts();

        // Assert
        expect(verdicts, hasLength(3));
        expect(verdicts, contains(ResetPinEntity.verdictExpiredResetPinSession));
        expect(verdicts, contains(ResetPinEntity.verdictInvalidResetPinSession));
        expect(verdicts, contains(ResetPinEntity.verdictMissingResetPinSession));
      });
    });

    group('Error Handling', () {
      test('should handle missing Dio dependency', () {
        // Arrange
        getIt.unregister<Dio>(); // Remove Dio dependency

        // Act & Assert
        expect(
          () => module.setUpDioInterceptor(),
          throwsA(isA<StateError>()),
        );
      });

      test('should handle interceptor creation errors gracefully', () {
        // Arrange
        getIt.unregister<AuthenticationRepo>();

        // Act & Assert
        expect(
          () => module.createUnauthorizedInterceptor(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('Integration', () {
      test('should integrate with EvoModuleNames enum', () {
        expect(module.name, equals(EvoModuleNames.networkConfig.value));
        expect(EvoModuleNames.fromValue(module.name), equals(EvoModuleNames.networkConfig));
      });

      test('should be part of core modules group', () {
        expect(EvoModuleNames.coreModules, contains(EvoModuleNames.networkConfig));
      });
    });


  });
}
