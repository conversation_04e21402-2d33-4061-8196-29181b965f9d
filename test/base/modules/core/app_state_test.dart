// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:ui';

import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/biometric/model/biometric_status_change_notifier.dart';
import 'package:evoapp/feature/biometric/model/biometric_ui_model.dart';
import 'package:evoapp/feature/inactive_detector/inactive_detector.dart';
import 'package:evoapp/feature/logging/event_tracking_shared_data.dart';
import 'package:evoapp/feature/pin/models/change_pin_status.dart';
import 'package:evoapp/model/user_info_notifier.dart';
import 'package:evoapp/model/user_status.dart';
import 'package:evoapp/model/user_token.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AppState', () {
    tearDown(() {
      // Reset singleton instance after each test to ensure test isolation
      AppState.resetInstance();
    });

    group('Singleton Pattern', () {
      test('should return the same instance when called multiple times', () {
        // Arrange & Act
        final AppState instance1 = AppState();
        final AppState instance2 = AppState();

        // Assert
        expect(identical(instance1, instance2), isTrue);
      });

      test('should create new instance after resetInstance is called', () {
        // Arrange
        final AppState instance1 = AppState();

        // Act
        AppState.resetInstance();
        final AppState instance2 = AppState();

        // Assert
        expect(identical(instance1, instance2), isFalse);
      });

      test('should handle concurrent access to singleton', () {
        // Arrange & Act - simulate concurrent access
        final List<AppState> instances = List.generate(5, (_) => AppState());

        // Assert - all instances should be identical
        for (int i = 1; i < instances.length; i++) {
          expect(identical(instances[0], instances[i]), isTrue);
        }
      });
    });

    group('Default Property Values', () {
      late AppState appState;

      setUp(() {
        appState = AppState();
      });

      test('should initialize locale with Vietnamese locale', () {
        expect(appState.locale, equals(const Locale('vi')));
      });

      test('should initialize appVersion as null', () {
        expect(appState.appVersion, isNull);
      });

      test('should initialize userInfo with null value', () {
        expect(appState.userInfo, isA<UserInfoNotifier>());
        expect(appState.userInfo.value, isNull);
      });

      test('should initialize isCheckShowUpdateOnce as null', () {
        expect(appState.isCheckShowUpdateOnce, isNull);
      });

      test('should initialize isUserLogIn as false', () {
        expect(appState.isUserLogIn, isFalse);
      });

      test('should initialize bioTypeInfo with faceAndFinger model', () {
        expect(appState.bioTypeInfo, isA<BiometricTypeUIModel>());
        // Verify it's the faceAndFinger type by checking properties
        final BiometricTypeUIModel expected = BiometricTypeUIModel.faceAndFinger();
        expect(appState.bioTypeInfo.biometricTypeName, equals(expected.biometricTypeName));
        expect(appState.bioTypeInfo.iconPath, equals(expected.iconPath));
        expect(appState.bioTypeInfo.iconSettingPath, equals(expected.iconSettingPath));
      });

      test('should initialize biometricStatusChangeNotifier with usable status', () {
        expect(appState.biometricStatusChangeNotifier, isA<BiometricStatusChangeNotifier>());
        expect(appState.biometricStatusChangeNotifier.value, equals(BiometricStatus.usable));
      });

      test('should initialize actionAfterLogin as null', () {
        expect(appState.actionAfterLogin, isNull);
      });

      test('should initialize appLifecycleState as null', () {
        expect(appState.appLifecycleState, isNull);
      });

      test('should initialize userStatus as null', () {
        expect(appState.userStatus, isNull);
      });

      test('should initialize inactiveDetectorController', () {
        expect(appState.inactiveDetectorController, isA<InactiveDetectorController>());
      });

      test('should initialize changePinStatusNotifier with available status', () {
        expect(appState.changePinStatusNotifier, isA<ChangePinStatusNotifier>());
        expect(appState.changePinStatusNotifier.value, equals(ChangePinStatus.available));
      });
    });

    group('Property Assignment', () {
      late AppState appState;

      setUp(() {
        appState = AppState();
      });

      test('should allow setting and getting locale', () {
        // Arrange
        const Locale newLocale = Locale('en');

        // Act
        appState.locale = newLocale;

        // Assert
        expect(appState.locale, equals(newLocale));
      });

      test('should allow setting and getting appVersion', () {
        // Arrange
        const String version = '1.0.0';

        // Act
        appState.appVersion = version;

        // Assert
        expect(appState.appVersion, equals(version));
      });

      test('should allow setting and getting isCheckShowUpdateOnce', () {
        // Act
        appState.isCheckShowUpdateOnce = true;

        // Assert
        expect(appState.isCheckShowUpdateOnce, isTrue);
      });

      test('should allow setting and getting isUserLogIn', () {
        // Act
        appState.isUserLogIn = true;

        // Assert
        expect(appState.isUserLogIn, isTrue);
      });

      test('should allow setting and getting actionAfterLogin', () {
        // Arrange
        bool actionCalled = false;
        void testAction() {
          actionCalled = true;
        }

        // Act
        appState.actionAfterLogin = testAction;
        appState.actionAfterLogin?.call();

        // Assert
        expect(appState.actionAfterLogin, equals(testAction));
        expect(actionCalled, isTrue);
      });

      test('should allow setting and getting appLifecycleState', () {
        // Act
        appState.appLifecycleState = AppLifecycleState.paused;

        // Assert
        expect(appState.appLifecycleState, equals(AppLifecycleState.paused));
      });

      test('should allow setting and getting userStatus', () {
        // Act
        appState.userStatus = UserStatus.hasJustActivated;

        // Assert
        expect(appState.userStatus, equals(UserStatus.hasJustActivated));
      });
    });

    group('UserToken Management', () {
      late AppState appState;

      setUp(() {
        appState = AppState();
      });

      test('should create new UserToken when accessed for first time', () {
        // Act
        final UserToken token = appState.userToken;

        // Assert
        expect(token, isA<UserToken>());
        expect(token.accessToken, isNull);
        expect(token.refreshToken, isNull);
      });

      test('should return same UserToken instance on subsequent calls', () {
        // Act
        final UserToken token1 = appState.userToken;
        final UserToken token2 = appState.userToken;

        // Assert
        expect(identical(token1, token2), isTrue);
      });

      test('should allow setting custom UserToken', () {
        // Arrange
        final UserToken customToken = UserToken()
          ..accessToken = 'test_access_token'
          ..refreshToken = 'test_refresh_token';

        // Act
        appState.userToken = customToken;

        // Assert
        expect(appState.userToken, equals(customToken));
        expect(appState.userToken.accessToken, equals('test_access_token'));
        expect(appState.userToken.refreshToken, equals('test_refresh_token'));
      });

      test('should allow setting UserToken to null', () {
        // Arrange
        appState.userToken = UserToken(); // Set initial token

        // Act
        appState.userToken = null;

        // Assert
        // When accessed again, should create new instance
        final UserToken newToken = appState.userToken;
        expect(newToken, isA<UserToken>());
        expect(newToken.accessToken, isNull);
        expect(newToken.refreshToken, isNull);
      });
    });

    group('Event Tracking', () {
      late AppState appState;

      setUp(() {
        appState = AppState();
      });

      test('should create EventTrackingSharedData lazily', () {
        // Act
        final EventTrackingSharedData sharedData = appState.eventTrackingSharedData;

        // Assert
        expect(sharedData, isA<EventTrackingSharedData>());
      });

      test('should return same EventTrackingSharedData instance on subsequent calls', () {
        // Act
        final EventTrackingSharedData sharedData1 = appState.eventTrackingSharedData;
        final EventTrackingSharedData sharedData2 = appState.eventTrackingSharedData;

        // Assert
        expect(identical(sharedData1, sharedData2), isTrue);
      });

      test('currentScreenId should handle null and undefined states correctly', () {
        // Test 1: Default state should return undefined
        expect(appState.currentScreenId, equals(EventTrackingScreenId.undefined));

        // Test 2: When explicitly set to null, should return undefined
        appState.eventTrackingSharedData.currentScreenId = null;
        expect(appState.currentScreenId, equals(EventTrackingScreenId.undefined));

        // Test 3: When set to a specific value, should return that value
        const EventTrackingScreenId testScreenId = EventTrackingScreenId('test_screen');
        appState.eventTrackingSharedData.currentScreenId = testScreenId;
        expect(appState.currentScreenId, equals(testScreenId));
      });
    });

    group('Notifier Behavior', () {
      late AppState appState;

      setUp(() {
        appState = AppState();
      });

      test('should update biometricStatusChangeNotifier and ignore duplicate updates', () {
        // Arrange
        const BiometricStatus initialStatus = BiometricStatus.usable;
        const BiometricStatus newStatus = BiometricStatus.deviceSettingChanged;

        // Verify initial state
        expect(appState.biometricStatusChangeNotifier.value, equals(initialStatus));

        // Act - update to new status
        appState.biometricStatusChangeNotifier.update(newStatus);
        expect(appState.biometricStatusChangeNotifier.value, equals(newStatus));

        // Act - try to update to same status (should be ignored)
        appState.biometricStatusChangeNotifier.update(newStatus);
        expect(appState.biometricStatusChangeNotifier.value, equals(newStatus));
      });

      test('should update changePinStatusNotifier value', () {
        // Arrange
        expect(appState.changePinStatusNotifier.value, equals(ChangePinStatus.available));

        // Act
        appState.changePinStatusNotifier.value = ChangePinStatus.locked;

        // Assert
        expect(appState.changePinStatusNotifier.value, equals(ChangePinStatus.locked));
      });

      test('should maintain independent notifier states across singleton resets', () {
        // Arrange
        final AppState appState1 = AppState();
        appState1.biometricStatusChangeNotifier.update(BiometricStatus.deviceSettingChanged);
        appState1.changePinStatusNotifier.value = ChangePinStatus.locked;

        // Act - reset and create new instance
        AppState.resetInstance();
        final AppState appState2 = AppState();

        // Assert - new instance should have default values, not previous state
        expect(appState2.biometricStatusChangeNotifier.value, equals(BiometricStatus.usable));
        expect(appState2.changePinStatusNotifier.value, equals(ChangePinStatus.available));

        // Old instance should maintain its state
        expect(appState1.biometricStatusChangeNotifier.value, equals(BiometricStatus.deviceSettingChanged));
        expect(appState1.changePinStatusNotifier.value, equals(ChangePinStatus.locked));
      });
    });

    group('State Consistency and Null Handling', () {
      late AppState appState;

      setUp(() {
        appState = AppState();
      });

      test('should maintain state consistency across multiple property changes', () {
        // Arrange
        const String version = '2.0.0';
        const Locale locale = Locale('en');
        final UserToken token = UserToken()
          ..accessToken = 'access'
          ..refreshToken = 'refresh';

        // Act - set multiple properties
        appState.appVersion = version;
        appState.locale = locale;
        appState.userToken = token;
        appState.isUserLogIn = true;
        appState.userStatus = UserStatus.hasJustActivated;
        appState.appLifecycleState = AppLifecycleState.resumed;

        // Assert - verify all properties maintain their values
        expect(appState.appVersion, equals(version));
        expect(appState.locale, equals(locale));
        expect(appState.userToken, equals(token));
        expect(appState.isUserLogIn, isTrue);
        expect(appState.userStatus, equals(UserStatus.hasJustActivated));
        expect(appState.appLifecycleState, equals(AppLifecycleState.resumed));
      });

      test('should handle null assignments and lazy initialization correctly', () {
        // Arrange - set some initial values
        appState.appVersion = 'test';
        appState.isCheckShowUpdateOnce = true;
        appState.userStatus = UserStatus.hasJustActivated;
        final UserToken initialToken = appState.userToken; // Force creation

        // Act - set properties to null
        appState.appVersion = null;
        appState.userToken = null;
        appState.isCheckShowUpdateOnce = null;
        appState.actionAfterLogin = null;
        appState.appLifecycleState = null;
        appState.userStatus = null;

        // Assert - verify null assignments
        expect(appState.appVersion, isNull);
        expect(appState.isCheckShowUpdateOnce, isNull);
        expect(appState.actionAfterLogin, isNull);
        expect(appState.appLifecycleState, isNull);
        expect(appState.userStatus, isNull);

        // Assert - userToken should create new instance when accessed after null assignment
        final UserToken newToken = appState.userToken;
        expect(newToken, isA<UserToken>());
        expect(identical(initialToken, newToken), isFalse);
        expect(newToken.accessToken, isNull);
        expect(newToken.refreshToken, isNull);
      });
    });

    group('State Isolation and Reset Behavior', () {
      test('should maintain state independence after singleton reset', () {
        // Arrange
        final AppState instance1 = AppState();
        instance1.appVersion = 'v1';
        instance1.isUserLogIn = true;
        instance1.userStatus = UserStatus.hasJustActivated;

        // Act
        AppState.resetInstance();
        final AppState instance2 = AppState();

        // Assert - new instance should have default values
        expect(instance2.appVersion, isNull);
        expect(instance2.isUserLogIn, isFalse);
        expect(instance2.userStatus, isNull);
        expect(identical(instance1, instance2), isFalse);
      });

      test('should handle complex EventTrackingSharedData state management', () {
        // Arrange
        final AppState appState = AppState();
        const EventTrackingScreenId screenId = EventTrackingScreenId('test_screen');

        // Act - set all tracking data
        appState.eventTrackingSharedData.currentScreenId = screenId;
        appState.eventTrackingSharedData.urlPath = '/current';
        appState.eventTrackingSharedData.previousUrlPath = '/previous';

        // Assert - verify all data is correctly set and accessible
        expect(appState.currentScreenId, equals(screenId));
        expect(appState.eventTrackingSharedData.urlPath, equals('/current'));
        expect(appState.eventTrackingSharedData.previousUrlPath, equals('/previous'));

        // Act - clear data
        appState.eventTrackingSharedData.clear();

        // Assert - verify data is cleared and currentScreenId falls back to undefined
        expect(appState.currentScreenId, equals(EventTrackingScreenId.undefined));
        expect(appState.eventTrackingSharedData.urlPath, isNull);
        expect(appState.eventTrackingSharedData.previousUrlPath, isNull);
      });

      test('should handle actionAfterLogin function lifecycle correctly', () {
        // Arrange
        final AppState appState = AppState();
        int executionCount = 0;

        void incrementAction() => executionCount++;
        void doubleIncrementAction() => executionCount += 2;

        // Test 1: Initial state
        expect(appState.actionAfterLogin, isNull);

        // Test 2: Set and execute action
        appState.actionAfterLogin = incrementAction;
        appState.actionAfterLogin?.call();
        expect(executionCount, equals(1));

        // Test 3: Replace action and execute
        appState.actionAfterLogin = doubleIncrementAction;
        appState.actionAfterLogin?.call();
        expect(executionCount, equals(3)); // 1 + 2

        // Test 4: Clear action
        appState.actionAfterLogin = null;
        appState.actionAfterLogin?.call(); // Should not execute
        expect(executionCount, equals(3)); // No change
      });
    });
  });
}
