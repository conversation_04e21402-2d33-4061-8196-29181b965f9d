// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/base/modules/core/auth_biometric_module.dart';
import 'package:evoapp/base/modules/evo_module_names.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_service.dart';
import 'package:evoapp/feature/biometric/utils/biometric_functions.dart';
import 'package:evoapp/feature/biometric/utils/biometric_status_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometric_type_helper.dart';
import 'package:evoapp/feature/biometric/utils/biometrics_authenticate.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:evoapp/util/token_utils/jwt_helper.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/network_module.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:ts_bio_detect_changed/ts_bio_detect_changed.dart';

class MockCommonHttpClient extends Mock implements CommonHttpClient {}
class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}
class MockAppState extends Mock implements AppState {}
class MockEvoFlutterWrapper extends Mock implements EvoFlutterWrapper {}
class MockDeviceInfoPlugin extends Mock implements DeviceInfoPlugin {}

void main() {
  group('AuthBiometricModule', () {
    late AuthBiometricModule module;
    late GetIt getIt;
    late MockCommonHttpClient mockHttpClient;
    late MockCommonHttpClient mockNonAuthHttpClient;
    late MockEvoLocalStorageHelper mockStorageHelper;
    late MockAppState mockAppState;
    late MockEvoFlutterWrapper mockFlutterWrapper;
    late MockDeviceInfoPlugin mockDeviceInfoPlugin;

    setUp(() {
      getIt = GetIt.instance;
      module = AuthBiometricModule(getIt);
      mockHttpClient = MockCommonHttpClient();
      mockNonAuthHttpClient = MockCommonHttpClient();
      mockStorageHelper = MockEvoLocalStorageHelper();
      mockAppState = MockAppState();
      mockFlutterWrapper = MockEvoFlutterWrapper();
      mockDeviceInfoPlugin = MockDeviceInfoPlugin();

      // Register required dependencies
      getIt.registerSingleton<CommonHttpClient>(mockHttpClient);
      getIt.registerSingleton<CommonHttpClient>(
        mockNonAuthHttpClient,
        instanceName: 'NonAuthenticationHttpClient',
      );
      getIt.registerSingleton<EvoLocalStorageHelper>(mockStorageHelper);
      getIt.registerSingleton<AppState>(mockAppState);
      getIt.registerSingleton<EvoFlutterWrapper>(mockFlutterWrapper);
      getIt.registerSingleton<DeviceInfoPlugin>(mockDeviceInfoPlugin);
    });

    tearDown(() {
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals(EvoModuleNames.authBiometric.value));
      });

      test('should have CoreModule and NetworkModule as dependencies', () {
        expect(module.dependencies.length, equals(2));
        expect(module.dependencies, contains(CoreModule));
        expect(module.dependencies, contains(NetworkModule));
      });

      test('should be instance of FeatureModule', () {
        expect(module, isA<FeatureModule>());
      });
    });

    group('Registration', () {
      test('should register all authentication and biometric services successfully', () async {
        // Act
        await module.register();

        // Assert - Core services
        expect(getIt.isRegistered<JwtHelper>(), isTrue);
        expect(getIt.isRegistered<AuthenticationRepo>(), isTrue);

        // Assert - Biometric services
        expect(getIt.isRegistered<BiometricsAuthenticate>(), isTrue);
        expect(getIt.isRegistered<TsBioDetectChanged>(), isTrue);
        expect(getIt.isRegistered<BiometricAuthenticationService>(), isTrue);
        expect(getIt.isRegistered<BiometricTypeHelper>(), isTrue);
        expect(getIt.isRegistered<BiometricStatusHelper>(), isTrue);
        expect(getIt.isRegistered<BiometricFunctions>(), isTrue);
      });

      test('should register authentication repository with both HTTP client dependencies', () async {
        // Act
        await module.register();

        // Assert - Verify repository is created with correct dependencies
        expect(getIt.isRegistered<AuthenticationRepo>(), isTrue);
        final AuthenticationRepo authRepo = getIt.get<AuthenticationRepo>();
        expect(authRepo, isA<AuthenticationRepo>());

        // Verify both HTTP clients are accessible (authenticated and non-authenticated)
        expect(getIt.isRegistered<CommonHttpClient>(), isTrue);
        expect(getIt.isRegistered<CommonHttpClient>(instanceName: 'NonAuthenticationHttpClient'), isTrue);
      });

      test('should configure biometric authenticate with strong biometric enforcement', () async {
        // Act
        await module.register();

        // Assert - Verify BiometricsAuthenticate is properly configured
        expect(getIt.isRegistered<BiometricsAuthenticate>(), isTrue);
        final BiometricsAuthenticate biometric = getIt.get<BiometricsAuthenticate>();
        expect(biometric, isA<BiometricsAuthenticate>());
        // The implementation should be BiometricAuthenticateImpl with isForceStrongBiometric: true
      });

      test('should register biometric authentication service with all required dependencies', () async {
        // Act
        await module.register();

        // Assert - Verify service is registered and has access to all dependencies
        expect(getIt.isRegistered<BiometricAuthenticationService>(), isTrue);
        final BiometricAuthenticationService service = getIt.get<BiometricAuthenticationService>();
        expect(service, isA<BiometricAuthenticationService>());

        // Verify all dependencies are available for the service
        expect(getIt.isRegistered<BiometricsAuthenticate>(), isTrue);
        expect(getIt.isRegistered<EvoLocalStorageHelper>(), isTrue);
        expect(getIt.isRegistered<TsBioDetectChanged>(), isTrue);
        expect(getIt.isRegistered<JwtHelper>(), isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle registration gracefully when dependencies are available', () async {
        // Act & Assert
        expect(() => module.register(), returnsNormally);
      });

      test('should handle registration errors properly', () async {
        // This test verifies that the module has proper error handling structure
        // The actual error throwing is tested in the Exception Scenarios group
        expect(module.register, isA<Function>());
      });
    });

    group('Integration', () {
      test('should integrate with EvoModuleNames enum and be part of core modules', () {
        expect(module.name, equals(EvoModuleNames.authBiometric.value));
        expect(EvoModuleNames.fromValue(module.name), equals(EvoModuleNames.authBiometric));
        expect(EvoModuleNames.coreModules, contains(EvoModuleNames.authBiometric));
      });

      test('should have focused responsibility for authentication and biometric services', () {
        expect(module.name, contains('auth_biometric'));
        expect(module.dependencies.length, equals(2)); // CoreModule and NetworkModule only
        expect(module.dependencies, contains(CoreModule));
        expect(module.dependencies, contains(NetworkModule));
      });
    });

    group('Exception Scenarios', () {
      late GetIt isolatedGetIt;
      late AuthBiometricModule isolatedModule;

      setUp(() {
        isolatedGetIt = GetIt.asNewInstance();
        isolatedModule = AuthBiometricModule(isolatedGetIt);
      });

      tearDown(() {
        isolatedGetIt.reset();
      });

      test('should handle missing CommonHttpClient dependency', () async {
        // Arrange - Register only partial dependencies
        isolatedGetIt.registerSingleton<EvoLocalStorageHelper>(mockStorageHelper);
        isolatedGetIt.registerSingleton<AppState>(mockAppState);

        // Act & Assert
        expect(
          () => isolatedModule.register(),
          throwsA(isA<StateError>()),
        );
      });

      test('should handle missing EvoLocalStorageHelper dependency', () async {
        // Arrange - Register only partial dependencies
        isolatedGetIt.registerSingleton<CommonHttpClient>(mockHttpClient);
        isolatedGetIt.registerSingleton<CommonHttpClient>(
          mockNonAuthHttpClient,
          instanceName: 'NonAuthenticationHttpClient',
        );
        isolatedGetIt.registerSingleton<AppState>(mockAppState);

        // Act & Assert
        expect(
          () => isolatedModule.register(),
          throwsA(isA<StateError>()),
        );
      });

      test('should handle missing AppState dependency', () async {
        // Arrange - Register only partial dependencies
        isolatedGetIt.registerSingleton<CommonHttpClient>(mockHttpClient);
        isolatedGetIt.registerSingleton<CommonHttpClient>(
          mockNonAuthHttpClient,
          instanceName: 'NonAuthenticationHttpClient',
        );
        isolatedGetIt.registerSingleton<EvoLocalStorageHelper>(mockStorageHelper);

        // Act & Assert
        expect(
          () => isolatedModule.register(),
          throwsA(isA<StateError>()),
        );
      });

      test('should handle missing non-authentication HTTP client', () async {
        // Arrange - Register dependencies except non-auth HTTP client
        isolatedGetIt.registerSingleton<CommonHttpClient>(mockHttpClient);
        isolatedGetIt.registerSingleton<EvoLocalStorageHelper>(mockStorageHelper);
        isolatedGetIt.registerSingleton<AppState>(mockAppState);
        isolatedGetIt.registerSingleton<EvoFlutterWrapper>(mockFlutterWrapper);
        isolatedGetIt.registerSingleton<DeviceInfoPlugin>(mockDeviceInfoPlugin);

        // Act & Assert
        expect(
          () => isolatedModule.register(),
          throwsA(isA<StateError>()),
        );
      });
    });

    group('Service Lifecycle', () {
      test('should maintain singleton behavior for core services', () async {
        // Act
        await module.register();

        // Assert - Verify singleton behavior for services that should be singletons
        final JwtHelper jwt1 = getIt.get<JwtHelper>();
        final JwtHelper jwt2 = getIt.get<JwtHelper>();
        expect(identical(jwt1, jwt2), isTrue);

        final AuthenticationRepo repo1 = getIt.get<AuthenticationRepo>();
        final AuthenticationRepo repo2 = getIt.get<AuthenticationRepo>();
        expect(identical(repo1, repo2), isTrue);

        final BiometricsAuthenticate bio1 = getIt.get<BiometricsAuthenticate>();
        final BiometricsAuthenticate bio2 = getIt.get<BiometricsAuthenticate>();
        expect(identical(bio1, bio2), isTrue);
      });

      test('should provide factory behavior for authentication service', () async {
        // Act
        await module.register();

        // Assert - BiometricAuthenticationService is registered as factory
        expect(getIt.isRegistered<BiometricAuthenticationService>(), isTrue);
        final BiometricAuthenticationService service = getIt.get<BiometricAuthenticationService>();
        expect(service, isA<BiometricAuthenticationService>());
        // Note: Even though registered as factory, the actual behavior depends on implementation
      });
    });
  });
}
