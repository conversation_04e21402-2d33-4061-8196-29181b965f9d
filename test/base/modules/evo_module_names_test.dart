// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:flutter_test/flutter_test.dart';
import 'package:evoapp/base/modules/evo_module_names.dart';

void main() {
  group('EvoModuleNames', () {
    group('Enum Values', () {
      test('should have correct app state module name', () {
        expect(EvoModuleNames.appState.value, equals('app_state_module'));
      });

      test('should have correct auth biometric module name', () {
        expect(EvoModuleNames.authBiometric.value, equals('auth_biometric_module'));
      });

      test('should have correct network config module name', () {
        expect(EvoModuleNames.networkConfig.value, equals('network_config_module'));
      });

      test('should have correct storage repository module name', () {
        expect(EvoModuleNames.storageRepository.value, equals('storage_repository_module'));
      });

      test('should have correct pin reset module name', () {
        expect(EvoModuleNames.pinReset.value, equals('pin_reset_module'));
      });

      test('should have correct authentication session module name', () {
        expect(EvoModuleNames.authenticationSession.value, equals('authentication_session_module'));
      });

      test('should have correct navigation theme module name', () {
        expect(EvoModuleNames.navigationTheme.value, equals('navigation_theme_module'));
      });

      test('should have correct app utilities module name', () {
        expect(EvoModuleNames.appUtilities.value, equals('app_utilities_module'));
      });

      test('should have correct privilege validation module name', () {
        expect(EvoModuleNames.privilegeValidation.value, equals('privilege_validation_module'));
      });

      test('should have correct utility wrapper module name', () {
        expect(EvoModuleNames.utilityWrapper.value, equals('utility_wrapper_module'));
      });
    });

    group('Enum Properties', () {
      test('should have all expected enum values', () {
        final allValues = EvoModuleNames.values;

        expect(allValues, hasLength(10));
        expect(allValues, contains(EvoModuleNames.appState));
        expect(allValues, contains(EvoModuleNames.authBiometric));
        expect(allValues, contains(EvoModuleNames.networkConfig));
        expect(allValues, contains(EvoModuleNames.storageRepository));
        expect(allValues, contains(EvoModuleNames.pinReset));
        expect(allValues, contains(EvoModuleNames.authenticationSession));
        expect(allValues, contains(EvoModuleNames.navigationTheme));
        expect(allValues, contains(EvoModuleNames.appUtilities));
        expect(allValues, contains(EvoModuleNames.privilegeValidation));
        expect(allValues, contains(EvoModuleNames.utilityWrapper));
      });

      test('should have unique values for all enum entries', () {
        final allValues = EvoModuleNames.values;
        final valueStrings = allValues.map((e) => e.value).toList();
        final uniqueValues = valueStrings.toSet();
        
        expect(uniqueValues.length, equals(valueStrings.length));
      });

      test('should have consistent naming pattern', () {
        final allValues = EvoModuleNames.values;
        
        for (final moduleEnum in allValues) {
          expect(moduleEnum.value, endsWith('_module'));
          expect(moduleEnum.value, isNot(contains(' ')));
          expect(moduleEnum.value, isNot(contains(RegExp(r'[A-Z]'))));
        }
      });
    });

    group('Static Methods', () {
      test('allNames should return all module names as strings', () {
        final allNames = EvoModuleNames.allNames;
        
        expect(allNames, hasLength(10));
        expect(allNames, contains('app_state_module'));
        expect(allNames, contains('auth_biometric_module'));
        expect(allNames, contains('network_config_module'));
      });

      test('fromValue should find correct module by value', () {
        expect(EvoModuleNames.fromValue('app_state_module'), equals(EvoModuleNames.appState));
        expect(EvoModuleNames.fromValue('auth_biometric_module'), equals(EvoModuleNames.authBiometric));
        expect(EvoModuleNames.fromValue('invalid_module'), isNull);
      });

      test('isValidModuleName should validate module names correctly', () {
        expect(EvoModuleNames.isValidModuleName('app_state_module'), isTrue);
        expect(EvoModuleNames.isValidModuleName('auth_biometric_module'), isTrue);
        expect(EvoModuleNames.isValidModuleName('invalid_module'), isFalse);
        expect(EvoModuleNames.isValidModuleName(''), isFalse);
      });
    });

    group('Module Categories', () {
      test('coreModules should return correct modules', () {
        final coreModules = EvoModuleNames.coreModules;
        
        expect(coreModules, hasLength(3));
        expect(coreModules, contains(EvoModuleNames.appState));
        expect(coreModules, contains(EvoModuleNames.authBiometric));
        expect(coreModules, contains(EvoModuleNames.networkConfig));
      });

      test('dataModules should return correct modules', () {
        final dataModules = EvoModuleNames.dataModules;
        
        expect(dataModules, hasLength(1));
        expect(dataModules, contains(EvoModuleNames.storageRepository));
      });

      test('featureModules should return correct modules', () {
        final featureModules = EvoModuleNames.featureModules;
        
        expect(featureModules, hasLength(2));
        expect(featureModules, contains(EvoModuleNames.pinReset));
        expect(featureModules, contains(EvoModuleNames.authenticationSession));
      });

      test('uiModules should return correct modules', () {
        final uiModules = EvoModuleNames.uiModules;
        
        expect(uiModules, hasLength(1));
        expect(uiModules, contains(EvoModuleNames.navigationTheme));
      });

      test('utilityModules should return correct modules', () {
        final utilityModules = EvoModuleNames.utilityModules;
        
        expect(utilityModules, hasLength(3));
        expect(utilityModules, contains(EvoModuleNames.appUtilities));
        expect(utilityModules, contains(EvoModuleNames.privilegeValidation));
        expect(utilityModules, contains(EvoModuleNames.utilityWrapper));
      });
    });

    group('Value Validation', () {
      test('should have non-empty values for all enums', () {
        for (final module in EvoModuleNames.values) {
          expect(module.value, isNotEmpty);
          expect(module.value.trim(), equals(module.value));
        }
      });

      test('should follow snake_case convention', () {
        final snakeCasePattern = RegExp(r'^[a-z]+(_[a-z]+)*$');
        
        for (final module in EvoModuleNames.values) {
          expect(module.value, matches(snakeCasePattern),
                 reason: '${module.value} should follow snake_case convention');
        }
      });
    });
  });
}
