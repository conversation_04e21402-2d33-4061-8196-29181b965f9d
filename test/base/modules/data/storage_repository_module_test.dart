// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/base/modules/data/storage_repository_module.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/base/modules/evo_module_names.dart';
import 'package:evoapp/data/repository/user_repo.dart';
import 'package:evoapp/data/repository/common_repo.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/core_module.dart';
import 'package:flutter_common_package/base/module/common_package_modules/network_module.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockCommonHttpClient extends Mock implements CommonHttpClient {}
class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}
class MockAppState extends Mock implements AppState {}
class MockFlutterSecureStorage extends Mock implements FlutterSecureStorage {}

void main() {
  group('StorageRepositoryModule', () {
    late StorageRepositoryModule module;
    late GetIt getIt;
    late MockCommonHttpClient mockHttpClient;
    late MockAppState mockAppState;
    late MockFlutterSecureStorage mockSecureStorage;

    setUp(() {
      getIt = GetIt.instance;
      module = StorageRepositoryModule(getIt);
      mockHttpClient = MockCommonHttpClient();
      mockAppState = MockAppState();
      mockSecureStorage = MockFlutterSecureStorage();

      // Register required dependencies
      getIt.registerSingleton<CommonHttpClient>(mockHttpClient);
      getIt.registerSingleton<AppState>(mockAppState);
      getIt.registerSingleton<FlutterSecureStorage>(mockSecureStorage);
    });

    tearDown(() {
      getIt.reset();
    });

    group('Module Configuration', () {
      test('should have correct module name', () {
        expect(module.name, equals(EvoModuleNames.storageRepository.value));
      });

      test('should have CoreModule and NetworkModule as dependencies', () {
        expect(module.dependencies.length, equals(2));
        expect(module.dependencies, contains(CoreModule));
        expect(module.dependencies, contains(NetworkModule));
      });

      test('should be instance of FeatureModule', () {
        expect(module, isA<FeatureModule>());
      });
    });

    group('Registration', () {
      test('should register all storage and repository services successfully', () async {
        // Act
        await module.register();

        // Assert - Verify all services are registered and retrievable
        expect(getIt.isRegistered<EvoLocalStorageHelper>(), isTrue);
        expect(getIt.get<EvoLocalStorageHelper>(), isA<EvoLocalStorageHelper>());

        expect(getIt.isRegistered<UserRepo>(), isTrue);
        expect(getIt.get<UserRepo>(), isA<UserRepo>());

        expect(getIt.isRegistered<CommonRepo>(), isTrue);
        expect(getIt.get<CommonRepo>(), isA<CommonRepo>());
      });

      test('should register services as lazy singletons', () async {
        // Act
        await module.register();

        // Assert - Verify singleton behavior by checking instance identity
        final storageHelper1 = getIt.get<EvoLocalStorageHelper>();
        final storageHelper2 = getIt.get<EvoLocalStorageHelper>();
        expect(identical(storageHelper1, storageHelper2), isTrue);

        final userRepo1 = getIt.get<UserRepo>();
        final userRepo2 = getIt.get<UserRepo>();
        expect(identical(userRepo1, userRepo2), isTrue);

        final commonRepo1 = getIt.get<CommonRepo>();
        final commonRepo2 = getIt.get<CommonRepo>();
        expect(identical(commonRepo1, commonRepo2), isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle registration gracefully when dependencies are available', () async {
        // This test ensures the module can handle registration without errors
        // when GetIt is properly initialized with dependencies
        expect(() => module.register(), returnsNormally);
      });

      test('should fail when FlutterSecureStorage dependency is missing', () async {
        // Arrange - Remove FlutterSecureStorage dependency needed for EvoLocalStorageHelper
        getIt.unregister<FlutterSecureStorage>();

        // Act & Assert
        expect(() => module.register(), throwsA(isA<Object>()));
      });

      test('should fail when CommonHttpClient dependency is missing', () async {
        // Arrange - Remove CommonHttpClient dependency needed for repositories
        getIt.unregister<CommonHttpClient>();

        // Act & Assert
        expect(() => module.register(), throwsA(isA<Object>()));
      });

      test('should fail when AppState dependency is missing', () async {
        // Arrange - Remove AppState dependency needed for UserRepo
        getIt.unregister<AppState>();

        // Act & Assert
        expect(() => module.register(), throwsA(isA<Object>()));
      });
    });

    group('Integration', () {
      test('should integrate with EvoModuleNames enum and data modules group', () {
        expect(module.name, equals(EvoModuleNames.storageRepository.value));
        expect(EvoModuleNames.fromValue(module.name), equals(EvoModuleNames.storageRepository));
        expect(EvoModuleNames.dataModules, contains(EvoModuleNames.storageRepository));
      });

      test('should have focused responsibility for storage and repository services', () {
        expect(module.name, contains('storage_repository'));
        expect(module.dependencies.length, equals(2)); // CoreModule and NetworkModule only
        expect(module.dependencies, containsAll([CoreModule, NetworkModule]));
      });
    });
  });
}
