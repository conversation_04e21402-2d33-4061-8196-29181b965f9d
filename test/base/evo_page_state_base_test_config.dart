import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_handler.dart';
import 'package:evoapp/feature/authorization_session_expired/authorization_session_expired_popup.dart';
import 'package:evoapp/feature/authorization_session_expired/force_logout_popup.dart';
import 'package:evoapp/feature/biometric/biometric_token/biometric_authentication_service.dart';
import 'package:evoapp/model/evo_dialog_id.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/util/dialog_functions.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/common_navigator_observer.dart';
import 'package:flutter_common_package/util/alert_manager.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/common_toast.dart';
import 'package:flutter_common_package/util/device_platform.dart';
import 'package:flutter_common_package/util/network_manager.dart';
import 'package:flutter_common_package/util/otp_auto_fill/otp_auto_fill.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:mocktail/mocktail.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../util/flutter_test_config.dart';

class MockAuthorizationSessionExpiredHandler extends Mock
    implements AuthorizationSessionExpiredHandler {}

class MockAuthorizationSessionExpiredPopup extends Mock
    implements AuthorizationSessionExpiredPopup {}

class MockForceLogoutPopup extends Mock implements ForceLogoutPopup {}

class MockBiometricsAuthenticationService extends Mock implements BiometricAuthenticationService {}

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockNetworkManager extends Mock implements NetworkManager {}

class MockCommonNavigatorObserver extends Mock implements CommonNavigatorObserver {}

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockDialogFunction extends Mock implements DialogFunction {}

class MockOtpAutoFill extends Mock implements OtpAutoFill {}

class MockDevicePlatform extends Mock implements DevicePlatform {}

/// Define fields
late NetworkManager mockNetworkManager;
late AuthorizationSessionExpiredHandler mockAuthorizationSessionExpiredHandler;
late AuthorizationSessionExpiredPopup mockAuthorizationSessionExpiredPopup;
late BuildContext mockNavigatorContext;
late EvoUtilFunction mockEvoUtilFunction;
late DialogFunction mockDialogFunction;
late CommonNavigator commonNavigator;

/// This method is called in setUpAll() method of test file
void initConfigEvoPageStateBase() {
  VisibilityDetectorController.instance.updateInterval = Duration.zero;
  registerFallbackValue(MockBuildContext());

  getIt.registerSingleton<AuthorizationSessionExpiredHandler>(
      MockAuthorizationSessionExpiredHandler());

  getIt.registerSingleton<AuthorizationSessionExpiredPopup>(MockAuthorizationSessionExpiredPopup());

  getIt.registerSingleton<ForceLogoutPopup>(MockForceLogoutPopup());

  getIt.registerSingleton<BiometricAuthenticationService>(MockBiometricsAuthenticationService());

  getIt.registerSingleton<AppState>(AppState());

  getItRegisterMockCommonUtilFunctionAndImageProvider();

  getIt.registerSingleton<CommonNavigator>(MockCommonNavigator());

  getItRegisterButtonStyle();
  getItRegisterTextStyle();
  getItRegisterColor();

  getIt.registerSingleton<LoggingRepo>(MockLoggingRepo());

  getIt.registerSingleton<NetworkManager>(MockNetworkManager());

  getIt.registerSingleton<CommonNavigatorObserver>(MockCommonNavigatorObserver());

  /// Set value for fields
  mockNetworkManager = getIt.get<NetworkManager>();
  mockAuthorizationSessionExpiredHandler = getIt.get<AuthorizationSessionExpiredHandler>();
  mockAuthorizationSessionExpiredPopup = getIt.get<AuthorizationSessionExpiredPopup>();

  getIt.registerLazySingleton<AlertManager>(() => CommonToast(FlutterToastWrapper()));

  mockNavigatorContext = MockBuildContext();
  setUpMockGlobalKeyProvider(mockNavigatorContext);

  mockEvoUtilFunction = getIt.get<EvoUtilFunction>();
  mockDialogFunction = getIt.registerSingleton(MockDialogFunction());

  getIt.registerSingleton<DevicePlatform>(MockDevicePlatform());

  getIt.registerSingleton<OtpAutoFill>(MockOtpAutoFill());

  commonNavigator = getIt.get<CommonNavigator>();

  when(() => commonNavigator.goNamed(
        any(),
        any(),
        extra: any(named: 'extra'),
      )).thenAnswer((_) => Future<void>.value());

  when(() => commonNavigator.pushNamed(
        any(),
        any(),
        extra: any(named: 'extra'),
      )).thenAnswer((_) => Future<void>.value());

  when(() => commonNavigator.pushReplacementNamed(
        any(),
        any(),
        extra: any(named: 'extra'),
      )).thenAnswer((_) => Future<void>.value());

  when(() => getIt<CommonNavigatorObserver>().topStackIsAPageRoute()).thenReturn(true);

  when(
    () => getIt<CommonImageProvider>().asset(
      any(),
      width: any(named: 'width'),
      height: any(named: 'height'),
      fit: any(named: 'fit'),
      color: any(named: 'color'),
      cornerRadius: any(named: 'cornerRadius'),
      cacheWidth: any(named: 'cacheWidth'),
      cacheHeight: any(named: 'cacheHeight'),
      package: any(named: 'package'),
    ),
  ).thenReturn(Text('mock_image'));

  registerFallbackValue(EvoDialogId.common);
  when(
    () => mockDialogFunction.showDialogConfirm(
      alertType: any(named: 'alertType'),
      dialogId: any(named: 'dialogId'),
      textPositive: any(named: 'textPositive'),
      content: any(named: 'content'),
      title: any(named: 'title'),
      textNegative: any(named: 'textNegative'),
      footer: any(named: 'footer'),
      onClickNegative: any(named: 'onClickNegative'),
      onClickPositive: any(named: 'onClickPositive'),
      imageHeader: any(named: 'imageHeader'),
      isDismissible: any(named: 'isDismissible'),
      positiveButtonStyle: any(named: 'positiveButtonStyle'),
      negativeButtonStyle: any(named: 'negativeButtonStyle'),
      titleTextStyle: any(named: 'titleTextStyle'),
      contentTextStyle: any(named: 'contentTextStyle'),
      isShowButtonClose: any(named: 'isShowButtonClose'),
      loggingEventMetaData: any(named: 'loggingEventMetaData'),
      loggingEventOnShowMetaData: any(named: 'loggingEventOnShowMetaData'),
      dialogHorizontalPadding: any(named: 'dialogHorizontalPadding'),
      autoClosePopupWhenClickCTA: any(named: 'autoClosePopupWhenClickCTA'),
    ),
  ).thenAnswer((_) => Future<void>.value());

  when(() => mockDialogFunction.showDialogSessionTokenExpired(
        onClickPositive: any(named: 'onClickPositive'),
        type: any(named: 'type'),
      )).thenAnswer((_) async {});

  /// Register OTP autofill
  when(() => getIt<OtpAutoFill>().startUserConsentListening(
        onExtractCode: any(named: 'onExtractCode'),
        onCode: any(named: 'onCode'),
        onError: any(named: 'onError'),
      )).thenAnswer((_) => Future<void>.value());
  when(() => getIt<OtpAutoFill>().stopListening()).thenAnswer((_) => Future<void>.value());
  when(() => getIt<DevicePlatform>().isAndroid()).thenReturn(true);
}

/// This method is called in setUp() method of test file
void setUpMockConfigEvoPageStateBase() {
  when(() => mockNetworkManager.myStreamNetwork).thenAnswer(
    (_) => Stream<bool>.fromIterable(<bool>[true]),
  );

  when(() => mockAuthorizationSessionExpiredHandler.getStreamSubscription()).thenAnswer((_) =>
      Stream<UnauthorizedSessionState>.fromIterable(
          <UnauthorizedSessionState>[UnauthorizedSessionState.invalidToken]));

  when(() => mockAuthorizationSessionExpiredPopup.checkCanShowPopup()).thenReturn(false);
  when(() => mockAuthorizationSessionExpiredPopup.show())
      .thenAnswer((_) async => Future<void>.value());

  when(() => mockEvoUtilFunction.clearDataOnTokenInvalid()).thenAnswer((_) async {
    return Future<void>.value();
  });
}
