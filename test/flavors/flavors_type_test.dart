import 'package:evoapp/flavors/flavors_type.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FlavorType', () {
    group('enum values', () {
      test('should have correct enum values', () {
        expect(FlavorType.values.length, equals(3));
        expect(FlavorType.values, contains(FlavorType.stag));
        expect(FlavorType.values, contains(FlavorType.uat));
        expect(FlavorType.values, contains(FlavorType.prod));
      });

      test('should have correct enum order', () {
        expect(FlavorType.values[0], equals(FlavorType.stag));
        expect(FlavorType.values[1], equals(FlavorType.uat));
        expect(FlavorType.values[2], equals(FlavorType.prod));
      });
    });

    group('name getter', () {
      test('should return correct name for stag', () {
        expect(FlavorType.stag.name, equals('stag'));
      });

      test('should return correct name for uat', () {
        expect(FlavorType.uat.name, equals('uat'));
      });

      test('should return correct name for prod', () {
        expect(FlavorType.prod.name, equals('prod'));
      });

      test('should return non-empty strings for all flavors', () {
        for (final FlavorType flavor in FlavorType.values) {
          expect(flavor.name, isNotEmpty);
          expect(flavor.name, isA<String>());
        }
      });
    });

    group('fromString method', () {
      test('should return correct FlavorType for valid strings', () {
        expect(FlavorType.fromString('stag'), equals(FlavorType.stag));
        expect(FlavorType.fromString('uat'), equals(FlavorType.uat));
        expect(FlavorType.fromString('prod'), equals(FlavorType.prod));
      });

      test('should throw UnimplementedError for invalid strings', () {
        expect(() => FlavorType.fromString('invalid'), throwsUnimplementedError);
        expect(() => FlavorType.fromString(''), throwsUnimplementedError);
        expect(() => FlavorType.fromString('STAG'), throwsUnimplementedError);
        expect(() => FlavorType.fromString('UAT'), throwsUnimplementedError);
        expect(() => FlavorType.fromString('PROD'), throwsUnimplementedError);
        expect(() => FlavorType.fromString('staging'), throwsUnimplementedError);
        expect(() => FlavorType.fromString('production'), throwsUnimplementedError);
      });

      test('should throw UnimplementedError for null-like values', () {
        expect(() => FlavorType.fromString('null'), throwsUnimplementedError);
        expect(() => FlavorType.fromString('undefined'), throwsUnimplementedError);
      });

      test('should be case sensitive', () {
        expect(() => FlavorType.fromString('Stag'), throwsUnimplementedError);
        expect(() => FlavorType.fromString('Uat'), throwsUnimplementedError);
        expect(() => FlavorType.fromString('Prod'), throwsUnimplementedError);
      });
    });

    group('edge cases', () {
      test('should throw UnimplementedError for whitespace in fromString', () {
        expect(() => FlavorType.fromString(' stag '), throwsUnimplementedError);
        expect(() => FlavorType.fromString('stag '), throwsUnimplementedError);
        expect(() => FlavorType.fromString(' stag'), throwsUnimplementedError);
      });

      test('should throw UnimplementedError for special characters in fromString', () {
        expect(() => FlavorType.fromString('stag-test'), throwsUnimplementedError);
        expect(() => FlavorType.fromString('stag_test'), throwsUnimplementedError);
        expect(() => FlavorType.fromString('stag.test'), throwsUnimplementedError);
      });
    });
  });
}
