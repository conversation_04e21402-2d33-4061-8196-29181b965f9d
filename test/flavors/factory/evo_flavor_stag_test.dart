import 'package:evoapp/flavors/evo_flavor_value_config.dart';
import 'package:evoapp/flavors/factory/evo_flavor.dart';
import 'package:evoapp/flavors/factory/evo_flavor_stag.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EvoFlavorStag', () {
    late EvoFlavorStag stagFlavor;

    setUp(() {
      stagFlavor = EvoFlavorStag();
    });

    group('inheritance and interface', () {
      test('should extend EvoFlavor', () {
        expect(stagFlavor, isA<EvoFlavor>());
      });

      test('should implement all required methods', () {
        expect(() => stagFlavor.getFlavorValue(), returnsNormally);
      });
    });

    group('getFlavorValue', () {
      test('should return correct staging configuration', () {
        final CommonFlavorValues flavorValue = stagFlavor.getFlavorValue();

        expect(flavorValue, isA<CommonFlavorValues>());
        expect(flavorValue.baseUrl, equals(EvoFlavorValueConfig.baseUrlStag));
        expect(flavorValue.oneSignalAppId, equals(EvoFlavorValueConfig.oneSignalAppIdStag));
        expect(flavorValue.initializeFirebaseSdk, isTrue);
        expect(flavorValue.firebaseOptions, isNull);
      });

      test('should return consistent values on multiple calls', () {
        final CommonFlavorValues flavorValue1 = stagFlavor.getFlavorValue();
        final CommonFlavorValues flavorValue2 = stagFlavor.getFlavorValue();

        expect(flavorValue1.baseUrl, equals(flavorValue2.baseUrl));
        expect(flavorValue1.oneSignalAppId, equals(flavorValue2.oneSignalAppId));
      });
    });

    group('instance behavior', () {
      test('should create independent instances with same values', () {
        final EvoFlavorStag stagFlavor1 = EvoFlavorStag();
        final EvoFlavorStag stagFlavor2 = EvoFlavorStag();

        expect(stagFlavor1, isNot(same(stagFlavor2)));
        expect(stagFlavor1.getFlavorValue().baseUrl, equals(stagFlavor2.getFlavorValue().baseUrl));
      });
    });
  });
}
