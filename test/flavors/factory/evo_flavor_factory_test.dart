import 'package:evoapp/flavors/factory/evo_flavor.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/factory/evo_flavor_prod.dart';
import 'package:evoapp/flavors/factory/evo_flavor_stag.dart';
import 'package:evoapp/flavors/factory/evo_flavor_uat.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EvoFlavorFactory', () {
    late EvoFlavorFactory factory;

    setUp(() {
      factory = EvoFlavorFactory();
    });

    group('getFlavor', () {
      test('should return EvoFlavorStag for FlavorType.stag', () {
        final EvoFlavor flavor = factory.getFlavor(FlavorType.stag);
        
        expect(flavor, isA<EvoFlavorStag>());
      });

      test('should return EvoFlavorUat for FlavorType.uat', () {
        final EvoFlavor flavor = factory.getFlavor(FlavorType.uat);
        
        expect(flavor, isA<EvoFlavorUat>());
      });

      test('should return EvoFlavorProd for FlavorType.prod', () {
        final EvoFlavor flavor = factory.getFlavor(FlavorType.prod);
        
        expect(flavor, isA<EvoFlavorProd>());
      });

      test('should return different instances for each call', () {
        final EvoFlavor flavor1 = factory.getFlavor(FlavorType.stag);
        final EvoFlavor flavor2 = factory.getFlavor(FlavorType.stag);
        
        expect(flavor1, isNot(same(flavor2)));
        expect(flavor1.runtimeType, equals(flavor2.runtimeType));
      });

      test('should return instances that implement EvoFlavor interface', () {
        for (final FlavorType flavorType in FlavorType.values) {
          final EvoFlavor flavor = factory.getFlavor(flavorType);
          expect(flavor, isA<EvoFlavor>());
        }
      });

      test('should create functional flavor instances', () {
        for (final FlavorType flavorType in FlavorType.values) {
          final EvoFlavor flavor = factory.getFlavor(flavorType);
          
          // Test that getFlavorValue works
          expect(() => flavor.getFlavorValue(), returnsNormally);
          
          // Test that the returned value is not null
          final CommonFlavorValues flavorValue = flavor.getFlavorValue();
          expect(flavorValue, isNotNull);
          expect(flavorValue.baseUrl, isNotNull);
          expect(flavorValue.baseUrl, isNotEmpty);
        }
      });
    });

    group('factory behavior', () {
      test('should be stateless - multiple factory instances behave the same', () {
        final EvoFlavorFactory factory1 = EvoFlavorFactory();
        final EvoFlavorFactory factory2 = EvoFlavorFactory();
        
        final EvoFlavor flavor1 = factory1.getFlavor(FlavorType.stag);
        final EvoFlavor flavor2 = factory2.getFlavor(FlavorType.stag);
        
        expect(flavor1.runtimeType, equals(flavor2.runtimeType));
        expect(flavor1.getFlavorValue().baseUrl, equals(flavor2.getFlavorValue().baseUrl));
      });

      test('should handle all enum values without throwing', () {
        for (final FlavorType flavorType in FlavorType.values) {
          expect(() => factory.getFlavor(flavorType), returnsNormally);
        }
      });
    });
  });
}
