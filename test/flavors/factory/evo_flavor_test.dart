import 'package:evoapp/flavors/factory/evo_flavor.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

// Mock implementation for testing abstract class behavior
class MockEvoFlavor extends EvoFlavor {
  @override
  CommonFlavorValues getFlavorValue() {
    return CommonFlavorValues(
      baseUrl: 'https://mock.example.com/',
      initializeFirebaseSdk: false,
      oneSignalAppId: 'mock-app-id',
    );
  }
}

void main() {
  group('EvoFlavor Abstract Class', () {
    group('interface contract', () {
      test('should define required abstract methods', () {
        // Test that the abstract class defines the contract
        // This is verified by the fact that concrete classes must implement these methods
        // and our mock implementation can successfully implement them
        final MockEvoFlavor mockFlavor = MockEvoFlavor();

        expect(mockFlavor, isA<EvoFlavor>());
        expect(() => mockFlavor.getFlavorValue(), returnsNormally);
      });

      test('should enforce implementation of required methods', () {
        // This test verifies that the abstract class contract is properly defined
        // by ensuring our mock implementation works correctly
        final EvoFlavor flavor = MockEvoFlavor();

        expect(flavor, isA<EvoFlavor>());
        expect(flavor.getFlavorValue, isA<Function>());
      });
    });

    group('mock implementation', () {
      late MockEvoFlavor mockFlavor;

      setUp(() {
        mockFlavor = MockEvoFlavor();
      });

      test('should extend EvoFlavor', () {
        expect(mockFlavor, isA<EvoFlavor>());
      });

      test('should implement getFlavorValue', () {
        final CommonFlavorValues flavorValue = mockFlavor.getFlavorValue();

        expect(flavorValue, isNotNull);

        expect(flavorValue.baseUrl, equals('https://mock.example.com/'));

        expect(flavorValue.initializeFirebaseSdk, isFalse);

        expect(flavorValue.oneSignalAppId, equals('mock-app-id'));
      });
    });
  });
}
