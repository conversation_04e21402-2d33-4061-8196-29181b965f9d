import 'package:evoapp/flavors/evo_flavor_value_config.dart';
import 'package:evoapp/flavors/factory/evo_flavor.dart';
import 'package:evoapp/flavors/factory/evo_flavor_prod.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EvoFlavorProd', () {
    late EvoFlavorProd prodFlavor;

    setUp(() {
      prodFlavor = EvoFlavorProd();
    });

    group('inheritance and interface', () {
      test('should extend EvoFlavor', () {
        expect(prodFlavor, isA<EvoFlavor>());
      });

      test('should implement all required methods', () {
        expect(() => prodFlavor.getFlavorValue(), returnsNormally);
      });
    });

    group('getFlavorValue', () {
      test('should return correct production configuration', () {
        final CommonFlavorValues flavorValue = prodFlavor.getFlavorValue();

        expect(flavorValue, isA<CommonFlavorValues>());
        expect(flavorValue.baseUrl, equals(EvoFlavorValueConfig.baseUrlProd));
        expect(flavorValue.oneSignalAppId, equals(EvoFlavorValueConfig.oneSignalAppIdProd));
        expect(flavorValue.initializeFirebaseSdk, isTrue);
        expect(flavorValue.firebaseOptions, isNull);
      });

      test('should return consistent values on multiple calls', () {
        final CommonFlavorValues flavorValue1 = prodFlavor.getFlavorValue();
        final CommonFlavorValues flavorValue2 = prodFlavor.getFlavorValue();

        expect(flavorValue1.baseUrl, equals(flavorValue2.baseUrl));
        expect(flavorValue1.oneSignalAppId, equals(flavorValue2.oneSignalAppId));
      });
    });

    group('instance behavior', () {
      test('should create independent instances with same behavior', () {
        final EvoFlavorProd prodFlavor1 = EvoFlavorProd();
        final EvoFlavorProd prodFlavor2 = EvoFlavorProd();

        expect(prodFlavor1, isNot(same(prodFlavor2)));
        expect(prodFlavor1.getFlavorValue().baseUrl, equals(prodFlavor2.getFlavorValue().baseUrl));
      });
    });
  });
}
