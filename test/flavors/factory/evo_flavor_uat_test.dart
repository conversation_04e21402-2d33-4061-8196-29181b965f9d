import 'package:evoapp/flavors/evo_flavor_value_config.dart';
import 'package:evoapp/flavors/factory/evo_flavor.dart';
import 'package:evoapp/flavors/factory/evo_flavor_uat.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('EvoFlavorUat', () {
    late EvoFlavorUat uatFlavor;

    setUp(() {
      uatFlavor = EvoFlavorUat();
    });

    group('inheritance and interface', () {
      test('should extend EvoFlavor', () {
        expect(uatFlavor, isA<EvoFlavor>());
      });

      test('should implement all required methods', () {
        expect(() => uatFlavor.getFlavorValue(), returnsNormally);
      });
    });

    group('getFlavorValue', () {
      test('should return correct UAT configuration', () {
        final CommonFlavorValues flavorValue = uatFlavor.getFlavorValue();

        expect(flavorValue, isA<CommonFlavorValues>());
        expect(flavorValue.baseUrl, equals(EvoFlavorValueConfig.baseUrlUat));
        expect(flavorValue.oneSignalAppId, equals(EvoFlavorValueConfig.oneSignalAppIdUat));
        expect(flavorValue.initializeFirebaseSdk, isTrue);
        expect(flavorValue.firebaseOptions, isNull);
      });

      test('should return consistent values on multiple calls', () {
        final CommonFlavorValues flavorValue1 = uatFlavor.getFlavorValue();
        final CommonFlavorValues flavorValue2 = uatFlavor.getFlavorValue();

        expect(flavorValue1.baseUrl, equals(flavorValue2.baseUrl));
        expect(flavorValue1.oneSignalAppId, equals(flavorValue2.oneSignalAppId));
      });
    });


    group('instance behavior', () {
      test('should create independent instances with same values', () {
        final EvoFlavorUat uatFlavor1 = EvoFlavorUat();
        final EvoFlavorUat uatFlavor2 = EvoFlavorUat();

        expect(uatFlavor1, isNot(same(uatFlavor2)));
        expect(uatFlavor1.getFlavorValue().baseUrl, equals(uatFlavor2.getFlavorValue().baseUrl));
      });
    });
  });
}
