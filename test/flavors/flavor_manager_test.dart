import 'package:evoapp/flavors/evo_flavor_value_config.dart';
import 'package:evoapp/flavors/factory/evo_flavor_factory.dart';
import 'package:evoapp/flavors/flavors_type.dart';
import 'package:evoapp/flavors/flavor_manager.dart';
import 'package:flutter_common_package/flavors/flavor_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('FlavorHelper', () {
    group('current flavor methods', () {
      testWidgets('should return correct flavor type and DOP link for staging', (WidgetTester tester) async {
        // Arrange
        FlavorConfig(
          flavor: FlavorType.stag.name,
          values: EvoFlavorFactory().getFlavor(FlavorType.stag).getFlavorValue(),
        );

        // Act & Assert
        expect(FlavorManager.currentFlavorType, equals(FlavorType.stag));
        expect(FlavorManager.getDOPLink(), equals(EvoFlavorValueConfig.dopLinkStag));
        expect(FlavorManager.getFlavorValues(), isA<CommonFlavorValues>());
      });

      testWidgets('should return correct flavor type and DOP link for UAT', (WidgetTester tester) async {
        // Arrange
        FlavorConfig(
          flavor: FlavorType.uat.name,
          values: EvoFlavorFactory().getFlavor(FlavorType.uat).getFlavorValue(),
        );

        // Act & Assert
        expect(FlavorManager.currentFlavorType, equals(FlavorType.uat));
        expect(FlavorManager.getDOPLink(), equals(EvoFlavorValueConfig.dopLinkUat));
        expect(FlavorManager.getFlavorValues(), isA<CommonFlavorValues>());
      });

      testWidgets('should handle production flavor correctly', (WidgetTester tester) async {
        // Arrange
        FlavorConfig(
          flavor: FlavorType.prod.name,
          values: EvoFlavorFactory().getFlavor(FlavorType.prod).getFlavorValue(),
        );

        // Act & Assert
        expect(FlavorManager.currentFlavorType, equals(FlavorType.prod));
        expect(() => FlavorManager.getDOPLink(), throwsUnimplementedError);
        expect(FlavorManager.getFlavorValues(), isA<CommonFlavorValues>());
      });
    });
  });
}
