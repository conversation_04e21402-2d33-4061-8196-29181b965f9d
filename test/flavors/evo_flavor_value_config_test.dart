import 'package:evoapp/flavors/evo_flavor_value_config.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify base URL', () {
    /// Base url config
    expect(EvoFlavorValueConfig.baseUrlStag, 'https://mobile-kada-staging.tsengineering.io/');
    expect(EvoFlavorValueConfig.baseUrlUat, 'https://mobile-kada-uat.tsengineering.io/');
    expect(EvoFlavorValueConfig.baseUrlProd, 'https://mobile-api.goevo.vn/');
  });

  test('verify OneSignal App ID', () {
    expect(EvoFlavorValueConfig.oneSignalAppIdStag, '************************************');
    expect(EvoFlavorValueConfig.oneSignalAppIdUat, '************************************');
    expect(EvoFlavorValueConfig.oneSignalAppIdProd, '************************************');
  });

  test('verify DopLink', () {
    expect(EvoFlavorValueConfig.dopLinkStag, 'https://dop-kada-ph-staging.tsengineering.io');
    expect(EvoFlavorValueConfig.dopLinkUat, 'https://dop-kada-ph-uat.tsengineering.io');
  });
}
