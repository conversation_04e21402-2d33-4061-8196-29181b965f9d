// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:ui';

import 'package:evoapp/base/modules/core/app_state_module.dart';
import 'package:evoapp/base/modules/core/auth_biometric_module.dart';
import 'package:evoapp/base/modules/core/network_config_module.dart';
import 'package:evoapp/base/modules/data/storage_repository_module.dart';
import 'package:evoapp/base/modules/feature/authentication_session_module.dart';
import 'package:evoapp/base/modules/feature/pin_reset_module.dart';
import 'package:evoapp/base/modules/ui/navigation_theme_module.dart';
import 'package:evoapp/base/modules/utility/app_utilities_module.dart';
import 'package:evoapp/base/modules/utility/privilege_validation_module.dart';
import 'package:evoapp/base/modules/utility/utility_wrapper_module.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('prepareForAppInitiation', () {
    setUp(() {
      // Reset GetIt for test isolation
      getIt.reset();
    });

    tearDown(() {
      getIt.reset();
    });

    group('Function Structure', () {
      test('should be a Future function returning void', () {
        // Act & Assert
        expect(prepareForAppInitiation, isA<Future<void> Function()>());
      });

      test('should use English locale as default', () {
        // Assert
        expect(defaultLocale.languageCode, equals('en'));
        expect(defaultLocale.countryCode, isNull);
      });

      test('should use singleton GetIt instance', () {
        // Assert
        expect(getIt, same(GetIt.instance));
      });
    });

    group('Module Instantiation', () {
      test('should create all custom modules as FeatureModule instances', () {
        // Arrange - All module types that should be instantiated
        final moduleConstructors = [
          () => AppStateModule(getIt),
          () => NetworkConfigModule(getIt),
          () => AuthBiometricModule(getIt),
          () => StorageRepositoryModule(getIt),
          () => NavigationThemeModule(getIt),
          () => AppUtilitiesModule(getIt),
          () => PrivilegeValidationModule(getIt),
          () => UtilityWrapperModule(getIt),
          () => AuthenticationSessionModule(getIt),
          () => PinResetModule(getIt),
        ];

        // Act & Assert - Each module should be creatable and extend FeatureModule
        for (final constructor in moduleConstructors) {
          final module = constructor();
          expect(module, isA<FeatureModule>());
          expect(module.getIt, same(getIt));
        }

        // Verify we have exactly 10 modules
        expect(moduleConstructors.length, equals(10));
      });

      test('should create modules with correct dependency injection', () {
        // Act
        final module = AppStateModule(getIt);

        // Assert - Test one representative module for DI pattern
        expect(module.getIt, same(getIt));
        expect(module, isA<FeatureModule>());
      });
    });

    group('Module Configuration', () {
      test('should register exactly 10 custom modules', () {
        // This test ensures module count consistency with the actual function
        final moduleTypes = [
          AppStateModule,
          NetworkConfigModule,
          AuthBiometricModule,
          StorageRepositoryModule,
          NavigationThemeModule,
          AppUtilitiesModule,
          PrivilegeValidationModule,
          UtilityWrapperModule,
          AuthenticationSessionModule,
          PinResetModule,
        ];

        expect(moduleTypes.length, equals(10));
      });

      test('should register exactly 9 common package modules', () {
        // This test ensures common package module count consistency
        final commonModules = [
          CommonPackageModuleNames.core,
          CommonPackageModuleNames.network,
          CommonPackageModuleNames.ui,
          CommonPackageModuleNames.utility,
          CommonPackageModuleNames.analytics,
          CommonPackageModuleNames.deviceInfo,
          CommonPackageModuleNames.ekyc,
          CommonPackageModuleNames.dataCollection,
          CommonPackageModuleNames.notification,
        ];

        expect(commonModules.length, equals(9));
      });
    });


  });
}
