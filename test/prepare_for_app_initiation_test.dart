// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'dart:ui';

import 'package:evoapp/base/modules/core/app_state_module.dart';
import 'package:evoapp/base/modules/core/auth_biometric_module.dart';
import 'package:evoapp/base/modules/core/network_config_module.dart';
import 'package:evoapp/base/modules/data/storage_repository_module.dart';
import 'package:evoapp/base/modules/feature/authentication_session_module.dart';
import 'package:evoapp/base/modules/feature/pin_reset_module.dart';
import 'package:evoapp/base/modules/ui/navigation_theme_module.dart';
import 'package:evoapp/base/modules/utility/app_utilities_module.dart';
import 'package:evoapp/base/modules/utility/privilege_validation_module.dart';
import 'package:evoapp/base/modules/utility/utility_wrapper_module.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

void main() {
  group('prepareForAppInitiation', () {
    setUp(() {
      // Reset GetIt for test isolation
      getIt.reset();
    });

    tearDown(() {
      getIt.reset();
    });

    group('Function Structure', () {
      test('should be a Future function', () {
        // Act & Assert
        expect(prepareForAppInitiation, isA<Future<void> Function()>());
      });

      test('should use correct default locale constant', () {
        // Assert
        expect(defaultLocale, equals(const Locale('en')));
      });

      test('should have global GetIt instance available', () {
        // Assert
        expect(getIt, isA<GetIt>());
        expect(getIt, equals(GetIt.instance));
      });
    });

    group('Module Types', () {
      test('should create AppStateModule instance', () {
        // Act
        final module = AppStateModule(getIt);

        // Assert
        expect(module, isA<AppStateModule>());
        expect(module, isA<FeatureModule>());
        expect(module.getIt, equals(getIt));
      });

      test('should create NetworkConfigModule instance', () {
        // Act
        final module = NetworkConfigModule(getIt);

        // Assert
        expect(module, isA<NetworkConfigModule>());
        expect(module, isA<FeatureModule>());
        expect(module.getIt, equals(getIt));
      });

      test('should create AuthBiometricModule instance', () {
        // Act
        final module = AuthBiometricModule(getIt);

        // Assert
        expect(module, isA<AuthBiometricModule>());
        expect(module, isA<FeatureModule>());
        expect(module.getIt, equals(getIt));
      });

      test('should create StorageRepositoryModule instance', () {
        // Act
        final module = StorageRepositoryModule(getIt);

        // Assert
        expect(module, isA<StorageRepositoryModule>());
        expect(module, isA<FeatureModule>());
        expect(module.getIt, equals(getIt));
      });

      test('should create NavigationThemeModule instance', () {
        // Act
        final module = NavigationThemeModule(getIt);

        // Assert
        expect(module, isA<NavigationThemeModule>());
        expect(module, isA<FeatureModule>());
        expect(module.getIt, equals(getIt));
      });

      test('should create AppUtilitiesModule instance', () {
        // Act
        final module = AppUtilitiesModule(getIt);

        // Assert
        expect(module, isA<AppUtilitiesModule>());
        expect(module, isA<FeatureModule>());
        expect(module.getIt, equals(getIt));
      });

      test('should create PrivilegeValidationModule instance', () {
        // Act
        final module = PrivilegeValidationModule(getIt);

        // Assert
        expect(module, isA<PrivilegeValidationModule>());
        expect(module, isA<FeatureModule>());
        expect(module.getIt, equals(getIt));
      });

      test('should create UtilityWrapperModule instance', () {
        // Act
        final module = UtilityWrapperModule(getIt);

        // Assert
        expect(module, isA<UtilityWrapperModule>());
        expect(module, isA<FeatureModule>());
        expect(module.getIt, equals(getIt));
      });

      test('should create AuthenticationSessionModule instance', () {
        // Act
        final module = AuthenticationSessionModule(getIt);

        // Assert
        expect(module, isA<AuthenticationSessionModule>());
        expect(module, isA<FeatureModule>());
        expect(module.getIt, equals(getIt));
      });

      test('should create PinResetModule instance', () {
        // Act
        final module = PinResetModule(getIt);

        // Assert
        expect(module, isA<PinResetModule>());
        expect(module, isA<FeatureModule>());
        expect(module.getIt, equals(getIt));
      });
    });

    group('Module Count', () {
      test('should have exactly 10 custom modules defined', () {
        // This test ensures we don't accidentally add/remove modules without updating tests
        final moduleTypes = [
          AppStateModule,
          NetworkConfigModule,
          AuthBiometricModule,
          StorageRepositoryModule,
          NavigationThemeModule,
          AppUtilitiesModule,
          PrivilegeValidationModule,
          UtilityWrapperModule,
          AuthenticationSessionModule,
          PinResetModule,
        ];

        expect(moduleTypes.length, equals(10));
      });

      test('should have correct module categories', () {
        // Core modules (4)
        final coreModules = [
          AppStateModule,
          NetworkConfigModule,
          AuthBiometricModule,
          StorageRepositoryModule,
        ];

        // UI modules (1)
        final uiModules = [
          NavigationThemeModule,
        ];

        // Utility modules (3)
        final utilityModules = [
          AppUtilitiesModule,
          PrivilegeValidationModule,
          UtilityWrapperModule,
        ];

        // Feature modules (2)
        final featureModules = [
          AuthenticationSessionModule,
          PinResetModule,
        ];

        expect(coreModules.length, equals(4));
        expect(uiModules.length, equals(1));
        expect(utilityModules.length, equals(3));
        expect(featureModules.length, equals(2));
        expect(coreModules.length + uiModules.length + utilityModules.length + featureModules.length, equals(10));
      });
    });

    group('Common Package Modules', () {
      test('should define correct common package module names', () {
        final expectedModules = [
          CommonPackageModuleNames.core,
          CommonPackageModuleNames.network,
          CommonPackageModuleNames.ui,
          CommonPackageModuleNames.utility,
          CommonPackageModuleNames.analytics,
          CommonPackageModuleNames.deviceInfo,
          CommonPackageModuleNames.ekyc,
          CommonPackageModuleNames.dataCollection,
          CommonPackageModuleNames.notification,
        ];

        expect(expectedModules.length, equals(9));
        expect(expectedModules, contains(CommonPackageModuleNames.core));
        expect(expectedModules, contains(CommonPackageModuleNames.network));
        expect(expectedModules, contains(CommonPackageModuleNames.ui));
        expect(expectedModules, contains(CommonPackageModuleNames.utility));
        expect(expectedModules, contains(CommonPackageModuleNames.analytics));
        expect(expectedModules, contains(CommonPackageModuleNames.deviceInfo));
        expect(expectedModules, contains(CommonPackageModuleNames.ekyc));
        expect(expectedModules, contains(CommonPackageModuleNames.dataCollection));
        expect(expectedModules, contains(CommonPackageModuleNames.notification));
      });
    });

    group('Constants', () {
      test('should use correct source identifier', () {
        // The source identifier used in registerAndInitializeCustomModules
        const expectedSource = 'evo_app';

        expect(expectedSource, equals('evo_app'));
        expect(expectedSource, isNotEmpty);
      });

      test('should have consistent locale configuration', () {
        // Verify the default locale is properly defined
        expect(defaultLocale, isA<Locale>());
        expect(defaultLocale.languageCode, equals('en'));
        expect(defaultLocale.countryCode, isNull);
      });
    });

    group('Documentation', () {
      test('should have proper function documentation structure', () {
        // This test ensures the function maintains its documented behavior
        // The function should handle:
        // 1. Initialize common package with selected modules
        // 2. Register custom host app modules
        // 3. Initialize custom modules
        // 4. Error handling with logging and rethrowing

        expect(prepareForAppInitiation, isA<Function>());
      });

      test('should maintain modular system principles', () {
        // Verify the function follows modular system design:
        // - Better separation of concerns
        // - Easier testing
        // - More maintainable code structure

        expect(prepareForAppInitiation, isA<Future<void> Function()>());
      });
    });
  });
}
