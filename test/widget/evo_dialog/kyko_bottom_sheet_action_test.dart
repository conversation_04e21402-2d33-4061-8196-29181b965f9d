import 'package:evoapp/widget/buttons.dart'; // Import for PrimaryButton and SecondaryButton
import 'package:evoapp/widget/evo_dialog/kyko_bottom_sheet_action.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../base/evo_page_state_base_test_config.dart';

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();
    setUpMockConfigEvoPageStateBase();
  });

  group('KykoBottomSheetAction', () {
    testWidgets('positive action renders a PrimaryButton with correct text',
        (WidgetTester tester) async {
      // Build the positive action widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: KykoBottomSheetAction.positive(
              text: 'Confirm',
              onPressed: () {},
            ),
          ),
        ),
      );

      // Verify the button is rendered with correct type and text
      expect(find.byType(PrimaryButton), findsOneWidget);
      expect(find.text('Confirm'), findsOneWidget);
    });

    testWidgets('negative action renders a SecondaryButton with correct text',
        (WidgetTester tester) async {
      // Build the negative action widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: KykoBottomSheetAction.negative(
              text: 'Cancel',
              onPressed: () {},
            ),
          ),
        ),
      );

      // Verify the button is rendered with correct type and text
      expect(find.byType(SecondaryButton), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets('positive action triggers callback when tapped', (WidgetTester tester) async {
      bool callbackTriggered = false;

      // Build the positive action with callback
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: KykoBottomSheetAction.positive(
              text: 'Confirm',
              onPressed: () {
                callbackTriggered = true;
              },
            ),
          ),
        ),
      );

      // Tap the button and verify callback was triggered
      await tester.tap(find.byType(PrimaryButton));
      expect(callbackTriggered, true);
    });

    testWidgets('negative action triggers callback when tapped', (WidgetTester tester) async {
      bool callbackTriggered = false;

      // Build the negative action with callback
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: KykoBottomSheetAction.negative(
              text: 'Cancel',
              onPressed: () {
                callbackTriggered = true;
              },
            ),
          ),
        ),
      );

      // Tap the button and verify callback was triggered
      await tester.tap(find.byType(SecondaryButton));
      expect(callbackTriggered, true);
    });

    testWidgets('positive and negative actions render differently', (WidgetTester tester) async {
      // Build both types of actions side by side
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                KykoBottomSheetAction.positive(
                  text: 'Confirm',
                  onPressed: () {},
                ),
                KykoBottomSheetAction.negative(
                  text: 'Cancel',
                  onPressed: () {},
                ),
              ],
            ),
          ),
        ),
      );

      // Verify both button types are rendered
      expect(find.byType(PrimaryButton), findsOneWidget);
      expect(find.byType(SecondaryButton), findsOneWidget);

      // Verify both texts are displayed
      expect(find.text('Confirm'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
    });
  });
}
