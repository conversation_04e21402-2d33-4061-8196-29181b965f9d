import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/screen_util.dart';
import 'package:evoapp/widget/evo_dialog/dialog_confirm.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_screen_id.dart';
import 'package:flutter_common_package/feature/server_logging/event_tracking_utils.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockLoggingRepo extends Mock implements LoggingRepo {}

class MockButtonStyles extends Mock implements EvoButtonStyles {}

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class MockBuildContext extends Mock implements BuildContext {}

class MockFeatureToggle extends Mock implements FeatureToggle {}

class MockAppState extends Mock implements AppState {}

class MockEventTrackingUtils extends Mock implements EventTrackingUtils {}

void main() {
  test('EvoDialogConfirm constants', () {
    expect(
      EvoDialogConfirm.dialogEdgePadding,
      24.w,
    );
    expect(
      EvoDialogConfirm.defaultHeaderPadding,
      EdgeInsets.only(top: 24.w),
    );
    expect(
      EvoDialogConfirm.defaultContentPadding,
      EdgeInsets.symmetric(horizontal: EvoDialogConfirm.dialogEdgePadding),
    );
    expect(
      EvoDialogConfirm.defaultCTAPadding,
      EdgeInsets.only(
        left: EvoDialogConfirm.dialogEdgePadding,
        right: EvoDialogConfirm.dialogEdgePadding,
        bottom: EvoDialogConfirm.dialogEdgePadding,
        top: 16.w,
      ),
    );
    expect(EvoDialogConfirm.defaultContentSpacing, 8.w);
    expect(EvoDialogConfirm.defaultCTASpacing, 8.w);
    expect(EvoDialogConfirm.defaultCTAOrientation, ButtonListOrientation.verticalUp);
    expect(EvoDialogConfirm.defaultDialogHorizontalPadding, 16.w);
  });

  group('Test EvoDialogConfirm UI', () {
    late LoggingRepo loggingRepo;

    const String textPositive = 'textPositive';
    const String dialogId = 'dialogId';

    const ButtonStyle defaultPositiveButtonStyle = ButtonStyle();
    const ButtonStyle defaultNegativeButtonStyle = ButtonStyle();

    setUpAll(() {
      getItRegisterColor();
      getItRegisterTextStyle();
      getIt.registerLazySingleton<CommonButtonStyles>(() => MockButtonStyles());
      getIt.registerLazySingleton<LoggingRepo>(() => MockLoggingRepo());
      getIt.registerLazySingleton<CommonImageProvider>(() => MockCommonImageProvider());
      getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
      getIt.registerLazySingleton<FeatureToggle>(() => MockFeatureToggle());
      getIt.registerLazySingleton<AppState>(() => MockAppState());
      getIt.registerLazySingleton<EventTrackingUtils>(() => MockEventTrackingUtils());

      loggingRepo = getIt.get<LoggingRepo>();
      final EventTrackingUtils eventTrackingUtils = getIt.get<EventTrackingUtils>();
      final AppState appState = getIt.get<AppState>();
      final FeatureToggle mockFeatureToggle = getIt.get<FeatureToggle>();

      registerFallbackValue(EventType.userAction);
      registerFallbackValue(MockBuildContext());

      when(
        () => eventTrackingUtils.sendUserActionEvent(
          eventId: any(named: 'eventId'),
          metaData: any(named: 'metaData'),
        ),
      ).thenAnswer((_) => Future<void>.value());

      when(() => appState.currentScreenId).thenReturn(EventTrackingScreenId.undefined);
      when(() => mockFeatureToggle.enableEventTrackingFeature).thenReturn(true);
      when(() => loggingRepo.logEvent(
            eventType: any(named: 'eventType'),
            data: any(named: 'data'),
          )).thenAnswer((_) => Future<void>.value());

      when(() => evoButtonStyles.primary(ButtonSize.large)).thenReturn(defaultPositiveButtonStyle);
      when(() => evoButtonStyles.secondary(ButtonSize.large))
          .thenReturn(defaultNegativeButtonStyle);

      when(() => getIt.get<CommonImageProvider>().asset(
            any(),
            package: any(named: 'package'),
            fit: any(named: 'fit'),
          )).thenReturn(const SizedBox());
    });

    testWidgets('Required params', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: EvoDialogConfirm(
          textPositive: textPositive,
          dialogId: dialogId,
        ),
      ));

      final Finder dialogFinder = find.byType(Dialog);
      expect(dialogFinder, findsOneWidget);

      final Dialog dialogWidget = tester.widget<Dialog>(dialogFinder);
      expect(dialogWidget.backgroundColor, Colors.transparent);
      expect(
        dialogWidget.insetPadding,
        EdgeInsets.symmetric(
          vertical: 20,
          horizontal: EvoDialogConfirm.defaultDialogHorizontalPadding,
        ),
      );

      final ClipRRect clipRRectWidget = dialogWidget.child as ClipRRect;
      expect(clipRRectWidget.borderRadius, BorderRadius.circular(8.w));

      final Container containerWidget = clipRRectWidget.child as Container;
      expect(
          containerWidget.decoration,
          isA<BoxDecoration>().having(
            (BoxDecoration widget) => widget.color,
            'verify container color',
            evoColors.grayBackground,
          ));

      final CommonDialogBottomSheet dialogContent =
          containerWidget.child as CommonDialogBottomSheet;
      expect(dialogContent.textPositive, textPositive);
      expect(dialogContent.header, isNull);
      expect(dialogContent.headerPadding, EvoDialogConfirm.defaultHeaderPadding);
      expect(dialogContent.title, isNull);
      expect(dialogContent.titleTextStyle, evoTextStyles.semibold(TextSize.h5));
      expect(dialogContent.titleTextAlign, TextAlign.center);
      expect(dialogContent.content, isNull);
      expect(
        dialogContent.contentTextStyle,
        evoTextStyles.regular(TextSize.base, color: evoColors.grayBase),
      );
      expect(dialogContent.contentTextAlign, TextAlign.center);
      expect(dialogContent.contentPadding, EvoDialogConfirm.defaultContentPadding);
      expect(dialogContent.contentSpacing, EvoDialogConfirm.defaultContentSpacing);
      expect(dialogContent.textPositive, textPositive);
      expect(dialogContent.onClickPositive, isNull);
      expect(dialogContent.positiveButtonStyle, defaultPositiveButtonStyle);
      expect(dialogContent.textNegative, isNull);
      expect(dialogContent.onClickNegative, isNull);
      expect(dialogContent.negativeButtonStyle, defaultNegativeButtonStyle);
      expect(dialogContent.ctaPadding, EvoDialogConfirm.defaultCTAPadding);
      expect(dialogContent.ctaSpacing, EvoDialogConfirm.defaultCTASpacing);
      expect(dialogContent.buttonListOrientation, EvoDialogConfirm.defaultCTAOrientation);
      expect(dialogContent.footer, isNull);
      expect(dialogContent.dialogId, dialogId);
      expect(dialogContent.isShowButtonClose, false);
    });

    testWidgets('All params', (WidgetTester tester) async {
      const String title = 'title';
      const String content = 'content';
      const String textNegative = 'textNegative';
      onClickNegative() {}
      const Text footer = Text('footer');
      onClickPositive() {}
      const Text imageHeader = Text('imageHeader');
      const ButtonStyle positiveButtonStyle = ButtonStyle();
      const ButtonStyle negativeButtonStyle = ButtonStyle();
      const TextStyle titleTextStyle = TextStyle();
      const TextStyle contentTextStyle = TextStyle();
      const TextAlign contentTextAlign = TextAlign.start;
      const TextAlign titleTextAlign = TextAlign.start;
      const EdgeInsets headerPadding = EdgeInsets.all(10);
      const EdgeInsets contentPadding = EdgeInsets.all(10);
      const EdgeInsets ctaPadding = EdgeInsets.all(10);
      const double contentSpacing = 10.0;
      const double ctaSpacing = 10.0;
      const ButtonListOrientation buttonListOrientation =
          ButtonListOrientation.horizontalLeftToRight;
      const bool isShowButtonClose = true;
      const double dialogHorizontalPadding = 30.0;
      final Map<String, String> loggingEventMetaData = <String, String>{'key': 'value'};
      final Map<String, String> loggingEventOnShowMetaData = <String, String>{'key': 'value'};

      await tester.pumpWidget(
        MaterialApp(
          home: EvoDialogConfirm(
            textPositive: textPositive,
            dialogId: dialogId,
            title: title,
            content: content,
            textNegative: textNegative,
            onClickNegative: onClickNegative,
            footer: footer,
            onClickPositive: onClickPositive,
            imageHeader: imageHeader,
            positiveButtonStyle: positiveButtonStyle,
            negativeButtonStyle: negativeButtonStyle,
            titleTextStyle: titleTextStyle,
            contentTextStyle: contentTextStyle,
            contentTextAlign: contentTextAlign,
            titleTextAlign: titleTextAlign,
            headerPadding: headerPadding,
            contentPadding: contentPadding,
            ctaPadding: ctaPadding,
            contentSpacing: contentSpacing,
            ctaSpacing: ctaSpacing,
            buttonListOrientation: buttonListOrientation,
            isShowButtonClose: isShowButtonClose,
            dialogHorizontalPadding: dialogHorizontalPadding,
            loggingEventMetaData: loggingEventMetaData,
            loggingEventOnShowMetaData: loggingEventOnShowMetaData,
          ),
        ),
      );

      final CommonDialogBottomSheet dialogContent = tester.widget<CommonDialogBottomSheet>(
        find.byType(CommonDialogBottomSheet),
      );

      expect(dialogContent.textPositive, textPositive);
      expect(dialogContent.dialogId, dialogId);
      expect(dialogContent.title, title);
      expect(dialogContent.content, content);
      expect(dialogContent.textNegative, textNegative);
      expect(dialogContent.onClickNegative, onClickNegative);
      expect(dialogContent.footer, footer);
      expect(dialogContent.onClickPositive, onClickPositive);
      expect(dialogContent.header, imageHeader);
      expect(dialogContent.positiveButtonStyle, positiveButtonStyle);
      expect(dialogContent.negativeButtonStyle, negativeButtonStyle);
      expect(dialogContent.titleTextStyle, titleTextStyle);
      expect(dialogContent.contentTextStyle, contentTextStyle);
      expect(dialogContent.contentTextAlign, contentTextAlign);
      expect(dialogContent.titleTextAlign, titleTextAlign);
      expect(dialogContent.headerPadding, headerPadding);
      expect(dialogContent.contentPadding, contentPadding);
      expect(dialogContent.ctaPadding, ctaPadding);
      expect(dialogContent.contentSpacing, contentSpacing);
      expect(dialogContent.ctaSpacing, ctaSpacing);
      expect(dialogContent.buttonListOrientation, buttonListOrientation);
      expect(dialogContent.isShowButtonClose, isShowButtonClose);
      expect(dialogContent.loggingEventMetaData, loggingEventMetaData);
      expect(dialogContent.loggingEventOnShowMetaData, loggingEventOnShowMetaData);

      final Dialog dialogWidget = tester.widget<Dialog>(find.byType(Dialog));
      expect(
        dialogWidget.insetPadding,
        EdgeInsets.symmetric(
          vertical: 20,
          horizontal: dialogHorizontalPadding,
        ),
      );
    });
  });
}
