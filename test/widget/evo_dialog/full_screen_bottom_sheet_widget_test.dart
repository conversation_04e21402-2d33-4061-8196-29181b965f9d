import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/evo_dialog/full_screen_bottom_sheet_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
    getIt.registerSingleton(GlobalKeyProvider());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('FullScreenBottomSheetWidget', () {
    test('should have close button size of 40', () {
      final FullScreenBottomSheetWidget widget = FullScreenBottomSheetWidget();
      expect(widget.btnCloseSize, 40);
    });

    testWidgets('should render with content', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FullScreenBottomSheetWidget(
            content: Text('Test Content'),
          ),
        ),
      );

      final Finder contentFinder = find.text('Test Content');
      expect(contentFinder, findsOneWidget);
    });

    testWidgets('should call onClose callback when close button is tapped',
        (WidgetTester tester) async {
      bool isClosed = false;
      await tester.pumpWidget(
        MaterialApp(
          home: FullScreenBottomSheetWidget(
            onClose: () => isClosed = true,
          ),
        ),
      );

      final Finder closeButtonFinder = find.byIcon(Icons.close);
      await tester.tap(closeButtonFinder);
      await tester.pumpAndSettle();

      expect(isClosed, isTrue);
    });

    testWidgets('should render close button icon with correct size and color',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FullScreenBottomSheetWidget(),
        ),
      );

      final Finder closeButtonFinder = find.byIcon(Icons.close);
      expect(closeButtonFinder, findsOneWidget);

      final Icon closeButtonIcon = tester.widget<Icon>(closeButtonFinder);
      expect(closeButtonIcon.size, 20);
      expect(closeButtonIcon.color, evoColors.icon);
    });

    testWidgets('should have correct height based on screen height and status bar height',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: FullScreenBottomSheetWidget(),
        ),
      );

      final Finder containerFinder = find.byType(Container).first;
      final Container containerWidget = tester.widget<Container>(containerFinder);

      final double closeButtonSize = 40;
      final double screenHeight = tester.view.physicalSize.height / tester.view.devicePixelRatio;
      final double statusBarHeight = tester.view.padding.top / tester.view.devicePixelRatio;
      final double expectedHeight =
          screenHeight - statusBarHeight - (kToolbarHeight - closeButtonSize) / 2;

      expect(containerWidget.constraints?.maxHeight, expectedHeight);
    });

    testWidgets('getHeight() should return correct value', (WidgetTester tester) async {
      final double screenHeight = 800;
      final double topPadding = 24;

      tester.view.devicePixelRatio = 1;
      tester.view.physicalSize = Size(400, screenHeight);
      tester.view.padding = FakeViewPadding(top: topPadding);
      addTearDown(tester.view.resetPhysicalSize);

      await tester.pumpWidget(
        MaterialApp(
          navigatorKey: globalKeyProvider.navigatorKey,
          home: FullScreenBottomSheetWidget(),
        ),
      );

      final Finder finder = find.byType(FullScreenBottomSheetWidget);
      final FullScreenBottomSheetWidget widget = tester.widget(finder);
      final BuildContext context = tester.element(finder);
      final double height = widget.getHeight(context);
      final double expectedHeight =
          screenHeight - topPadding - ((kToolbarHeight - widget.btnCloseSize) / 2);
      expect(height, expectedHeight);
    });
  });
}
