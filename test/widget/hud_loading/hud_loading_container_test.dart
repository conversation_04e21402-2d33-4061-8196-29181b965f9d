import 'package:evoapp/widget/hud_loading/hud_loading_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('HudLoadingContainer', () {
    testWidgets('should render indicator and mask color', (WidgetTester tester) async {
      final Widget indicator = SizedBox();
      final Color maskColor = Colors.blue;

      await tester.pumpWidget(MaterialApp(
        home: HudLoadingContainer(
          indicator: indicator,
          maskColor: maskColor,
        ),
      ));

      expect(find.byWidget(indicator), findsOneWidget);
      final Container container = tester.widget(find.byType(Container));
      expect(container.color, maskColor);
    });

    testWidgets('should use default mask color when no color is passed',
        (WidgetTester tester) async {
      final Widget indicator = SizedBox();

      await tester.pumpWidget(MaterialApp(
        home: HudLoadingContainer(indicator: indicator),
      ));

      final Container container = tester.widget(find.byType(Container));
      expect(container.color, Colors.black.withOpacity(0.2));
    });
  });
}
