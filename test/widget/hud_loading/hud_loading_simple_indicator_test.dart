import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/hud_loading/hud_loading_simple_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
  });

  test('verify default value of HubLoadingSimpleIndicator', () {
    expect(HubLoadingSimpleIndicator.defaultSize, 90);
    expect(HubLoadingSimpleIndicator.defaultBorderRadius, 13);
    expect(HubLoadingSimpleIndicator.defaultPadding, 21);
  });

  testWidgets('HubLoadingSimpleIndicator has default size and white background', (WidgetTester tester) async {
    // Define a simple animation widget for testing
    const SizedBox animationWidget = SizedBox();

    // Build the widget
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: HubLoadingSimpleIndicator(animation: animationWidget),
        ),
      ),
    );

    // Find the HubLoadingSimpleIndicator
    final Finder hubLoadingSimpleIndicator = find.byType(HubLoadingSimpleIndicator);

    expect(hubLoadingSimpleIndicator, findsOneWidget);

    // Verify the size and padding
    final Container container = tester.widget<Container>(find.byType(Container));
    expect(container.constraints!.maxWidth, HubLoadingSimpleIndicator.defaultSize);
    expect(container.constraints!.maxHeight, HubLoadingSimpleIndicator.defaultSize);
    expect(container.padding, const EdgeInsets.all(HubLoadingSimpleIndicator.defaultPadding));

    // Verify the background color and border radius
    final BoxDecoration decoration = container.decoration as BoxDecoration;
    expect(decoration.color, evoColors.defaultWhite);
    expect(decoration.borderRadius, const BorderRadius.all(Radius.circular(HubLoadingSimpleIndicator.defaultBorderRadius)));

    // Verify the child animation widget
    final Finder animation = find.byType(SizedBox);
    expect(animation, findsOneWidget);
  });

  testWidgets('HubLoadingSimpleIndicator displays provided animation', (WidgetTester tester) async {
    // Define a custom animation widget for testing
    const CircularProgressIndicator testAnimation = CircularProgressIndicator();

    // Build the widget with a custom animation
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: HubLoadingSimpleIndicator(animation: testAnimation),
        ),
      ),
    );

    // Verify the child animation widget
    final Finder animation = find.byType(CircularProgressIndicator);
    expect(animation, findsOneWidget);
  });

}