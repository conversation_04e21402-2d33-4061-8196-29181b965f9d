import 'package:evoapp/widget/hud_loading/hud_loading.dart';
import 'package:evoapp/widget/hud_loading/hud_loading_container.dart';
import 'package:evoapp/widget/hud_loading/hud_loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('HudLoadingWidget', () {
    testWidgets('should contain Overlay for a HudLoadingContainer and a widget',
        (WidgetTester tester) async {
      await tester.pumpWidget(Directionality(
        textDirection: TextDirection.ltr,
        child: HudLoadingWidget(child: Container(key: Key('1'))),
      ));

      final Finder overlayFinder = find.byType(Overlay);
      expect(overlayFinder, findsOneWidget);
      final Overlay overlay = tester.widget<Overlay>(overlayFinder);
      final List<OverlayEntry> entries = overlay.initialEntries;
      expect(entries.length, 2);

      HudLoading.instance.show(indicator: Container());
      await tester.pumpAndSettle();

      // overlay entry 1
      expect(
        find.descendant(of: overlayFinder, matching: find.byType(HudLoadingContainer)),
        findsOneWidget,
      );

      // overlay entry 2
      expect(
        find.descendant(of: overlayFinder, matching: find.byKey(Key('1'))),
        findsOneWidget,
      );
    });

    testWidgets('should render SizedBox when child is null', (WidgetTester tester) async {
      await tester.pumpWidget(Directionality(
        textDirection: TextDirection.ltr,
        child: HudLoadingWidget(child: null),
      ));

      final Finder overlayFinder = find.byType(Overlay);
      expect(overlayFinder, findsOneWidget);

      HudLoading.instance.show(indicator: Container());
      await tester.pumpAndSettle();

      expect(
        find.descendant(of: overlayFinder, matching: find.byType(SizedBox)),
        findsOneWidget,
      );
    });
  });
}
