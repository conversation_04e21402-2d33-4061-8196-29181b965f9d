import 'package:evoapp/widget/hud_loading/hud_loading.dart';
import 'package:evoapp/widget/hud_loading/hud_loading_container.dart';
import 'package:evoapp/widget/hud_loading/hud_loading_overlay_entry.dart';
import 'package:evoapp/widget/hud_loading/hud_loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockHudLoadingOverlayEntry extends Mock implements HudLoadingOverlayEntry {}

void main() {
  final HudLoadingOverlayEntry overlayEntry = MockHudLoadingOverlayEntry();
  when(() => overlayEntry.markNeedsBuild()).thenReturn(null);

  group('HudLoading', () {
    test('should be singleton', () {
      expect(identical(HudLoading(), HudLoading()), isTrue);
      expect(identical(HudLoading.instance, HudLoading()), isTrue);
      expect(identical(HudLoading.instance, HudLoading.instance), isTrue);
    });

    test('init() should return TransitionBuilder that returns HudLoadingWidget', () {
      final HudLoading hud = HudLoading();
      final TransitionBuilder builder = hud.init();
      final Widget result = builder(MockBuildContext(), Container());
      expect(result, isA<HudLoadingWidget>());

      final Widget? resultChild = (result as HudLoadingWidget).child;
      expect(resultChild, isA<Container>());
    });

    test('init() should return TransitionBuilder that calls provided builder', () {
      final HudLoading hud = HudLoading();

      Widget builderWrapper(BuildContext context, Widget? child) {
        return SizedBox(child: child);
      }

      final TransitionBuilder builder = hud.init(builder: builderWrapper);
      final Widget result = builder(MockBuildContext(), Container());
      expect(result, isA<SizedBox>());

      final Widget? resultChild = (result as SizedBox).child;
      expect(resultChild, isA<HudLoadingWidget>());

      final Widget? resultChildChild = (resultChild as HudLoadingWidget).child;
      expect(resultChildChild, isA<Container>());
    });

    test('show() should set key, set container, markNeedsBuild', () {
      final HudLoading hud = HudLoading();
      hud.overlayEntry = overlayEntry;

      expect(hud.key, isNull);
      expect(hud.container, isNull);

      final Widget indicator = SizedBox();
      final Color maskColor = Colors.blue;
      hud.show(indicator: indicator, maskColor: maskColor);

      expect(hud.key, isNotNull);
      expect(hud.container, isNotNull);
      final HudLoadingContainer container = hud.container as HudLoadingContainer;
      expect(container.indicator, indicator);
      expect(container.maskColor, maskColor);

      verify(() => overlayEntry.markNeedsBuild()).called(1);
    });

    test('show() should reset if called again', () {
      final HudLoading hud = HudLoading();
      hud.overlayEntry = overlayEntry;
      hud.show(indicator: SizedBox());
      final dynamic oldKey = hud.key;
      final dynamic oldContainer = hud.container;

      hud.show(indicator: SizedBox());
      expect(hud.key, isNot(equals(oldKey)));
      expect(hud.container, isNot(equals(oldContainer)));
      verify(() => overlayEntry.markNeedsBuild()).called(2);
    });
  });
}
