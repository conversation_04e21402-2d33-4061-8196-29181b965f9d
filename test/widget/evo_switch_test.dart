import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/widget/evo_switch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('EvoSwitch', () {
    testWidgets('should render with correct default values', (WidgetTester tester) async {
      await tester.pumpWidget(
        EvoSwitch(
          value: true,
          onToggle: (_) {},
        ),
      );

      final EvoSwitch evoSwitch = tester.widget(find.byType(EvoSwitch));
      expect(evoSwitch.width, 52);
      expect(evoSwitch.height, 32);
      expect(evoSwitch.toggleSize, 24);
      expect(evoSwitch.activeColor, null);
      expect(evoSwitch.inactiveColor, null);
      expect(evoSwitch.toggleColor, Colors.white);
      expect(evoSwitch.disabled, false);
    });

    testWidgets('should be tappable and right opacity when enabled', (WidgetTester tester) async {
      bool value = false;

      await tester.pumpWidget(
        EvoSwitch(
          value: value,
          onToggle: (bool newValue) {
            value = newValue;
          },
        ),
      );

      await tester.tap(find.byType(EvoSwitch));
      await tester.pumpAndSettle();
      expect(value, isTrue);

      final Opacity opacity = tester.firstWidget(find.byType(Opacity));
      expect(opacity.opacity, 1);
    });

    testWidgets('should NOT be tappable with right opacity when disabled',
        (WidgetTester tester) async {
      bool value = false;
      await tester.pumpWidget(
        EvoSwitch(
          disabled: true,
          value: value,
          onToggle: (bool newValue) {
            value = newValue;
          },
        ),
      );

      await tester.tap(find.byType(EvoSwitch));
      await tester.pumpAndSettle();
      expect(value, isFalse);

      final Opacity opacity = tester.firstWidget(find.byType(Opacity));
      expect(opacity.opacity, 0.6);
    });

    testWidgets('should apply active color and opacity when on', (WidgetTester tester) async {
      const MaterialColor activeColor = Colors.green;
      const MaterialColor inactiveColor = Colors.red;

      await tester.pumpWidget(
        EvoSwitch(
          value: true,
          onToggle: (_) {},
          activeColor: activeColor,
          inactiveColor: inactiveColor,
        ),
      );

      final Container container = tester.firstWidget(find.byType(Container));
      final ShapeDecoration decoration = container.decoration as ShapeDecoration;
      expect(decoration.color, activeColor);
    });

    testWidgets('should apply inactive color when off', (WidgetTester tester) async {
      const MaterialColor activeColor = Colors.green;
      const MaterialColor inactiveColor = Colors.red;

      await tester.pumpWidget(
        EvoSwitch(
          value: false,
          onToggle: (_) {},
          activeColor: activeColor,
          inactiveColor: inactiveColor,
        ),
      );

      final Container container = tester.firstWidget(find.byType(Container));
      final ShapeDecoration decoration = container.decoration as ShapeDecoration;
      expect(decoration.color, inactiveColor);
    });

    testWidgets('should do nothing with the same value after rebuilt', (WidgetTester tester) async {
      await tester.pumpWidget(
        StatefulBuilder(builder: (_, StateSetter setter) {
          return EvoSwitch(
            value: false,
            onToggle: (_) {
              setter(() {});
            },
          );
        }),
      );

      final AnimatedBuilder animatedBuilder = tester.widget(find.byType(AnimatedBuilder));
      final Animation<double> animation = animatedBuilder.animation as Animation<double>;
      expect(animation.value, 0);

      await tester.tap(find.byType(EvoSwitch));
      await tester.pumpAndSettle();
      expect(animation.value, 0);
    });

    testWidgets('should forward/reverse animation with a different value after rebuilt',
        (WidgetTester tester) async {
      bool value = false;
      await tester.pumpWidget(
        StatefulBuilder(builder: (_, StateSetter setter) {
          return EvoSwitch(
            value: value,
            onToggle: (_) {
              value = !value;
              setter(() {});
            },
          );
        }),
      );

      final AnimatedBuilder animatedBuilder = tester.widget(find.byType(AnimatedBuilder));
      final Animation<double> animation = animatedBuilder.animation as Animation<double>;
      expect(animation.value, 0);

      // forward animation
      await tester.tap(find.byType(EvoSwitch));
      await tester.pumpAndSettle();
      expect(animation.value, 1);

      // reserve animation
      await tester.tap(find.byType(EvoSwitch));
      await tester.pumpAndSettle();
      expect(animation.value, 0);
    });
  });
}
