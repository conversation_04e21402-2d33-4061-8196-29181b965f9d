import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/screen_util.dart';
import 'package:evoapp/widget/app_bar_wrapper.dart';
import 'package:evoapp/widget/appbar/evo_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    setUpMockGlobalKeyProvider(MockContext());

    //mock evoImageProvider.asset for arrow back
    when(() => evoImageProvider.asset(EvoImages.icArrowBack, width: 24.w, height: 24.w))
        .thenReturn(SizedBox(
      width: 24.w,
      height: 24.w,
    ));
  });

  group('AppBarWrapper', () {
    testWidgets('should render child widget', (WidgetTester tester) async {
      const Key childKey = Key('test-child');
      await tester.pumpWidget(
        MaterialApp(
          home: AppBarWrapper(
            child: Container(key: childKey),
          ),
        ),
      );

      expect(find.byKey(childKey), findsOneWidget);
    });

    testWidgets('should use default EvoAppBar when customAppbar is null',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: AppBarWrapper(
            child: Container(),
          ),
        ),
      );

      expect(find.byType(EvoAppBar), findsOneWidget);
    });

    testWidgets('should use customAppbar when provided', (WidgetTester tester) async {
      final GlobalKey<State<StatefulWidget>> customAppBarKey = GlobalKey();
      final EvoAppBar customAppbar = EvoAppBar(key: customAppBarKey);

      await tester.pumpWidget(
        MaterialApp(
          home: AppBarWrapper(
            customAppbar: customAppbar,
            child: Container(),
          ),
        ),
      );

      expect(find.byKey(customAppBarKey), findsOneWidget);
    });

    testWidgets('should use default backgroundColor when not provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: AppBarWrapper(
            child: Container(),
          ),
        ),
      );

      final Scaffold scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, equals(evoColors.grayBackground));
    });

    testWidgets('should use custom backgroundColor when provided', (WidgetTester tester) async {
      const MaterialColor customColor = Colors.red;

      await tester.pumpWidget(
        MaterialApp(
          home: AppBarWrapper(
            backgroundColor: customColor,
            child: Container(),
          ),
        ),
      );

      final Scaffold scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, equals(customColor));
    });

    testWidgets('should use default padding when contentPadding is null',
        (WidgetTester tester) async {
      const Key childKey = Key('test-child');

      await tester.pumpWidget(
        MaterialApp(
          home: AppBarWrapper(
            child: Container(key: childKey),
          ),
        ),
      );

      // Find the Padding widget that has our test child as a descendant
      // This ensures we're testing the specific Padding widget that wraps the child
      final Finder paddingFinder = find.ancestor(
        of: find.byKey(childKey),
        matching: find.byType(Padding),
      );

      final Padding padding = tester.widget<Padding>(paddingFinder.first);
      final EdgeInsets expectedPadding =
          EdgeInsets.only(left: 16.w, right: 16.w, bottom: EvoDimension.screenBottomPadding);

      expect(padding.padding, equals(expectedPadding));
    });

    testWidgets('should use custom contentPadding when provided', (WidgetTester tester) async {
      const EdgeInsets customPadding = EdgeInsets.all(20);
      const Key childKey = Key('test-child');

      await tester.pumpWidget(
        MaterialApp(
          home: AppBarWrapper(
            contentPadding: customPadding,
            child: Container(key: childKey),
          ),
        ),
      );

      // Find the Padding widget that has our test child as a descendant
      // This ensures we're testing the specific Padding widget that wraps the child
      final Finder paddingFinder = find.ancestor(
        of: find.byKey(childKey),
        matching: find.byType(Padding),
      );

      final Padding padding = tester.widget<Padding>(paddingFinder.first);
      expect(padding.padding, equals(customPadding));
    });

    testWidgets('should have resizeToAvoidBottomInset set to false', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: AppBarWrapper(
            child: Container(),
          ),
        ),
      );

      final Scaffold scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.resizeToAvoidBottomInset, isFalse);
    });
  });
}
