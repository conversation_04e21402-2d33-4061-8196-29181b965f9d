import 'package:evoapp/feature/main_screen/card_page/models/transaction_model.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/widget/elevated_container.dart';
import 'package:evoapp/widget/transaction_widget/transaction_item.dart';
import 'package:evoapp/widget/transaction_widget/transaction_section.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();

    final EvoUtilFunction evoFunction = getIt();
    when(() => evoFunction.evoFormatCurrency(any())).thenReturn('₱1234');
  });

  setUp(() {
    when(() => mockCommonImageProvider.asset(
          any(),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenAnswer((_) => Container());
  });

  tearDown(() {
    reset(mockCommonImageProvider);
  });

  testWidgets('should display recent transactions text', (WidgetTester tester) async {
    // Arrange
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: TransactionSection(),
        ),
      ),
    );

    // Act & Assert
    expect(find.text(EvoStrings.recentTransaction), findsOneWidget);

    // Act
    final Text textWidget = tester.widget<Text>(
      find.text(EvoStrings.recentTransaction),
    );

    // Assert
    final TextStyle textStyle = textWidget.style!;
    expect(textStyle.fontWeight, FontWeight.bold);
    expect(textStyle.fontSize, TextSize.xl.fontSize);
    expect(textStyle.color, evoColors.textPassive);
  });

  testWidgets('should display view more button', (WidgetTester tester) async {
    // Arrange
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: TransactionSection(),
        ),
      ),
    );

    // Act & Assert
    expect(find.text(EvoStrings.viewMore), findsOneWidget);

    // Act
    //final Finder commonButtonFinder = find.byType(CommonButton);
    final Finder viewMoreCommonButton = find.ancestor(
      of: find.text(EvoStrings.viewMore),
      matching: find.byType(CommonButton),
    );
    final CommonButton commonButtonWidget = tester.widget<CommonButton>(viewMoreCommonButton);
    final ButtonStyle buttonStyle = commonButtonWidget.style;

    // Assert
    final ButtonStyle expectedButtonStyle = evoButtonStyles.utility(ButtonSize.small);
    expect(
      buttonStyle.padding?.resolve(<WidgetState>{}),
      expectedButtonStyle.padding?.resolve(<WidgetState>{}),
    );
  });

  testWidgets('should call onViewMoreClicked when view more button is tapped',
      (WidgetTester tester) async {
    // Arrange
    bool wasTapped = false;
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TransactionSection(
            onViewMoreClicked: () {
              wasTapped = true;
            },
          ),
        ),
      ),
    );

    // Act
    await tester.tap(find.text(EvoStrings.viewMore));
    await tester.pump(); // Rebuild the widget

    // Assert
    expect(wasTapped, isTrue);
  });

  testWidgets('should display no transaction yet text', (WidgetTester tester) async {
    // Arrange
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: TransactionSection(),
        ),
      ),
    );

    // Assert Text
    expect(find.text(EvoStrings.noTransactionYet), findsOneWidget);

    // Assert height of ElevatedContainer
    final Finder elevatedContainerFinder = find.byType(ElevatedContainer);
    final Size containerSize = tester.getSize(elevatedContainerFinder);

    expect(containerSize.height, 90);

    /// Assert Image
    verify(() => evoImageProvider.asset(EvoImages.icInbox,
        color: evoColors.inputUnfocusedColor, fit: BoxFit.scaleDown)).called(1);
  });

  testWidgets('should display a list of transaction items and dividers',
      (WidgetTester tester) async {
    final List<TransactionModel> items = TransactionSection.mockData;
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TransactionSection(items: items),
        ),
      ),
    );

    final Finder listView = find.byType(ListView);
    expect(listView, findsOneWidget);
    expect(find.descendant(of: listView, matching: find.byType(TransactionItemWidget)),
        findsExactly(items.length));
    expect(find.descendant(of: listView, matching: find.byType(Divider)),
        findsExactly(items.length - 1));
  });
}
