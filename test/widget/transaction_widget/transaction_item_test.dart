import 'package:evoapp/feature/main_screen/card_page/models/transaction_model.dart';
import 'package:evoapp/widget/transaction_widget/transaction_item.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/functions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

void main() {
  late EvoUtilFunction mockEvoUtilFunction;
  late CommonUtilFunction mockCommonUtilFunction;
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    registerFallbackValue(MockContext());
    registerFallbackValue(BoxFit.scaleDown);

    getItRegisterMockCommonUtilFunctionAndImageProvider();
    mockCommonImageProvider = getIt.get<CommonImageProvider>();

    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getItRegisterColor();

    mockEvoUtilFunction = getIt.get<EvoUtilFunction>();
    mockCommonUtilFunction = getIt.get<CommonUtilFunction>();
    when(() => mockEvoUtilFunction.evoFormatCurrency(any())).thenReturn('₱1234');
    when(() => mockCommonUtilFunction.toDateTime(any()))
        .thenReturn(DateTime(2022, 11, 2, 15, 4, 5));
  });

  setUp(() {
    when(() => mockCommonImageProvider.asset(
          any(),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
          width: any(named: 'width'),
          height: any(named: 'height'),
        )).thenAnswer((_) => Container());
  });

  tearDown(() {
    reset(mockCommonImageProvider);
  });

  testWidgets('TransactionItemWidget displays correct information and styles',
      (WidgetTester tester) async {
    // Arrange
    final TransactionModel transactionModel = TransactionModel(
      merchantName: 'Test Merchant',
      amount: 123.45,
      purchaseAt: '2022-11-02T15:04:05Z',
      fourLastDigitOfCardNumberUsed: '1234',
    );

    // Act
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: TransactionItemWidget(model: transactionModel),
        ),
      ),
    );


    /// Assert icons
    verify(() => mockCommonImageProvider.asset(
          EvoImages.icCard,
          color: evoColors.accent90,
          fit: BoxFit.scaleDown,
        )).called(1);

    verify(() => mockCommonImageProvider.asset(
          EvoImages.icArrowRight,
          color: evoColors.screenTitle,
          width: 20,
          height: 20,
          fit: BoxFit.contain,
        )).called(1);

    /// Assert Merchant
    expect(find.text('Test Merchant'), findsOneWidget);
    final Text merchantNameText = tester.widget<Text>(find.text('Test Merchant'));
    expect(
      merchantNameText.style,
      evoTextStyles.regular(
        TextSize.sm,
        color: evoColors.textNormal,
      ),
    );

    /// Assert Amount
    expect(find.text('₱1234'), findsOneWidget); // Adjust currency format as needed
    final Text amountText = tester.widget<Text>(find.text('₱1234'));
    expect(amountText.style, evoTextStyles.bold(
      TextSize.base,
      color: evoColors.textNormal,
    ));

    /// Assert Mask CardNumber
    expect(find.text('via ···· 1234'), findsOneWidget); // Adjust card number mask format as needed
    final Text cardNumberText = tester.widget<Text>(find.text('via ···· 1234'));
    expect(cardNumberText.style, evoTextStyles.regular(
      TextSize.sm,
      color: evoColors.textActive,
    ));

    ///
    expect(find.text('Nov 02, 2022'), findsOneWidget); // Adjust date format as needed
    final Text purchaseDateText = tester.widget<Text>(find.text('Nov 02, 2022'));
    expect(purchaseDateText.style, evoTextStyles.regular(
      TextSize.sm,
      color: evoColors.textNormal,
    ));

  });
}
