import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/default_obscure_widget.dart';
import 'package:evoapp/widget/evo_pin_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_code_field_shape.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_code_fields.dart';
import 'package:flutter_common_package/widget/pin_code/common_pin_theme.dart';
import 'package:flutter_common_package/widget/pin_code/pin_code_widget.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider imageProvider;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    imageProvider = getIt.get<CommonImageProvider>();
  });

  setUp(() {
    when(() => imageProvider.asset(any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
        color: any(named: 'color'),
        cornerRadius: any(named: 'cornerRadius'))).thenAnswer(
      (_) => const Offstage(),
    );
  });

  tearDown(() {
    reset(imageProvider);
  });

  test('verify constant value', () {
    expect(EvoPinTextField.defaultPinTextFieldSize, 40);
    expect(EvoPinTextField.defaultPinCursorHeight, 20);
  });

  testWidgets('Should display EvoPinTextField with correct properties',
      (WidgetTester tester) async {
    // Define the necessary properties
    const int pinLength = 4;
    final TextEditingController textEditingController = TextEditingController();
    final FocusNode focusNode = FocusNode();
    const bool autoFocus = true;
    const bool autoUnFocus = false;
    const bool isObscureText = true;

    // Build the widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoPinTextField(
            pinLength: pinLength,
            textEditingController: textEditingController,
            focusNode: focusNode,
            isObscureText: isObscureText,
          ),
        ),
      ),
    );

    // Verify the CommonPinCode widget is found
    final Finder commonPinCodeFinder = find.byType(CommonPinCode);
    expect(commonPinCodeFinder, findsOneWidget);

    // Verify the properties are set correctly
    final CommonPinCode commonPinCode = tester.widget(commonPinCodeFinder);
    expect(commonPinCode.focusNode, focusNode);
    expect(commonPinCode.textController, textEditingController);
    expect(commonPinCode.autoFocus, autoFocus);
    expect(commonPinCode.autoUnFocus, autoUnFocus);
    expect(commonPinCode.pinLength, pinLength);
    expect(commonPinCode.obscuringWidget, isA<DefaultObscureWidget>());
    expect(commonPinCode.cursorHeight, EvoPinTextField.defaultPinCursorHeight);
    expect(commonPinCode.cursorColor, evoColors.textActive);
    expect(commonPinCode.showCursor, true);
  });

  testWidgets('Should display EvoPinTextField with correct theme', (WidgetTester tester) async {
    // Define the necessary properties
    const int pinLength = 4;
    final TextEditingController textEditingController = TextEditingController();

    // Build the widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoPinTextField(
            pinLength: pinLength,
            textEditingController: textEditingController,
          ),
        ),
      ),
    );

    // Verify the CommonPinCode widget is found
    final Finder commonPinCodeFinder = find.byType(CommonPinCode);
    expect(commonPinCodeFinder, findsOneWidget);

    // Verify the properties are set correctly
    final CommonPinCode commonPinCode = tester.widget(commonPinCodeFinder);
    final CommonPinTheme? pinTheme = commonPinCode.pinTheme;

    expect(pinTheme?.fieldHeight, EvoPinTextField.defaultPinTextFieldSize);
    expect(pinTheme?.fieldWidth, EvoPinTextField.defaultPinTextFieldSize);
    expect(pinTheme?.shape, CommonPinCodeFieldShape.box);
    expect(pinTheme?.borderWidth, 1);
    expect(pinTheme?.activeBorderWidth, 1);
    expect(pinTheme?.inactiveBorderWidth, 1);
    expect(pinTheme?.selectedBorderWidth, 2);
    expect(pinTheme?.selectedColor, evoColors.secondaryBase);
    expect(pinTheme?.inactiveColor, evoColors.grayBorders);
    expect(pinTheme?.activeColor, evoColors.grayBorders);
    expect(pinTheme?.selectedFillColor, evoColors.defaultTransparent);
    expect(pinTheme?.inactiveFillColor, evoColors.defaultTransparent);
    expect(pinTheme?.activeFillColor, evoColors.defaultTransparent);
  });

  testWidgets('Should call onChange and onSubmit callbacks', (WidgetTester tester) async {
    // Define the necessary properties
    const int pinLength = 4;
    final TextEditingController textEditingController = TextEditingController();
    bool onChangeCalled = false;
    bool onSubmitCalled = false;

    // Build the widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoPinTextField(
            pinLength: pinLength,
            textEditingController: textEditingController,
            onChange: (String value) {
              onChangeCalled = true;
            },
            onSubmit: (String value) {
              onSubmitCalled = true;
            },
          ),
        ),
      ),
    );

    // Enter text into the TextField
    final Finder commonPinCodeFinder = find.byType(CommonPinCode);
    expect(commonPinCodeFinder, findsOneWidget);

    // Verify the callbacks are called
    final CommonPinCode commonPinCode = tester.widget(commonPinCodeFinder);
    commonPinCode.onChange?.call('123');
    expect(onChangeCalled, isTrue);

    commonPinCode.onSubmit?.call('123');
    expect(onSubmitCalled, isTrue);
  });

  testWidgets('Should display error colors when errorMessage is not empty',
      (WidgetTester tester) async {
    // Define the necessary properties
    const int pinLength = 4;
    final TextEditingController textEditingController = TextEditingController();
    const String errorMessage = 'error_message';

    // Build the widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoPinTextField(
            pinLength: pinLength,
            textEditingController: textEditingController,
            errorMessage: errorMessage,
          ),
        ),
      ),
    );

    // Get the widget instance
    final EvoPinTextField widget = tester.widget(find.byType(EvoPinTextField));
    expect(widget.isError, isTrue);

    // Verify the error text is displayed
    expect(find.text(errorMessage), findsOneWidget);

    // Verify the CommonPinCode widget is found
    final Finder commonPinCodeFinder = find.byType(CommonPinCode);
    expect(commonPinCodeFinder, findsOneWidget);

    // Verify the pinTheme colors are set correctly for error state
    final CommonPinCode commonPinCode = tester.widget(commonPinCodeFinder);
    final CommonPinTheme? pinTheme = commonPinCode.pinTheme;

    expect(pinTheme?.inactiveColor, evoColors.errorBase);
    expect(pinTheme?.activeColor, evoColors.errorBase);
  });

  testWidgets('Should apply borderBuilder and borderRadiusBuilder', (WidgetTester tester) async {
    const int pinLength = 4;
    final TextEditingController textEditingController = TextEditingController();

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoPinTextField(
            pinLength: pinLength,
            textEditingController: textEditingController,
          ),
        ),
      ),
    );
    final EvoPinTextField widget = tester.widget(find.byType(EvoPinTextField));
    final CommonPinCode commonPinCode = tester.widget(find.byType(CommonPinCode));
    expect(commonPinCode.borderBuilder, widget.getBorder);
    expect(commonPinCode.borderRadiusBuilder, widget.getBorderRadius);
  });

  testWidgets('Should apply correct border color in normal state', (WidgetTester tester) async {
    const int pinLength = 4;
    final TextEditingController textEditingController = TextEditingController();

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoPinTextField(
            pinLength: pinLength,
            textEditingController: textEditingController,
          ),
        ),
      ),
    );

    // Get the widget instance
    final EvoPinTextField widget = tester.widget(find.byType(EvoPinTextField));
    expect(widget.isError, isFalse);

    // Get the CommonPinCode widget
    final CommonPinCode commonPinCode = tester.widget(find.byType(CommonPinCode));
    final CommonPinTheme? pinTheme = commonPinCode.pinTheme;

    // Verify normal state colors
    expect(pinTheme?.inactiveColor, evoColors.grayBorders);
    expect(pinTheme?.activeColor, evoColors.grayBorders);
    expect(pinTheme?.selectedColor, evoColors.secondaryBase);

    // Check border building function
    expect(commonPinCode.borderBuilder, isNotNull);

    // Call the borderBuilder with different states and verify results
    if (commonPinCode.borderBuilder != null) {
      final BorderSide normalBorderSide = BorderSide(color: evoColors.grayBorders, width: 1);

      // First field (index 0) should have border on all sides
      final Border firstBorder = widget.getBorder(
        tester.element(find.byType(EvoPinTextField)),
        FieldStatus.inactive,
        normalBorderSide,
        0,
      );
      expect(firstBorder.top, normalBorderSide);
      expect(firstBorder.bottom, normalBorderSide);
      expect(firstBorder.left, normalBorderSide);
      expect(firstBorder.right, normalBorderSide);

      // Middle field should have border only on right, top and bottom
      final Border middleBorder = widget.getBorder(
        tester.element(find.byType(EvoPinTextField)),
        FieldStatus.inactive,
        normalBorderSide,
        1,
      );
      expect(middleBorder.top, normalBorderSide);
      expect(middleBorder.bottom, normalBorderSide);
      expect(middleBorder.right, normalBorderSide);
      expect(middleBorder.left, BorderSide.none);
    }
  });

  testWidgets('Should apply correct border radius to first and last fields',
      (WidgetTester tester) async {
    const int pinLength = 4;
    final TextEditingController textEditingController = TextEditingController();

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoPinTextField(
            pinLength: pinLength,
            textEditingController: textEditingController,
          ),
        ),
      ),
    );

    final EvoPinTextField widget = tester.widget(find.byType(EvoPinTextField));
    final BuildContext context = tester.element(find.byType(EvoPinTextField));

    // First field should have radius on left side
    final BorderRadius firstFieldRadius = widget.getBorderRadius(context, FieldStatus.inactive, 0);
    expect(firstFieldRadius.topLeft, Radius.circular(8));
    expect(firstFieldRadius.bottomLeft, Radius.circular(8));
    expect(firstFieldRadius.topRight, Radius.zero);
    expect(firstFieldRadius.bottomRight, Radius.zero);

    // Last field should have radius on right side
    final BorderRadius lastFieldRadius =
        widget.getBorderRadius(context, FieldStatus.inactive, pinLength - 1);
    expect(lastFieldRadius.topRight, Radius.circular(8));
    expect(lastFieldRadius.bottomRight, Radius.circular(8));
    expect(lastFieldRadius.topLeft, Radius.zero);
    expect(lastFieldRadius.bottomLeft, Radius.zero);

    // Middle field should have no radius
    final BorderRadius middleFieldRadius = widget.getBorderRadius(context, FieldStatus.inactive, 1);
    expect(middleFieldRadius, BorderRadius.zero);
  });

  testWidgets('Should use thicker border when field is focused', (WidgetTester tester) async {
    const int pinLength = 4;
    final TextEditingController textEditingController = TextEditingController();

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoPinTextField(
            pinLength: pinLength,
            textEditingController: textEditingController,
          ),
        ),
      ),
    );

    final EvoPinTextField widget = tester.widget(find.byType(EvoPinTextField));
    final BuildContext context = tester.element(find.byType(EvoPinTextField));

    // Normal border width is 1
    final BorderSide normalBorderSide = BorderSide(color: evoColors.grayBorders, width: 2);

    // Selected border should have width 2
    final Border selectedBorder = widget.getBorder(
      context,
      FieldStatus.selected,
      normalBorderSide,
      1, // Middle field
    );

    // Selected border should have width 2 for all sides
    expect(selectedBorder.top.width, 2);
    expect(selectedBorder.bottom.width, 2);
    expect(selectedBorder.right.width, 2);
    expect(selectedBorder.left.width, 2);
  });
}
