import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/evo_progress_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
  });

  testWidgets('renders with default colors', (WidgetTester tester) async {
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: EvoProgressBar(progress: 0.5),
        ),
      ),
    );

    final progressBar = tester.widget<LinearProgressIndicator>(find.byType(LinearProgressIndicator));
    expect(progressBar.value, 0.5);
    expect(progressBar.backgroundColor, evoColors.accent70);
    expect((progressBar.valueColor as AlwaysStoppedAnimation).value, evoColors.accent90);
  });

  testWidgets('renders with custom colors', (WidgetTester tester) async {
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: EvoProgressBar(
            progress: 0.5,
            backgroundColor: Colors.red,
            valueColor: Colors.green,
          ),
        ),
      ),
    );

    final LinearProgressIndicator progressBar = tester.widget<LinearProgressIndicator>(find.byType(LinearProgressIndicator));
    expect(progressBar.value, 0.5);
    expect(progressBar.backgroundColor, Colors.red);
    expect((progressBar.valueColor as AlwaysStoppedAnimation).value, Colors.green);
  });

  testWidgets('progress value is correctly passed', (WidgetTester tester) async {
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: EvoProgressBar(progress: 0.75),
        ),
      ),
    );

    final LinearProgressIndicator progressBar = tester.widget<LinearProgressIndicator>(find.byType(LinearProgressIndicator));
    expect(progressBar.value, 0.75);
  });
}