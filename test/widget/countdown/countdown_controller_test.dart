import 'package:evoapp/widget/countdown/countdown_widget_builder.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('CountdownController', () {
    late CountdownController countdownController;

    setUp(() {
      countdownController = CountdownController(onDone: () {});
    });

    test('VoidCallback properties should return normally', () {
      expect(countdownController.start, returnsNormally);
      expect(countdownController.cancel, returnsNormally);
      expect(countdownController.pause, returnsNormally);
      expect(countdownController.resume, returnsNormally);
      expect(countdownController.onDone, returnsNormally);
    });

    test('Start should trigger start callback', () {
      bool startTriggered = false;
      countdownController.start = () {
        startTriggered = true;
      };
      countdownController.start();
      expect(startTriggered, isTrue);
    });

    test('Cancel should trigger cancel callback', () {
      bool cancelTriggered = false;
      countdownController.cancel = () {
        cancelTriggered = true;
      };
      countdownController.cancel();
      expect(cancelTriggered, isTrue);
    });

    test('Pause should trigger pause callback', () {
      bool pauseTriggered = false;
      countdownController.pause = () {
        pauseTriggered = true;
      };
      countdownController.pause();
      expect(pauseTriggered, isTrue);
    });

    test('Resume should trigger resume callback', () {
      bool resumeTriggered = false;
      countdownController.resume = () {
        resumeTriggered = true;
      };
      countdownController.resume();
      expect(resumeTriggered, isTrue);
    });

    test('onDone callback should be called', () {
      bool onDoneTriggered = false;
      countdownController = CountdownController(onDone: () {
        onDoneTriggered = true;
      });

      countdownController.onDone();
      expect(onDoneTriggered, isTrue);
    });
  });
}
