import 'package:evoapp/widget/countdown/countdown_widget_builder.dart';
import 'package:fake_async/fake_async.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const int durationInSecs = 10;

  Widget buildWidget(CountdownController controller) {
    return MaterialApp(
      home: CountdownWidgetBuilder(
        builder: (_, Duration duration) {
          return Text('${duration.inSeconds}');
        },
        controller: controller,
        duration: const Duration(seconds: durationInSecs),
      ),
    );
  }

  setUpAll(() {});

  testWidgets('CountdownWidgetBuilder test', (WidgetTester tester) async {
    final CountdownController controller = CountdownController(onDone: () {});
    await tester.pumpWidget(buildWidget(controller));
    expect(find.text('$durationInSecs'), findsOneWidget);

    fakeAsync((FakeAsync async) async {
      controller.start();
    });
  });

  testWidgets('CountdownWidgetBuilder correctly build widget when time elapsed ',
      (WidgetTester tester) async {
    final CountdownController controller = CountdownController(onDone: () {});
    await tester.pumpWidget(buildWidget(controller));
    expect(find.text('$durationInSecs'), findsOneWidget);

    fakeAsync((FakeAsync async) async {
      controller.start();
      async.elapse(const Duration(seconds: 3));
      expect(find.text('7'), findsOneWidget);
      async.flushMicrotasks();
    });
  });

  testWidgets('CountdownWidgetBuilder correctly build widget when time paused and resumed  ',
      (WidgetTester tester) async {
    final CountdownController controller = CountdownController(onDone: () {});
    await tester.pumpWidget(buildWidget(controller));
    expect(find.text('$durationInSecs'), findsOneWidget);

    fakeAsync((FakeAsync async) async {
      controller.start();
      async.elapse(const Duration(seconds: 3));
      controller.pause();
      expect(find.text('7'), findsOneWidget);
      async.elapse(const Duration(seconds: 10));
      controller.resume();
      async.elapse(const Duration(seconds: 7));
      expect(find.text('0'), findsOneWidget);
      async.flushMicrotasks();
    });
  });

  testWidgets('CountdownWidgetBuilder correctly build widget when time done should call onDone',
      (WidgetTester tester) async {
    bool onDoneCalled = false;
    final CountdownController controller = CountdownController(onDone: () {
      onDoneCalled = true;
    });
    await tester.pumpWidget(buildWidget(controller));
    expect(find.text('$durationInSecs'), findsOneWidget);

    fakeAsync((FakeAsync async) async {
      controller.start();
      async.elapse(const Duration(seconds: durationInSecs));
      expect(find.text('0'), findsOneWidget);
      expect(onDoneCalled, isTrue);
      async.flushMicrotasks();
    });
  });
}
