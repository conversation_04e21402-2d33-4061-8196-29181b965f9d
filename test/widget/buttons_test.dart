import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/buttons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockEvoButtonStyles extends Mock implements EvoButtonStyles {}

void main() {
  late CommonButtonStyles buttonStyles;

  setUpAll(() {
    registerFallbackValue(ButtonSize.small);

    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
    buttonStyles = getIt.registerSingleton<CommonButtonStyles>(MockEvoButtonStyles());
  });

  group('PrimaryButton', () {
    testWidgets('should render with primary style and respond to tap', (WidgetTester tester) async {
      when(() => buttonStyles.primary(any())).thenReturn(ButtonStyle());
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PrimaryButton(
              text: 'Primary',
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      final Finder buttonFinder = find.byType(CommonButton);
      expect(buttonFinder, findsOneWidget);

      final CommonButton button = tester.widget(buttonFinder);
      expect(button.isWrapContent, false);

      verify(() => buttonStyles.primary(any())).called(1);

      expect(find.text('Primary'), findsOneWidget);

      await tester.tap(buttonFinder);
      expect(tapped, true);
    });
  });

  group('SecondaryButton', () {
    testWidgets('should render with secondary style and respond to tap',
        (WidgetTester tester) async {
      when(() => buttonStyles.secondary(any())).thenReturn(ButtonStyle());
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SecondaryButton(
              text: 'Secondary',
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      final Finder buttonFinder = find.byType(CommonButton);
      expect(buttonFinder, findsOneWidget);

      final CommonButton button = tester.widget(buttonFinder);
      expect(button.isWrapContent, false);

      verify(() => buttonStyles.secondary(any())).called(1);

      expect(find.text('Secondary'), findsOneWidget);

      await tester.tap(buttonFinder);
      expect(tapped, true);
    });
  });

  group('TertiaryButton', () {
    testWidgets('should render with tertiary style and respond to tap',
        (WidgetTester tester) async {
      when(() => buttonStyles.tertiary(any())).thenReturn(ButtonStyle());
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TertiaryButton(
              text: 'Tertiary',
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      final Finder buttonFinder = find.byType(CommonButton);
      expect(buttonFinder, findsOneWidget);

      final CommonButton button = tester.widget(buttonFinder);
      expect(button.isWrapContent, false);

      verify(() => buttonStyles.tertiary(any())).called(1);

      expect(find.text('Tertiary'), findsOneWidget);

      await tester.tap(buttonFinder);
      expect(tapped, true);
    });
  });
}
