// Copyright (c) 2024 Trusting Social. All rights reserved.
// Proprietary and confidential. Unauthorized copying or distribution is prohibited.

import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/elevated_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
    getItRegisterButtonStyle();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('ElevatedContainer', () {
    testWidgets('should render with default properties', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ElevatedContainer(
            child: Text('Test Child'),
          ),
        ),
      );

      expect(find.byType(ElevatedContainer), findsOneWidget);
      expect(find.text('Test Child'), findsOneWidget);
      expect(find.byType(Container), findsOneWidget);
      expect(find.byType(Material), findsOneWidget);
      expect(find.byType(InkWell), findsOneWidget);
    });

    testWidgets('should render with custom surface color', (WidgetTester tester) async {
      const Color customColor = Colors.blue;

      await tester.pumpWidget(
        MaterialApp(
          home: ElevatedContainer(
            surfaceColor: customColor,
            child: Text('Test Child'),
          ),
        ),
      );

      final Material material = tester.widget<Material>(find.byType(Material));
      expect(material.color, equals(customColor));
    });

    testWidgets('should use default white color when surfaceColor is null',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ElevatedContainer(
            surfaceColor: null,
            child: Text('Test Child'),
          ),
        ),
      );

      final Material material = tester.widget<Material>(find.byType(Material));
      expect(material.color, equals(evoColors.defaultWhite));
    });

    testWidgets('should render with custom width and height', (WidgetTester tester) async {
      const double customWidth = 200.0;
      const double customHeight = 100.0;

      await tester.pumpWidget(
        MaterialApp(
          home: ElevatedContainer(
            width: customWidth,
            height: customHeight,
            child: Text('Test Child'),
          ),
        ),
      );

      final Container container = tester.widget<Container>(find.byType(Container));
      expect(container.constraints?.maxWidth, equals(customWidth));
      expect(container.constraints?.maxHeight, equals(customHeight));
    });

    testWidgets('should respond to tap when onTap is provided', (WidgetTester tester) async {
      bool tapped = false;
      void onTapCallback() => tapped = true;

      await tester.pumpWidget(
        MaterialApp(
          home: ElevatedContainer(
            onTap: onTapCallback,
            child: Text('Tappable'),
          ),
        ),
      );

      await tester.tap(find.byType(InkWell));
      await tester.pump();

      expect(tapped, true);
    });

    testWidgets('should have proper shadow decoration', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: ElevatedContainer(
            child: Text('Test Child'),
          ),
        ),
      );

      final Container container = tester.widget<Container>(find.byType(Container));
      final BoxDecoration decoration = container.decoration as BoxDecoration;

      expect(decoration.boxShadow, isNotNull);
      expect(decoration.boxShadow!.length, equals(1));

      final BoxShadow shadow = decoration.boxShadow!.first;
      expect(shadow.color, equals(evoColors.shadowColor));
      expect(shadow.spreadRadius, equals(-8));
      expect(shadow.blurRadius, equals(24));
      expect(shadow.offset, equals(Offset(0, 15)));
    });
  });
}
