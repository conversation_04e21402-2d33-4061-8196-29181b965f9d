import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/colors.dart';
import 'package:evoapp/resources/text_styles.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/widget/appbar/appbar_support_action_button.dart';
import 'package:evoapp/widget/appbar/evo_support_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

void main() {
  late CommonImageProvider imageProvider;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
    getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());

    imageProvider = getIt.get<CommonImageProvider>();

    when(() => imageProvider.asset(any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
        color: any(named: 'color'),
        cornerRadius: any(named: 'cornerRadius'))).thenAnswer((_) => const Offstage());
  });

  group('verify AppbarSupportActionButton', () {
    testWidgets('should have correct button text', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(const MaterialApp(
        home: AppbarSupportActionButton(),
      ));

      expect(find.text(EvoStrings.needHelp), findsOne);
    });

    testWidgets('should show EvoSupportDialog when tap on button',
        (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(MaterialApp(
        home: Builder(builder: (BuildContext context) {
          setUpMockGlobalKeyProvider(context);
          return const AppbarSupportActionButton();
        }),
      ));

      await widgetTester.tap(find.byType(AppbarSupportActionButton));
      await widgetTester.pump();

      expect(find.byType(EvoSupportDialog), findsOne);
    });
  });
}
