import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/colors.dart';
import 'package:evoapp/resources/text_styles.dart';
import 'package:evoapp/widget/appbar/appbar_support_action_button.dart';
import 'package:evoapp/widget/appbar/evo_appbar.dart';
import 'package:evoapp/widget/appbar/evo_appbar_leading_button.dart';
import 'package:evoapp/widget/appbar/evo_support_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/button_styles.dart';
import 'package:flutter_common_package/resources/colors.dart';
import 'package:flutter_common_package/resources/text_styles.dart';
import 'package:flutter_common_package/widget/common_button.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider imageProvider;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
    getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<GlobalKeyProvider>(() => GlobalKeyProvider());

    imageProvider = getIt.get<CommonImageProvider>();

    when(() => imageProvider.asset(any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
        color: any(named: 'color'),
        cornerRadius: any(named: 'cornerRadius'))).thenAnswer(
      (_) => const Offstage(),
    );
  });

  group('verify EvoSupportAppbar', () {
    test('should be subtype of EvoAppBar', () {
      expect(EvoSupportAppbar(), isA<EvoAppBar>());
    });

    testWidgets('should default leading have properly padding', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: EvoSupportAppbar.defaultLeading,
        ),
      ));

      final Finder finder = find.byWidgetPredicate((Widget widget) =>
          widget is Padding &&
          widget.padding == const EdgeInsets.only(left: EvoSupportAppbar.leadingLeftInset));

      expect(finder, findsOne);
    });

    testWidgets('should default actions have properly padding', (WidgetTester widgetTester) async {
      await widgetTester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: EvoSupportAppbar.defaultActions.firstOrNull,
        ),
      ));

      final Finder finder = find.byWidgetPredicate((Widget widget) =>
          widget is Padding && widget.padding == const EdgeInsets.only(right: 24));

      expect(finder, findsOne);
    });

    testWidgets('should display correctly with initial property ',
        (WidgetTester widgetTester) async {
      final EvoSupportAppbar appbar = EvoSupportAppbar();

      await widgetTester.pumpWidget(MaterialApp(
        home: Scaffold(
          appBar: appbar,
        ),
      ));

      expect(EvoSupportAppbar.leadingLeftInset, 12);
      expect(appbar.preferredSize, const Size.fromHeight(64));
      expect(find.byType(AppbarSupportActionButton), findsOne);
      expect(find.byType(EvoAppBarLeadingButton), findsOne);
      expect(appbar.leading, EvoSupportAppbar.defaultLeading);
      expect(appbar.actions, EvoSupportAppbar.defaultActions);
    });

    testWidgets('should display correctly with given property', (WidgetTester widgetTester) async {
      const Offstage leadingWidget = Offstage();
      const Offstage firstAction = Offstage();
      const Offstage secondAction = Offstage();

      await widgetTester.pumpWidget(MaterialApp(
        home: Scaffold(
          appBar: EvoSupportAppbar(
            leading: leadingWidget,
            actions: const <Widget>[
              firstAction,
              secondAction,
            ],
          ),
        ),
      ));

      expect(find.byWidget(leadingWidget), findsOne);
      expect(find.byWidget(firstAction), findsOne);
      expect(find.byWidget(secondAction), findsOne);
    });

    testWidgets('invokes onClickButton when provided', (WidgetTester tester) async {
      bool buttonClicked = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AppbarSupportActionButton(
              onClickButton: () {
                buttonClicked = true;
              },
            ),
          ),
        ),
      );

      // Tap the button
      await tester.tap(find.byType(CommonButton));
      await tester.pumpAndSettle();

      // Verify the button was clicked
      expect(buttonClicked, isTrue);
    });
  });
}
