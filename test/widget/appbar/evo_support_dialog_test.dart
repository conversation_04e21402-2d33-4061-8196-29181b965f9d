import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/extension.dart';
import 'package:evoapp/util/url_launcher_uri_wrapper.dart';
import 'package:evoapp/widget/appbar/evo_support_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/common_package/url_launcher.dart';
import 'package:flutter_common_package/util/common_navigator.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockBuildContext extends Mock implements BuildContext {}

class MockCommonNavigator extends Mock implements CommonNavigator {}

class MockUrlLauncherWrapper extends Mock implements UrlLauncherWrapper {}

void main() {
  late CommonImageProvider imageProvider;
  late BuildContext mockNavigatorContext;
  late UrlLauncherWrapper mockUrlLauncherWrapper;

  Widget createWidgetUnderTest() {
    return const MaterialApp(
      home: Scaffold(
        body: EvoSupportDialog(),
      ),
    );
  }

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterColor();
    getItRegisterButtonStyle();
    getItRegisterTextStyle();
    getIt.registerLazySingleton<CommonNavigator>(() => MockCommonNavigator());
    getIt.registerLazySingleton<UrlLauncherWrapper>(() => MockUrlLauncherWrapper());
    mockUrlLauncherWrapper = getIt.get<UrlLauncherWrapper>();

    mockNavigatorContext = MockBuildContext();
    setUpMockGlobalKeyProvider(mockNavigatorContext);

    imageProvider = getIt.get<CommonImageProvider>();

    when(() => imageProvider.asset(any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
        color: any(named: 'color'),
        cornerRadius: any(named: 'cornerRadius'))).thenAnswer(
      (_) => const Text('X'),
    );

    when(() => mockNavigatorContext.pop(result: any(named: 'result'))).thenAnswer((_) {});

    registerFallbackValue(Uri.base);
    registerFallbackValue(LaunchMode.platformDefault);
    registerFallbackValue(const WebViewConfiguration());

    when(() => mockUrlLauncherWrapper.launchUrl(
          any(),
          mode: any(named: 'mode'),
          webViewConfiguration: any(named: 'webViewConfiguration'),
          webOnlyWindowName: any(named: 'webOnlyWindowName'),
        )).thenAnswer((_) async {
      return true;
    });
  });

  group('verify EvoSupportDialog', () {
    testWidgets('should display dialog header and content', (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      expect(find.text(EvoStrings.needHelp), findsOneWidget);
      expect(find.byType(GestureDetector), findsOneWidget);
      expect(
          find.text(
            '${EvoStrings.feedbackAndContact}\n${ContactInfo.contactSupportEmail}',
            findRichText: true,
          ),
          findsOneWidget);
    });

    testWidgets('should close dialog when close button is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());

      await tester.tap(find.byType(GestureDetector));
      await tester.pump();

      verify(() => mockNavigatorContext.pop(result: any(named: 'result'))).called(1);
    });

    testWidgets('should launchUrl when tapped on contact support email',
        (WidgetTester tester) async {
      await tester.pumpWidget(createWidgetUnderTest());
      await tester.tapOnText(find.textRange.ofSubstring(ContactInfo.contactSupportEmail));
      await tester.pump();

      final Uri? capturedUri = verify(() => mockUrlLauncherWrapper.launchUrl(
            captureAny(),
          )).captured.firstOrNull;

      expect(capturedUri, ContactInfo.contactSupportEmail.uriForSendMail());
    });
  });
}
