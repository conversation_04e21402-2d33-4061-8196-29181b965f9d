import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/global.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/widget/appbar/appbar_support_action_button.dart';
import 'package:evoapp/widget/appbar/need_help_support_appbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

class MockEvoUtilFunction extends Mock implements EvoUtilFunction {}

class _FakeLeadingWidget extends SizedBox {}

void main() {
  late EvoUtilFunction utilFunction;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterButtonStyle();
    getItRegisterTextStyle();

    utilFunction = getIt.registerSingleton<EvoUtilFunction>(MockEvoUtilFunction());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('NeedHelpSupportAppbar', () {
    testWidgets('should have exactly one widget AppbarSupportActionButton',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: NeedHelpSupportAppbar()));

      expect(find.byType(AppbarSupportActionButton), findsOneWidget);
    });

    testWidgets('should render the leading widget when provided', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: NeedHelpSupportAppbar(
          leading: _FakeLeadingWidget(),
        ),
      ));

      expect(find.byType(_FakeLeadingWidget), findsOneWidget);
    });

    testWidgets('AppbarSupportActionButton should open webview on tap',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: NeedHelpSupportAppbar(),
      ));

      await tester.tap(find.byType(AppbarSupportActionButton));
      await tester.pumpAndSettle();

      verify(
        () => utilFunction.openInAppWebView(
          title: EvoStrings.contactItemTitle,
          url: WebsiteUrl.evoContactUrl,
        ),
      ).called(1);
    });
  });
}
