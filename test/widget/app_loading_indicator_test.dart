import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/app_loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('AppLoadingIndicator', () {
    testWidgets('should render correct UI', (WidgetTester tester) async {
      await tester.pumpWidget(AppLoadingIndicator());

      final SizedBox box = tester.widget(find.byType(SizedBox));
      expect(box.height, 64);
      expect(box.width, 64);

      final CircularProgressIndicator indicator =
          tester.widget(find.byType(CircularProgressIndicator));
      expect(indicator.color, evoColors.secondaryBase);
      expect(indicator.backgroundColor, evoColors.secondaryInteractive);
      expect(indicator.strokeCap, StrokeCap.butt);
      expect(indicator.strokeWidth, 4);
    });
  });
}
