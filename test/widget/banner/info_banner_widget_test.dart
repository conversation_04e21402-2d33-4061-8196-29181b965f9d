import 'package:collection/collection.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/images.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/banners/info_banner_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider imageProvider;

  // Define a list of info strings
  final List<String> infoStrings = <String>[
    'Item 1',
    'Item 2',
    'Item 3',
  ];

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();

    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    imageProvider = getIt.get<CommonImageProvider>();
  });

  setUp(() {
    // stub the image provider to return a Icons.add
    when(() => imageProvider.asset(any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
        color: any(named: 'color'),
        cornerRadius: any(named: 'cornerRadius'))).thenAnswer(
      (_) => const Icon(Icons.add),
    );
  });

  tearDown(() {
    reset(imageProvider);
  });

  testWidgets('InfoBannerWidget display background correctly', (WidgetTester tester) async {
    /// Build the InfoBannerWidget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: InfoBannerWidget(info: infoStrings),
        ),
      ),
    );

    /// Find the first Container widget
    final Finder containerFinder = find.byType(Container).first;

    /// Verify that there is at least one Container widget
    expect(containerFinder, findsOneWidget);

    /// Retrieve the Container widget
    final Container containerWidget = tester.widget(containerFinder);
    expect(containerWidget.padding, const EdgeInsets.all(16));

    /// Verify the decoration property of the Container
    final BoxDecoration decoration = containerWidget.decoration as BoxDecoration;
    expect(decoration.color, evoColors.informationBackground);
    expect(decoration.borderRadius, const BorderRadius.all(Radius.circular(8)));
  });

  testWidgets('InfoBannerWidget displays a list of info strings', (WidgetTester tester) async {
    /// Define a list of info strings
    final List<String> infoStrings = <String>[
      'Item 1',
      'Item 2',
      'Item 3',
    ];

    /// Build the InfoBannerWidget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: InfoBannerWidget(info: infoStrings),
        ),
      ),
    );

    for (final String str in infoStrings) {
      expect(find.text('  •  $str'), findsOneWidget);
    }
  });
}
