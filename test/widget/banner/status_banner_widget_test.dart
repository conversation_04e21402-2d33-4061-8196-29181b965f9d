import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/banners/status_banner/status_banner_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider imageProvider;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();

    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    imageProvider = getIt.get<CommonImageProvider>();
  });

  setUp(() {
    // stub the image provider to return a Icons.add
    when(() => imageProvider.asset(any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
        color: any(named: 'color'),
        cornerRadius: any(named: 'cornerRadius'))).thenAnswer(
      (_) => const Icon(Icons.add),
    );
  });

  tearDown(() {
    reset(imageProvider);
  });

  void verifyOrderOfUIComponents({
    required WidgetTester tester,
    required Color expectedBackgroundColor,
    required String expectedTitle,
    String? expectedDescription,
  }) {
    // Verify the background color
    final Finder containerFinder = find.byType(Container);
    final Container container = tester.widget<Container>(containerFinder);
    final BoxDecoration decoration = container.decoration as BoxDecoration;
    expect(decoration.color, expectedBackgroundColor);

    // Verify the order of components
    final Finder columnFinder = find.descendant(of: containerFinder, matching: find.byType(Column));
    final Column columnWidget = tester.widget<Column>(columnFinder);

    expect(columnWidget.crossAxisAlignment, CrossAxisAlignment.start);

    // Verify the first child is Row
    expect(columnWidget.children[0], isA<Row>());

    final Row rowWidget = columnWidget.children[0] as Row;

    // Verify the order of children in Row
    expect(rowWidget.children[0], isA<Icon>());
    expect(rowWidget.children[1], isA<SizedBox>());
    expect(rowWidget.children[2], isA<Expanded>());

    final Expanded expandedWidget = rowWidget.children[2] as Expanded;

    // Verify the Text widget inside Expanded
    expect(expandedWidget.child, isA<Text>());
    final Text textWidget = expandedWidget.child as Text;
    expect(textWidget.data, expectedTitle);

    // Verify the second child in Column is SizedBox.shrink
    if (expectedDescription == null || expectedDescription.isEmpty) {
      expect(columnWidget.children[1], isA<SizedBox>());
      final SizedBox sizedBoxWidget = columnWidget.children[1] as SizedBox;
      expect(sizedBoxWidget.width, 0);
      expect(sizedBoxWidget.height, 0);
    } else {
      expect(columnWidget.children[1], isA<Padding>());
      final Padding paddingWidget = columnWidget.children[1] as Padding;
      expect(paddingWidget.padding, const EdgeInsets.only(top: 16.0));
      final Text descriptionTextWidget = paddingWidget.child as Text;
      expect(descriptionTextWidget.data, expectedDescription);
    }
  }

  testWidgets('StatusBannerWidget displays correct title and description for success type',
      (WidgetTester tester) async {
    // Build the widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: StatusBannerWidget.create(
            type: StatusBannerType.success,
            title: 'Success Title',
            description: 'This is a success description.',
          ),
        ),
      ),
    );

    verifyOrderOfUIComponents(
      tester: tester,
      expectedBackgroundColor: evoColors.positive70,
      expectedTitle: 'Success Title',
      expectedDescription: 'This is a success description.',
    );

    // Verify the commonImageProvider is called with correct parameters based on the type
    verify(
      () => imageProvider.asset(
        EvoImages.icSuccessBanner,
        height: StatusBannerWidget.defaultIconSize,
        color: evoColors.positive100,
      ),
    ).called(1);
  });

  testWidgets('StatusBannerWidget displays correct title and icon for error type',
      (WidgetTester tester) async {
    // Build the widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: StatusBannerWidget.create(
            type: StatusBannerType.error,
            title: 'Error Title',
          ),
        ),
      ),
    );

    verifyOrderOfUIComponents(
      tester: tester,
      expectedBackgroundColor: evoColors.error70,
      expectedTitle: 'Error Title',
      expectedDescription: '',
    );

    // Verify the commonImageProvider is called with correct parameters
    verify(
      () => imageProvider.asset(
        EvoImages.icErrorBanner,
        height: StatusBannerWidget.defaultIconSize,
        color: evoColors.error100,
      ),
    ).called(1);
  });

  testWidgets('StatusBannerWidget displays correct title and icon for warning type',
      (WidgetTester tester) async {
    // Build the widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: StatusBannerWidget.create(
            type: StatusBannerType.warning,
            title: 'Warning Title',
          ),
        ),
      ),
    );

    verifyOrderOfUIComponents(
      tester: tester,
      expectedBackgroundColor: evoColors.warning70,
      expectedTitle: 'Warning Title',
    );

    // Verify the commonImageProvider is called with correct parameters
    verify(
      () => imageProvider.asset(
        EvoImages.icWarningBanner,
        height: StatusBannerWidget.defaultIconSize,
        color: evoColors.warning100,
      ),
    ).called(1);
  });

  group('verify if parameter has description Widget', () {
    const String defaultDescription = 'This is a description widget.';
    testWidgets('StatusBannerWidget displays correct title and description Widget',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatusBannerWidget.create(
              type: StatusBannerType.warning,
              title: 'Warning Title',
              descriptionWidget: Container(
                color: Colors.amber,
                child: const Text(defaultDescription),
              ),
            ),
          ),
        ),
      );

      final Finder containerFinder = find.byType(Container);

      // Verify the order of components
      final Finder columnFinder =
          find.descendant(of: containerFinder, matching: find.byType(Column));
      final Column columnWidget = tester.widget<Column>(columnFinder);

      expect(columnWidget.children[1], isA<Padding>());
      final Padding paddingWidget = columnWidget.children[1] as Padding;
      expect(paddingWidget.padding, const EdgeInsets.only(top: 16.0));
      final Container descriptionContainer = paddingWidget.child as Container;
      expect(descriptionContainer.child, isA<Text>());
      expect((descriptionContainer.child as Text).data, defaultDescription);

      // Verify the commonImageProvider is called with correct parameters
      verify(
        () => imageProvider.asset(
          EvoImages.icWarningBanner,
          height: StatusBannerWidget.defaultIconSize,
          color: evoColors.warning100,
        ),
      ).called(1);
    });

    testWidgets(
        'StatusBannerWidget displays correct description if both description and descriptionWidget are provided',
        (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatusBannerWidget.create(
              type: StatusBannerType.warning,
              title: 'Warning Title',
              description: 'my description.',
              descriptionWidget: Container(
                color: Colors.amber,
                child: const Text(defaultDescription),
              ),
            ),
          ),
        ),
      );

      verifyOrderOfUIComponents(
        tester: tester,
        expectedBackgroundColor: evoColors.warning70,
        expectedTitle: 'Warning Title',
        expectedDescription: 'my description.',
      );

      // Verify the text is not displayed
      expect(find.text(defaultDescription), findsNothing);

      // Verify the commonImageProvider is called with correct parameters
      verify(
        () => imageProvider.asset(
          EvoImages.icWarningBanner,
          height: StatusBannerWidget.defaultIconSize,
          color: evoColors.warning100,
        ),
      ).called(1);
    });
  });
}
