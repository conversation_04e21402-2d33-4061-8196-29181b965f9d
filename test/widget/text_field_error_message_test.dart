import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/widget/text_field_error_message.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

void main() {
  late CommonImageProvider imageProvider;

  setUpAll(() {
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterTextStyle();
    getItRegisterColor();
    imageProvider = getIt.get<CommonImageProvider>();
  });

  setUp(() {
    when(() => imageProvider.asset(any(),
        height: any(named: 'height'),
        width: any(named: 'width'),
        fit: any(named: 'fit'),
        color: any(named: 'color'),
        cornerRadius: any(named: 'cornerRadius'))).thenAnswer(
      (_) => const Offstage(),
    );
  });

  tearDown(() {
    reset(imageProvider);
  });

  group('TextFieldErrorMessage', () {
    testWidgets('displays nothing when errorMessage is null', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: TextFieldErrorMessage(),
          ),
        ),
      );

      expect(find.byType(Row), findsNothing);
    });

    testWidgets('displays nothing when errorMessage is an empty string',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: TextFieldErrorMessage(errorMessage: ''),
          ),
        ),
      );

      expect(find.byType(Row), findsNothing);
    });

    testWidgets('displays error message when errorMessage is not empty',
        (WidgetTester tester) async {
      const String errorMessage = 'This is an error message';

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: TextFieldErrorMessage(errorMessage: errorMessage),
          ),
        ),
      );

      expect(find.text(errorMessage), findsOneWidget);
      verify(() => imageProvider.asset(captureAny(),
          height: any(named: 'height'),
          width: any(named: 'width'),
          fit: any(named: 'fit'),
          color: any(named: 'color'),
          cornerRadius: any(named: 'cornerRadius'))).called(1);
    });
  });
}
