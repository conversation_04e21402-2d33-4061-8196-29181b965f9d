import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/animation/loading_animation_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterColor();
  });

  test('verify const value of LoadingAnimationWidget', () {
    expect(LoadingAnimationWidget.defaultSize, 48);
  });

  testWidgets('LoadingAnimationWidget has default size and color', (WidgetTester tester) async {
    // Build the widget
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: LoadingAnimationWidget(),
        ),
      ),
    );

    // Find the CircularProgressIndicator
    final Finder circularProgressIndicator = find.byType(CircularProgressIndicator);
    expect(circularProgressIndicator, findsOneWidget);

    // Verify the size
    final SizedBox sizedBox = tester.widget<SizedBox>(find.byType(SizedBox));
    expect(sizedBox.width, LoadingAnimationWidget.defaultSize);
    expect(sizedBox.height, LoadingAnimationWidget.defaultSize);

    // Verify the color
    final CircularProgressIndicator circularProgressIndicatorWidget =
        tester.widget<CircularProgressIndicator>(circularProgressIndicator);
    expect(circularProgressIndicatorWidget.color, evoColors.accent90);
    expect(circularProgressIndicatorWidget.backgroundColor, evoColors.defaultTransparent);
    expect(circularProgressIndicatorWidget.strokeCap, StrokeCap.round);
  });

  testWidgets('LoadingAnimationWidget uses provided color', (WidgetTester tester) async {
    const Color testColor = Colors.red;

    // Build the widget with a custom color
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: LoadingAnimationWidget(color: testColor),
        ),
      ),
    );

    // Find the CircularProgressIndicator
    final Finder circularProgressIndicator = find.byType(CircularProgressIndicator);

    expect(circularProgressIndicator, findsOneWidget);

    // Verify the color
    final CircularProgressIndicator circularProgressIndicatorWidget =
        tester.widget<CircularProgressIndicator>(circularProgressIndicator);
    expect(circularProgressIndicatorWidget.color, testColor);
  });
}
