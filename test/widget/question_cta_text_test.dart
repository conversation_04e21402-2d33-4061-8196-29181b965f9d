import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/screen_util.dart';
import 'package:evoapp/widget/question_cta_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  setUpAll(() {
    getItRegisterTextStyle();
    getItRegisterColor();
    getItRegisterButtonStyle();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('QuestionCtaText', () {
    const String question = 'Test question';
    const String cta = 'Test CTA';

    testWidgets('has correct order of spans', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: QuestionCtaText(
            question: question,
            cta: cta,
            onTap: null,
          ),
        ),
      ));

      final RichText richText = tester.widget<RichText>(find.byType(RichText));
      final TextSpan rootSpan = richText.text as TextSpan;
      final List<InlineSpan> children = rootSpan.children!;

      expect(children.length, 3);
      expect(children[0], isA<TextSpan>());
      expect(children[1], isA<WidgetSpan>());
      expect(children[2], isA<TextSpan>());
    });

    testWidgets('displays correct question text', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: QuestionCtaText(
            question: question,
            cta: cta,
            onTap: null,
          ),
        ),
      ));

      final RichText richText = tester.widget<RichText>(find.byType(RichText));
      final TextSpan rootSpan = richText.text as TextSpan;
      final TextSpan questionSpan = rootSpan.children![0] as TextSpan;

      expect(questionSpan.text, equals(question));
    });

    testWidgets('displays correct CTA text', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: QuestionCtaText(
            question: question,
            cta: cta,
            onTap: null,
          ),
        ),
      ));

      final RichText richText = tester.widget<RichText>(find.byType(RichText));
      final TextSpan rootSpan = richText.text as TextSpan;
      final TextSpan ctaSpan = rootSpan.children![2] as TextSpan;

      expect(ctaSpan.text, equals(cta));
    });

    testWidgets('has correct spacing widget', (WidgetTester tester) async {
      await tester.pumpWidget(const MaterialApp(
        home: Scaffold(
          body: QuestionCtaText(
            question: question,
            cta: cta,
            onTap: null,
          ),
        ),
      ));

      final RichText richText = tester.widget<RichText>(find.byType(RichText));
      final TextSpan rootSpan = richText.text as TextSpan;
      final WidgetSpan spacingSpan = rootSpan.children![1] as WidgetSpan;

      expect(spacingSpan.child, isA<SizedBox>());
      expect((spacingSpan.child as SizedBox).width, equals(8.w));
    });

    testWidgets('CTA uses custom color when provided and enabled',
        (WidgetTester tester) async {
      const Color customColor = Colors.purple;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: QuestionCtaText(
            question: question,
            cta: cta,
            onTap: () {},
            ctaColor: customColor,
          ),
        ),
      ));

      final RichText richText = tester.widget<RichText>(find.byType(RichText));
      final TextSpan rootSpan = richText.text as TextSpan;
      final TextSpan ctaSpan = rootSpan.children![2] as TextSpan;

      expect(ctaSpan.style?.color, equals(customColor));
    });

    testWidgets('CTA uses grey color when disabled, regardless of custom color',
        (WidgetTester tester) async {
      const Color customColor = Colors.purple;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: QuestionCtaText(
            question: question,
            cta: cta,
            onTap: null,
            ctaColor: customColor,
          ),
        ),
      ));

      final RichText richText = tester.widget<RichText>(find.byType(RichText));
      final TextSpan rootSpan = richText.text as TextSpan;
      final TextSpan ctaSpan = rootSpan.children![2] as TextSpan;

      expect(ctaSpan.style?.color, equals(evoColors.greyScale70));
    });

    testWidgets('CTA uses primary color when enabled and no custom color provided',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: QuestionCtaText(
            question: question,
            cta: cta,
            onTap: () {},
          ),
        ),
      ));

      final RichText richText = tester.widget<RichText>(find.byType(RichText));
      final TextSpan rootSpan = richText.text as TextSpan;
      final TextSpan ctaSpan = rootSpan.children![2] as TextSpan;

      expect(ctaSpan.style?.color, equals(evoColors.primary));
    });
  });
}
