import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/default_obscure_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  final String obscureChar = '•';

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('DefaultObscureWidget', () {
    testWidgets('should render the obscure character $obscureChar', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DefaultObscureWidget(),
        ),
      );

      final Finder textFinder = find.text(obscureChar);
      expect(textFinder, findsOneWidget);
    });

    testWidgets('should apply correct text style', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DefaultObscureWidget(),
        ),
      );

      final Finder textFinder = find.text(obscureChar);
      final Text textWidget = tester.widget<Text>(textFinder);
      expect(textWidget.style, evoTextStyles.h300(color: evoColors.textActive));
    });
  });
}
