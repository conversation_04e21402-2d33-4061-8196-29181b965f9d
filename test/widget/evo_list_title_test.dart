import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/screen_util.dart';
import 'package:evoapp/widget/evo_list_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../util/flutter_test_config.dart';

void main() {
  const String title = 'Test Title';

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
  });

  testWidgets('EvoListTileWidget displays title', (WidgetTester tester) async {
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: EvoListTileWidget(
            title: title,
          ),
        ),
      ),
    );

    expect(find.text(title), findsOneWidget);
    final Text titleText = tester.widget(find.text(title));
    expect(titleText.style,
        evoTextStyles.regular(TextSize.sm, color: evoColors.textNormal));

    final EvoListTileWidget widget =
        tester.widget(find.byType(EvoListTileWidget));
    expect(widget.trailingPadding, EdgeInsets.zero);
  });

  testWidgets('EvoListTileWidget displays leading widget',
      (WidgetTester tester) async {
    const Icon leadingIcon = Icon(Icons.star);

    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: EvoListTileWidget(
            title: title,
            leading: leadingIcon,
          ),
        ),
      ),
    );

    expect(find.byIcon(Icons.star), findsOneWidget);


    final Padding paddingWidget = tester.widget(find.ancestor(
      of: find.byIcon(Icons.star),
      matching: find.byType(Padding),
    ).first);
    expect(paddingWidget.padding, EdgeInsets.only(right: 20.w));
  });

  testWidgets('EvoListTileWidget displays trailing widget',
      (WidgetTester tester) async {
    const Icon trailingIcon = Icon(Icons.arrow_forward);

    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: EvoListTileWidget(
            title: title,
            trailing: trailingIcon,
          ),
        ),
      ),
    );

    expect(find.byIcon(Icons.arrow_forward), findsOneWidget);
  });

  testWidgets('EvoListTileWidget triggers onPress callback',
      (WidgetTester tester) async {
    bool pressed = false;

    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: EvoListTileWidget(
            title: title,
            onPress: () {
              pressed = true;
            },
          ),
        ),
      ),
    );

    await tester.tap(find.byType(InkWell));
    await tester.pump();

    expect(pressed, isTrue);
  });
}
