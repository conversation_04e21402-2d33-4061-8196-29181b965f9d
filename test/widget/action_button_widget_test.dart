import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/widget/action_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

class MockCallback extends Mock {
  void call();
}

void main() {
  final String mockTitle = 'mock-title';
  final String mockIcon = 'mock-icon';
  late CommonImageProvider mockImageProvider;
  late MockCallback mockCallback;

  setUpAll(() {
    getItRegisterMockCommonUtilFunctionAndImageProvider();
    getItRegisterTextStyle();
    getItRegisterColor();

    mockImageProvider = getIt.get<CommonImageProvider>();
  });

  setUp(() {
    mockCallback = MockCallback();

    when(() => mockImageProvider.asset(any(),
        width: any(named: 'width'),
        height: any(named: 'height'),
        fit: any(named: 'fit'),
        color: any(named: 'color'))).thenAnswer((_) {
      return const SizedBox.shrink();
    });
  });

  group('verify ActionButtonWidget', () {
    testWidgets('should build correctly with default params', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: ActionButtonWidget(
            title: mockTitle,
            icon: mockIcon,
          ),
        ),
      ));

      expect(find.byWidgetPredicate((Widget widget) {
        return widget is Text && widget.style?.color == evoColors.grayText;
      }), findsOne);

      verify(() => mockImageProvider.asset(
            mockIcon,
            width: any(named: 'width'),
            height: any(named: 'height'),
            fit: any(named: 'fit'),
          )).called(1);

      verify(() => mockImageProvider.asset(EvoImages.icArrowRight,
          width: any(named: 'width'),
          height: any(named: 'height'),
          fit: any(named: 'fit'),
          color: evoColors.grayBase)).called(1);
    });

    testWidgets('should build correctly trailing widget instead of default arrow',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: ActionButtonWidget(
            title: mockTitle,
            icon: mockIcon,
            trailing: const Text('trailing-widget'),
          ),
        ),
      ));

      verifyNever(() => mockImageProvider.asset(
            EvoImages.icArrowRight,
            width: any(named: 'width'),
            height: any(named: 'height'),
            fit: any(named: 'fit'),
          ));

      expect(find.text('trailing-widget'), findsOne);
    });

    testWidgets('should build correctly with given params and trailing widget is null',
        (WidgetTester tester) async {
      const Color iconColor = Colors.black;
      const Color titleColor = Colors.black12;
      const Color trailingColor = Colors.black26;

      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: ActionButtonWidget(
            title: mockTitle,
            icon: mockIcon,
            onPress: mockCallback.call,
            iconColor: iconColor,
            titleColor: titleColor,
            trailingColor: trailingColor,
          ),
        ),
      ));

      expect(find.byWidgetPredicate((Widget widget) {
        return widget is Text && widget.style?.color == titleColor;
      }), findsOne);

      verify(() => mockImageProvider.asset(
            mockIcon,
            width: any(named: 'width'),
            height: any(named: 'height'),
            fit: any(named: 'fit'),
            color: iconColor,
          )).called(1);

      verify(() => mockImageProvider.asset(
            EvoImages.icArrowRight,
            width: any(named: 'width'),
            height: any(named: 'height'),
            fit: any(named: 'fit'),
            color: trailingColor,
          )).called(1);

      await tester.tap(find.byType(ActionButtonWidget));
      verify(() => mockCallback.call()).called(1);
    });
  });
}
