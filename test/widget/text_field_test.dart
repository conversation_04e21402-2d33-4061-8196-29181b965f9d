import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/button_dimensions.dart';
import 'package:evoapp/resources/button_styles.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/screen_util.dart';
import 'package:evoapp/widget/evo_text_field.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/resources/button_dimensions.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/util/extension.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

class MockTextSelectionControls extends Mock implements TextSelectionControls {}

void main() {
  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    getIt.registerLazySingleton<EvoColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
    getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());
  });

  group('EvoTextField static constants', () {
    test('should have correct static constants', () {
      expect(EvoTextField.horizontalSpacing, equals(12.w));
      expect(EvoTextField.verticalSpacing, equals(12.w));

      expect(EvoTextField.iconBoxConstraints, isA<BoxConstraints>());
      expect(EvoTextField.iconBoxConstraints.minWidth, 0.0);
      expect(EvoTextField.iconBoxConstraints.maxWidth, double.infinity);
      expect(EvoTextField.iconBoxConstraints.minHeight, 0.0);
      expect(EvoTextField.iconBoxConstraints.maxHeight, double.infinity);

      expect(EvoTextField.borderRadius, equals(8.w));
      expect(EvoTextField.outerFocusedBorderPadding, equals(2.w));
      expect(EvoTextField.outerFocusedBorderWidth, equals(2.w));
      expect(EvoTextField.focusedBorderPadding,
          equals(EvoTextField.outerFocusedBorderPadding + EvoTextField.outerFocusedBorderWidth));
      expect(EvoTextField.focusedBorderPadding, equals(4.w));
    });
  });

  group('EvoTextField basic functionality', () {
    testWidgets('setup correctly with corresponding properties', (WidgetTester tester) async {
      const String initialValue = 'Initial Value';
      const String prefixText = 'Prefix:';
      const String suffixText = 'Suffix:';
      const String labelText = 'label';
      const TextInputType keyboardType = TextInputType.number;
      const int maxLines = 3;
      const int minLines = 2;
      const int maxLength = 10;

      final List<TextInputFormatter> inputFormatters = <TextInputFormatter>[
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(10),
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoTextField(
              initialValue: initialValue,
              prefixBuilder: (_) => Text(prefixText),
              suffixBuilder: (_) => Text(suffixText),
              label: labelText,
              isRequired: true,
              inputFormatters: inputFormatters,
              keyboardType: keyboardType,
              maxLines: maxLines,
              minLines: minLines,
              textInputAction: TextInputAction.done,
              maxLength: 10,
            ),
          ),
        ),
      );

      expect(find.text(initialValue), findsOneWidget);
      expect(find.text(prefixText), findsOneWidget);
      expect(find.text(suffixText), findsOneWidget);
      expect(
          find.textContaining('$labelText ${EvoStrings.textFieldLabelRequiredMark}',
              findRichText: true),
          findsOneWidget);

      final Finder textFieldFinder = find.byType(TextField);
      expect(textFieldFinder, findsOneWidget);

      final TextField textField = tester.widget(textFieldFinder);
      expect(textField.inputFormatters, inputFormatters);
      expect(textField.keyboardType, keyboardType);
      expect(textField.maxLines, maxLines);
      expect(textField.minLines, minLines);
      expect(textField.textInputAction, TextInputAction.done);
      expect(textField.maxLength, maxLength);
      expect(textField.selectionControls, isA<MaterialTextSelectionControls>());
      expect(textField.controller, isNotNull);
      expect(textField.controller?.text, initialValue);
    });

    testWidgets('setup correctly with custom TextEditingController', (WidgetTester tester) async {
      const String initialValue = 'ABC';
      final TextEditingController textEditingController = TextEditingController(text: initialValue);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoTextField(
              textEditingController: textEditingController,
              keyboardType: TextInputType.number,
            ),
          ),
        ),
      );

      expect(find.text(initialValue), findsOneWidget);

      final Finder textFieldFinder = find.byType(TextField);
      expect(textFieldFinder, findsOneWidget);

      final TextField textField = tester.widget(textFieldFinder);
      expect(textField.controller, textEditingController);
      expect(textField.controller?.text, textEditingController.text);
    });

    testWidgets('setup correctly with custom SelectionControls', (WidgetTester tester) async {
      const String initialValue = 'ABC';
      final TextSelectionControls selectionControls = MockTextSelectionControls();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoTextField(
              initialValue: initialValue,
              keyboardType: TextInputType.number,
              selectionControls: selectionControls,
            ),
          ),
        ),
      );

      expect(find.text(initialValue), findsOneWidget);

      final Finder textFieldFinder = find.byType(TextField);
      expect(textFieldFinder, findsOneWidget);

      final TextField textField = tester.widget(textFieldFinder);
      expect(textField.selectionControls, selectionControls);
    });

    testWidgets('calls onChanged callback', (WidgetTester tester) async {
      String? changedValue;
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: EvoTextField(
              onChanged: (String value) {
                changedValue = value;
              },
            ),
          ),
        ),
      );

      await tester.enterText(find.byType(TextField), 'New Value');
      expect(changedValue, 'New Value');
    });

    testWidgets('shows hint text', (WidgetTester tester) async {
      const String hintText = 'Enter text';
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoTextField(
              hintText: hintText,
            ),
          ),
        ),
      );

      expect(find.text(hintText), findsOneWidget);
    });
  });

  group('EvoTextField states', () {
    testWidgets('verify TextField with [Disable] state', (WidgetTester tester) async {
      final Color expectedBorderColor = evoColors.grayBorders.withOpacity(0.5);

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoTextField(
              isEnabled: false,
              hintText: 'Test Hint',
            ),
          ),
        ),
      );

      final Finder textFieldFinder = find.byType(TextField);
      final TextField textField = tester.widget(textFieldFinder);
      expect(textField.enabled, isFalse);
      expect(find.text('Test Hint'), findsOneWidget);

      expect(
          textField.decoration,
          isA<InputDecoration>().having(
            (InputDecoration decoration) => decoration.disabledBorder?.borderSide.color,
            'verify showing disabled border',
            expectedBorderColor,
          ));
    });

    testWidgets('verify TextField with [Error] state', (WidgetTester tester) async {
      const String errorMessage = 'This is an error message';
      final Color expectedErrorColor = evoColors.errorBase;

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoTextField(
              errMessage: errorMessage,
            ),
          ),
        ),
      );

      expect(find.text(errorMessage), findsOneWidget);

      final Finder errorMessageFinder = find.text(errorMessage);
      final Text errorMessageText = tester.widget(errorMessageFinder);
      expect(errorMessageText.style!.color, expectedErrorColor);

      final Finder textFieldFinder = find.byType(TextField);
      final TextField textField = tester.widget(textFieldFinder);

      expect(
          textField.decoration,
          isA<InputDecoration>().having(
            (InputDecoration decoration) => decoration.errorBorder?.borderSide.color,
            'verify showing border',
            expectedErrorColor,
          ));
    });

    testWidgets('verify TextField with [Focused] state', (WidgetTester tester) async {
      Finder findFocusedBorder(WidgetTester tester) {
        return find.byWidgetPredicate((Widget widget) =>
            widget is Container &&
            widget.foregroundDecoration.let((Decoration? decoration) {
              if (decoration is BoxDecoration) {
                final Border? border = decoration.border as Border?;
                if (border == null) return false;

                final Set<Color> colorSet = {
                  border.top.color,
                  border.bottom.color,
                  border.left.color,
                  border.right.color
                };

                return colorSet.length == 1 && colorSet.first == evoColors.secondaryBase;
              }
              return false;
            }));
      }

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoTextField(
              hintText: 'Test Hint',
            ),
          ),
        ),
      );

      final Finder textFieldFinder = find.byType(TextField);
      expect(textFieldFinder, findsOneWidget);
      expect(findFocusedBorder(tester), findsNothing);

      await tester.tap(textFieldFinder);
      await tester.pumpAndSettle();

      final FocusNode focusNode = tester.widget<EditableText>(find.byType(EditableText)).focusNode;
      expect(focusNode.hasFocus, isTrue);
      expect(findFocusedBorder(tester), findsOneWidget);
    });

    testWidgets('verify TextField in [Normal] state', (WidgetTester tester) async {
      final Color expectedBorderColor = evoColors.grayBorders;
      final Color expectedTextColor = evoColors.textActive;

      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: EvoTextField(
              hintText: 'Test Hint',
            ),
          ),
        ),
      );

      final Finder textFieldFinder = find.byType(TextField);
      expect(textFieldFinder, findsOneWidget);

      final FocusNode focusNode = tester.widget<EditableText>(find.byType(EditableText)).focusNode;
      expect(focusNode.hasFocus, isFalse);

      final TextField textField = tester.widget(textFieldFinder);
      expect(textField.style!.color, expectedTextColor);

      expect(
          textField.decoration,
          isA<InputDecoration>()
              .having(
            (InputDecoration decoration) => decoration.enabledBorder?.borderSide.color,
            'verify showing enabled border',
            expectedBorderColor,
          )
              .having(
                  (InputDecoration decoration) => (
                        decoration.isDense,
                        (decoration.contentPadding as EdgeInsets).vertical,
                      ),
                  'verify decoration layout',
                  (
                true,
                EvoTextField.verticalSpacing * 2,
              )));
    });

    testWidgets('should update state on didUpdateWidget', (WidgetTester tester) async {
      late StateSetter setState;
      bool isEnabled = true;
      String errMessage = '';

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StatefulBuilder(builder: (_, StateSetter setter) {
              setState = setter;
              return EvoTextField(
                initialValue: 'text',
                isEnabled: isEnabled,
                errMessage: errMessage,
              );
            }),
          ),
        ),
      );

      final ValueListenableBuilder<Set<WidgetState>> listenableBuilder =
          tester.widget(find.byType(ValueListenableBuilder<Set<WidgetState>>));
      Set<WidgetState> values() => listenableBuilder.valueListenable.value;
      expect(values(), isEmpty);

      isEnabled = false;
      setState(() {});
      await tester.pumpAndSettle();
      expect(values(), contains(WidgetState.disabled));

      isEnabled = true;
      setState(() {});
      await tester.pumpAndSettle();
      expect(values(), isNot(contains(WidgetState.disabled)));

      errMessage = 'error';
      setState(() {});
      await tester.pumpAndSettle();
      expect(values(), contains(WidgetState.error));

      errMessage = '';
      setState(() {});
      await tester.pumpAndSettle();
      expect(values(), isNot(contains(WidgetState.error)));
    });
  });

  group('EvoTextField with Prefix', () {
    const String prefixText = 'prefix-text';
    final GlobalKey key = GlobalKey();

    Future<void> buildWidget(WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: EvoTextField(
            hintText: 'Test Hint',
            prefixBuilder: (EdgeInsets padding) {
              return Padding(padding: padding, child: Text(prefixText, key: key));
            },
          ),
        ),
      ));
    }

    testWidgets('should return SizeBox prefix when prefixBuilder is null',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: EvoTextField()),
      ));

      final Finder textFieldFinder = find.byType(TextField);
      final TextField textField = tester.widget<TextField>(textFieldFinder);

      expect(
        textField.decoration?.prefixIcon,
        isA<SizedBox>().having(
          (SizedBox widget) => widget.width,
          'verify SizedBox width',
          EvoTextField.horizontalSpacing,
        ),
      );
    });

    testWidgets('should display correctly when prefixBuilder is set', (WidgetTester tester) async {
      await buildWidget(tester);

      final Finder prefixTextFinder = find.text(prefixText);
      expect(prefixTextFinder, findsOneWidget);

      final BuildContext context = key.currentContext!;
      expect(
        find.byWidgetPredicate((Widget widget) =>
            widget is Padding &&
            (widget.padding as EdgeInsets).left == EvoTextField.horizontalSpacing),
        findsOneWidget,
      );

      expect(
          context.dependOnInheritedWidgetOfExactType<IconTheme>()?.data.color, evoColors.grayBase);
    });

    testWidgets('should change text color when focused', (WidgetTester tester) async {
      await buildWidget(tester);

      final BuildContext context = key.currentContext!;
      expect(DefaultTextStyle.of(context).style.color, evoColors.grayBase);

      final Finder textField = find.byType(TextField);
      await tester.tap(textField);
      await tester.pumpAndSettle();

      expect(DefaultTextStyle.of(context).style.color, evoColors.textActive);
    });
  });

  group('EvoTextField with Suffix', () {
    const String suffixText = 'suffix-text';
    final GlobalKey key = GlobalKey();

    Future<void> buildWidget(WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: EvoTextField(
            hintText: 'Test Hint',
            suffixBuilder: (EdgeInsets padding) {
              return Padding(padding: padding, child: Text(suffixText, key: key));
            },
          ),
        ),
      ));
    }

    testWidgets('should return SizeBox suffix when suffixBuilder is null',
        (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(body: EvoTextField()),
      ));

      final Finder textFieldFinder = find.byType(TextField);
      final TextField textField = tester.widget<TextField>(textFieldFinder);

      expect(
        textField.decoration?.suffixIcon,
        isA<SizedBox>().having(
          (SizedBox widget) => widget.width,
          'verify SizedBox width',
          EvoTextField.horizontalSpacing,
        ),
      );
    });

    testWidgets('should display correctly when suffixBuilder is set', (WidgetTester tester) async {
      await buildWidget(tester);

      final Finder suffixTextFinder = find.text(suffixText);
      expect(suffixTextFinder, findsOneWidget);

      final BuildContext context = key.currentContext!;
      expect(
        find.byWidgetPredicate((Widget widget) =>
            widget is Padding &&
            (widget.padding as EdgeInsets).right == EvoTextField.horizontalSpacing),
        findsOneWidget,
      );

      expect(
          context.dependOnInheritedWidgetOfExactType<IconTheme>()?.data.color, evoColors.grayBase);
    });

    testWidgets('should change text color when focused', (WidgetTester tester) async {
      await buildWidget(tester);

      final BuildContext context = key.currentContext!;
      expect(DefaultTextStyle.of(context).style.color, evoColors.grayBase);

      final Finder textField = find.byType(TextField);
      await tester.tap(textField);
      await tester.pumpAndSettle();

      expect(DefaultTextStyle.of(context).style.color, evoColors.textActive);
    });
  });
}
