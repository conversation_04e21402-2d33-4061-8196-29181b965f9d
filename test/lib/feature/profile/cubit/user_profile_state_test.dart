import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/feature/profile/cubit/user_profile_cubit.dart';
import 'package:flutter_common_package/ui_model/error_ui_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Test UserProfileState', () {
    test('should be subtype of UserProfileState', () {
      expect(UserProfileInitial(), isA<UserProfileState>());
      expect(UserProfileLoading(), isA<UserProfileState>());
      expect(UserProfileLoadedSuccess(), isA<UserProfileState>());
      expect(UserProfileLoadedFail(), isA<UserProfileState>());
    });

    test('UserProfileLoadedSuccess should hold user information', () {
      const UserInformationEntity user = UserInformationEntity();
      final UserProfileLoadedSuccess state = UserProfileLoadedSuccess(user: user);
      expect(state.user, equals(user));
    });

    test('UserProfileLoadedFail should hold error information', () {
      final ErrorUIModel error = ErrorUIModel();
      final UserProfileLoadedFail state = UserProfileLoadedFail(error: error);
      expect(state.error, equals(error));
    });
  });
}
