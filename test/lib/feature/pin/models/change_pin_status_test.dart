import 'package:evoapp/feature/pin/models/change_pin_status.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify initial value is subtype of ValueNotifier', () {
    final ChangePinStatusNotifier notifier = ChangePinStatusNotifier();
    expect(notifier, isA<ValueNotifier<ChangePinStatus>>());
    expect(notifier.value, ChangePinStatus.available);
  });
}
