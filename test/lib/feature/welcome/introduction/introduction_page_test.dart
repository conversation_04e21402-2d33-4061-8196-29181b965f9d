import 'package:evoapp/feature/welcome/introduction/introduction_page.dart';
import 'package:evoapp/resources/ui_strings.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../base/evo_page_state_base_test_config.dart';

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  testWidgets('IntroductionPage displays content correctly', (WidgetTester tester) async {
    // Mock data
    const String testImage = 'test_image.png';
    const String testDescription = 'Test description';
    bool skipPressed = false;

    // Build the widget
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: IntroductionPage(
            image: testImage,
            description: testDescription,
            onSkip: () => skipPressed = true,
          ),
        ),
      ),
    );

    // Verify the description is displayed
    expect(find.text(testDescription), findsOneWidget);

    // Verify the image is displayed
    expect(find.text('mock_image'), findsOneWidget);

    // Verify the button is displayed
    expect(find.text(EvoStrings.introductionNext), findsOneWidget);

    // Tap the button and verify callback is called
    await tester.tap(find.text(EvoStrings.introductionNext));
    await tester.pump();
    expect(skipPressed, true);
  });

  testWidgets('IntroductionPage with null onSkip does not show button',
      (WidgetTester tester) async {
    // Build the widget with null onSkip
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: IntroductionPage(
            image: 'test_image.png',
            description: 'Test description',
            onSkip: null,
          ),
        ),
      ),
    );

    // Verify the button is not displayed
    expect(find.text(EvoStrings.introductionNext), findsNothing);
  });
}
