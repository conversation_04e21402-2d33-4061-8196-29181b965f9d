import 'package:evoapp/feature/welcome/introduction/widget/dot_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../../base/evo_page_state_base_test_config.dart';

void main() {
  setUpAll(() {
    initConfigEvoPageStateBase();
  });

  setUp(() {
    setUpMockConfigEvoPageStateBase();
  });

  testWidgets('DotIndicator displays correct number of dots', (WidgetTester tester) async {
    // Test with 3 dots, current index 0
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: Center(
            child: DotIndicator(count: 3, currentIndex: 0),
          ),
        ),
      ),
    );

    // Find the animated containers (dots)
    expect(find.byType(AnimatedContainer), findsNWidgets(3));
  });

  testWidgets('DotIndicator has correct active dots based on currentIndex',
      (WidgetTester tester) async {
    // Test with 4 dots, current index 2
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: Center(
            child: DotIndicator(count: 4, currentIndex: 2),
          ),
        ),
      ),
    );

    // Find all animated containers (dots)
    final List<AnimatedContainer> dots =
        tester.widgetList<AnimatedContainer>(find.byType(AnimatedContainer)).toList();

    // Verify we have 4 dots
    expect(dots.length, 4);

    // Verify the decoration of each dot (first 3 should be active, last one inactive)
    for (int i = 0; i < dots.length; i++) {
      final BoxDecoration decoration = dots[i].decoration as BoxDecoration;
      final Color? color = decoration.color;

      // Dots 0, 1, 2 should be active (full white), dot 3 should be inactive (opacity 0.2)
      if (i <= 2) {
        expect(color?.opacity, 1.0); // Active dots have full opacity
      } else {
        expect(color?.opacity, 0.2); // Inactive dots have 0.2 opacity
      }
    }
  });

  testWidgets('DotIndicator updates when currentIndex changes', (WidgetTester tester) async {
    // Create a stateful widget to control the current index
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: Center(
            child: _TestDotIndicatorController(),
          ),
        ),
      ),
    );

    // Initially, only the first dot should be active
    List<AnimatedContainer> dots =
        tester.widgetList<AnimatedContainer>(find.byType(AnimatedContainer)).toList();

    expect(dots.length, 3);
    expect((dots[0].decoration as BoxDecoration).color?.opacity, 1.0); // Active
    expect((dots[1].decoration as BoxDecoration).color?.opacity, 0.2); // Inactive
    expect((dots[2].decoration as BoxDecoration).color?.opacity, 0.2); // Inactive

    // Tap the button to increase the index to 1
    await tester.tap(find.text('Next'));
    await tester.pumpAndSettle();

    // Now the first two dots should be active
    dots = tester.widgetList<AnimatedContainer>(find.byType(AnimatedContainer)).toList();

    expect((dots[0].decoration as BoxDecoration).color?.opacity, 1.0); // Active
    expect((dots[1].decoration as BoxDecoration).color?.opacity, 1.0); // Active
    expect((dots[2].decoration as BoxDecoration).color?.opacity, 0.2); // Inactive
  });
}

// Test helper widget to control the current index
class _TestDotIndicatorController extends StatefulWidget {
  const _TestDotIndicatorController();

  @override
  State<_TestDotIndicatorController> createState() => _TestDotIndicatorControllerState();
}

class _TestDotIndicatorControllerState extends State<_TestDotIndicatorController> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        DotIndicator(count: 3, currentIndex: _currentIndex),
        ElevatedButton(
          onPressed: () {
            setState(() {
              if (_currentIndex < 2) {
                _currentIndex++;
              } else {
                _currentIndex = 0;
              }
            });
          },
          child: const Text('Next'),
        ),
      ],
    );
  }
}
