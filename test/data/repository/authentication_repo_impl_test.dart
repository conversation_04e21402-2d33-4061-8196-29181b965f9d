import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/constants.dart';
import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/repository/authentication_repo_impl.dart';
import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/data/request/login_new_device_request.dart';
import 'package:evoapp/data/request/reset_pin_request.dart';
import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:evoapp/feature/pin/mock/mock_pin_use_case.dart';
import 'package:evoapp/feature/verify_otp/mock/mock_verify_otp_use_case.dart';
import 'package:evoapp/model/user_token.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/common_request_option.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';
import '../../util/test_util.dart';

class MockCommonHttpClient extends Mock implements CommonHttpClient {}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockActivateAccountRequest extends Mock implements ActivateAccountRequest {}

void main() {
  test('Test SignInRequestParam', () {
    expect(SignInRequestParam.type.value, 'type');
    expect(SignInRequestParam.phoneNumber.value, 'phone_number');
    expect(SignInRequestParam.otp.value, 'otp');
    expect(SignInRequestParam.pin.value, 'pin');
    expect(SignInRequestParam.refreshToken.value, 'refresh_token');
    expect(SignInRequestParam.biometricToken.value, 'biometric_token');
  });

  test('Test TypeLogin', () {
    expect(TypeLogin.otp.value, 'otp');
    expect(TypeLogin.verifyOTP.value, 'verify_otp');
    expect(TypeLogin.verifyPin.value, 'verify_pin');
    expect(TypeLogin.refreshToken.value, 'refresh_token');
    expect(TypeLogin.biometricToken.value, 'biometric_token');
  });

  test('Test ResetPinType', () {
    expect(ResetPinType.none.value, 'none');
    expect(ResetPinType.verifyOTP.value, 'verify_otp');
    expect(ResetPinType.changePin.value, 'change_pin');
    expect(ResetPinType.faceAuth.value, 'face_auth');
  });

  group('Test AuthenticationRepo', () {
    late AuthenticationRepoImpl authenticationRepo;
    final CommonHttpClient evoHttpClient = MockCommonHttpClient();
    late EvoLocalStorageHelper evoLocalStorageHelper;
    final CommonHttpClient nonAuthenticationEvoHttpClient = MockCommonHttpClient();
    final AppState appState = AppState();
    late ActivateAccountRequest mockRequest;

    const String fakeAccessToken = 'access_token';
    const String fakeSessionToken = 'session_token';
    const String fakeDeviceToken = 'device_token';
    const String fakeRefreshToken = 'refresh_token';
    const String fakeBiometricToken = 'biometric_token';
    const int fakeUserId = 0;
    const String fakeAuthNotiToken = 'auth_noti_token';

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      mockLoggingRepo();

      registerFallbackValue(CommonHttpClient.SUCCESS);
    });

    setUp(() {
      appState.userToken = null;

      evoLocalStorageHelper = MockEvoLocalStorageHelper();
      authenticationRepo = AuthenticationRepoImpl(
        evoHttpClient: evoHttpClient,
        evoLocalStorageHelper: evoLocalStorageHelper,
        nonAuthenticationEvoHttpClient: nonAuthenticationEvoHttpClient,
        appState: appState,
      );

      mockRequest = MockActivateAccountRequest();

      when(() => evoLocalStorageHelper.setDeviceToken(any())).thenAnswer((_) async {});
      when(() => evoLocalStorageHelper.setBiometricToken(any())).thenAnswer((_) async {});
      when(() => evoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => null);
      when(() => mockRequest.toJson()).thenAnswer((_) => <String, dynamic>{});
    });

    void mockCommonUtilFunction() {
      if (getIt.isRegistered<CommonUtilFunction>()) {
        getIt.unregister<CommonUtilFunction>();
      }
      getIt.registerLazySingleton<CommonUtilFunction>(() => MockCommonUtilFunction());
      when(() => commonUtilFunction.handleSignInSucceedData(
            accessToken: any(named: 'accessToken'),
            userId: any(named: 'userId'),
            notificationAuthKey: any(named: 'notificationAuthKey'),
          )).thenAnswer((_) async {});
    }

    void verifySignInDataHandling() {
      expect(appState.userToken.accessToken, fakeAccessToken);
      expect(appState.userToken.refreshToken, fakeRefreshToken);
      verify(() => evoLocalStorageHelper.setDeviceToken(fakeDeviceToken)).called(1);
      verify(() => evoLocalStorageHelper.setBiometricToken(fakeBiometricToken)).called(1);
      verify(() => commonUtilFunction.handleSignInSucceedData(
            accessToken: fakeAccessToken,
            userId: 0,
            notificationAuthKey: 'auth_noti_token',
          )).called(1);
    }

    void verifyNotHandleSignInData() {
      expect(appState.userToken.accessToken, isNull);
      expect(appState.userToken.refreshToken, isNull);
      verifyNever(() => evoLocalStorageHelper.setDeviceToken(any()));
      verifyNever(() => evoLocalStorageHelper.setBiometricToken(any()));
      verifyNever(() => commonUtilFunction.handleSignInSucceedData(
            accessToken: any(named: 'accessToken'),
            userId: any(named: 'userId'),
            notificationAuthKey: any(named: 'notificationAuthKey'),
          ));
    }

    test('Test constants', () {
      expect(AuthenticationRepoImpl.signInUrl, 'user/signin');
    });

    group('Test signIn', () {
      const TypeLogin type = TypeLogin.otp;
      const String fakePhoneNumber = 'fakePhoneNumber';
      const String fakeOtp = 'fakeOtp';
      const String fakePin = 'fakePin';
      const String fakeBiometricToken = 'fakeBiometricToken';
      const MockConfig fakeMockConfig = MockConfig(
        enable: true,
        fileName: 'signin_success.json',
      );

      setUpAll(() {
        mockCommonUtilFunction();
      });

      tearDownAll(() {
        getIt.unregister<CommonUtilFunction>();
      });

      Future<void> mockSignInSucceed() async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('signin_success.json');

        final BaseResponse mockResponse = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: responseData,
        );

        when(
          () => evoHttpClient.post(
            AuthenticationRepoImpl.signInUrl,
            requestOption: any(named: 'requestOption'),
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return mockResponse;
        });

        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenAnswer((_) {
          return SignInEntity.fromBaseResponse(mockResponse);
        });
      }

      Future<void> mockSignInFailed() async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock('signin_invalid_parameters.json');

        final BaseResponse mockResponse = BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: responseData,
        );

        when(
          () => evoHttpClient.post(
            AuthenticationRepoImpl.signInUrl,
            requestOption: any(named: 'requestOption'),
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return mockResponse;
        });

        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenAnswer((_) {
          return SignInEntity.fromBaseResponse(mockResponse);
        });
      }

      TypeMatcher<CommonRequestOption> verifyHeaders({
        String? deviceTokenMatcher,
        String? sessionTokenMatcher,
      }) {
        return isA<CommonRequestOption>().having(
          (CommonRequestOption p0) => p0.headers,
          'verify headers',
          isA<Map<String, dynamic>>()
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.deviceToken],
                'verify deviceToken',
                deviceTokenMatcher,
              )
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.sessionToken],
                'verify sessionToken',
                sessionTokenMatcher,
              ),
        );
      }

      TypeMatcher<Map<String, dynamic>> verifyData({
        required String type,
        String? phoneNumber,
        String? otp,
        String? pin,
        String? refreshToken,
        String? biometricToken,
      }) {
        return isA<Map<String, dynamic>>()
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.type.value],
              'verify type',
              type,
            )
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.phoneNumber.value],
              'verify phoneNumber',
              phoneNumber,
            )
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.otp.value],
              'verify otp',
              otp,
            )
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.pin.value],
              'verify pin',
              pin,
            )
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.refreshToken.value],
              'verify refreshToken',
              refreshToken,
            )
            .having(
              (Map<String, dynamic> p0) => p0[SignInRequestParam.biometricToken.value],
              'verify biometricToken',
              biometricToken,
            );
      }

      /// Test signIn with only required params
      test('Call signIn succeed with only required params', () async {
        mockSignInSucceed();
        final SignInEntity signInEntity = await authenticationRepo.signIn(type);
        expect(signInEntity.statusCode, CommonHttpClient.SUCCESS);
        expect(
          verify(() => evoHttpClient.post(
                AuthenticationRepoImpl.signInUrl,
                requestOption: captureAny(named: 'requestOption'),
                data: captureAny(named: 'data'),
                mockConfig: captureAny(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            verifyHeaders(),
            verifyData(type: TypeLogin.otp.value),
            isNull,
          ],
        );

        verify(() => evoLocalStorageHelper.getDeviceToken()).called(1);
        verifySignInDataHandling();
      });

      test('Call signIn failed with only required params', () async {
        mockSignInFailed();
        final SignInEntity signInEntity = await authenticationRepo.signIn(type);
        expect(signInEntity.statusCode, CommonHttpClient.BAD_REQUEST);
        expect(
          verify(() => evoHttpClient.post(
                AuthenticationRepoImpl.signInUrl,
                requestOption: captureAny(named: 'requestOption'),
                data: captureAny(named: 'data'),
                mockConfig: captureAny(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            verifyHeaders(),
            verifyData(type: TypeLogin.otp.value),
            isNull,
          ],
        );

        verify(() => evoLocalStorageHelper.getDeviceToken()).called(1);
        verifyNotHandleSignInData();
      });

      /// Test signIn with all params
      test('Call signIn succeed with all params', () async {
        mockSignInSucceed();
        final SignInEntity signInEntity = await authenticationRepo.signIn(
          type,
          phoneNumber: fakePhoneNumber,
          otp: fakeOtp,
          pin: fakePin,
          refreshToken: fakeRefreshToken,
          biometricToken: fakeBiometricToken,
          sessionToken: fakeSessionToken,
          mockConfig: fakeMockConfig,
        );
        expect(signInEntity.statusCode, CommonHttpClient.SUCCESS);
        expect(
          verify(() => evoHttpClient.post(
                AuthenticationRepoImpl.signInUrl,
                requestOption: captureAny(named: 'requestOption'),
                data: captureAny(named: 'data'),
                mockConfig: captureAny(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            verifyHeaders(sessionTokenMatcher: fakeSessionToken),
            verifyData(
              type: TypeLogin.otp.value,
              phoneNumber: fakePhoneNumber,
              otp: fakeOtp,
              pin: fakePin,
              refreshToken: fakeRefreshToken,
              biometricToken: fakeBiometricToken,
            ),
            fakeMockConfig,
          ],
        );

        verify(() => evoLocalStorageHelper.getDeviceToken()).called(1);
        verifySignInDataHandling();
      });

      /// Test signIn with deviceToken
      test('Call signIn succeed with deviceToken', () async {
        mockSignInSucceed();
        when(() => evoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => fakeDeviceToken);
        final SignInEntity signInEntity = await authenticationRepo.signIn(type);
        expect(signInEntity.statusCode, CommonHttpClient.SUCCESS);
        expect(
          verify(() => evoHttpClient.post(
                AuthenticationRepoImpl.signInUrl,
                requestOption: captureAny(named: 'requestOption'),
                data: captureAny(named: 'data'),
                mockConfig: captureAny(named: 'mockConfig'),
              )).captured,
          <dynamic>[
            verifyHeaders(deviceTokenMatcher: fakeDeviceToken),
            verifyData(type: TypeLogin.otp.value),
            isNull,
          ],
        );

        verify(() => evoLocalStorageHelper.getDeviceToken()).called(1);
        verifySignInDataHandling();
      });

      test('should return SignInEntity.unserializable() when serialize() returns null', () async {
        when(() => evoHttpClient.post(
              AuthenticationRepoImpl.signInUrl,
              requestOption: any(named: 'requestOption'),
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));
        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenReturn(null);

        final SignInEntity entity = await authenticationRepo.signIn(type);

        expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      });

      test('should return SignInEntity with correct data', () async {
        mockSignInSucceed();
        final SignInEntity entity1 = await authenticationRepo.signIn(type);

        final List<dynamic> captured = verify(() => commonUtilFunction.serialize(
              captureAny<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).captured;

        final SerializeFunction<SignInEntity> serializeFunction =
            captured.first as SerializeFunction<SignInEntity>;
        final SignInEntity entity2 = serializeFunction();

        expect(entity1.refreshToken, entity2.refreshToken);
        expect(entity1.accessToken, entity2.accessToken);
        expect(entity1.authNotiToken, entity2.authNotiToken);
        expect(entity1.biometricToken, entity2.biometricToken);
        expect(entity1.deviceToken, entity2.deviceToken);
        expect(entity1.statusCode, entity2.statusCode);
        expect(entity1.userId, entity2.userId);
        expect(entity1.otpResendSecs, entity2.otpResendSecs);
        expect(entity1.otpValiditySecs, entity2.otpValiditySecs);
        expect(entity1.sessionToken, entity2.sessionToken);
        expect(entity1.ekycClientSettings, entity2.ekycClientSettings);
      });
    });

    group('Test refreshToken', () {
      setUpAll(() {
        mockCommonUtilFunction();
      });

      tearDownAll(() {
        getIt.unregister<CommonUtilFunction>();
      });

      Future<void> stubRefreshTokenRequest(
          {required String jsonFileName, required int statusCode}) async {
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(jsonFileName);

        final BaseResponse mockResponse = BaseResponse(
          statusCode: statusCode,
          response: responseData,
        );

        when(
          () => nonAuthenticationEvoHttpClient.post(
            AuthenticationRepoImpl.signInUrl,
            requestOption: any(named: 'requestOption'),
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          ),
        ).thenAnswer((_) async {
          return mockResponse;
        });

        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenAnswer((_) {
          return SignInEntity.fromBaseResponse(mockResponse);
        });
      }

      test('Call refreshToken succeed with given params', () async {
        /// Arrange
        await stubRefreshTokenRequest(
          jsonFileName: 'refresh_token_success.json',
          statusCode: CommonHttpClient.SUCCESS,
        );

        when(() => evoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => fakeDeviceToken);

        /// Action
        final SignInEntity signInEntity = await authenticationRepo.refreshToken(fakeRefreshToken);
        expect(signInEntity.statusCode, CommonHttpClient.SUCCESS);

        //// capture parameters of the call
        final List<dynamic> capturedData = verify(() => nonAuthenticationEvoHttpClient.post(
              AuthenticationRepoImpl.signInUrl,
              requestOption: captureAny(named: 'requestOption'),
              data: captureAny(named: 'data'),
              mockConfig: captureAny(named: 'mockConfig'),
            )).captured;

        /// Assert the requestOption
        final CommonRequestOption requestOption = capturedData[0] as CommonRequestOption;
        expect(requestOption.headers?[HeaderKey.deviceToken], fakeDeviceToken);

        /// Assert the request Data
        final Map<String, dynamic>? data = capturedData[1] as Map<String, dynamic>?;
        expect(data?['type'], TypeLogin.refreshToken.value);
        expect(data?['refresh_token'], fakeRefreshToken);

        /// Assert the mockConfig
        final MockConfig? mockConfig = capturedData[2] as MockConfig?;
        expect(mockConfig, isNull);

        /// Assert the mockConfig
        verify(() => evoLocalStorageHelper.getDeviceToken()).called(1);

        /// Assert AccessToken, RefreshToken is stored in memory
        expect(appState.userToken.accessToken, signInEntity.accessToken);
        expect(appState.userToken.refreshToken, signInEntity.refreshToken);

        /// Assert DeviceToken is stored in secure storage
        verify(() => evoLocalStorageHelper.setDeviceToken('device_token')).called(1);

        /// Assert handleSignInSucceedData is called with correct parameters
        verify(() => commonUtilFunction.handleSignInSucceedData(
              accessToken: signInEntity.accessToken,
              userId: signInEntity.userId,
              notificationAuthKey: signInEntity.authNotiToken,
            )).called(1);
      });

      test('Call refreshToken failed', () async {
        /// Arrange
        await stubRefreshTokenRequest(
          jsonFileName: 'refresh_token_failure.json',
          statusCode: CommonHttpClient.UNKNOWN_ERRORS,
        );

        /// Action
        final SignInEntity signInEntity = await authenticationRepo.refreshToken(fakeRefreshToken);
        expect(signInEntity.statusCode, CommonHttpClient.UNKNOWN_ERRORS);

        /// Assert AccessToken, RefreshToken is not stored in memory
        expect(appState.userToken.accessToken, isNull);
        expect(appState.userToken.refreshToken, isNull);

        /// Assert DeviceToken is not stored in secure storage
        verifyNever(() => evoLocalStorageHelper.setDeviceToken('device_token'));

        /// Assert handleSignInSucceedData is not called
        verifyNever(
          () => commonUtilFunction.handleSignInSucceedData(
            accessToken: any(named: 'accessToken'),
            userId: any(named: 'userId'),
            notificationAuthKey: any(named: 'notificationAuthKey'),
          ),
        );
      });

      test('Call refreshToken with device_token = NULL', () async {
        /// Arrange
        await stubRefreshTokenRequest(
          jsonFileName: 'refresh_token_success.json',
          statusCode: CommonHttpClient.SUCCESS,
        );

        /// Action
        final SignInEntity signInEntity = await authenticationRepo.refreshToken(fakeRefreshToken);
        expect(signInEntity.statusCode, CommonHttpClient.SUCCESS);

        //// capture parameters of the call
        final List<dynamic> capturedData = verify(() => nonAuthenticationEvoHttpClient.post(
              AuthenticationRepoImpl.signInUrl,
              requestOption: captureAny(named: 'requestOption'),
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        /// Assert the requestOption
        final CommonRequestOption requestOption = capturedData[0] as CommonRequestOption;
        expect(requestOption.headers?[HeaderKey.deviceToken], isNull);
      });

      test('should return SignInEntity.unserializable() when serialize() return null', () async {
        when(() => nonAuthenticationEvoHttpClient.post(
              AuthenticationRepoImpl.signInUrl,
              requestOption: any(named: 'requestOption'),
              data: any(named: 'data'),
            )).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));
        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenReturn(null);

        final SignInEntity entity = await authenticationRepo.refreshToken('token');

        expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      });

      test('should return SignInEntity with correct data', () async {
        await stubRefreshTokenRequest(
          jsonFileName: 'refresh_token_success.json',
          statusCode: CommonHttpClient.SUCCESS,
        );
        final SignInEntity entity1 = await authenticationRepo.refreshToken('token');

        final List<dynamic> captured = verify(() => commonUtilFunction.serialize(
              captureAny<SerializeFunction<SignInEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).captured;

        final SerializeFunction<SignInEntity> serializeFunction =
            captured.first as SerializeFunction<SignInEntity>;
        final SignInEntity entity2 = serializeFunction();

        expect(entity1.refreshToken, entity2.refreshToken);
        expect(entity1.accessToken, entity2.accessToken);
        expect(entity1.authNotiToken, entity2.authNotiToken);
        expect(entity1.biometricToken, entity2.biometricToken);
        expect(entity1.deviceToken, entity2.deviceToken);
        expect(entity1.statusCode, entity2.statusCode);
        expect(entity1.userId, entity2.userId);
        expect(entity1.otpResendSecs, entity2.otpResendSecs);
        expect(entity1.otpValiditySecs, entity2.otpValiditySecs);
        expect(entity1.sessionToken, entity2.sessionToken);
        expect(entity1.ekycClientSettings, entity2.ekycClientSettings);
      });
    });

    group('Test createSignInHeaders', () {
      test('Give sessionToken null, deviceToken null', () {
        final Future<Map<String, dynamic>> headers = authenticationRepo.createSignInHeaders(null);
        expect(
          headers,
          completion(
            isA<Map<String, dynamic>>()
                .having(
                  (Map<String, dynamic> p0) => p0[HeaderKey.sessionToken],
                  'verify sessionToken',
                  isNull,
                )
                .having(
                  (Map<String, dynamic> p0) => p0[HeaderKey.deviceToken],
                  'verify deviceToken',
                  isNull,
                ),
          ),
        );
      });

      test('Give sessionToken NOT null, deviceToken null', () async {
        final Map<String, dynamic> headers =
            await authenticationRepo.createSignInHeaders(fakeSessionToken);
        expect(
          headers,
          isA<Map<String, dynamic>>()
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.sessionToken],
                'verify sessionToken',
                fakeSessionToken,
              )
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.deviceToken],
                'verify deviceToken',
                isNull,
              ),
        );
      });

      test('Give sessionToken null, deviceToken NOT null', () async {
        when(() => evoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => fakeDeviceToken);
        final Map<String, dynamic> headers = await authenticationRepo.createSignInHeaders(null);
        expect(
          headers,
          isA<Map<String, dynamic>>()
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.sessionToken],
                'verify sessionToken',
                isNull,
              )
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.deviceToken],
                'verify deviceToken',
                fakeDeviceToken,
              ),
        );
      });

      test('Give sessionToken NOT null, deviceToken NOT null', () async {
        when(() => evoLocalStorageHelper.getDeviceToken()).thenAnswer((_) async => fakeDeviceToken);
        final Map<String, dynamic> headers =
            await authenticationRepo.createSignInHeaders(fakeSessionToken);
        expect(
          headers,
          isA<Map<String, dynamic>>()
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.sessionToken],
                'verify sessionToken',
                fakeSessionToken,
              )
              .having(
                (Map<String, dynamic> p0) => p0[HeaderKey.deviceToken],
                'verify deviceToken',
                fakeDeviceToken,
              ),
        );
      });
    });

    group('verify resetPin', () {
      const String sessionToken = 'mock-session-token';
      final ResetPinRequest request = InitializeResetPinRequest(phoneNumber: 'mock-phone-number');

      setUpAll(() {
        getIt.registerSingleton<CommonUtilFunction>(CommonUtilFunction());
      });

      tearDownAll(() {
        getIt.unregister<CommonUtilFunction>();
      });

      setUp(() {
        when(() => evoHttpClient.patch(
              AuthenticationRepoImpl.resetPinUrl,
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
              requestOption: any(named: 'requestOption'),
            )).thenAnswer((_) async {
          final Map<String, dynamic> responseData =
              await TestUtil.getResponseMock(getMockPinFileNameByCase(
            MockPinUseCase.getResetPinInitializeSuccess,
          ));

          return BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          );
        });
      });

      void verifyHttpCalled({required ResetPinRequest request}) {
        final List<dynamic> captures = verify(() => evoHttpClient.patch(
              AuthenticationRepoImpl.resetPinUrl,
              data: captureAny(named: 'data'),
              requestOption: captureAny(named: 'requestOption'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        expect(captures[0], equals(request.toJson()));

        if (request.sessionToken != null) {
          expect(
              captures[1],
              isA<CommonRequestOption>().having(
                  (CommonRequestOption reqOptions) => reqOptions.headers,
                  'verify headers', <String, String>{
                HeaderKey.sessionToken: sessionToken,
              }));
        }
      }

      group('verify if request is InitializeResetPinRequest', () {
        test('mock is true and get response success', () async {
          final ResetPinEntity entity = await authenticationRepo.resetPin(
            request: request,
            mockConfig: MockConfig(
                enable: true,
                fileName: getMockPinFileNameByCase(
                  MockPinUseCase.getResetPinInitializeSuccess,
                )),
          );

          expect(entity.statusCode, CommonHttpClient.SUCCESS);
          verifyHttpCalled(request: request);
        });

        test('mock is false and get response success', () async {
          final ResetPinEntity entity = await authenticationRepo.resetPin(
            request: request,
          );

          expect(entity.statusCode, CommonHttpClient.SUCCESS);
          verifyHttpCalled(request: request);
        });

        test('mock is false and get response failure', () async {
          when(() => evoHttpClient.patch(
                AuthenticationRepoImpl.resetPinUrl,
                data: any(named: 'data'),
                mockConfig: any(named: 'mockConfig'),
                requestOption: any(named: 'requestOption'),
              )).thenAnswer((_) async {
            return BaseResponse(
              statusCode: CommonHttpClient.BAD_REQUEST,
              response: <String, dynamic>{},
            );
          });

          final ResetPinEntity entity = await authenticationRepo.resetPin(
            request: request,
          );

          expect(entity.statusCode, CommonHttpClient.BAD_REQUEST);
          verifyHttpCalled(request: request);
        });
      });

      group('verify if request is ResetPinVerifyOTPRequest', () {
        test('should have session token when request is ResetPinVerifyOTPRequest', () async {
          final ResetPinRequest request =
              ResetPinVerifyOTPRequest(otp: 'otp', sessionToken: sessionToken);
          await authenticationRepo.resetPin(
              request: request,
              mockConfig: MockConfig(
                  enable: true,
                  fileName: getMockPinFileNameByCase(
                    MockPinUseCase.getResetPinInitializeSuccess,
                  )));
          verifyHttpCalled(request: request);
        });
      });

      test('should return ResetPinEntity.unSerializable() when data format is invalid', () async {
        when(() => evoHttpClient.patch(
              AuthenticationRepoImpl.resetPinUrl,
              requestOption: any(named: 'requestOption'),
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => BaseResponse(
              statusCode: null,
              response: <String, dynamic>{
                'data': <String, dynamic>{
                  'session_token': 0,
                },
              },
            ));

        final ResetPinEntity entity = await authenticationRepo.resetPin(request: request);

        expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      });
    });

    group('verify logOut()', () {
      test('should clear access token and refresh token', () async {
        final UserToken token = UserToken();
        token.accessToken = fakeAccessToken;
        token.refreshToken = fakeRefreshToken;

        await authenticationRepo.logout();

        expect(appState.userToken.accessToken, null);
        expect(appState.userToken.refreshToken, null);
        verifyNever(() => evoLocalStorageHelper.setDeviceToken(any()));
        verifyNever(() => evoLocalStorageHelper.setBiometricToken(any()));
      });
    });

    group('verify activateAccount', () {
      verifyHttpCalled() {
        verify(() => evoHttpClient.post(
              AuthenticationRepoImpl.activateAccountUrl,
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
              requestOption: captureAny(named: 'requestOption'),
            )).called(1);
      }

      setUpAll(() {
        getIt.registerSingleton<CommonUtilFunction>(CommonUtilFunction());
        getIt.registerSingleton<CommonHttpClient>(evoHttpClient);
      });

      tearDownAll(() {
        if (getIt.isRegistered<CommonUtilFunction>()) {
          getIt.unregister<CommonUtilFunction>();
        }
        getIt.unregister<CommonHttpClient>();
      });

      setUp(() {
        when(() => evoHttpClient.post(
              AuthenticationRepoImpl.activateAccountUrl,
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
              requestOption: any(named: 'requestOption'),
            )).thenAnswer((_) async {
          final Map<String, dynamic> responseData = await TestUtil.getResponseMock(
              getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getSignInOtpSuccess));

          return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
        });

        when(() => evoHttpClient.addHeaders(any())).thenReturn(null);
      });

      test('mock is true and get response success', () async {
        final AccountActivationEntity entity = await authenticationRepo.activateAccount(
          request: mockRequest,
          mockConfig: MockConfig(
              enable: true,
              fileName: getMockVerifyOtpFileNameByCase(MockVerifyOtpUseCase.getSignInOtpSuccess)),
        );

        expect(entity.statusCode, CommonHttpClient.SUCCESS);
        verifyHttpCalled();
      });

      test('mock is false and get response success', () async {
        final AccountActivationEntity entity = await authenticationRepo.activateAccount(
          request: mockRequest,
        );
        expect(entity.statusCode, CommonHttpClient.SUCCESS);
        verifyHttpCalled();
      });

      test('mock is false and get response failure', () async {
        when(() => evoHttpClient.post(
              AuthenticationRepoImpl.activateAccountUrl,
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
              requestOption: any(named: 'requestOption'),
            )).thenAnswer((_) async {
          return BaseResponse(
            statusCode: CommonHttpClient.BAD_REQUEST,
            response: <String, dynamic>{},
          );
        });

        final AccountActivationEntity entity = await authenticationRepo.activateAccount(
          request: mockRequest,
        );

        expect(entity.statusCode, CommonHttpClient.BAD_REQUEST);
        verifyHttpCalled();
      });

      test('should verify requestOption and data parameters', () async {
        const String fakeSessionToken = 'test-session-token';
        final Map<String, dynamic> fakeRequestData = <String, dynamic>{'key': 'value'};

        when(() => mockRequest.sessionToken).thenReturn(fakeSessionToken);
        when(() => mockRequest.toJson()).thenReturn(fakeRequestData);

        await authenticationRepo.activateAccount(request: mockRequest);

        final List<dynamic> captured = verify(() => evoHttpClient.post(
              AuthenticationRepoImpl.activateAccountUrl,
              data: captureAny(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
              requestOption: captureAny(named: 'requestOption'),
            )).captured;

        // Verify data parameter
        expect(captured[0], equals(fakeRequestData));

        // Verify requestOption parameter
        final CommonRequestOption requestOption = captured[1] as CommonRequestOption;
        expect(requestOption.headers, isNotNull);
        expect(requestOption.headers?[HeaderKey.sessionToken], equals(fakeSessionToken));
      });

      test('should store device token when provided', () async {
        when(
          () => evoHttpClient.post(
            AuthenticationRepoImpl.activateAccountUrl,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
            requestOption: any(named: 'requestOption'),
          ),
        ).thenAnswer(
          (_) async => BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: <String, dynamic>{
              'data': <String, dynamic>{
                'device_token': 'test-device-token',
                'access_token': 'test-access-token',
                'refresh_token': 'test-refresh-token',
              },
            },
          ),
        );

        await authenticationRepo.activateAccount(request: mockRequest);
        verifyHttpCalled();

        verify(() => evoLocalStorageHelper.setDeviceToken('test-device-token')).called(1);
      });

      test('should NOT store device token when NOT provided', () async {
        when(
          () => evoHttpClient.post(
            AuthenticationRepoImpl.activateAccountUrl,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
            requestOption: any(named: 'requestOption'),
          ),
        ).thenAnswer(
          (_) async => BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: <String, dynamic>{},
          ),
        );

        await authenticationRepo.activateAccount(request: mockRequest);
        verifyHttpCalled();

        verifyNever(() => evoLocalStorageHelper.setDeviceToken('test-device-token'));
      });

      group('store authentication data', () {
        setUpAll(() {
          mockCommonUtilFunction();
        });

        tearDownAll(() {
          getIt.unregister<CommonUtilFunction>();
        });

        test('should not store data when hasAuthorizeToken is false', () async {
          final AccountActivationEntity mockEntity = AccountActivationEntity();

          when(() => commonUtilFunction.serialize(
                any<SerializeFunction<AccountActivationEntity>>(),
                originalData: any<dynamic>(named: 'originalData'),
              )).thenReturn(mockEntity);

          await authenticationRepo.activateAccount(request: mockRequest);

          verifyNotHandleSignInData();
        });

        test('should store data when hasAuthorizeToken is true', () async {
          final AccountActivationEntity mockEntity = AccountActivationEntity.fromBaseResponse(
              BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{
            'data': <String, dynamic>{
              'access_token': fakeAccessToken,
              'refresh_token': fakeRefreshToken,
              'device_token': fakeDeviceToken,
              'biometric_token': fakeBiometricToken,
              'auth_noti_token': fakeAuthNotiToken,
              'user_id': 0,
            }
          }));

          when(() => commonUtilFunction.serialize(
                any<SerializeFunction<AccountActivationEntity>>(),
                originalData: any<dynamic>(named: 'originalData'),
              )).thenReturn(mockEntity);

          await authenticationRepo.activateAccount(request: mockRequest);

          verifySignInDataHandling();
        });
      });
    });

    group('Test login with new device', () {
      const String fakeUsername = 'fakeUsername';
      const String mockSessionToken = 'test_session_token';
      late UserNameRequest request;

      setUpAll(() {
        mockCommonUtilFunction();
      });

      tearDownAll(() {
        getIt.unregister<CommonUtilFunction>();
      });

      setUp(() {
        request = UserNameRequest(userName: fakeUsername);
      });

      tearDown(() {
        reset(evoHttpClient);
      });

      Future<void> mockHttpResponse(String jsonFile, int statusCode) async {
        final Map<String, dynamic> responseData = await TestUtil.getResponseMock(jsonFile);
        final BaseResponse mockResponse = BaseResponse(
          statusCode: statusCode,
          response: responseData,
        );

        when(() => evoHttpClient.post(
              AuthenticationRepoImpl.loginNewDeviceUrl,
              requestOption: any(named: 'requestOption'),
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
            )).thenAnswer((_) async => mockResponse);

        when(() => commonUtilFunction.serialize(
              any<SerializeFunction<LoginNewDeviceEntity>>(),
              originalData: any<dynamic>(named: 'originalData'),
            )).thenAnswer((_) => LoginNewDeviceEntity.fromBaseResponse(mockResponse));
      }

      group('successful scenarios', () {
        setUp(() => mockHttpResponse(
            'login_new_device_verify_username_success.json', CommonHttpClient.SUCCESS));

        test('returns success response with correct data', () async {
          final LoginNewDeviceEntity loginEntity =
              await authenticationRepo.loginNewDevice(request: request);

          expect(loginEntity.statusCode, CommonHttpClient.SUCCESS);
          expect(loginEntity.challengeType, 'verify_pin');
          expect(loginEntity.sessionToken, 'session_token');
        });

        test('sends correct request data', () async {
          await authenticationRepo.loginNewDevice(request: request);

          verify(() => evoHttpClient.post(
                AuthenticationRepoImpl.loginNewDeviceUrl,
                requestOption: any(named: 'requestOption'),
                data: predicate<Map<String, dynamic>>((Map<String, dynamic> data) =>
                    data['user_name'] == fakeUsername && data['type'] == LoginStepType.none.value),
                mockConfig: any(named: 'mockConfig'),
              )).called(1);
        });

        test('handles mock config correctly', () async {
          const MockConfig mockConfig = MockConfig(
            enable: true,
            fileName: 'login_new_device_verify_username_success.json',
          );

          await authenticationRepo.loginNewDevice(request: request, mockConfig: mockConfig);

          verify(() => evoHttpClient.post(
                AuthenticationRepoImpl.loginNewDeviceUrl,
                requestOption: any(named: 'requestOption'),
                data: any(named: 'data'),
                mockConfig: any<MockConfig?>(
                    named: 'mockConfig',
                    that: predicate((dynamic config) =>
                        config is MockConfig &&
                        config.enable == mockConfig.enable &&
                        config.fileName == mockConfig.fileName)),
              )).called(1);
        });
      });

      group('failure scenarios', () {
        test('returns unserializable entity when response is invalid', () async {
          when(() => evoHttpClient.post(
                AuthenticationRepoImpl.loginNewDeviceUrl,
                requestOption: any(named: 'requestOption'),
                data: any(named: 'data'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));

          when(() => commonUtilFunction.serialize(
                any<SerializeFunction<LoginNewDeviceEntity>>(),
                originalData: any<dynamic>(named: 'originalData'),
              )).thenReturn(null);

          final LoginNewDeviceEntity entity =
              await authenticationRepo.loginNewDevice(request: request);
          expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
        });

        test('handles server error response', () async {
          await mockHttpResponse(
              'login_new_device_verify_username_bad_request.json', CommonHttpClient.BAD_REQUEST);

          final LoginNewDeviceEntity entity =
              await authenticationRepo.loginNewDevice(request: request);
          expect(entity.statusCode, CommonHttpClient.BAD_REQUEST);
          expect(entity.verdict, 'invalid_parameters');
        });
      });

      test('handles headers correctly when session token is provided', () async {
        final UserNameRequest requestWithSession = UserNameRequest(userName: fakeUsername);
        await mockHttpResponse(
            'login_new_device_verify_username_success.json', CommonHttpClient.SUCCESS);

        await authenticationRepo.loginNewDevice(request: requestWithSession);

        final List captured = verify(() => evoHttpClient.post(
              AuthenticationRepoImpl.loginNewDeviceUrl,
              requestOption: captureAny(named: 'requestOption'),
              data: any(named: 'data'),
              mockConfig: any(named: 'mockConfig'),
            )).captured;

        final CommonRequestOption requestOption = captured[0] as CommonRequestOption;
        expect(requestOption.headers?[HeaderKey.sessionToken], isNull);
      });

      group('store authentication data', () {
        test('should not store data when status code is not success', () async {
          await mockHttpResponse(
              'login_new_device_verify_username_bad_request.json', CommonHttpClient.BAD_REQUEST);

          await authenticationRepo.loginNewDevice(request: request);

          verifyNotHandleSignInData();
        });

        test('should store data when status code is success', () async {
          // Mock a successful response with authentication tokens
          final Map<String, dynamic> responseData = <String, dynamic>{
            'data': <String, dynamic>{
              'access_token': fakeAccessToken,
              'refresh_token': fakeRefreshToken,
              'device_token': fakeDeviceToken,
              'biometric_token': fakeBiometricToken,
              'auth_noti_token': fakeAuthNotiToken,
              'user_id': fakeUserId,
              'challenge_type': 'verify_pin',
              'session_token': mockSessionToken,
            }
          };

          final BaseResponse mockResponse = BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          );

          when(() => evoHttpClient.post(
                AuthenticationRepoImpl.loginNewDeviceUrl,
                requestOption: any(named: 'requestOption'),
                data: any(named: 'data'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async => mockResponse);

          when(() => commonUtilFunction.serialize(
                any<SerializeFunction<LoginNewDeviceEntity>>(),
                originalData: any<dynamic>(named: 'originalData'),
              )).thenAnswer((_) => LoginNewDeviceEntity.fromBaseResponse(mockResponse));

          await authenticationRepo.loginNewDevice(request: request);

          verifySignInDataHandling();
        });

        test('should not store data when entity has no authorization tokens', () async {
          // Mock a successful response but without authentication tokens
          final Map<String, dynamic> responseData = <String, dynamic>{
            'data': <String, dynamic>{
              'challenge_type': 'verify_pin',
              'session_token': mockSessionToken,
              // No authentication tokens provided
            }
          };

          final BaseResponse mockResponse = BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: responseData,
          );

          when(() => evoHttpClient.post(
                AuthenticationRepoImpl.loginNewDeviceUrl,
                requestOption: any(named: 'requestOption'),
                data: any(named: 'data'),
                mockConfig: any(named: 'mockConfig'),
              )).thenAnswer((_) async => mockResponse);

          when(() => commonUtilFunction.serialize(
                any<SerializeFunction<LoginNewDeviceEntity>>(),
                originalData: any<dynamic>(named: 'originalData'),
              )).thenAnswer((_) => LoginNewDeviceEntity.fromBaseResponse(mockResponse));

          await authenticationRepo.loginNewDevice(request: request);

          verifyNotHandleSignInData();
        });
      });
    });
  });
}
