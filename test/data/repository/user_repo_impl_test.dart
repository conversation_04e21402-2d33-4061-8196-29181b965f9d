import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/constants.dart';
import 'package:evoapp/data/repository/user_repo_impl.dart';
import 'package:evoapp/data/request/change_pin_request.dart';
import 'package:evoapp/data/response/biometric_token_entity.dart';
import 'package:evoapp/data/response/change_pin_entity.dart';
import 'package:evoapp/data/response/user_entity.dart';
import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:evoapp/feature/biometric/base/ext_biometric_token_entity.dart';
import 'package:evoapp/feature/biometric/mock/mock_biometric_token_use_case.dart';
import 'package:evoapp/feature/pin/mock/mock_pin_use_case.dart';
import 'package:evoapp/feature/profile/mock/mock_profile_use_case.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/evo_flutter_wrapper.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/common_request_option.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';
import '../../util/test_util.dart';
import 'mock_common_http_client.dart';

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

void main() {
  late CommonHttpClient httpClient;
  late EvoLocalStorageHelper mockStorageHelper;
  late UserRepoImpl userRepoImpl;
  late CommonUtilFunction commonUtilFunction;
  late CommonUtilFunction mockCommonUtilFunction;
  const String mockChallengeType = 'verify_pin';
  final AppState appState = AppState();

  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    getIt.registerLazySingleton<EvoLocalStorageHelper>(() => MockEvoLocalStorageHelper());
    mockStorageHelper = getIt.get<EvoLocalStorageHelper>();

    getIt.registerLazySingleton<CommonHttpClient>(() => MockCommonHttpClient());
    httpClient = getIt.get<CommonHttpClient>();

    getIt.registerLazySingleton<EvoFlutterWrapper>(() => EvoFlutterWrapper());
    getIt.registerLazySingleton<EvoUtilFunction>(() => EvoUtilFunction());

    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    commonUtilFunction = getIt.get<CommonUtilFunction>();

    mockLoggingRepo();
  });

  setUp(() {
    appState.userToken = null;
    userRepoImpl = UserRepoImpl(httpClient, mockStorageHelper, appState);
  });

  tearDown(() {
    reset(httpClient);
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  registerMockStorageHelper() {
    when(() => mockStorageHelper.setBiometricToken(any())).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => mockStorageHelper.setDeviceToken(any())).thenAnswer((_) async {
      return Future<void>.value();
    });

    when(() => mockCommonUtilFunction.handleSignInSucceedData(
          accessToken: any(named: 'accessToken'),
          userId: any(named: 'userId'),
          notificationAuthKey: any(named: 'notificationAuthKey'),
        )).thenAnswer((_) async {
      return Future<void>.value();
    });
  }

  verifySaveAuthenticationInfoCalled(
      {required String? accessToken,
      required String? refreshToken,
      required String? deviceToken,
      int? userId,
      String? notificationAuthKey}) {
    expect(appState.userToken.accessToken, accessToken);
    expect(appState.userToken.refreshToken, refreshToken);
    verify(() => mockStorageHelper.setDeviceToken(deviceToken)).called(1);
    verify(() => commonUtilFunction.handleSignInSucceedData(
          accessToken: accessToken,
          userId: userId,
          notificationAuthKey: notificationAuthKey,
        )).called(1);
  }

  verifySaveAuthenticationInfoDidNotCall({
    required String? accessToken,
    required String? refreshToken,
    required String? deviceToken,
    String? biometricToken,
    int? userId,
    String? notificationAuthKey,
  }) {
    expect(appState.userToken.accessToken, isNull);
    expect(appState.userToken.refreshToken, isNull);
    verifyNever(() => mockStorageHelper.setBiometricToken(biometricToken));
    verifyNever(() => mockStorageHelper.setDeviceToken(deviceToken));
    verifyNever(() => commonUtilFunction.handleSignInSucceedData(
          accessToken: accessToken,
          userId: userId,
          notificationAuthKey: notificationAuthKey,
        ));
  }

  group('verify getBiometricTokenByPin method', () {
    setUpAll(() {
      mockCommonUtilFunction = MockCommonUtilFunction();
    });

    tearDownAll(() {
      reset(mockCommonUtilFunction);
    });

    setUp(() {
      registerMockStorageHelper();
    });

    tearDown(() {
      reset(httpClient);
      reset(mockStorageHelper);
    });

    test('test getBiometricTokenByPin success with mock is enable', () async {
      final String fileName = getMockBiometricTokenFileNameByCase(
        MockTestBiometricTokenUseCase.getBiometricTokenSuccess,
      );
      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(fileName);

      when(() => httpClient.post(
            UserRepoImpl.biometricToken,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final BiometricTokenEntity entity = await userRepoImpl.getBiometricTokenByPin(
          mockConfig: MockConfig(
        enable: true,
        fileName: fileName,
      ));

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.biometricToken, isNotNull);
      expect(entity.challengeType, isNotNull);
      expect(entity.challengeType, 'none');
      expect(entity.accessToken, isNotNull);
      expect(entity.refreshToken, isNotNull);
      expect(entity.deviceToken, isNotNull);

      verifySaveAuthenticationInfoCalled(
        accessToken: entity.accessToken,
        refreshToken: entity.refreshToken,
        deviceToken: entity.deviceToken,
      );
    });

    test('test getBiometricTokenByPin success with mock is disabled', () async {
      final String fileName = getMockBiometricTokenFileNameByCase(
        MockTestBiometricTokenUseCase.getBiometricTokenSuccess,
      );
      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(fileName);

      when(() => httpClient.post(
            UserRepoImpl.biometricToken,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final BiometricTokenEntity entity = await userRepoImpl.getBiometricTokenByPin();

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.biometricToken, isNotNull);
      expect(entity.challengeType, isNotNull);
      expect(entity.challengeType, 'none');
      expect(entity.accessToken, isNotNull);
      expect(entity.refreshToken, isNotNull);
      expect(entity.deviceToken, isNotNull);

      verifySaveAuthenticationInfoCalled(
        accessToken: entity.accessToken,
        refreshToken: entity.refreshToken,
        deviceToken: entity.deviceToken,
      );
    });

    test('test getBiometricTokenByPin has challengeType with mock is enable', () async {
      final String fileName = getMockBiometricTokenFileNameByCase(
        MockTestBiometricTokenUseCase.getBiometricTokenWithChallengeType,
      );

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(fileName);

      when(() => httpClient.post(UserRepoImpl.biometricToken,
          data: any(named: 'data'), mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final BiometricTokenEntity entity = await userRepoImpl.getBiometricTokenByPin(
          mockConfig: MockConfig(enable: true, fileName: fileName));

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.biometricToken, isNotNull);
      expect(entity.challengeType, isNotNull);
      expect(entity.challengeType, mockChallengeType);
      expect(entity.accessToken, isNotNull);
      expect(entity.refreshToken, isNotNull);

      verifySaveAuthenticationInfoDidNotCall(
        accessToken: entity.accessToken,
        refreshToken: entity.refreshToken,
        deviceToken: entity.deviceToken,
      );
    });

    test('test getBiometricTokenByPin has challengeType with mock is disabled', () async {
      final String fileName = getMockBiometricTokenFileNameByCase(
        MockTestBiometricTokenUseCase.getBiometricTokenWithChallengeType,
      );

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(fileName);

      when(() => httpClient.post(UserRepoImpl.biometricToken,
          data: any(named: 'data'), mockConfig: any(named: 'mockConfig'))).thenAnswer((_) async {
        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });

      final BiometricTokenEntity entity = await userRepoImpl.getBiometricTokenByPin();

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.biometricToken, isNotNull);
      expect(entity.challengeType, isNotNull);
      expect(entity.challengeType, mockChallengeType);
      expect(entity.accessToken, isNotNull);
      expect(entity.refreshToken, isNotNull);

      verifySaveAuthenticationInfoDidNotCall(
        accessToken: entity.accessToken,
        refreshToken: entity.refreshToken,
        deviceToken: entity.deviceToken,
      );
    });

    test('test getBiometricTokenByPin failed with mock is disable and statusCode is not 200',
        () async {
      final BaseResponse response =
          BaseResponse(statusCode: CommonHttpClient.UNKNOWN_ERRORS, response: null);

      when(() => httpClient.post(UserRepoImpl.biometricToken, data: any(named: 'data')))
          .thenAnswer((_) async => response);

      final BiometricTokenEntity entity = await userRepoImpl.getBiometricTokenByPin();

      expect(entity.statusCode, CommonHttpClient.UNKNOWN_ERRORS);
      expect(entity.biometricToken, isNull);
      expect(entity.challengeType, isNull);
      expect(entity.accessToken, isNull);
      expect(entity.refreshToken, isNull);

      final bool isNeedChallengeBiometricToken = entity.isNeedChallenge();
      expect(isNeedChallengeBiometricToken, false);
      verifySaveAuthenticationInfoDidNotCall(
        accessToken: entity.accessToken,
        refreshToken: entity.refreshToken,
        deviceToken: entity.deviceToken,
      );
    });

    test('test getBiometricTokenByPin success with mock is disable and statusCode is 200',
        () async {
      final String fileName = getMockBiometricTokenFileNameByCase(
        MockTestBiometricTokenUseCase.getBiometricTokenSuccess,
      );

      final Map<String, dynamic> responseData = await TestUtil.getResponseMock(fileName);

      final BaseResponse response =
          BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);

      when(() => httpClient.post(UserRepoImpl.biometricToken, data: any(named: 'data')))
          .thenAnswer((_) async => response);

      final BiometricTokenEntity entity = await userRepoImpl.getBiometricTokenByPin();

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      expect(entity.biometricToken, isNotNull);
      expect(entity.challengeType, 'none');
      expect(entity.accessToken, isNotNull);
      expect(entity.refreshToken, isNotNull);
    });

    test('should send pin along with HTTP request', () async {
      when(() => httpClient.post(
            UserRepoImpl.biometricToken,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        return BaseResponse(
          statusCode: null,
          response: null,
        );
      });

      final String pin = '1234';
      await userRepoImpl.getBiometricTokenByPin(pin: pin);

      final List<dynamic> captured = verify(() => httpClient.post(
            UserRepoImpl.biometricToken,
            data: captureAny(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          )).captured;

      final Map<String, dynamic> data = captured.first as Map<String, dynamic>;
      expect(data['pin'], pin);
    });

    test('should return BiometricTokenEntity.unserializable() when data format is invalid',
        () async {
      when(() => httpClient.post(
            UserRepoImpl.biometricToken,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
          )).thenAnswer((_) async {
        return BaseResponse(
          statusCode: null,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'access_token': 0,
            }
          },
        );
      });

      final BiometricTokenEntity entity = await userRepoImpl.getBiometricTokenByPin();

      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });

  group('verify Init change pin', () {
    verifyHttpCalled() {
      final CommonRequestOption captured = verify(() => httpClient.patch(
            UserRepoImpl.changePinUrl,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
            requestOption: captureAny(named: 'requestOption'),
          )).captured.first as CommonRequestOption;

      expect(captured.headers, isNull);
    }

    setUp(() {
      when(() => httpClient.patch(
            UserRepoImpl.changePinUrl,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
            requestOption: any(named: 'requestOption'),
          )).thenAnswer((_) async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock(getMockPinFileNameByCase(
          MockPinUseCase.getChangePinVerifyCurrentSuccess,
        ));

        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });
    });

    test('mock is true and get response success', () async {
      final ChangePinEntity response = await userRepoImpl.changePin(
          mockConfig: MockConfig(
            enable: true,
            fileName: getMockPinFileNameByCase(
              MockPinUseCase.getVerifyPinLimitExceeded,
            ),
          ),
          request: InitializeChangePinSessionRequest());

      expect(response.sessionToken, 'mock_session_token');
      expect(response.challengeType, 'verify_pin');
      verifyHttpCalled();
    });

    test('mock is false and get response success', () async {
      final ChangePinEntity response = await userRepoImpl.changePin(
        request: InitializeChangePinSessionRequest(),
      );

      expect(response.sessionToken, 'mock_session_token');
      expect(response.challengeType, 'verify_pin');
      verifyHttpCalled();
    });

    test('mock is false and get response failure', () async {
      when(() => httpClient.patch(
            UserRepoImpl.changePinUrl,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
            requestOption: any(named: 'requestOption'),
          )).thenAnswer((_) async {
        return BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: <String, dynamic>{},
        );
      });

      final ChangePinEntity response = await userRepoImpl.changePin(
        request: InitializeChangePinSessionRequest(),
      );

      expect(response.statusCode, CommonHttpClient.BAD_REQUEST);
      expect(response.sessionToken, null);
      expect(response.challengeType, null);
      verifyHttpCalled();
    });
  });

  group('verify  changePin', () {
    const String mockPin = '12345';
    const String sessionToken = 'session_token';
    final VerifyCurrentPinRequest mockRequest = VerifyCurrentPinRequest(
      pin: mockPin,
      sessionToken: sessionToken,
    );

    verifyHttpCalled() {
      final List<dynamic> captures = verify(() => httpClient.patch(
            UserRepoImpl.changePinUrl,
            data: captureAny(named: 'data'),
            requestOption: captureAny(named: 'requestOption'),
            mockConfig: any(named: 'mockConfig'),
          )).captured;

      expect(captures[0], equals(mockRequest.toJson()));
      expect(
          captures[1],
          isA<CommonRequestOption>()
              .having((CommonRequestOption reqOptions) => reqOptions.headers, 'verify headers', {
            HeaderKey.sessionToken: sessionToken,
          }));
    }

    setUp(() {
      when(() => httpClient.patch(
            UserRepoImpl.changePinUrl,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
            requestOption: any(named: 'requestOption'),
          )).thenAnswer((_) async {
        final Map<String, dynamic> responseData =
            await TestUtil.getResponseMock(getMockPinFileNameByCase(
          MockPinUseCase.getConfirmResetPinSuccess,
        ));

        return BaseResponse(statusCode: CommonHttpClient.SUCCESS, response: responseData);
      });
    });

    test('mock is true and get response success', () async {
      final ChangePinEntity entity = await userRepoImpl.changePin(
        request: mockRequest,
        mockConfig: MockConfig(
          enable: true,
          fileName: getMockPinFileNameByCase(
            MockPinUseCase.getConfirmResetPinSuccess,
          ),
        ),
      );

      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      verifyHttpCalled();
    });

    test('mock is false and get response success', () async {
      final ChangePinEntity entity = await userRepoImpl.changePin(
        request: mockRequest,
      );
      expect(entity.statusCode, CommonHttpClient.SUCCESS);
      verifyHttpCalled();
    });

    test('mock is false and get response failure', () async {
      when(() => httpClient.patch(
            UserRepoImpl.changePinUrl,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
            requestOption: any(named: 'requestOption'),
          )).thenAnswer((_) async {
        return BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: <String, dynamic>{},
        );
      });

      final ChangePinEntity entity = await userRepoImpl.changePin(
        request: mockRequest,
      );

      expect(entity.statusCode, CommonHttpClient.BAD_REQUEST);
      verifyHttpCalled();
    });

    test('should return ChangePinEntity.unserializable() when data format is invalid', () async {
      when(() => httpClient.patch(
            UserRepoImpl.changePinUrl,
            data: any(named: 'data'),
            mockConfig: any(named: 'mockConfig'),
            requestOption: any(named: 'requestOption'),
          )).thenAnswer((_) async {
        return BaseResponse(
          statusCode: CommonHttpClient.BAD_REQUEST,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'challenge_type': 0,
            }
          },
        );
      });

      final ChangePinEntity entity = await userRepoImpl.changePin(request: mockRequest);

      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });
  });

  group('verify getUserInfo()', () {
    Future<BaseResponse> httpRequest() => httpClient.get(
          UserRepoImpl.userProfile,
          mockConfig: any(named: 'mockConfig'),
        );

    test('should make an HTTP request', () async {
      when(httpRequest).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));

      await userRepoImpl.getUserInfo();

      verify(httpRequest).called(1);
    });

    test('should make an HTTP request with passed mockConfig', () async {
      when(httpRequest).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));

      final MockConfig config = MockConfig(enable: true);
      await userRepoImpl.getUserInfo(mockConfig: config);

      final List<dynamic> captured = verify(() => httpClient.get(
            UserRepoImpl.userProfile,
            mockConfig: captureAny(named: 'mockConfig'),
          )).captured;
      final MockConfig passedConfig = captured.first as MockConfig;
      expect(passedConfig, equals(config));
    });

    test('should return UserEntity.unserializable() when data format is invalid', () async {
      when(httpRequest).thenAnswer((_) async => BaseResponse(
            statusCode: null,
            response: <String, dynamic>{
              'data': <String, dynamic>{
                'user_information': '(invalid_format)',
              }
            },
          ));

      final UserEntity entity = await userRepoImpl.getUserInfo();

      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
    });

    test('should return entity with correct data', () async {
      final String mockFile =
          getMockProfileFileNameByCase(MockProfileUseCase.getUserInformationSuccess);
      final Map<String, dynamic> response = await TestUtil.getResponseMock(mockFile);

      when(httpRequest).thenAnswer((_) async => BaseResponse(
            statusCode: CommonHttpClient.SUCCESS,
            response: response,
          ));
      final UserEntity entity = await userRepoImpl.getUserInfo();

      expect(entity.userInformation, isNotNull);
      final UserInformationEntity info = entity.userInformation!;

      final Map<String, dynamic> data = response['data']['user_information'];
      expect(info.fullName, data['full_name']);
      expect(info.gender, data['gender']);
      expect(info.birthday, data['birthday']);
      expect(info.identityCardIssueDate, data['identity_card_issue_date']);
      expect(info.identityCardNumber, data['identity_card_number']);
      expect(info.phoneNumber, data['phone_number']);
      expect(info.email, data['email']);
      expect(info.avatarUrl, data['avatar_url']);
    });
  });
}
