import 'package:evoapp/data/repository/common_repo_impl.dart';
import 'package:evoapp/data/response/force_update_entity.dart';
import 'package:evoapp/feature/check_force_update/mock/mock_check_force_update_use_case.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/mock_config.dart';
import 'package:flutter_common_package/util/functions.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/flutter_test_config.dart';
import '../../util/test_util.dart';
import 'mock_common_http_client.dart';

void main() {
  late CommonHttpClient client;
  late CommonRepoImpl repo;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    getIt.registerLazySingleton(() => CommonUtilFunction());
    mockLoggingRepo();
  });

  setUp(() {
    client = MockCommonHttpClient();
    repo = CommonRepoImpl(client);
  });

  tearDownAll(() async {
    await getIt.reset();
  });

  group('CommonRepoImpl', () {
    group('getForceUpdate()', () {
      Future<BaseResponse> httpRequest() => client.get(
            CommonRepoImpl.checkForceUpdate,
            mockConfig: any(named: 'mockConfig'),
          );

      test('should make an HTTP request', () async {
        when(httpRequest).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));

        await repo.getForceUpdate();

        verify(httpRequest).called(1);
      });

      test('should make an HTTP request with passed mockConfig', () async {
        when(httpRequest).thenAnswer((_) async => BaseResponse(statusCode: null, response: null));

        final MockConfig config = MockConfig(enable: true);
        await repo.getForceUpdate(mockConfig: config);

        final List<dynamic> captured = verify(() => client.get(
              CommonRepoImpl.checkForceUpdate,
              mockConfig: captureAny(named: 'mockConfig'),
            )).captured;
        final MockConfig passedConfig = captured.first as MockConfig;
        expect(passedConfig, equals(config));
      });

      test('should return entity with correct data', () async {
        final Map<String, dynamic> response =
            await TestUtil.getResponseMock(MockCheckForceUpdateUseCase.checkForceUpdate.value);
        when(httpRequest)
            .thenAnswer((_) async => BaseResponse(statusCode: null, response: response));

        final ForceUpdateEntity entity = await repo.getForceUpdate();

        final Map<String, dynamic> data = response['data'];
        expect(entity.forceToUpdate, data['force_to_update']);
        expect(entity.hasNewerVersion, data['has_newer_version']);
        expect(entity.latestVersion, data['latest_version']);
      });

      test('should return UserEntity.unserializable() when data format is invalid', () async {
        when(httpRequest).thenAnswer((_) async => BaseResponse(
              statusCode: null,
              response: <String, dynamic>{
                'data': <String, dynamic>{
                  'force_to_update': '(invalid_format)',
                }
              },
            ));

        final ForceUpdateEntity entity = await repo.getForceUpdate();

        expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      });
    });
  });
}
