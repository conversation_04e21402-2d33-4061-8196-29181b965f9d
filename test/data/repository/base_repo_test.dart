import 'package:evoapp/base/modules/core/app_state.dart';
import 'package:evoapp/data/repository/base_repo.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/util/secure_storage_helper/secure_storage_helper.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockBaseRepo extends BaseRepo {
  MockBaseRepo(super.client, {super.localStorageHelper, super.appState});
}

class MockEvoLocalStorageHelper extends Mock implements EvoLocalStorageHelper {}

class MockCommonUtilFunction extends Mock implements CommonUtilFunction {}

class MockCommonHttpClient extends Mock implements CommonHttpClient {}

void main() {
  const String deviceToken = 'device_token';
  const String biometricToken = 'biometric_token';
  const String accessToken = 'access_token';
  const String refreshToken = 'refresh_token';

  final CommonHttpClient httpClient = MockCommonHttpClient();
  final EvoLocalStorageHelper storageHelper = MockEvoLocalStorageHelper();
  final AppState appState = AppState();

  late MockBaseRepo repo;
  late MockCommonUtilFunction commonUtilFunction;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    commonUtilFunction = MockCommonUtilFunction();
    getIt.registerLazySingleton<CommonUtilFunction>(() => commonUtilFunction);

    when(() => storageHelper.setDeviceToken(any())).thenAnswer((_) => Future<void>.value());
    when(() => storageHelper.setBiometricToken(any())).thenAnswer((_) => Future<void>.value());
  });

  setUp(() {
    repo = MockBaseRepo(
      httpClient,
      localStorageHelper: storageHelper,
      appState: appState,
    );
  });

  group('test saveAuthenticationInfo() function', () {
    const int userId = 111;
    const String notificationAuthKey = 'notification_auth_key';

    setUpAll(() {

      when(() => commonUtilFunction.handleSignInSucceedData(
            accessToken: any(named: 'accessToken'),
            userId: any(named: 'userId'),
            notificationAuthKey: any(named: 'notificationAuthKey'),
          )).thenAnswer((_) => Future<void>.value());
    });

    setUp(() {
      appState.userToken = null;
    });

    test('work correctly with biometricToken != NULL', () async {
      await repo.saveAuthenticationInfo(
        accessToken: accessToken,
        refreshToken: refreshToken,
        deviceToken: deviceToken,
        biometricToken: biometricToken,
        userId: userId,
        notificationAuthKey: notificationAuthKey,
      );

      expect(appState.userToken.accessToken, accessToken);
      expect(appState.userToken.refreshToken, refreshToken);
      verify(() => storageHelper.setDeviceToken(deviceToken)).called(1);
      verify(() => storageHelper.setBiometricToken(biometricToken)).called(1);

      verify(() => commonUtilFunction.handleSignInSucceedData(
            accessToken: accessToken,
            userId: userId,
            notificationAuthKey: notificationAuthKey,
          )).called(1);
    });

    test('work correctly with biometricToken == NULL', () async {
      await repo.saveAuthenticationInfo(
        accessToken: accessToken,
        refreshToken: refreshToken,
        deviceToken: deviceToken,
        userId: userId,
        notificationAuthKey: notificationAuthKey,
      );

      expect(appState.userToken.accessToken, accessToken);
      expect(appState.userToken.refreshToken, refreshToken);
      verify(() => storageHelper.setDeviceToken(deviceToken)).called(1);
      verifyNever(() => storageHelper.setBiometricToken(biometricToken));

      verify(() => commonUtilFunction.handleSignInSucceedData(
            accessToken: accessToken,
            userId: userId,
            notificationAuthKey: notificationAuthKey,
          )).called(1);
    });
  });

  group('Test storeTokensInSecureStorage', () {
    test('Give only device token', () async {
      await repo.storeTokensInSecureStorage(deviceToken: deviceToken);

      verify(() => storageHelper.setDeviceToken(deviceToken)).called(1);
      verifyNever(() => storageHelper.setBiometricToken(any()));
    });

    test('Give all params', () async {
      await repo.storeTokensInSecureStorage(
        deviceToken: deviceToken,
        biometricToken: biometricToken,
      );

      verify(() => storageHelper.setDeviceToken(deviceToken)).called(1);
      verify(() => storageHelper.setBiometricToken(biometricToken)).called(1);
    });
  });

  group('Test storeTokensInMemory', () {
    setUp(() {
      appState.userToken = null;
    });

    test('Test storeTokensInMemory', () {
      expect(appState.userToken.accessToken, isNull);
      expect(appState.userToken.refreshToken, isNull);

      repo.storeTokensInMemory(
        accessToken: accessToken,
        refreshToken: refreshToken,
      );

      expect(appState.userToken.accessToken, accessToken);
      expect(appState.userToken.refreshToken, refreshToken);
    });
  });
}
