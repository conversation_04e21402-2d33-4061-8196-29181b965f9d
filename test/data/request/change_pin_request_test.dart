import 'package:evoapp/data/request/change_pin_request.dart';
import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify subtype', () {
    expect(
        VerifyCurrentPinRequest(pin: '', sessionToken: ''),
        isA<ChangePinRequest>().having(
            (ChangePinRequest req) => req.type, 'verify type', AuthChallengeType.verifyPin));
    expect(
        ConfirmNewPinRequest(pin: '', sessionToken: ''),
        isA<ChangePinRequest>().having(
          (ChangePinRequest req) => req.type,
          'verify type',
          AuthChallengeType.changePin,
        ));
    expect(
        InitializeChangePinSessionRequest(),
        isA<InitializeChangePinSessionRequest>().having(
          (ChangePinRequest req) => req.type,
          'verify type',
          AuthChallengeType.none,
        ));
  });

  test('verify InitializeChangePinSessionRequest should parse toJson correctly', () {
    final InitializeChangePinSessionRequest request = InitializeChangePinSessionRequest();

    expect(
        request.toJson(),
        equals(<String, String>{
          'type': 'none',
        }));
  });

  test('verify VerifyCurrentPinRequest should parse toJson correctly', () {
    const String mockPin = 'mock_pin';
    final VerifyCurrentPinRequest request = VerifyCurrentPinRequest(pin: mockPin, sessionToken: '');

    expect(
        request.toJson(),
        equals(<String, String>{
          'type': 'verify_pin',
          'current_pin': mockPin,
        }));
  });

  test('verify ConfirmNewPinRequest should parse toJson correctly', () {
    const String mockPin = 'mock_pin';
    final ConfirmNewPinRequest request = ConfirmNewPinRequest(pin: mockPin, sessionToken: '');

    expect(
        request.toJson(),
        equals(<String, String>{
          'type': 'change_pin',
          'new_pin': mockPin,
        }));
  });
}
