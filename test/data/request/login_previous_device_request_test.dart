import 'package:evoapp/data/request/login_previous_device_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('LoginType', () {
    test('should have correct value for mPin', () {
      expect(LoginType.mPin.value, equals('device_token_with_pin'));
    });
  });

  group('LoginWithMPINRequest', () {
    const String pin = '123456';
    const String deviceToken = 'test_device_token';

    test('should be initialized with required parameters', () {
      final LoginWithMPINRequest request = LoginWithMPINRequest(pin: pin, deviceToken: deviceToken);

      expect(request.pin, equals(pin));
      expect(request.deviceToken, equals(deviceToken));
    });

    test('should generate correct JSON', () {
      final LoginWithMPINRequest request = LoginWithMPINRequest(pin: pin, deviceToken: deviceToken);
      final Map<String, dynamic> json = request.toJson();

      expect(json, isA<Map<String, dynamic>>());
      expect(json[LoginPreviousDeviceRequest.typeKey], equals(LoginType.mPin.value));

      final Map<String, dynamic> mPinData = json[LoginType.mPin.value] as Map<String, dynamic>;
      expect(mPinData, isNotNull);
      expect(mPinData[LoginWithMPINRequest.pinKey], equals(pin));
      expect(mPinData[LoginWithMPINRequest.deviceTokenKey], equals(deviceToken));
    });

    test('should have correct constant values', () {
      expect(LoginWithMPINRequest.pinKey, equals('pin'));
      expect(LoginWithMPINRequest.deviceTokenKey, equals('device_token'));
    });
  });
}
