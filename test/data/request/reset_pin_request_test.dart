import 'package:evoapp/data/repository/authentication_repo.dart';
import 'package:evoapp/data/request/reset_pin_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String mockPhoneNumber = 'mock-phone-number';
  const String mockSessionToken = 'mock-session-token';
  const String mockOtp = 'mock-otp';
  const String mockPin = 'mock-pin';

  group('verify InitializeResetPinRequest', () {
    test('verify subtype', () {
      expect(
          InitializeResetPinRequest(phoneNumber: ''),
          isA<ResetPinRequest>().having((ResetPinRequest req) => (req.type, req.sessionToken),
              'verify type, sessionToken', equals((ResetPinType.none, null))));
    });

    test('should parse toJson() correctly ', () {
      expect(
          InitializeResetPinRequest(phoneNumber: mockPhoneNumber).toJson(),
          equals({
            'type': ResetPinType.none.value,
            'phone_number': mockPhoneNumber,
          }));
    });
  });

  group('verify ResetPinVerifyOTPRequest', () {
    test('verify subtype', () {
      expect(
          ResetPinVerifyOTPRequest(otp: 'mock-otp', sessionToken: 'mock-session-token'),
          isA<ResetPinRequest>().having((ResetPinRequest req) => (req.type, req.sessionToken),
              'verify type, sessionToken', equals((ResetPinType.verifyOTP, 'mock-session-token'))));
    });

    test('should parse toJson() correctly ', () {
      expect(
          ResetPinVerifyOTPRequest(otp: mockOtp, sessionToken: mockSessionToken).toJson(),
          equals(<String, String>{
            'otp': mockOtp,
            'type': ResetPinType.verifyOTP.value,
          }));
    });
  });

  group('verify ResetPinConfirmNewPinRequest', () {
    test('verify subtype', () {
      expect(
          ResetPinConfirmNewPinRequest(newPin: 'mock-pin', sessionToken: 'mock-session-token'),
          isA<ResetPinRequest>().having((ResetPinRequest req) => (req.type, req.sessionToken),
              'verify type, sessionToken', equals((ResetPinType.none, 'mock-session-token'))));
    });

    test('should parse toJson() correctly ', () {
      expect(
          ResetPinConfirmNewPinRequest(
            newPin: mockPin,
            sessionToken: mockSessionToken,
          ).toJson(),
          equals(<String, String>{
            'new_pin': mockPin,
            'type': ResetPinType.none.value,
          }));
    });
  });

  group('verify ResetPinFaceAuthRequest', () {
    test('verify subtype', () {
      expect(
          ResetPinFaceAuthRequest(sessionToken: 'mock-session-token'),
          isA<ResetPinRequest>().having((ResetPinRequest req) => (req.type, req.sessionToken),
              'verify type, sessionToken', equals((ResetPinType.faceAuth, 'mock-session-token'))));
    });

    test('should parse toJson() correctly', () {
      final List<String> mockImageIds = ['image1', 'image2'];
      final List<String> mockVideoIds = ['video1'];
      final String selfieType = 'front';

      final ResetPinFaceAuthRequest request = ResetPinFaceAuthRequest(
        sessionToken: 'session123',
        imageIds: mockImageIds,
        videoIds: mockVideoIds,
        selfieType: selfieType,
      );
      final Map<String, dynamic> json = request.toJson();

      expect(
          json,
          equals(<String, dynamic>{
            'type': 'face_auth', // Assuming ResetPinType.faceAuth.value is 'faceAuth'
            'face_auth_request': <String, Object>{
              'face_image_ids': mockImageIds,
              'video_ids': mockVideoIds,
              'selfie_type': selfieType
            }
          }));
    });
  });
}
