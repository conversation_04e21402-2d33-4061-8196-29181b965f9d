import 'package:evoapp/data/request/login_new_device_request.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AccountType', () {
    test('should have correct string values', () {
      expect(LoginStepType.none.value, equals('none'));
      expect(LoginStepType.verifyPin.value, equals('verify_pin'));
      expect(LoginStepType.faceAuth.value, equals('face_auth'));
      expect(LoginStepType.biometricToken.value, equals('biometric_token'));
    });
  });

  group('UserNameRequest', () {
    test('should create request with username', () {
      const UserNameRequest request = UserNameRequest(username: 'testUser');
      expect(request.type, equals(LoginStepType.none));
      expect(request.username, equals('testUser'));

      final Map<String, dynamic> json = request.toJson();

      expect(
          json,
          equals(<String, dynamic>{
            'type': 'none',
            'user_name': 'testUser',
            'session_token': null,
          }));
    });
  });

  group('VerifyMPinRequest', () {
    test('should create request with pin and sessionToken', () {
      const VerifyMPinRequest request = VerifyMPinRequest(
        pin: '1234',
        sessionToken: 'test-session-token',
      );

      expect(request.type, equals(LoginStepType.verifyPin));
      expect(request.pin, equals('1234'));
      expect(request.sessionToken, equals('test-session-token'));

      final Map<String, dynamic> json = request.toJson();

      expect(
          json,
          equals(<String, String>{
            'type': 'verify_pin',
            'pin': '1234',
            'session_token': 'test-session-token',
          }));
    });
  });

  group('SelfieAuthRequest', () {
    test('should create request with only sessionToken', () {
      final SelfieAuthRequest request = SelfieAuthRequest(
        sessionToken: 'test-session-token',
        imageIds: <String>[],
        videoIds: <String>[],
        selfieType: 'test-selfie-type',
      );

      expect(request.type, equals(LoginStepType.faceAuth));
      expect(request.sessionToken, equals('test-session-token'));
      expect(request.imageIds, isNotNull);
      expect(request.videoIds, isNotNull);
      expect(request.selfieType, equals('test-selfie-type'));

      final Map<String, dynamic> json = request.toJson();

      expect(
          json,
          equals(<String, Object?>{
            'type': 'face_auth',
            'session_token': 'test-session-token',
            'face_auth_request': <String, Object?>{
              'face_image_ids': <String>[],
              'video_ids': <String>[],
              'selfie_type': 'test-selfie-type',
            }
          }));
    });

    test('should create request with all parameters', () {
      final SelfieAuthRequest request = SelfieAuthRequest(
        sessionToken: 'test-session-token',
        imageIds: <String>['image1', 'image2'],
        videoIds: <String>['video1'],
        selfieType: 'verification',
      );

      expect(request.type, equals(LoginStepType.faceAuth));
      expect(request.sessionToken, equals('test-session-token'));
      expect(request.imageIds, equals(<String>['image1', 'image2']));
      expect(request.videoIds, equals(<String>['video1']));
      expect(request.selfieType, equals('verification'));

      final Map<String, dynamic> json = request.toJson();

      expect(
          json,
          equals(<String, dynamic>{
            'type': 'face_auth',
            'session_token': 'test-session-token',
            'face_auth_request': <String, Object?>{
              'face_image_ids': <String>['image1', 'image2'],
              'video_ids': <String>['video1'],
              'selfie_type': 'verification',
            }
          }));
    });
  });

  group('EnableBiometricRequest', () {
    test('should create request with skip flag and sessionToken', () {
      const bool skipValue = true;
      const String sessionTokenValue = 'test-session-token';

      final EnableBiometricRequest request = EnableBiometricRequest(
        skip: skipValue,
        sessionToken: sessionTokenValue,
      );

      expect(request.type, equals(LoginStepType.biometricToken));
      expect(request.skip, equals(skipValue));
      expect(request.sessionToken, equals(sessionTokenValue));

      final Map<String, dynamic> json = request.toJson();

      expect(
          json,
          equals(<String, dynamic>{
            'type': 'biometric_token',
            'session_token': sessionTokenValue,
            'skip': skipValue,
          }));
    });
  });
}
