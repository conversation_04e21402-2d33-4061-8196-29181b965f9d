import 'package:evoapp/data/request/activate_account_request.dart';
import 'package:evoapp/feature/account_activation/handler/account_activation_ui_handler.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ActivateAccountRequest', () {
    test('base class toJson should include type', () {
      final TestActivateAccountRequest request = TestActivateAccountRequest();

      expect(
        request.toJson(),
        equals(<String, String>{
          'type': AccountActivationType.none.value,
        }),
      );
    });
  });

  group('ActivateAccountVerifyOTPRequest', () {
    const String mockOtp = '123456';
    const String mockSessionToken = 'mock_session_token';

    test('should have correct type', () {
      final ActivateAccountVerifyOTPRequest request = ActivateAccountVerifyOTPRequest(
        otp: mockOtp,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.verifyOTP));
    });

    test('toJson should include type and otp', () {
      final ActivateAccountVerifyOTPRequest request = ActivateAccountVerifyOTPRequest(
        otp: mockOtp,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.verifyOTP.value,
          'otp': mockOtp,
        }),
      );
    });
  });

  group('ActivateAccountVerifyPhoneNumberRequest', () {
    const String phoneNumber = '123456';

    test('should have correct type', () {
      final ActivateAccountVerifyPhoneNumberRequest request =
          ActivateAccountVerifyPhoneNumberRequest(phoneNumber: phoneNumber);

      expect(request.type, equals(AccountActivationType.none));
    });

    test('toJson should include type and phone number', () {
      final ActivateAccountVerifyPhoneNumberRequest request =
          ActivateAccountVerifyPhoneNumberRequest(phoneNumber: phoneNumber);

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.none.value,
          'phone_number': phoneNumber,
        }),
      );
    });
  });

  group('ActivateAccountCreateUsernameRequest', () {
    const String mockUsername = 'test_username';
    const String mockSessionToken = 'mock_session_token';

    test('should have correct type', () {
      final ActivateAccountCreateUsernameRequest request = ActivateAccountCreateUsernameRequest(
        username: mockUsername,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.createUsername));
    });

    test('toJson should include type and username', () {
      final ActivateAccountCreateUsernameRequest request = ActivateAccountCreateUsernameRequest(
        username: mockUsername,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.createUsername.value,
          'user_name': mockUsername,
        }),
      );
    });

    test('constructor should set required fields', () {
      final ActivateAccountCreateUsernameRequest request = ActivateAccountCreateUsernameRequest(
        username: mockUsername,
        sessionToken: mockSessionToken,
      );

      expect(request.username, equals(mockUsername));
      expect(request.sessionToken, equals(mockSessionToken));
    });
  });

  group('ActivateAccountVerifySelfieRequest', () {
    const String mockSelfieType = 'selfie_type';
    const List<String> mockImageIds = <String>['image_id_1', 'image_id_2'];
    const List<String> mockVideoIds = <String>['video_id_1', 'video_id_2'];
    const String mockSessionToken = 'mock_token';

    test('should have correct type', () {
      final ActivateAccountVerifySelfieRequest request = ActivateAccountVerifySelfieRequest(
        selfieType: mockSelfieType,
        imageIds: mockImageIds,
        videoIds: mockVideoIds,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.verifySelfie));
    });

    test('toJson should include correct data', () {
      final ActivateAccountVerifySelfieRequest request = ActivateAccountVerifySelfieRequest(
        selfieType: mockSelfieType,
        imageIds: mockImageIds,
        videoIds: mockVideoIds,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.verifySelfie.value,
          'face_auth_request': <String, dynamic>{
            'selfie_type': mockSelfieType,
            'face_image_ids': mockImageIds,
            'video_ids': mockVideoIds,
          },
        }),
      );
    });

    test('constructor should set required fields', () {
      final ActivateAccountVerifySelfieRequest request = ActivateAccountVerifySelfieRequest(
        selfieType: mockSelfieType,
        imageIds: mockImageIds,
        videoIds: mockVideoIds,
        sessionToken: mockSessionToken,
      );

      expect(request.selfieType, equals(mockSelfieType));
      expect(request.imageIds, equals(mockImageIds));
      expect(request.videoIds, equals(mockVideoIds));
      expect(request.sessionToken, equals(mockSessionToken));
    });
  });

  group('ActivateAccountCreatePinRequest', () {
    const String mockPin = '123456';
    const String mockSessionToken = 'mock_session_token';

    test('should have correct type', () {
      final ActivateAccountCreatePinRequest request = ActivateAccountCreatePinRequest(
        pin: mockPin,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.createPin));
    });

    test('toJson should include type and pin', () {
      final ActivateAccountCreatePinRequest request = ActivateAccountCreatePinRequest(
        pin: mockPin,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.createPin.value,
          'pin': mockPin,
        }),
      );
    });

    test('constructor should set required fields', () {
      final ActivateAccountCreatePinRequest request = ActivateAccountCreatePinRequest(
        pin: mockPin,
        sessionToken: mockSessionToken,
      );

      expect(request.pin, equals(mockPin));
      expect(request.sessionToken, equals(mockSessionToken));
    });
  });

  group('ActivateAccountVerifyEmailRequest', () {
    const String mockEmail = '<EMAIL>';
    const String mockSessionToken = 'mock_session_token';

    test('should have correct fields', () {
      final ActivateAccountVerifyEmailRequest request = ActivateAccountVerifyEmailRequest(
        email: mockEmail,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.verifyEmail));
      expect(request.email, equals(mockEmail));
      expect(request.sessionToken, equals(mockSessionToken));
    });

    test('toJson should include type and email', () {
      final ActivateAccountVerifyEmailRequest request = ActivateAccountVerifyEmailRequest(
        email: mockEmail,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.verifyEmail.value,
          'email': mockEmail,
        }),
      );
    });
  });

  group('ActivateAccountVerifyEmailOTPRequest', () {
    const String mockOtp = '123456';
    const String mockSessionToken = 'mock_session_token';

    test('should have correct fields', () {
      final ActivateAccountVerifyEmailOTPRequest request = ActivateAccountVerifyEmailOTPRequest(
        otp: mockOtp,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.verifyEmailOtp));
      expect(request.otp, equals(mockOtp));
      expect(request.sessionToken, equals(mockSessionToken));
    });

    test('toJson should include type and otp', () {
      final ActivateAccountVerifyEmailOTPRequest request = ActivateAccountVerifyEmailOTPRequest(
        otp: mockOtp,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.verifyEmailOtp.value,
          'otp': mockOtp,
        }),
      );
    });
  });

  group('ActivateAccountEnableBiometricRequest', () {
    const String mockSessionToken = 'mock_session_token';

    test('should have correct type', () {
      final ActivateAccountEnableBiometricRequest request = ActivateAccountEnableBiometricRequest(
        skip: true,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.biometricToken));
    });

    test('toJson should include type and skip flag', () {
      final ActivateAccountEnableBiometricRequest request = ActivateAccountEnableBiometricRequest(
        skip: true,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.biometricToken.value,
          'skip': true,
        }),
      );
    });

    test('constructor should set required fields', () {
      final ActivateAccountEnableBiometricRequest request = ActivateAccountEnableBiometricRequest(
        skip: false,
        sessionToken: mockSessionToken,
      );

      expect(request.skip, equals(false));
      expect(request.sessionToken, equals(mockSessionToken));
    });
  });

  group('ActivateAccountActivateCardRequest', () {
    const String mockSessionToken = 'mock_session_token';

    test('should have correct type', () {
      final ActivateAccountActivateCardRequest request = ActivateAccountActivateCardRequest(
        skip: true,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.activateCard));
      expect(request.skip, true);
      expect(request.sessionToken, equals(mockSessionToken));
    });

    test('toJson should include type and skip flag is true', () {
      final ActivateAccountActivateCardRequest request = ActivateAccountActivateCardRequest(
        skip: true,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.activateCard.value,
          'skip': true,
        }),
      );
    });

    test('toJson should include type and skip flag is false', () {
      final ActivateAccountActivateCardRequest request = ActivateAccountActivateCardRequest(
        skip: false,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.activateCard.value,
          'skip': false,
        }),
      );
    });
  });

  group('ActivateAccountActivateCardVerifyOtpRequest', () {
    const String mockOtp = '123456';
    const String mockSessionToken = 'mock_session_token';

    test('should have correct type', () {
      final ActivateAccountActivateCardVerifyOtpRequest request =
          ActivateAccountActivateCardVerifyOtpRequest(
        otp: mockOtp,
        sessionToken: mockSessionToken,
      );

      expect(request.type, equals(AccountActivationType.activateCardVerifyOtp));
    });

    test('toJson should include type and otp', () {
      final ActivateAccountActivateCardVerifyOtpRequest request =
          ActivateAccountActivateCardVerifyOtpRequest(
        otp: mockOtp,
        sessionToken: mockSessionToken,
      );

      expect(
        request.toJson(),
        equals(<String, dynamic>{
          'type': AccountActivationType.activateCardVerifyOtp.value,
          'otp': mockOtp,
        }),
      );
    });

    test('constructor should set required fields', () {
      final ActivateAccountActivateCardVerifyOtpRequest request =
          ActivateAccountActivateCardVerifyOtpRequest(
        otp: mockOtp,
        sessionToken: mockSessionToken,
      );

      expect(request.otp, equals(mockOtp));
      expect(request.sessionToken, equals(mockSessionToken));
    });
  });
}

// Helper class for testing the abstract base class
class TestActivateAccountRequest extends ActivateAccountRequest {
  TestActivateAccountRequest({super.sessionToken});

  @override
  AccountActivationType get type => AccountActivationType.none;
}
