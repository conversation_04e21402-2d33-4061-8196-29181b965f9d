import 'package:evoapp/data/response/login_new_device_entity.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';

void main() {
  group('LoginNewDeviceEntity', () {
    test('should create an instance with LoginNewDeviceEntity-specific parameters', () {
      final LoginNewDeviceEntity entity = LoginNewDeviceEntity(
        challengeType: 'email',
        sessionToken: 'abc123',
        accessToken: 'access123', // Include one inherited field to verify inheritance works
      );

      expect(entity.challengeType, 'email');
      expect(entity.sessionToken, 'abc123');
      expect(entity.accessToken, 'access123'); // Verify inherited field works
    });

    test('should create an unserializable instance with null LoginNewDeviceEntity-specific fields',
        () {
      final LoginNewDeviceEntity entity = LoginNewDeviceEntity.unserializable();

      // Test LoginNewDeviceEntity-specific fields
      expect(entity.challengeType, null);
      expect(entity.sessionToken, null);
      // Inherited fields are tested in AuthorizationEntity tests
    });

    test('should create an instance from BaseResponse with LoginNewDeviceEntity-specific fields',
        () {
      final Map<String, Object> mockData = <String, Object>{
        'challenge_type': 'email',
        'session_token': 'xyz789',
        'access_token': 'access789', // Include one inherited field to verify inheritance
      };
      final BaseResponse baseResponse = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{'data': mockData});

      final LoginNewDeviceEntity entity = LoginNewDeviceEntity.fromBaseResponse(baseResponse);

      // Test LoginNewDeviceEntity-specific fields
      expect(entity.challengeType, 'email');
      expect(entity.sessionToken, 'xyz789');
      // Verify one inherited field works
      expect(entity.accessToken, 'access789');
    });

    test('should convert LoginNewDeviceEntity-specific fields to JSON correctly', () {
      final LoginNewDeviceEntity entity = LoginNewDeviceEntity(
        challengeType: 'email',
        sessionToken: 'abc123',
        accessToken: 'access123', // Include one inherited field to verify inheritance
      );

      final Map<String, dynamic> json = entity.toJson();

      // Test LoginNewDeviceEntity-specific fields in JSON
      expect(json['challenge_type'], 'email');
      expect(json['session_token'], 'abc123');
      // Verify one inherited field is included in JSON
      expect(json['access_token'], 'access123');
    });
  });
}
