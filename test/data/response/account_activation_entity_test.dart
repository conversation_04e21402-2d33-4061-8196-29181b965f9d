import 'package:evoapp/data/response/account_activation_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AccountActivationEntity', () {
    test('should verify hardcoded string constants', () {
      expect(AccountActivationEntity.verdictInvalidEntityId, 'invalid_entity_id');
      expect(AccountActivationEntity.verdictInvalidKitNo, 'invalid_kit_no');
      expect(AccountActivationEntity.verdictKitNotMappedToEntity, 'kit_not_mapped_to_entity');

      expect(AccountActivationEntity.verdictStatusRejected, 'rejected_applicant');
      expect(AccountActivationEntity.verdictStatusProcessing, 'in_progress_applicant');
      expect(AccountActivationEntity.verdictStatusNone, 'none_applicant');
      expect(AccountActivationEntity.verdictStatusExisting, 'already_sign_up');
      expect(AccountActivationEntity.verdictStatusCancelled, 'canceled_applicant');

      expect(AccountActivationEntity.verdictSuccessEmailExists, 'success_and_email_exists');
      expect(AccountActivationEntity.verdictDuplicate, 'duplicate');
    });

    test('should create an instance with AccountActivationEntity-specific parameters', () {
      final AccountActivationEntity entity = AccountActivationEntity(
        challengeType: 'challenge-type',
        sessionToken: 'abc123',
        otpResendSecs: 30,
        otpValiditySecs: 120,
        email: '<EMAIL>',
        accessToken: 'access123', // Include one inherited field to verify inheritance works
      );

      expect(entity.challengeType, 'challenge-type');
      expect(entity.sessionToken, 'abc123');
      expect(entity.otpResendSecs, 30);
      expect(entity.otpValiditySecs, 120);
      expect(entity.email, '<EMAIL>');
      expect(entity.accessToken, 'access123'); // Verify inherited field works
    });

    test(
        'should create an unserializable instance with null AccountActivationEntity-specific fields',
        () {
      final AccountActivationEntity entity = AccountActivationEntity.unserializable();

      // Test AccountActivationEntity-specific fields
      expect(entity.challengeType, null);
      expect(entity.sessionToken, null);
      expect(entity.otpResendSecs, null);
      expect(entity.otpValiditySecs, null);
      expect(entity.email, null);
      // Inherited fields are tested in AuthorizationEntity tests
    });

    test('should create an instance from BaseResponse with AccountActivationEntity-specific fields',
        () {
      final Map<String, Object> mockData = <String, Object>{
        'challenge_type': 'email',
        'session_token': 'xyz789',
        'otp_resend_secs': 60,
        'otp_validity_secs': 300,
        'email': '<EMAIL>',
        'access_token': 'access789', // Include one inherited field to verify inheritance
      };
      final BaseResponse baseResponse = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS, response: <String, dynamic>{'data': mockData});

      final AccountActivationEntity entity = AccountActivationEntity.fromBaseResponse(baseResponse);

      // Test AccountActivationEntity-specific fields
      expect(entity.challengeType, 'email');
      expect(entity.sessionToken, 'xyz789');
      expect(entity.otpResendSecs, 60);
      expect(entity.otpValiditySecs, 300);
      expect(entity.email, '<EMAIL>');
      // Verify one inherited field works
      expect(entity.accessToken, 'access789');
    });

    test('should convert AccountActivationEntity-specific fields to JSON correctly', () {
      final AccountActivationEntity entity = AccountActivationEntity(
        challengeType: 'challenge-type',
        sessionToken: 'abc123',
        otpResendSecs: 30,
        otpValiditySecs: 120,
        email: '<EMAIL>',
        accessToken: 'access123', // Include one inherited field to verify inheritance
      );

      final Map<String, dynamic> json = entity.toJson();

      // Test AccountActivationEntity-specific fields in JSON
      expect(json['challenge_type'], 'challenge-type');
      expect(json['session_token'], 'abc123');
      expect(json['otp_resend_secs'], 30);
      expect(json['otp_validity_secs'], 120);
      expect(json['email'], '<EMAIL>');
      // Verify one inherited field is included in JSON
      expect(json['access_token'], 'access123');
    });
  });
}
