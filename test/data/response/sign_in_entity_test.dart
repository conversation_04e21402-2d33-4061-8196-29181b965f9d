import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:evoapp/data/response/sign_in_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SignInEntity', () {
    test('should verify SignInEntity-specific constants', () {
      expect(SignInEntity.verdictInvalidParameter, 'invalid_parameters');
      expect(SignInEntity.verdictInvalidCredential, 'invalid_credential');
      expect(SignInEntity.verdictIncorrectOTP, 'incorrect_otp');
      expect(SignInEntity.verdictExpiredData, 'expired_data');
      expect(SignInEntity.verdictOneLastTry, 'one_last_try');
      expect(SignInEntity.verdictInvalidToken, 'invalid_token');
      expect(SignInEntity.verdictInvalidDeviceToken, 'invalid_device_token');
      expect(SignInEntity.verdictUserNotExisted, 'record_not_found');
      expect(SignInEntity.verdictLimitExceeded, 'limit_exceeded');
      expect(SignInEntity.defaultResendOtpIntervalTimeInSecs, 60);
    });

    test('should create instance from BaseResponse with default otp_resend_secs', () {
      const AuthChallengeType fakeChallengeType = AuthChallengeType.none;
      const int fakeOtpValiditySecs = 300;
      const String fakeSessionToken = 'fakeSessionToken';
      const String fakeAccessToken = 'fakeAccessToken'; // Include one inherited field

      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'challenge_type': fakeChallengeType.value,
            'otp_validity_secs': fakeOtpValiditySecs,
            'session_token': fakeSessionToken,
            'access_token': fakeAccessToken,
          },
        },
      );

      final SignInEntity result = SignInEntity.fromBaseResponse(baseResponse);

      // Test SignInEntity-specific fields
      expect(result.challengeType, fakeChallengeType.value);
      expect(result.otpResendSecs, SignInEntity.defaultResendOtpIntervalTimeInSecs);
      expect(result.otpValiditySecs, fakeOtpValiditySecs);
      expect(result.sessionToken, fakeSessionToken);
      expect(result.ekycClientSettings, isNull);
      // Verify one inherited field works
      expect(result.accessToken, fakeAccessToken);
    });

    test('should create instance from BaseResponse with all SignInEntity-specific fields', () {
      const AuthChallengeType fakeChallengeType = AuthChallengeType.none;
      const int fakeOtpResendSecs = 50;
      const int fakeOtpValiditySecs = 300;
      const String fakeSessionToken = 'fakeSessionToken';
      const String fakeAccessToken = 'fakeAccessToken'; // Include one inherited field
      final Map<String, dynamic> ekycClientSettings = <String, dynamic>{'key': 'value'};

      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'challenge_type': fakeChallengeType.value,
            'otp_resend_secs': fakeOtpResendSecs,
            'otp_validity_secs': fakeOtpValiditySecs,
            'session_token': fakeSessionToken,
            'ekyc_client_settings': ekycClientSettings,
            'access_token': fakeAccessToken,
          },
        },
      );

      final SignInEntity result = SignInEntity.fromBaseResponse(baseResponse);

      // Test SignInEntity-specific fields
      expect(result.challengeType, fakeChallengeType.value);
      expect(result.otpResendSecs, fakeOtpResendSecs);
      expect(result.otpValiditySecs, fakeOtpValiditySecs);
      expect(result.sessionToken, fakeSessionToken);
      expect(result.ekycClientSettings, ekycClientSettings);
      // Verify one inherited field works
      expect(result.accessToken, fakeAccessToken);
    });

    test('should create unserializable instance with null SignInEntity-specific fields', () {
      final SignInEntity result = SignInEntity.unserializable();

      // Test SignInEntity-specific fields
      expect(result.challengeType, isNull);
      expect(result.otpResendSecs, 0); // This has a default value of 0
      expect(result.otpValiditySecs, isNull);
      expect(result.sessionToken, isNull);
      expect(result.ekycClientSettings, isNull);
      // Inherited fields are tested in AuthorizationEntity tests
    });

    test('should convert SignInEntity-specific fields to JSON correctly', () {
      const AuthChallengeType fakeChallengeType = AuthChallengeType.none;
      const int fakeOtpResendSecs = 50;
      const int fakeOtpValiditySecs = 300;
      const String fakeSessionToken = 'fakeSessionToken';
      const String fakeAccessToken = 'fakeAccessToken'; // Include one inherited field
      final Map<String, dynamic> ekycClientSettings = <String, dynamic>{'key': 'value'};

      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'challenge_type': fakeChallengeType.value,
            'otp_resend_secs': fakeOtpResendSecs,
            'otp_validity_secs': fakeOtpValiditySecs,
            'session_token': fakeSessionToken,
            'ekyc_client_settings': ekycClientSettings,
            'access_token': fakeAccessToken,
          },
        },
      );
      final SignInEntity result = SignInEntity.fromBaseResponse(baseResponse);
      final Map<String, dynamic> json = result.toJson();

      // Test SignInEntity-specific fields in JSON
      expect(json['challenge_type'], fakeChallengeType.value);
      expect(json['otp_resend_secs'], fakeOtpResendSecs);
      expect(json['otp_validity_secs'], fakeOtpValiditySecs);
      expect(json['session_token'], fakeSessionToken);
      expect(json['ekyc_client_settings'], ekycClientSettings);
      // Verify one inherited field is included in JSON
      expect(json['access_token'], fakeAccessToken);
    });
  });
}
