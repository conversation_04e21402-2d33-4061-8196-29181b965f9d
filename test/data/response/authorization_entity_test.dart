import 'package:evoapp/data/response/authorization_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

// Concrete test implementation of the abstract AuthorizationEntity
class TestAuthorizationEntity extends AuthorizationEntity {
  TestAuthorizationEntity({
    super.accessToken,
    super.authNotiToken,
    super.biometricToken,
    super.deviceToken,
    super.refreshToken,
    super.userId,
  });

  TestAuthorizationEntity.unserializable({
    super.localExceptionCode,
  }) : super.unserializable();

  TestAuthorizationEntity.fromBaseResponse(super.baseResponse) : super.fromBaseResponse();
}

void main() {
  group('AuthorizationEntity', () {
    const String fakeAccessToken = 'fakeAccessToken';
    const String fakeAuthNotiToken = 'fakeAuthNotiToken';
    const String fakeBiometricToken = 'fakeBiometricToken';
    const String fakeDeviceToken = 'fakeDeviceToken';
    const String fakeRefreshToken = 'fakeRefreshToken';
    const int fakeUserId = 12345;

    group('default constructor', () {
      test('should create entity with all parameters', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity(
          accessToken: fakeAccessToken,
          authNotiToken: fakeAuthNotiToken,
          biometricToken: fakeBiometricToken,
          deviceToken: fakeDeviceToken,
          refreshToken: fakeRefreshToken,
          userId: fakeUserId,
        );

        expect(entity.accessToken, fakeAccessToken);
        expect(entity.authNotiToken, fakeAuthNotiToken);
        expect(entity.biometricToken, fakeBiometricToken);
        expect(entity.deviceToken, fakeDeviceToken);
        expect(entity.refreshToken, fakeRefreshToken);
        expect(entity.userId, fakeUserId);
      });

      test('should create entity with null parameters', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity();

        expect(entity.accessToken, isNull);
        expect(entity.authNotiToken, isNull);
        expect(entity.biometricToken, isNull);
        expect(entity.deviceToken, isNull);
        expect(entity.refreshToken, isNull);
        expect(entity.userId, isNull);
      });

      test('should create entity with partial parameters', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity(
          accessToken: fakeAccessToken,
          deviceToken: fakeDeviceToken,
        );

        expect(entity.accessToken, fakeAccessToken);
        expect(entity.authNotiToken, isNull);
        expect(entity.biometricToken, isNull);
        expect(entity.deviceToken, fakeDeviceToken);
        expect(entity.refreshToken, isNull);
        expect(entity.userId, isNull);
      });
    });

    group('unserializable constructor', () {
      test('should create entity with null values and default exception code', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity.unserializable();

        expect(entity.accessToken, isNull);
        expect(entity.authNotiToken, isNull);
        expect(entity.biometricToken, isNull);
        expect(entity.deviceToken, isNull);
        expect(entity.refreshToken, isNull);
        expect(entity.userId, isNull);
        expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      });

      test('should create entity with custom exception code', () {
        const int customExceptionCode = 999;
        final TestAuthorizationEntity entity = TestAuthorizationEntity.unserializable(
          localExceptionCode: customExceptionCode,
        );

        expect(entity.accessToken, isNull);
        expect(entity.authNotiToken, isNull);
        expect(entity.biometricToken, isNull);
        expect(entity.deviceToken, isNull);
        expect(entity.refreshToken, isNull);
        expect(entity.userId, isNull);
        expect(entity.localExceptionCode, customExceptionCode);
      });
    });

    group('fromBaseResponse constructor', () {
      test('should create entity from BaseResponse with all fields', () {
        final BaseResponse baseResponse = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'access_token': fakeAccessToken,
              'auth_noti_token': fakeAuthNotiToken,
              'biometric_token': fakeBiometricToken,
              'device_token': fakeDeviceToken,
              'refresh_token': fakeRefreshToken,
              'user_id': fakeUserId,
            },
          },
        );

        final TestAuthorizationEntity entity =
            TestAuthorizationEntity.fromBaseResponse(baseResponse);

        expect(entity.accessToken, fakeAccessToken);
        expect(entity.authNotiToken, fakeAuthNotiToken);
        expect(entity.biometricToken, fakeBiometricToken);
        expect(entity.deviceToken, fakeDeviceToken);
        expect(entity.refreshToken, fakeRefreshToken);
        expect(entity.userId, fakeUserId);
      });

      test('should create entity from BaseResponse with null data', () {
        final BaseResponse baseResponse = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': null,
          },
        );

        final TestAuthorizationEntity entity =
            TestAuthorizationEntity.fromBaseResponse(baseResponse);

        expect(entity.accessToken, isNull);
        expect(entity.authNotiToken, isNull);
        expect(entity.biometricToken, isNull);
        expect(entity.deviceToken, isNull);
        expect(entity.refreshToken, isNull);
        expect(entity.userId, isNull);
      });

      test('should create entity from BaseResponse with partial data', () {
        final BaseResponse baseResponse = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{
              'access_token': fakeAccessToken,
              'device_token': fakeDeviceToken,
              'user_id': fakeUserId,
            },
          },
        );

        final TestAuthorizationEntity entity =
            TestAuthorizationEntity.fromBaseResponse(baseResponse);

        expect(entity.accessToken, fakeAccessToken);
        expect(entity.authNotiToken, isNull);
        expect(entity.biometricToken, isNull);
        expect(entity.deviceToken, fakeDeviceToken);
        expect(entity.refreshToken, isNull);
        expect(entity.userId, fakeUserId);
      });

      test('should handle empty data object', () {
        final BaseResponse baseResponse = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': <String, dynamic>{},
          },
        );

        final TestAuthorizationEntity entity =
            TestAuthorizationEntity.fromBaseResponse(baseResponse);

        expect(entity.accessToken, isNull);
        expect(entity.authNotiToken, isNull);
        expect(entity.biometricToken, isNull);
        expect(entity.deviceToken, isNull);
        expect(entity.refreshToken, isNull);
        expect(entity.userId, isNull);
      });
    });

    group('toJson method', () {
      test('should return correct JSON with all fields', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity(
          accessToken: fakeAccessToken,
          authNotiToken: fakeAuthNotiToken,
          biometricToken: fakeBiometricToken,
          deviceToken: fakeDeviceToken,
          refreshToken: fakeRefreshToken,
          userId: fakeUserId,
        );

        final Map<String, dynamic> json = entity.toJson();

        expect(json['access_token'], fakeAccessToken);
        expect(json['auth_noti_token'], fakeAuthNotiToken);
        expect(json['biometric_token'], fakeBiometricToken);
        expect(json['device_token'], fakeDeviceToken);
        expect(json['refresh_token'], fakeRefreshToken);
        expect(json['user_id'], fakeUserId);
      });

      test('should return correct JSON with null fields', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity();

        final Map<String, dynamic> json = entity.toJson();

        expect(json['access_token'], isNull);
        expect(json['auth_noti_token'], isNull);
        expect(json['biometric_token'], isNull);
        expect(json['device_token'], isNull);
        expect(json['refresh_token'], isNull);
        expect(json['user_id'], isNull);
      });

      test('should return correct JSON with partial fields', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity(
          accessToken: fakeAccessToken,
          deviceToken: fakeDeviceToken,
          userId: fakeUserId,
        );

        final Map<String, dynamic> json = entity.toJson();

        expect(json['access_token'], fakeAccessToken);
        expect(json['auth_noti_token'], isNull);
        expect(json['biometric_token'], isNull);
        expect(json['device_token'], fakeDeviceToken);
        expect(json['refresh_token'], isNull);
        expect(json['user_id'], fakeUserId);
      });
    });

    group('hasAuthorizeToken getter', () {
      test('should return true when all required tokens are present', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity(
          accessToken: fakeAccessToken,
          deviceToken: fakeDeviceToken,
          refreshToken: fakeRefreshToken,
        );

        expect(entity.hasAuthorizeToken, isTrue);
      });

      test('should return false when accessToken is null', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity(
          deviceToken: fakeDeviceToken,
          refreshToken: fakeRefreshToken,
        );

        expect(entity.hasAuthorizeToken, isFalse);
      });

      test('should return false when deviceToken is null', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity(
          accessToken: fakeAccessToken,
          refreshToken: fakeRefreshToken,
        );

        expect(entity.hasAuthorizeToken, isFalse);
      });

      test('should return false when refreshToken is null', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity(
          accessToken: fakeAccessToken,
          deviceToken: fakeDeviceToken,
        );

        expect(entity.hasAuthorizeToken, isFalse);
      });

      test('should return false when all required tokens are null', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity();

        expect(entity.hasAuthorizeToken, isFalse);
      });

      test('should return true even when optional tokens are null', () {
        final TestAuthorizationEntity entity = TestAuthorizationEntity(
          accessToken: fakeAccessToken,
          deviceToken: fakeDeviceToken,
          refreshToken: fakeRefreshToken,
          // authNotiToken and biometricToken are null
        );

        expect(entity.hasAuthorizeToken, isTrue);
      });
    });

    group('roundtrip serialization', () {
      test('should maintain data integrity through fromBaseResponse and toJson', () {
        final Map<String, dynamic> originalData = <String, dynamic>{
          'access_token': fakeAccessToken,
          'auth_noti_token': fakeAuthNotiToken,
          'biometric_token': fakeBiometricToken,
          'device_token': fakeDeviceToken,
          'refresh_token': fakeRefreshToken,
          'user_id': fakeUserId,
        };

        final BaseResponse baseResponse = BaseResponse(
          statusCode: CommonHttpClient.SUCCESS,
          response: <String, dynamic>{
            'data': originalData,
          },
        );

        final TestAuthorizationEntity entity =
            TestAuthorizationEntity.fromBaseResponse(baseResponse);
        final Map<String, dynamic> json = entity.toJson();

        expect(json['access_token'], originalData['access_token']);
        expect(json['auth_noti_token'], originalData['auth_noti_token']);
        expect(json['biometric_token'], originalData['biometric_token']);
        expect(json['device_token'], originalData['device_token']);
        expect(json['refresh_token'], originalData['refresh_token']);
        expect(json['user_id'], originalData['user_id']);
      });
    });
  });
}
