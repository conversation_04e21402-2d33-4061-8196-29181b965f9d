import 'package:evoapp/data/response/transaction_list_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TransactionGroupItemEntity', () {
    test('should create an instance with correct values', () {
      final TransactionGroupItemEntity item = TransactionGroupItemEntity(
        id: '123',
        paidTo: 'Netflix',
        amount: 100.0,
        dateTime: DateTime(2024, 3, 15),
        paidBy: '5638',
      );

      expect(item.id, '123');
      expect(item.paidTo, 'Netflix');
      expect(item.amount, 100.0);
      expect(item.dateTime, DateTime(2024, 3, 15));
      expect(item.paidBy, '5638');
    });

    test('should handle nullable fields', () {
      final TransactionGroupItemEntity item = TransactionGroupItemEntity(
        id: null,
        paidTo: null,
        amount: null,
        dateTime: null,
        paidBy: null,
      );

      expect(item.id, null);
      expect(item.paidTo, null);
      expect(item.amount, null);
      expect(item.dateTime, null);
      expect(item.paidBy, null);
    });
  });

  group('TransactionGroupEntity', () {
    final List<TransactionGroupItemEntity> testTransactions = <TransactionGroupItemEntity>[
      TransactionGroupItemEntity(
        id: '123',
        paidTo: 'Netflix',
        amount: 100.0,
        dateTime: DateTime(2024, 3, 15),
        paidBy: '5638',
      ),
      TransactionGroupItemEntity(
        id: '456',
        paidTo: 'Spotify',
        amount: 50.0,
        dateTime: DateTime(2024, 3, 16),
        paidBy: '5638',
      ),
    ];

    test('should create instance with correct values', () {
      final TransactionGroupEntity group = TransactionGroupEntity(
        name: 'March 2024',
        total: 150.0,
        transactions: testTransactions,
      );

      expect(group.name, 'March 2024');
      expect(group.total, 150.0);
      expect(group.transactions, testTransactions);
      expect(group.transactions.length, 2);
    });
  });

  group('TransactionListEntity', () {
    final List<TransactionGroupEntity> testGroups = <TransactionGroupEntity>[
      TransactionGroupEntity(
        name: 'March 2024',
        total: 150.0,
        transactions: <TransactionGroupItemEntity>[
          TransactionGroupItemEntity(
            id: '123',
            paidTo: 'Netflix',
            amount: 100.0,
            dateTime: DateTime(2024, 3, 15),
            paidBy: '5638',
          ),
        ],
      ),
      TransactionGroupEntity(
        name: 'February 2024',
        total: 200.0,
        transactions: <TransactionGroupItemEntity>[
          TransactionGroupItemEntity(
            id: '456',
            paidTo: 'Spotify',
            amount: 50.0,
            dateTime: DateTime(2024, 2, 16),
            paidBy: '5638',
          ),
        ],
      ),
    ];

    test('should create instance with correct values', () {
      final TransactionListEntity list = TransactionListEntity(
        groups: testGroups,
      );

      expect(list.groups, testGroups);
      expect(list.groups?.length, 2);
    });
  });
}
