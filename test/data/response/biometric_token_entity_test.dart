import 'package:evoapp/data/response/biometric_token_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeAccessToken = 'access_token_example';
  const String fakeRefreshToken = 'refresh_token_example';
  const String fakeDeviceToken = 'device_token_example';
  const String fakeBiometricToken = 'biometric_token_example';
  const String fakeChallengeType = 'challenge_type_example';

  group('BiometricTokenEntity', () {
    test('unserializable creates a BiometricTokenEntity with localExceptionCode', () {
      final BiometricTokenEntity entity = BiometricTokenEntity.unserializable();
      expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
      expect(entity.accessToken, null);
      expect(entity.refreshToken, null);
      expect(entity.biometricToken, null);
      expect(entity.challengeType, null);
      expect(entity.deviceToken, null);
    });

    test('fromBaseResponse creates a valid BiometricTokenEntity', () {
      final BaseResponse response = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'access_token': fakeAccessToken,
            'refresh_token': fakeRefreshToken,
            'device_token': fakeDeviceToken,
            'biometric_token': fakeBiometricToken,
            'challenge_type': fakeChallengeType,
          }
        },
      );

      final BiometricTokenEntity entity = BiometricTokenEntity.fromBaseResponse(response);
      expect(entity.accessToken, fakeAccessToken);
      expect(entity.refreshToken, fakeRefreshToken);
      expect(entity.deviceToken, fakeDeviceToken);
      expect(entity.biometricToken, fakeBiometricToken);
      expect(entity.challengeType, fakeChallengeType);
    });

    test('toJson returns a valid Map', () {
      final BiometricTokenEntity entity = BiometricTokenEntity.newInstance(
        accessToken: fakeAccessToken,
        refreshToken: fakeRefreshToken,
        deviceToken: fakeDeviceToken,
        biometricToken: fakeBiometricToken,
        challengeType: fakeChallengeType,
      );

      final Map<String, dynamic> json = entity.toJson();
      expect(json['access_token'], fakeAccessToken);
      expect(json['refresh_token'], fakeRefreshToken);
      expect(json['device_token'], fakeDeviceToken);
      expect(json['biometric_token'], fakeBiometricToken);
      expect(json['challenge_type'], fakeChallengeType);
    });
  });
}
