import 'package:evoapp/data/response/force_update_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String fakeLatestVersion = '1.0.1';

  group('ForceUpdateEntity', () {
    test('unserializable should create ForceUpdateEntity with null values', () {
      final ForceUpdateEntity entity = ForceUpdateEntity.unserializable();

      expect(entity.forceToUpdate, null);
      expect(entity.hasNewerVersion, null);
      expect(entity.latestVersion, null);
    });

    test('fromBaseResponse should create ForceUpdateEntity from BaseResponse', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'force_to_update': true,
            'has_newer_version': true,
            'latest_version': fakeLatestVersion,
          }
        },
      );

      final ForceUpdateEntity entity = ForceUpdateEntity.fromBaseResponse(baseResponse);

      expect(entity.forceToUpdate, true);
      expect(entity.hasNewerVersion, true);
      expect(entity.latestVersion, fakeLatestVersion);
    });
  });
}
