import 'package:evoapp/data/response/user_information_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('UserInformationEntity', () {
    test('copyWith method should update specified fields', () {
      const UserInformationEntity original = UserInformationEntity(
        fullName: '<PERSON>',
        gender: 'Male',
        birthday: '1990-01-01',
        identityCardIssueDate: '2010-01-01',
        identityCardNumber: '123456789',
        phoneNumber: '555-1234',
        email: '<EMAIL>',
        avatarUrl: 'http://example.com/avatar.jpg',
      );

      final UserInformationEntity updated = original.copyWith(
        fullName: '<PERSON>',
        email: '<EMAIL>',
      );

      expect(updated.fullName, '<PERSON>');
      expect(updated.email, '<EMAIL>');
      expect(updated.gender, original.gender);
      expect(updated.birthday, original.birthday);
      expect(updated.identityCardIssueDate, original.identityCardIssueDate);
      expect(updated.identityCardNumber, original.identityCardNumber);
      expect(updated.phoneNumber, original.phoneNumber);
      expect(updated.avatarUrl, original.avatarUrl);
    });

    test('copyWith method should return identical object when no fields are updated', () {
      const UserInformationEntity original = UserInformationEntity(
        fullName: 'John Doe',
        gender: 'Male',
        birthday: '1990-01-01',
        identityCardIssueDate: '2010-01-01',
        identityCardNumber: '123456789',
        phoneNumber: '555-1234',
        email: '<EMAIL>',
        avatarUrl: 'http://example.com/avatar.jpg',
      );

      final UserInformationEntity updated = original.copyWith();

      expect(updated, original);
    });
  });
}
