import 'package:evoapp/data/response/reset_pin_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('verify verdict constant', () {

    /// verify verdict from status code [CommonHttpClient.LIMIT_EXCEEDED]
    expect(ResetPinEntity.verdictLimitExceeded, 'limit_exceeded');

    /// verify verdict from status code [CommonHttpClient.NOT_FOUND]
    expect(ResetPinEntity.verdictUserNotExisted, 'record_not_found');

    /// verify verdict from status code [CommonHttpClient.LOCKED_RESOURCE]
    expect(ResetPinEntity.verdictLockedResource, 'locked_resource');

    /// verify verdict from status code [CommonHttpClient.BAD_REQUEST]
    expect(ResetPinEntity.verdictInvalidParameters, 'invalid_parameters');
    expect(ResetPinEntity.verdictExpiredData, 'expired_data');
    expect(ResetPinEntity.verdictInvalidPin, 'invalid_pin');
    expect(ResetPinEntity.verdictIncorrectOtp, 'incorrect_otp');
    expect(ResetPinEntity.verdictIncrementPin, 'incremental_pin');
    expect(ResetPinEntity.verdictDecrementalPin, 'decremental_pin');
    expect(ResetPinEntity.verdictRepeatedPin, 'repeated_pin');
    expect(ResetPinEntity.verdictPinMatchHistory, 'pin_match_history');

    /// verify verdict from status code [CommonHttpClient.INVALID_TOKEN]
    expect(ResetPinEntity.verdictMissingResetPinSession, 'missing_authorization');
    expect(ResetPinEntity.verdictInvalidResetPinSession, 'invalid_token');
    expect(ResetPinEntity.verdictExpiredResetPinSession, 'expired_token');

    /// verify verdict from status code [CommonHttpClient.UNKNOWN_ERRORS]
    expect(ResetPinEntity.verdictFailure, 'failure');
  });

  group('verify ResetPinEntity', () {
    const String fakeChallengeType = 'sample_challenge_type';
    const String fakeSessionToken = 'sample_session_token';
    const int fakeOtpResendSecs = 60;
    const int fakeOtpValiditySecs = 3;
    const int fakeRetryRemainingCount = 3;
    const Map<String, dynamic> fakeEkycClientSettings = <String, dynamic>{};

    test('fromBaseResponse should return a valid ResetPinEntity', () {
      final BaseResponse baseResponse = BaseResponse(
        statusCode: CommonHttpClient.SUCCESS,
        response: <String, dynamic>{
          'data': <String, dynamic>{
            'challenge_type': fakeChallengeType,
            'session_token': fakeSessionToken,
            'otp_resend_secs': fakeOtpResendSecs,
            'otp_validity_secs': fakeOtpValiditySecs,
            'retry_remaining_count': fakeRetryRemainingCount,
            'ekyc_client_settings': fakeEkycClientSettings,
          },
        },
      );

      final ResetPinEntity result = ResetPinEntity.fromBaseResponse(baseResponse);

      expect(result.challengeType, fakeChallengeType);
      expect(result.sessionToken, fakeSessionToken);
      expect(result.otpResendSecs, fakeOtpResendSecs);
      expect(result.otpValiditySecs, fakeOtpValiditySecs);
      expect(result.retryRemainingCount, fakeRetryRemainingCount);
      expect(result.ekycClientSettings, fakeEkycClientSettings);
    });

    test('toJson should return a valid JSON representation', () {
      final ResetPinEntity resetPinEntity = ResetPinEntity(
        challengeType: fakeChallengeType,
        sessionToken: fakeSessionToken,
        otpResendSecs: fakeOtpResendSecs,
        otpValiditySecs: fakeOtpValiditySecs,
        retryRemainingCount: fakeRetryRemainingCount,
        ekycClientSettings: fakeEkycClientSettings,
      );

      final Map<String, dynamic> json = resetPinEntity.toJson();

      expect(json['challenge_type'], fakeChallengeType);
      expect(json['session_token'], fakeSessionToken);
      expect(json['otp_resend_secs'], fakeOtpResendSecs);
      expect(json['otp_validity_secs'], fakeOtpValiditySecs);
      expect(json['retry_remaining_count'], fakeRetryRemainingCount);
      expect(json['ekyc_client_settings'], fakeEkycClientSettings);
    });

    test('Constructor unSerializable() should create an instance with null properties', () {
      final ResetPinEntity resetPinEntity = ResetPinEntity.unSerializable();

      expect(resetPinEntity.challengeType, null);
      expect(resetPinEntity.sessionToken, null);
      expect(resetPinEntity.otpResendSecs, null);
      expect(resetPinEntity.otpValiditySecs, null);
      expect(resetPinEntity.retryRemainingCount, null);
      expect(resetPinEntity.ekycClientSettings, null);
    });
  });
}
