import 'package:evoapp/data/response/transaction_details_entity.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('TransactionDetailsEntity', () {
    test('should create an instance with default values', () {
      final TransactionDetailsEntity entity = TransactionDetailsEntity();

      expect(entity.id, isNull);
      expect(entity.amount, isNull);
      expect(entity.paidTo, isNull);
      expect(entity.paidBy, isNull);
      expect(entity.dateTime, isNull);
      expect(entity.postingDate, isNull);
      expect(entity.byVirtualCard, isNull);
    });

    test('should create an instance with provided values', () {
      final DateTime dateTime = DateTime(2024, 2, 16, 19, 09);
      final DateTime postingDate = DateTime(2024, 2, 19, 19, 09);

      final TransactionDetailsEntity entity = TransactionDetailsEntity(
        id: 'tx-123456',
        amount: 150.75,
        paidTo: 'Coffee Shop',
        paidBy: '4321',
        dateTime: dateTime,
        postingDate: postingDate,
        byVirtualCard: true,
      );

      expect(entity.id, 'tx-123456');
      expect(entity.amount, 150.75);
      expect(entity.paidTo, 'Coffee Shop');
      expect(entity.paidBy, '4321');
      expect(entity.dateTime, dateTime);
      expect(entity.postingDate, postingDate);
      expect(entity.byVirtualCard, true);
    });
  });
}
