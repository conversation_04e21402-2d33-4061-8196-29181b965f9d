import 'package:evoapp/data/response/change_pin_entity.dart';
import 'package:flutter_common_package/data/http_client/base_response.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  const String mockSessionToken = 'mock_session_token';
  const String mockChallengeType = 'mock_challenge_type';
  final BaseResponse mockResponse = BaseResponse(
    statusCode: CommonHttpClient.SUCCESS,
    response: <String, dynamic>{
      'data': <String, dynamic>{
        'session_token': mockSessionToken,
        'challenge_type': mockChallengeType,
      }
    },
  );

  test('verify verdict', () {
    expect(ChangePinEntity.verdictInvalidCredential, 'invalid_credential');
    expect(ChangePinEntity.verdictLockedResource, 'locked_resource');
  });

  test('unserializable creates a ChangePinEntity with localExceptionCode', () {
    final ChangePinEntity entity = ChangePinEntity.unserializable();
    expect(entity.challengeType, isNull);
    expect(entity.sessionToken, isNull);
    expect(entity.localExceptionCode, CommonHttpClient.INVALID_FORMAT);
  });

  test('fromBaseResponse creates a valid ChangePinEntity', () {
    final ChangePinEntity entity = ChangePinEntity.fromBaseResponse(mockResponse);

    expect(entity.sessionToken, mockSessionToken);
    expect(entity.challengeType, mockChallengeType);
  });

  test('toJson returns a valid Map', () {
    final ChangePinEntity entity = ChangePinEntity.fromBaseResponse(mockResponse);

    final Map<String, dynamic> json = entity.toJson();

    expect(json['challenge_type'], mockChallengeType);
    expect(json['session_token'], mockSessionToken);
  });
}
