import 'package:evoapp/data/response/auth_challenge_type.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('verify SignInChallengeType', () {
    test('fromString returns correct enum for valid strings', () {
      expect(AuthChallengeType.fromString('verify_pin'), AuthChallengeType.verifyPin);
      expect(AuthChallengeType.fromString('create_pin'), AuthChallengeType.createPin);
      expect(AuthChallengeType.fromString('change_pin'), AuthChallengeType.changePin);
      expect(AuthChallengeType.fromString('face_auth'), AuthChallengeType.faceAuth);
      expect(AuthChallengeType.fromString('none'), AuthChallengeType.none);
    });

    test('fromString returns none for invalid strings', () {
      expect(AuthChallengeType.fromString('invalid_string'), AuthChallengeType.none);
      expect(AuthChallengeType.fromString('another_invalid_string'), AuthChallengeType.none);
    });

    test('fromString returns null for null input', () {
      expect(AuthChallengeType.fromString(null), isNull);
    });

    test('Enum values are correct', () {
      expect(AuthChallengeType.none.value, 'none');
      expect(AuthChallengeType.verifyPin.value, 'verify_pin');
      expect(AuthChallengeType.createPin.value, 'create_pin');
      expect(AuthChallengeType.changePin.value, 'change_pin');
      expect(AuthChallengeType.faceAuth.value, 'face_auth');
    });
  });
}
