import 'package:evoapp/data/constants.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  test('HeaderKey constants have the expected values', () {
    expect(HeaderKey.sessionToken, 'X-SESSION');
    expect(HeaderKey.deviceToken, 'x-device-token');
    expect(HeaderKey.authorization, 'Authorization');
  });

  test('DurationConstants constants have the expected values', () {
    expect(DurationConstants.defaultForegroundInactiveDurationInSec, 300);
    expect(DurationConstants.defaultDurationLogoutAfterInactiveInSec, 60);
    expect(DurationConstants.defaultBackgroundInactiveDurationInSec, 300);
  });
}
